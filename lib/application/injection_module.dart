import 'dart:developer';

import 'package:avema_v2/application/dio_curl_logger_interceptor.dart';
import 'package:avema_v2/application/utils/avema_helper.dart';
import 'package:avema_v2/features/curl_helper/application/curl_helper_bloc.dart';
import 'package:avema_v2/features/setting/setting_bloc.dart';
import 'package:avema_v2/injection/injection.dart';
import 'package:avema_v2/rest_client/main_rest_client.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import 'unauthorized_interceptor.dart';

@module
abstract class ClientInjectableModule {
  @lazySingleton
  Dio get dio => Dio()
    ..interceptors.add(
      CurlLoggerDioInterceptor(
        printCURL: (curl) {
          log('''
          '===RESTClient curl==='
          $curl
          ======================
          ''');
        },
        onDone: (status, message, apiId, response, appVersionByString) async {
          getIt<CurlHelperBloc>().add(
            CurlHelperEvent.update(
              statusCode: status,
              message: message,
              apiId: apiId,
              response: response,
            ),
          );

          if (appVersionByString.isNotEmpty) {
            final appVersionFromAPI = AvemaHelper.getVersionApp(
              versionAppByString: appVersionByString.first,
            );

            final bool versionAppNull =
                getIt<SettingBloc>().state.versionApp == null;

            final shouldSyncToken = versionAppNull ||
                appVersionFromAPI != getIt<SettingBloc>().state.versionApp;

            if (shouldSyncToken) {
              getIt<SettingBloc>().add(
                SettingEvent.syncVersionApp(
                  versionApp: appVersionFromAPI,
                ),
              );
            }
          }
        },
        onRequestAPI: (curlRepresentation) {
          getIt<CurlHelperBloc>().add(
            CurlHelperEvent.add(
              curl: curlRepresentation,
            ),
          );
        },
      ),
    )
    ..interceptors.add(
      UnauthorizedInterceptor(),
    );

  @lazySingleton
  MainRESTClient get mainRestClient => MainRESTClient(
        getIt<Dio>(),
        // baseUrl: 'http://192.168.38.99:24579/api',
        // baseUrl: 'https://binhlh-gw.vietmap.vn/m-dev/api',
        baseUrl: AvemaAPI.baseUrl,
      );
}

class AvemaAPI {
  static final AvemaAPI _instance = AvemaAPI._internal();

  AvemaAPI._internal();

  static AvemaAPI get instance => _instance;

  static String baseUrl =
      //  'https://binhlh-gw.vietmap.vn/m-dev/api';
      'https://api-gateway.avema-iot.com/m-prod/api';
  // 'https://vietmap.nangphanvan.software/api';

  // QR CODE

  static String urlGetQRCode = '$baseUrl/v1/locator/getlocatorqrcode';

  static const int unAuthorized = 401;

  static Map<String, dynamic>? queryParametersGetQRCode({
    required id,
    required userId,
  }) {
    return {
      'id': id,
      'userId': userId,
    };
  }

  // LANGUAGE

  static String getLanguageFromServer = '$baseUrl/v1/language/get';
}
