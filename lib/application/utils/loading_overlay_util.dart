import 'package:flutter/material.dart';


class LoadingOverlayUtil {
  static OverlayEntry? _overlayEntry;

  static void show(BuildContext context) {
    hide();

    _overlayEntry = OverlayEntry(
      builder: (_) => Container(
        color: Colors.black.withOpacity(0.5),
        child: const Center(
          child: CircularProgressIndicator(
            strokeWidth: 4,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  static Future<void> hide() async {
    if (_overlayEntry == null) return;

    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  static void hideSync() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}
