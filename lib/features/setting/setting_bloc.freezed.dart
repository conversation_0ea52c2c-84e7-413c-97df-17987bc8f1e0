// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'setting_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SettingEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getDefaultSetting,
    required TResult Function(MonitorSettingModal setting) adjustMonitorSetting,
    required TResult Function(HistorySettingModal setting) adjustHistorySetting,
    required TResult Function(List<String> listFilterVehicleId)
        changeFilterVehicleList,
    required TResult Function(int versionApp) syncVersionApp,
    required TResult Function() resetFilterVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getDefaultSetting,
    TResult? Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult? Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult? Function(List<String> listFilterVehicleId)?
        changeFilterVehicleList,
    TResult? Function(int versionApp)? syncVersionApp,
    TResult? Function()? resetFilterVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getDefaultSetting,
    TResult Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult Function(List<String> listFilterVehicleId)? changeFilterVehicleList,
    TResult Function(int versionApp)? syncVersionApp,
    TResult Function()? resetFilterVehicle,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDefaultSetting value) getDefaultSetting,
    required TResult Function(_AdjustMonitorSetting value) adjustMonitorSetting,
    required TResult Function(_AdjustHistorySetting value) adjustHistorySetting,
    required TResult Function(_ChangeFilterVehicleList value)
        changeFilterVehicleList,
    required TResult Function(_SyncVersionApp value) syncVersionApp,
    required TResult Function(_ResetFilterVehicle value) resetFilterVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult? Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult? Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult? Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult? Function(_SyncVersionApp value)? syncVersionApp,
    TResult? Function(_ResetFilterVehicle value)? resetFilterVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult Function(_SyncVersionApp value)? syncVersionApp,
    TResult Function(_ResetFilterVehicle value)? resetFilterVehicle,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingEventCopyWith<$Res> {
  factory $SettingEventCopyWith(
          SettingEvent value, $Res Function(SettingEvent) then) =
      _$SettingEventCopyWithImpl<$Res, SettingEvent>;
}

/// @nodoc
class _$SettingEventCopyWithImpl<$Res, $Val extends SettingEvent>
    implements $SettingEventCopyWith<$Res> {
  _$SettingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$GetDefaultSettingImplCopyWith<$Res> {
  factory _$$GetDefaultSettingImplCopyWith(_$GetDefaultSettingImpl value,
          $Res Function(_$GetDefaultSettingImpl) then) =
      __$$GetDefaultSettingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetDefaultSettingImplCopyWithImpl<$Res>
    extends _$SettingEventCopyWithImpl<$Res, _$GetDefaultSettingImpl>
    implements _$$GetDefaultSettingImplCopyWith<$Res> {
  __$$GetDefaultSettingImplCopyWithImpl(_$GetDefaultSettingImpl _value,
      $Res Function(_$GetDefaultSettingImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$GetDefaultSettingImpl implements _GetDefaultSetting {
  const _$GetDefaultSettingImpl();

  @override
  String toString() {
    return 'SettingEvent.getDefaultSetting()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetDefaultSettingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getDefaultSetting,
    required TResult Function(MonitorSettingModal setting) adjustMonitorSetting,
    required TResult Function(HistorySettingModal setting) adjustHistorySetting,
    required TResult Function(List<String> listFilterVehicleId)
        changeFilterVehicleList,
    required TResult Function(int versionApp) syncVersionApp,
    required TResult Function() resetFilterVehicle,
  }) {
    return getDefaultSetting();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getDefaultSetting,
    TResult? Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult? Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult? Function(List<String> listFilterVehicleId)?
        changeFilterVehicleList,
    TResult? Function(int versionApp)? syncVersionApp,
    TResult? Function()? resetFilterVehicle,
  }) {
    return getDefaultSetting?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getDefaultSetting,
    TResult Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult Function(List<String> listFilterVehicleId)? changeFilterVehicleList,
    TResult Function(int versionApp)? syncVersionApp,
    TResult Function()? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (getDefaultSetting != null) {
      return getDefaultSetting();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDefaultSetting value) getDefaultSetting,
    required TResult Function(_AdjustMonitorSetting value) adjustMonitorSetting,
    required TResult Function(_AdjustHistorySetting value) adjustHistorySetting,
    required TResult Function(_ChangeFilterVehicleList value)
        changeFilterVehicleList,
    required TResult Function(_SyncVersionApp value) syncVersionApp,
    required TResult Function(_ResetFilterVehicle value) resetFilterVehicle,
  }) {
    return getDefaultSetting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult? Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult? Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult? Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult? Function(_SyncVersionApp value)? syncVersionApp,
    TResult? Function(_ResetFilterVehicle value)? resetFilterVehicle,
  }) {
    return getDefaultSetting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult Function(_SyncVersionApp value)? syncVersionApp,
    TResult Function(_ResetFilterVehicle value)? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (getDefaultSetting != null) {
      return getDefaultSetting(this);
    }
    return orElse();
  }
}

abstract class _GetDefaultSetting implements SettingEvent {
  const factory _GetDefaultSetting() = _$GetDefaultSettingImpl;
}

/// @nodoc
abstract class _$$AdjustMonitorSettingImplCopyWith<$Res> {
  factory _$$AdjustMonitorSettingImplCopyWith(_$AdjustMonitorSettingImpl value,
          $Res Function(_$AdjustMonitorSettingImpl) then) =
      __$$AdjustMonitorSettingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({MonitorSettingModal setting});

  $MonitorSettingModalCopyWith<$Res> get setting;
}

/// @nodoc
class __$$AdjustMonitorSettingImplCopyWithImpl<$Res>
    extends _$SettingEventCopyWithImpl<$Res, _$AdjustMonitorSettingImpl>
    implements _$$AdjustMonitorSettingImplCopyWith<$Res> {
  __$$AdjustMonitorSettingImplCopyWithImpl(_$AdjustMonitorSettingImpl _value,
      $Res Function(_$AdjustMonitorSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setting = null,
  }) {
    return _then(_$AdjustMonitorSettingImpl(
      setting: null == setting
          ? _value.setting
          : setting // ignore: cast_nullable_to_non_nullable
              as MonitorSettingModal,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $MonitorSettingModalCopyWith<$Res> get setting {
    return $MonitorSettingModalCopyWith<$Res>(_value.setting, (value) {
      return _then(_value.copyWith(setting: value));
    });
  }
}

/// @nodoc

class _$AdjustMonitorSettingImpl implements _AdjustMonitorSetting {
  const _$AdjustMonitorSettingImpl({required this.setting});

  @override
  final MonitorSettingModal setting;

  @override
  String toString() {
    return 'SettingEvent.adjustMonitorSetting(setting: $setting)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdjustMonitorSettingImpl &&
            (identical(other.setting, setting) || other.setting == setting));
  }

  @override
  int get hashCode => Object.hash(runtimeType, setting);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AdjustMonitorSettingImplCopyWith<_$AdjustMonitorSettingImpl>
      get copyWith =>
          __$$AdjustMonitorSettingImplCopyWithImpl<_$AdjustMonitorSettingImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getDefaultSetting,
    required TResult Function(MonitorSettingModal setting) adjustMonitorSetting,
    required TResult Function(HistorySettingModal setting) adjustHistorySetting,
    required TResult Function(List<String> listFilterVehicleId)
        changeFilterVehicleList,
    required TResult Function(int versionApp) syncVersionApp,
    required TResult Function() resetFilterVehicle,
  }) {
    return adjustMonitorSetting(setting);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getDefaultSetting,
    TResult? Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult? Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult? Function(List<String> listFilterVehicleId)?
        changeFilterVehicleList,
    TResult? Function(int versionApp)? syncVersionApp,
    TResult? Function()? resetFilterVehicle,
  }) {
    return adjustMonitorSetting?.call(setting);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getDefaultSetting,
    TResult Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult Function(List<String> listFilterVehicleId)? changeFilterVehicleList,
    TResult Function(int versionApp)? syncVersionApp,
    TResult Function()? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (adjustMonitorSetting != null) {
      return adjustMonitorSetting(setting);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDefaultSetting value) getDefaultSetting,
    required TResult Function(_AdjustMonitorSetting value) adjustMonitorSetting,
    required TResult Function(_AdjustHistorySetting value) adjustHistorySetting,
    required TResult Function(_ChangeFilterVehicleList value)
        changeFilterVehicleList,
    required TResult Function(_SyncVersionApp value) syncVersionApp,
    required TResult Function(_ResetFilterVehicle value) resetFilterVehicle,
  }) {
    return adjustMonitorSetting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult? Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult? Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult? Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult? Function(_SyncVersionApp value)? syncVersionApp,
    TResult? Function(_ResetFilterVehicle value)? resetFilterVehicle,
  }) {
    return adjustMonitorSetting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult Function(_SyncVersionApp value)? syncVersionApp,
    TResult Function(_ResetFilterVehicle value)? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (adjustMonitorSetting != null) {
      return adjustMonitorSetting(this);
    }
    return orElse();
  }
}

abstract class _AdjustMonitorSetting implements SettingEvent {
  const factory _AdjustMonitorSetting(
          {required final MonitorSettingModal setting}) =
      _$AdjustMonitorSettingImpl;

  MonitorSettingModal get setting;
  @JsonKey(ignore: true)
  _$$AdjustMonitorSettingImplCopyWith<_$AdjustMonitorSettingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AdjustHistorySettingImplCopyWith<$Res> {
  factory _$$AdjustHistorySettingImplCopyWith(_$AdjustHistorySettingImpl value,
          $Res Function(_$AdjustHistorySettingImpl) then) =
      __$$AdjustHistorySettingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({HistorySettingModal setting});

  $HistorySettingModalCopyWith<$Res> get setting;
}

/// @nodoc
class __$$AdjustHistorySettingImplCopyWithImpl<$Res>
    extends _$SettingEventCopyWithImpl<$Res, _$AdjustHistorySettingImpl>
    implements _$$AdjustHistorySettingImplCopyWith<$Res> {
  __$$AdjustHistorySettingImplCopyWithImpl(_$AdjustHistorySettingImpl _value,
      $Res Function(_$AdjustHistorySettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setting = null,
  }) {
    return _then(_$AdjustHistorySettingImpl(
      setting: null == setting
          ? _value.setting
          : setting // ignore: cast_nullable_to_non_nullable
              as HistorySettingModal,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $HistorySettingModalCopyWith<$Res> get setting {
    return $HistorySettingModalCopyWith<$Res>(_value.setting, (value) {
      return _then(_value.copyWith(setting: value));
    });
  }
}

/// @nodoc

class _$AdjustHistorySettingImpl implements _AdjustHistorySetting {
  const _$AdjustHistorySettingImpl({required this.setting});

  @override
  final HistorySettingModal setting;

  @override
  String toString() {
    return 'SettingEvent.adjustHistorySetting(setting: $setting)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdjustHistorySettingImpl &&
            (identical(other.setting, setting) || other.setting == setting));
  }

  @override
  int get hashCode => Object.hash(runtimeType, setting);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AdjustHistorySettingImplCopyWith<_$AdjustHistorySettingImpl>
      get copyWith =>
          __$$AdjustHistorySettingImplCopyWithImpl<_$AdjustHistorySettingImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getDefaultSetting,
    required TResult Function(MonitorSettingModal setting) adjustMonitorSetting,
    required TResult Function(HistorySettingModal setting) adjustHistorySetting,
    required TResult Function(List<String> listFilterVehicleId)
        changeFilterVehicleList,
    required TResult Function(int versionApp) syncVersionApp,
    required TResult Function() resetFilterVehicle,
  }) {
    return adjustHistorySetting(setting);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getDefaultSetting,
    TResult? Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult? Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult? Function(List<String> listFilterVehicleId)?
        changeFilterVehicleList,
    TResult? Function(int versionApp)? syncVersionApp,
    TResult? Function()? resetFilterVehicle,
  }) {
    return adjustHistorySetting?.call(setting);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getDefaultSetting,
    TResult Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult Function(List<String> listFilterVehicleId)? changeFilterVehicleList,
    TResult Function(int versionApp)? syncVersionApp,
    TResult Function()? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (adjustHistorySetting != null) {
      return adjustHistorySetting(setting);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDefaultSetting value) getDefaultSetting,
    required TResult Function(_AdjustMonitorSetting value) adjustMonitorSetting,
    required TResult Function(_AdjustHistorySetting value) adjustHistorySetting,
    required TResult Function(_ChangeFilterVehicleList value)
        changeFilterVehicleList,
    required TResult Function(_SyncVersionApp value) syncVersionApp,
    required TResult Function(_ResetFilterVehicle value) resetFilterVehicle,
  }) {
    return adjustHistorySetting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult? Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult? Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult? Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult? Function(_SyncVersionApp value)? syncVersionApp,
    TResult? Function(_ResetFilterVehicle value)? resetFilterVehicle,
  }) {
    return adjustHistorySetting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult Function(_SyncVersionApp value)? syncVersionApp,
    TResult Function(_ResetFilterVehicle value)? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (adjustHistorySetting != null) {
      return adjustHistorySetting(this);
    }
    return orElse();
  }
}

abstract class _AdjustHistorySetting implements SettingEvent {
  const factory _AdjustHistorySetting(
          {required final HistorySettingModal setting}) =
      _$AdjustHistorySettingImpl;

  HistorySettingModal get setting;
  @JsonKey(ignore: true)
  _$$AdjustHistorySettingImplCopyWith<_$AdjustHistorySettingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeFilterVehicleListImplCopyWith<$Res> {
  factory _$$ChangeFilterVehicleListImplCopyWith(
          _$ChangeFilterVehicleListImpl value,
          $Res Function(_$ChangeFilterVehicleListImpl) then) =
      __$$ChangeFilterVehicleListImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> listFilterVehicleId});
}

/// @nodoc
class __$$ChangeFilterVehicleListImplCopyWithImpl<$Res>
    extends _$SettingEventCopyWithImpl<$Res, _$ChangeFilterVehicleListImpl>
    implements _$$ChangeFilterVehicleListImplCopyWith<$Res> {
  __$$ChangeFilterVehicleListImplCopyWithImpl(
      _$ChangeFilterVehicleListImpl _value,
      $Res Function(_$ChangeFilterVehicleListImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listFilterVehicleId = null,
  }) {
    return _then(_$ChangeFilterVehicleListImpl(
      listFilterVehicleId: null == listFilterVehicleId
          ? _value._listFilterVehicleId
          : listFilterVehicleId // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _$ChangeFilterVehicleListImpl implements _ChangeFilterVehicleList {
  const _$ChangeFilterVehicleListImpl(
      {required final List<String> listFilterVehicleId})
      : _listFilterVehicleId = listFilterVehicleId;

  final List<String> _listFilterVehicleId;
  @override
  List<String> get listFilterVehicleId {
    if (_listFilterVehicleId is EqualUnmodifiableListView)
      return _listFilterVehicleId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listFilterVehicleId);
  }

  @override
  String toString() {
    return 'SettingEvent.changeFilterVehicleList(listFilterVehicleId: $listFilterVehicleId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeFilterVehicleListImpl &&
            const DeepCollectionEquality()
                .equals(other._listFilterVehicleId, _listFilterVehicleId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_listFilterVehicleId));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeFilterVehicleListImplCopyWith<_$ChangeFilterVehicleListImpl>
      get copyWith => __$$ChangeFilterVehicleListImplCopyWithImpl<
          _$ChangeFilterVehicleListImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getDefaultSetting,
    required TResult Function(MonitorSettingModal setting) adjustMonitorSetting,
    required TResult Function(HistorySettingModal setting) adjustHistorySetting,
    required TResult Function(List<String> listFilterVehicleId)
        changeFilterVehicleList,
    required TResult Function(int versionApp) syncVersionApp,
    required TResult Function() resetFilterVehicle,
  }) {
    return changeFilterVehicleList(listFilterVehicleId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getDefaultSetting,
    TResult? Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult? Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult? Function(List<String> listFilterVehicleId)?
        changeFilterVehicleList,
    TResult? Function(int versionApp)? syncVersionApp,
    TResult? Function()? resetFilterVehicle,
  }) {
    return changeFilterVehicleList?.call(listFilterVehicleId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getDefaultSetting,
    TResult Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult Function(List<String> listFilterVehicleId)? changeFilterVehicleList,
    TResult Function(int versionApp)? syncVersionApp,
    TResult Function()? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (changeFilterVehicleList != null) {
      return changeFilterVehicleList(listFilterVehicleId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDefaultSetting value) getDefaultSetting,
    required TResult Function(_AdjustMonitorSetting value) adjustMonitorSetting,
    required TResult Function(_AdjustHistorySetting value) adjustHistorySetting,
    required TResult Function(_ChangeFilterVehicleList value)
        changeFilterVehicleList,
    required TResult Function(_SyncVersionApp value) syncVersionApp,
    required TResult Function(_ResetFilterVehicle value) resetFilterVehicle,
  }) {
    return changeFilterVehicleList(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult? Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult? Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult? Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult? Function(_SyncVersionApp value)? syncVersionApp,
    TResult? Function(_ResetFilterVehicle value)? resetFilterVehicle,
  }) {
    return changeFilterVehicleList?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult Function(_SyncVersionApp value)? syncVersionApp,
    TResult Function(_ResetFilterVehicle value)? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (changeFilterVehicleList != null) {
      return changeFilterVehicleList(this);
    }
    return orElse();
  }
}

abstract class _ChangeFilterVehicleList implements SettingEvent {
  const factory _ChangeFilterVehicleList(
          {required final List<String> listFilterVehicleId}) =
      _$ChangeFilterVehicleListImpl;

  List<String> get listFilterVehicleId;
  @JsonKey(ignore: true)
  _$$ChangeFilterVehicleListImplCopyWith<_$ChangeFilterVehicleListImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SyncVersionAppImplCopyWith<$Res> {
  factory _$$SyncVersionAppImplCopyWith(_$SyncVersionAppImpl value,
          $Res Function(_$SyncVersionAppImpl) then) =
      __$$SyncVersionAppImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int versionApp});
}

/// @nodoc
class __$$SyncVersionAppImplCopyWithImpl<$Res>
    extends _$SettingEventCopyWithImpl<$Res, _$SyncVersionAppImpl>
    implements _$$SyncVersionAppImplCopyWith<$Res> {
  __$$SyncVersionAppImplCopyWithImpl(
      _$SyncVersionAppImpl _value, $Res Function(_$SyncVersionAppImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? versionApp = null,
  }) {
    return _then(_$SyncVersionAppImpl(
      versionApp: null == versionApp
          ? _value.versionApp
          : versionApp // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$SyncVersionAppImpl implements _SyncVersionApp {
  const _$SyncVersionAppImpl({required this.versionApp});

  @override
  final int versionApp;

  @override
  String toString() {
    return 'SettingEvent.syncVersionApp(versionApp: $versionApp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SyncVersionAppImpl &&
            (identical(other.versionApp, versionApp) ||
                other.versionApp == versionApp));
  }

  @override
  int get hashCode => Object.hash(runtimeType, versionApp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SyncVersionAppImplCopyWith<_$SyncVersionAppImpl> get copyWith =>
      __$$SyncVersionAppImplCopyWithImpl<_$SyncVersionAppImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getDefaultSetting,
    required TResult Function(MonitorSettingModal setting) adjustMonitorSetting,
    required TResult Function(HistorySettingModal setting) adjustHistorySetting,
    required TResult Function(List<String> listFilterVehicleId)
        changeFilterVehicleList,
    required TResult Function(int versionApp) syncVersionApp,
    required TResult Function() resetFilterVehicle,
  }) {
    return syncVersionApp(versionApp);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getDefaultSetting,
    TResult? Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult? Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult? Function(List<String> listFilterVehicleId)?
        changeFilterVehicleList,
    TResult? Function(int versionApp)? syncVersionApp,
    TResult? Function()? resetFilterVehicle,
  }) {
    return syncVersionApp?.call(versionApp);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getDefaultSetting,
    TResult Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult Function(List<String> listFilterVehicleId)? changeFilterVehicleList,
    TResult Function(int versionApp)? syncVersionApp,
    TResult Function()? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (syncVersionApp != null) {
      return syncVersionApp(versionApp);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDefaultSetting value) getDefaultSetting,
    required TResult Function(_AdjustMonitorSetting value) adjustMonitorSetting,
    required TResult Function(_AdjustHistorySetting value) adjustHistorySetting,
    required TResult Function(_ChangeFilterVehicleList value)
        changeFilterVehicleList,
    required TResult Function(_SyncVersionApp value) syncVersionApp,
    required TResult Function(_ResetFilterVehicle value) resetFilterVehicle,
  }) {
    return syncVersionApp(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult? Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult? Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult? Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult? Function(_SyncVersionApp value)? syncVersionApp,
    TResult? Function(_ResetFilterVehicle value)? resetFilterVehicle,
  }) {
    return syncVersionApp?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult Function(_SyncVersionApp value)? syncVersionApp,
    TResult Function(_ResetFilterVehicle value)? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (syncVersionApp != null) {
      return syncVersionApp(this);
    }
    return orElse();
  }
}

abstract class _SyncVersionApp implements SettingEvent {
  const factory _SyncVersionApp({required final int versionApp}) =
      _$SyncVersionAppImpl;

  int get versionApp;
  @JsonKey(ignore: true)
  _$$SyncVersionAppImplCopyWith<_$SyncVersionAppImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetFilterVehicleImplCopyWith<$Res> {
  factory _$$ResetFilterVehicleImplCopyWith(_$ResetFilterVehicleImpl value,
          $Res Function(_$ResetFilterVehicleImpl) then) =
      __$$ResetFilterVehicleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetFilterVehicleImplCopyWithImpl<$Res>
    extends _$SettingEventCopyWithImpl<$Res, _$ResetFilterVehicleImpl>
    implements _$$ResetFilterVehicleImplCopyWith<$Res> {
  __$$ResetFilterVehicleImplCopyWithImpl(_$ResetFilterVehicleImpl _value,
      $Res Function(_$ResetFilterVehicleImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ResetFilterVehicleImpl implements _ResetFilterVehicle {
  const _$ResetFilterVehicleImpl();

  @override
  String toString() {
    return 'SettingEvent.resetFilterVehicle()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetFilterVehicleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getDefaultSetting,
    required TResult Function(MonitorSettingModal setting) adjustMonitorSetting,
    required TResult Function(HistorySettingModal setting) adjustHistorySetting,
    required TResult Function(List<String> listFilterVehicleId)
        changeFilterVehicleList,
    required TResult Function(int versionApp) syncVersionApp,
    required TResult Function() resetFilterVehicle,
  }) {
    return resetFilterVehicle();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getDefaultSetting,
    TResult? Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult? Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult? Function(List<String> listFilterVehicleId)?
        changeFilterVehicleList,
    TResult? Function(int versionApp)? syncVersionApp,
    TResult? Function()? resetFilterVehicle,
  }) {
    return resetFilterVehicle?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getDefaultSetting,
    TResult Function(MonitorSettingModal setting)? adjustMonitorSetting,
    TResult Function(HistorySettingModal setting)? adjustHistorySetting,
    TResult Function(List<String> listFilterVehicleId)? changeFilterVehicleList,
    TResult Function(int versionApp)? syncVersionApp,
    TResult Function()? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (resetFilterVehicle != null) {
      return resetFilterVehicle();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDefaultSetting value) getDefaultSetting,
    required TResult Function(_AdjustMonitorSetting value) adjustMonitorSetting,
    required TResult Function(_AdjustHistorySetting value) adjustHistorySetting,
    required TResult Function(_ChangeFilterVehicleList value)
        changeFilterVehicleList,
    required TResult Function(_SyncVersionApp value) syncVersionApp,
    required TResult Function(_ResetFilterVehicle value) resetFilterVehicle,
  }) {
    return resetFilterVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult? Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult? Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult? Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult? Function(_SyncVersionApp value)? syncVersionApp,
    TResult? Function(_ResetFilterVehicle value)? resetFilterVehicle,
  }) {
    return resetFilterVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDefaultSetting value)? getDefaultSetting,
    TResult Function(_AdjustMonitorSetting value)? adjustMonitorSetting,
    TResult Function(_AdjustHistorySetting value)? adjustHistorySetting,
    TResult Function(_ChangeFilterVehicleList value)? changeFilterVehicleList,
    TResult Function(_SyncVersionApp value)? syncVersionApp,
    TResult Function(_ResetFilterVehicle value)? resetFilterVehicle,
    required TResult orElse(),
  }) {
    if (resetFilterVehicle != null) {
      return resetFilterVehicle(this);
    }
    return orElse();
  }
}

abstract class _ResetFilterVehicle implements SettingEvent {
  const factory _ResetFilterVehicle() = _$ResetFilterVehicleImpl;
}

/// @nodoc
mixin _$SettingState {
  MonitorSettingModal get settingMonitor => throw _privateConstructorUsedError;
  HistorySettingModal get settingHistory => throw _privateConstructorUsedError;
  List<String> get listFilterVehicleId => throw _privateConstructorUsedError;
  int? get versionApp => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SettingStateCopyWith<SettingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingStateCopyWith<$Res> {
  factory $SettingStateCopyWith(
          SettingState value, $Res Function(SettingState) then) =
      _$SettingStateCopyWithImpl<$Res, SettingState>;
  @useResult
  $Res call(
      {MonitorSettingModal settingMonitor,
      HistorySettingModal settingHistory,
      List<String> listFilterVehicleId,
      int? versionApp});

  $MonitorSettingModalCopyWith<$Res> get settingMonitor;
  $HistorySettingModalCopyWith<$Res> get settingHistory;
}

/// @nodoc
class _$SettingStateCopyWithImpl<$Res, $Val extends SettingState>
    implements $SettingStateCopyWith<$Res> {
  _$SettingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? settingMonitor = null,
    Object? settingHistory = null,
    Object? listFilterVehicleId = null,
    Object? versionApp = freezed,
  }) {
    return _then(_value.copyWith(
      settingMonitor: null == settingMonitor
          ? _value.settingMonitor
          : settingMonitor // ignore: cast_nullable_to_non_nullable
              as MonitorSettingModal,
      settingHistory: null == settingHistory
          ? _value.settingHistory
          : settingHistory // ignore: cast_nullable_to_non_nullable
              as HistorySettingModal,
      listFilterVehicleId: null == listFilterVehicleId
          ? _value.listFilterVehicleId
          : listFilterVehicleId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      versionApp: freezed == versionApp
          ? _value.versionApp
          : versionApp // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MonitorSettingModalCopyWith<$Res> get settingMonitor {
    return $MonitorSettingModalCopyWith<$Res>(_value.settingMonitor, (value) {
      return _then(_value.copyWith(settingMonitor: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $HistorySettingModalCopyWith<$Res> get settingHistory {
    return $HistorySettingModalCopyWith<$Res>(_value.settingHistory, (value) {
      return _then(_value.copyWith(settingHistory: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SettingStateImplCopyWith<$Res>
    implements $SettingStateCopyWith<$Res> {
  factory _$$SettingStateImplCopyWith(
          _$SettingStateImpl value, $Res Function(_$SettingStateImpl) then) =
      __$$SettingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MonitorSettingModal settingMonitor,
      HistorySettingModal settingHistory,
      List<String> listFilterVehicleId,
      int? versionApp});

  @override
  $MonitorSettingModalCopyWith<$Res> get settingMonitor;
  @override
  $HistorySettingModalCopyWith<$Res> get settingHistory;
}

/// @nodoc
class __$$SettingStateImplCopyWithImpl<$Res>
    extends _$SettingStateCopyWithImpl<$Res, _$SettingStateImpl>
    implements _$$SettingStateImplCopyWith<$Res> {
  __$$SettingStateImplCopyWithImpl(
      _$SettingStateImpl _value, $Res Function(_$SettingStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? settingMonitor = null,
    Object? settingHistory = null,
    Object? listFilterVehicleId = null,
    Object? versionApp = freezed,
  }) {
    return _then(_$SettingStateImpl(
      settingMonitor: null == settingMonitor
          ? _value.settingMonitor
          : settingMonitor // ignore: cast_nullable_to_non_nullable
              as MonitorSettingModal,
      settingHistory: null == settingHistory
          ? _value.settingHistory
          : settingHistory // ignore: cast_nullable_to_non_nullable
              as HistorySettingModal,
      listFilterVehicleId: null == listFilterVehicleId
          ? _value._listFilterVehicleId
          : listFilterVehicleId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      versionApp: freezed == versionApp
          ? _value.versionApp
          : versionApp // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$SettingStateImpl implements _SettingState {
  const _$SettingStateImpl(
      {required this.settingMonitor,
      required this.settingHistory,
      required final List<String> listFilterVehicleId,
      required this.versionApp})
      : _listFilterVehicleId = listFilterVehicleId;

  @override
  final MonitorSettingModal settingMonitor;
  @override
  final HistorySettingModal settingHistory;
  final List<String> _listFilterVehicleId;
  @override
  List<String> get listFilterVehicleId {
    if (_listFilterVehicleId is EqualUnmodifiableListView)
      return _listFilterVehicleId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listFilterVehicleId);
  }

  @override
  final int? versionApp;

  @override
  String toString() {
    return 'SettingState(settingMonitor: $settingMonitor, settingHistory: $settingHistory, listFilterVehicleId: $listFilterVehicleId, versionApp: $versionApp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SettingStateImpl &&
            (identical(other.settingMonitor, settingMonitor) ||
                other.settingMonitor == settingMonitor) &&
            (identical(other.settingHistory, settingHistory) ||
                other.settingHistory == settingHistory) &&
            const DeepCollectionEquality()
                .equals(other._listFilterVehicleId, _listFilterVehicleId) &&
            (identical(other.versionApp, versionApp) ||
                other.versionApp == versionApp));
  }

  @override
  int get hashCode => Object.hash(runtimeType, settingMonitor, settingHistory,
      const DeepCollectionEquality().hash(_listFilterVehicleId), versionApp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingStateImplCopyWith<_$SettingStateImpl> get copyWith =>
      __$$SettingStateImplCopyWithImpl<_$SettingStateImpl>(this, _$identity);
}

abstract class _SettingState implements SettingState {
  const factory _SettingState(
      {required final MonitorSettingModal settingMonitor,
      required final HistorySettingModal settingHistory,
      required final List<String> listFilterVehicleId,
      required final int? versionApp}) = _$SettingStateImpl;

  @override
  MonitorSettingModal get settingMonitor;
  @override
  HistorySettingModal get settingHistory;
  @override
  List<String> get listFilterVehicleId;
  @override
  int? get versionApp;
  @override
  @JsonKey(ignore: true)
  _$$SettingStateImplCopyWith<_$SettingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
