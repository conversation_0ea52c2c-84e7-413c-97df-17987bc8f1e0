// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_setting_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeviceSettingFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DeviceSettingFailureUnexpected value) unexpected,
    required TResult Function(_DeviceSettingFailureUnauthorized value)
        unauthorized,
    required TResult Function(_DeviceSettingFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DeviceSettingFailureServerError value)
        serverError,
    required TResult Function(_DeviceSettingFailureNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult? Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult? Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult? Function(_DeviceSettingFailureServerError value)? serverError,
    TResult? Function(_DeviceSettingFailureNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult Function(_DeviceSettingFailureServerError value)? serverError,
    TResult Function(_DeviceSettingFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceSettingFailureCopyWith<$Res> {
  factory $DeviceSettingFailureCopyWith(DeviceSettingFailure value,
          $Res Function(DeviceSettingFailure) then) =
      _$DeviceSettingFailureCopyWithImpl<$Res, DeviceSettingFailure>;
}

/// @nodoc
class _$DeviceSettingFailureCopyWithImpl<$Res,
        $Val extends DeviceSettingFailure>
    implements $DeviceSettingFailureCopyWith<$Res> {
  _$DeviceSettingFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$DeviceSettingFailureUnexpectedImplCopyWith<$Res> {
  factory _$$DeviceSettingFailureUnexpectedImplCopyWith(
          _$DeviceSettingFailureUnexpectedImpl value,
          $Res Function(_$DeviceSettingFailureUnexpectedImpl) then) =
      __$$DeviceSettingFailureUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$DeviceSettingFailureUnexpectedImplCopyWithImpl<$Res>
    extends _$DeviceSettingFailureCopyWithImpl<$Res,
        _$DeviceSettingFailureUnexpectedImpl>
    implements _$$DeviceSettingFailureUnexpectedImplCopyWith<$Res> {
  __$$DeviceSettingFailureUnexpectedImplCopyWithImpl(
      _$DeviceSettingFailureUnexpectedImpl _value,
      $Res Function(_$DeviceSettingFailureUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$DeviceSettingFailureUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeviceSettingFailureUnexpectedImpl
    implements _DeviceSettingFailureUnexpected {
  const _$DeviceSettingFailureUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'DeviceSettingFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceSettingFailureUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceSettingFailureUnexpectedImplCopyWith<
          _$DeviceSettingFailureUnexpectedImpl>
      get copyWith => __$$DeviceSettingFailureUnexpectedImplCopyWithImpl<
          _$DeviceSettingFailureUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DeviceSettingFailureUnexpected value) unexpected,
    required TResult Function(_DeviceSettingFailureUnauthorized value)
        unauthorized,
    required TResult Function(_DeviceSettingFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DeviceSettingFailureServerError value)
        serverError,
    required TResult Function(_DeviceSettingFailureNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult? Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult? Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult? Function(_DeviceSettingFailureServerError value)? serverError,
    TResult? Function(_DeviceSettingFailureNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult Function(_DeviceSettingFailureServerError value)? serverError,
    TResult Function(_DeviceSettingFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _DeviceSettingFailureUnexpected implements DeviceSettingFailure {
  const factory _DeviceSettingFailureUnexpected({required final String error}) =
      _$DeviceSettingFailureUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$DeviceSettingFailureUnexpectedImplCopyWith<
          _$DeviceSettingFailureUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeviceSettingFailureUnauthorizedImplCopyWith<$Res> {
  factory _$$DeviceSettingFailureUnauthorizedImplCopyWith(
          _$DeviceSettingFailureUnauthorizedImpl value,
          $Res Function(_$DeviceSettingFailureUnauthorizedImpl) then) =
      __$$DeviceSettingFailureUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeviceSettingFailureUnauthorizedImplCopyWithImpl<$Res>
    extends _$DeviceSettingFailureCopyWithImpl<$Res,
        _$DeviceSettingFailureUnauthorizedImpl>
    implements _$$DeviceSettingFailureUnauthorizedImplCopyWith<$Res> {
  __$$DeviceSettingFailureUnauthorizedImplCopyWithImpl(
      _$DeviceSettingFailureUnauthorizedImpl _value,
      $Res Function(_$DeviceSettingFailureUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DeviceSettingFailureUnauthorizedImpl
    implements _DeviceSettingFailureUnauthorized {
  const _$DeviceSettingFailureUnauthorizedImpl();

  @override
  String toString() {
    return 'DeviceSettingFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceSettingFailureUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DeviceSettingFailureUnexpected value) unexpected,
    required TResult Function(_DeviceSettingFailureUnauthorized value)
        unauthorized,
    required TResult Function(_DeviceSettingFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DeviceSettingFailureServerError value)
        serverError,
    required TResult Function(_DeviceSettingFailureNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult? Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult? Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult? Function(_DeviceSettingFailureServerError value)? serverError,
    TResult? Function(_DeviceSettingFailureNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult Function(_DeviceSettingFailureServerError value)? serverError,
    TResult Function(_DeviceSettingFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _DeviceSettingFailureUnauthorized
    implements DeviceSettingFailure {
  const factory _DeviceSettingFailureUnauthorized() =
      _$DeviceSettingFailureUnauthorizedImpl;
}

/// @nodoc
abstract class _$$DeviceSettingFailureUnauthenticatedImplCopyWith<$Res> {
  factory _$$DeviceSettingFailureUnauthenticatedImplCopyWith(
          _$DeviceSettingFailureUnauthenticatedImpl value,
          $Res Function(_$DeviceSettingFailureUnauthenticatedImpl) then) =
      __$$DeviceSettingFailureUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeviceSettingFailureUnauthenticatedImplCopyWithImpl<$Res>
    extends _$DeviceSettingFailureCopyWithImpl<$Res,
        _$DeviceSettingFailureUnauthenticatedImpl>
    implements _$$DeviceSettingFailureUnauthenticatedImplCopyWith<$Res> {
  __$$DeviceSettingFailureUnauthenticatedImplCopyWithImpl(
      _$DeviceSettingFailureUnauthenticatedImpl _value,
      $Res Function(_$DeviceSettingFailureUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DeviceSettingFailureUnauthenticatedImpl
    implements _DeviceSettingFailureUnauthenticated {
  const _$DeviceSettingFailureUnauthenticatedImpl();

  @override
  String toString() {
    return 'DeviceSettingFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceSettingFailureUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DeviceSettingFailureUnexpected value) unexpected,
    required TResult Function(_DeviceSettingFailureUnauthorized value)
        unauthorized,
    required TResult Function(_DeviceSettingFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DeviceSettingFailureServerError value)
        serverError,
    required TResult Function(_DeviceSettingFailureNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult? Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult? Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult? Function(_DeviceSettingFailureServerError value)? serverError,
    TResult? Function(_DeviceSettingFailureNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult Function(_DeviceSettingFailureServerError value)? serverError,
    TResult Function(_DeviceSettingFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _DeviceSettingFailureUnauthenticated
    implements DeviceSettingFailure {
  const factory _DeviceSettingFailureUnauthenticated() =
      _$DeviceSettingFailureUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$DeviceSettingFailureServerErrorImplCopyWith<$Res> {
  factory _$$DeviceSettingFailureServerErrorImplCopyWith(
          _$DeviceSettingFailureServerErrorImpl value,
          $Res Function(_$DeviceSettingFailureServerErrorImpl) then) =
      __$$DeviceSettingFailureServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeviceSettingFailureServerErrorImplCopyWithImpl<$Res>
    extends _$DeviceSettingFailureCopyWithImpl<$Res,
        _$DeviceSettingFailureServerErrorImpl>
    implements _$$DeviceSettingFailureServerErrorImplCopyWith<$Res> {
  __$$DeviceSettingFailureServerErrorImplCopyWithImpl(
      _$DeviceSettingFailureServerErrorImpl _value,
      $Res Function(_$DeviceSettingFailureServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DeviceSettingFailureServerErrorImpl
    implements _DeviceSettingFailureServerError {
  const _$DeviceSettingFailureServerErrorImpl();

  @override
  String toString() {
    return 'DeviceSettingFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceSettingFailureServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DeviceSettingFailureUnexpected value) unexpected,
    required TResult Function(_DeviceSettingFailureUnauthorized value)
        unauthorized,
    required TResult Function(_DeviceSettingFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DeviceSettingFailureServerError value)
        serverError,
    required TResult Function(_DeviceSettingFailureNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult? Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult? Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult? Function(_DeviceSettingFailureServerError value)? serverError,
    TResult? Function(_DeviceSettingFailureNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult Function(_DeviceSettingFailureServerError value)? serverError,
    TResult Function(_DeviceSettingFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _DeviceSettingFailureServerError
    implements DeviceSettingFailure {
  const factory _DeviceSettingFailureServerError() =
      _$DeviceSettingFailureServerErrorImpl;
}

/// @nodoc
abstract class _$$DeviceSettingFailureNoInternetImplCopyWith<$Res> {
  factory _$$DeviceSettingFailureNoInternetImplCopyWith(
          _$DeviceSettingFailureNoInternetImpl value,
          $Res Function(_$DeviceSettingFailureNoInternetImpl) then) =
      __$$DeviceSettingFailureNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeviceSettingFailureNoInternetImplCopyWithImpl<$Res>
    extends _$DeviceSettingFailureCopyWithImpl<$Res,
        _$DeviceSettingFailureNoInternetImpl>
    implements _$$DeviceSettingFailureNoInternetImplCopyWith<$Res> {
  __$$DeviceSettingFailureNoInternetImplCopyWithImpl(
      _$DeviceSettingFailureNoInternetImpl _value,
      $Res Function(_$DeviceSettingFailureNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DeviceSettingFailureNoInternetImpl
    implements _DeviceSettingFailureNoInternet {
  const _$DeviceSettingFailureNoInternetImpl();

  @override
  String toString() {
    return 'DeviceSettingFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceSettingFailureNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DeviceSettingFailureUnexpected value) unexpected,
    required TResult Function(_DeviceSettingFailureUnauthorized value)
        unauthorized,
    required TResult Function(_DeviceSettingFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DeviceSettingFailureServerError value)
        serverError,
    required TResult Function(_DeviceSettingFailureNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult? Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult? Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult? Function(_DeviceSettingFailureServerError value)? serverError,
    TResult? Function(_DeviceSettingFailureNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DeviceSettingFailureUnexpected value)? unexpected,
    TResult Function(_DeviceSettingFailureUnauthorized value)? unauthorized,
    TResult Function(_DeviceSettingFailureUnauthenticated value)?
        unauthenticated,
    TResult Function(_DeviceSettingFailureServerError value)? serverError,
    TResult Function(_DeviceSettingFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _DeviceSettingFailureNoInternet implements DeviceSettingFailure {
  const factory _DeviceSettingFailureNoInternet() =
      _$DeviceSettingFailureNoInternetImpl;
}
