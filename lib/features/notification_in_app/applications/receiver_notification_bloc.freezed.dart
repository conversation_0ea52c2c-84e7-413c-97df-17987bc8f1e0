// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'receiver_notification_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ReceiverNotificationEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RemoteMessage firebaseMessage)
        receivedNotification,
    required TResult Function() clearNotification,
    required TResult Function() initializeLocalNotification,
    required TResult Function(RemoteMessage firebaseMessage)
        showNotificationInApp,
    required TResult Function() dispose,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult? Function()? clearNotification,
    TResult? Function()? initializeLocalNotification,
    TResult? Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult? Function()? dispose,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult Function()? clearNotification,
    TResult Function()? initializeLocalNotification,
    TResult Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult Function()? dispose,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ReceivedNotification value) receivedNotification,
    required TResult Function(_ClearNotification value) clearNotification,
    required TResult Function(_Initialize value) initializeLocalNotification,
    required TResult Function(_ShowNotificationInApp value)
        showNotificationInApp,
    required TResult Function(_Dispose value) dispose,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ReceivedNotification value)? receivedNotification,
    TResult? Function(_ClearNotification value)? clearNotification,
    TResult? Function(_Initialize value)? initializeLocalNotification,
    TResult? Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult? Function(_Dispose value)? dispose,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ReceivedNotification value)? receivedNotification,
    TResult Function(_ClearNotification value)? clearNotification,
    TResult Function(_Initialize value)? initializeLocalNotification,
    TResult Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReceiverNotificationEventCopyWith<$Res> {
  factory $ReceiverNotificationEventCopyWith(ReceiverNotificationEvent value,
          $Res Function(ReceiverNotificationEvent) then) =
      _$ReceiverNotificationEventCopyWithImpl<$Res, ReceiverNotificationEvent>;
}

/// @nodoc
class _$ReceiverNotificationEventCopyWithImpl<$Res,
        $Val extends ReceiverNotificationEvent>
    implements $ReceiverNotificationEventCopyWith<$Res> {
  _$ReceiverNotificationEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$ReceivedNotificationImplCopyWith<$Res> {
  factory _$$ReceivedNotificationImplCopyWith(_$ReceivedNotificationImpl value,
          $Res Function(_$ReceivedNotificationImpl) then) =
      __$$ReceivedNotificationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RemoteMessage firebaseMessage});
}

/// @nodoc
class __$$ReceivedNotificationImplCopyWithImpl<$Res>
    extends _$ReceiverNotificationEventCopyWithImpl<$Res,
        _$ReceivedNotificationImpl>
    implements _$$ReceivedNotificationImplCopyWith<$Res> {
  __$$ReceivedNotificationImplCopyWithImpl(_$ReceivedNotificationImpl _value,
      $Res Function(_$ReceivedNotificationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firebaseMessage = null,
  }) {
    return _then(_$ReceivedNotificationImpl(
      firebaseMessage: null == firebaseMessage
          ? _value.firebaseMessage
          : firebaseMessage // ignore: cast_nullable_to_non_nullable
              as RemoteMessage,
    ));
  }
}

/// @nodoc

class _$ReceivedNotificationImpl implements _ReceivedNotification {
  const _$ReceivedNotificationImpl({required this.firebaseMessage});

  @override
  final RemoteMessage firebaseMessage;

  @override
  String toString() {
    return 'ReceiverNotificationEvent.receivedNotification(firebaseMessage: $firebaseMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReceivedNotificationImpl &&
            (identical(other.firebaseMessage, firebaseMessage) ||
                other.firebaseMessage == firebaseMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, firebaseMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReceivedNotificationImplCopyWith<_$ReceivedNotificationImpl>
      get copyWith =>
          __$$ReceivedNotificationImplCopyWithImpl<_$ReceivedNotificationImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RemoteMessage firebaseMessage)
        receivedNotification,
    required TResult Function() clearNotification,
    required TResult Function() initializeLocalNotification,
    required TResult Function(RemoteMessage firebaseMessage)
        showNotificationInApp,
    required TResult Function() dispose,
  }) {
    return receivedNotification(firebaseMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult? Function()? clearNotification,
    TResult? Function()? initializeLocalNotification,
    TResult? Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult? Function()? dispose,
  }) {
    return receivedNotification?.call(firebaseMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult Function()? clearNotification,
    TResult Function()? initializeLocalNotification,
    TResult Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (receivedNotification != null) {
      return receivedNotification(firebaseMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ReceivedNotification value) receivedNotification,
    required TResult Function(_ClearNotification value) clearNotification,
    required TResult Function(_Initialize value) initializeLocalNotification,
    required TResult Function(_ShowNotificationInApp value)
        showNotificationInApp,
    required TResult Function(_Dispose value) dispose,
  }) {
    return receivedNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ReceivedNotification value)? receivedNotification,
    TResult? Function(_ClearNotification value)? clearNotification,
    TResult? Function(_Initialize value)? initializeLocalNotification,
    TResult? Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return receivedNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ReceivedNotification value)? receivedNotification,
    TResult Function(_ClearNotification value)? clearNotification,
    TResult Function(_Initialize value)? initializeLocalNotification,
    TResult Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (receivedNotification != null) {
      return receivedNotification(this);
    }
    return orElse();
  }
}

abstract class _ReceivedNotification implements ReceiverNotificationEvent {
  const factory _ReceivedNotification(
          {required final RemoteMessage firebaseMessage}) =
      _$ReceivedNotificationImpl;

  RemoteMessage get firebaseMessage;
  @JsonKey(ignore: true)
  _$$ReceivedNotificationImplCopyWith<_$ReceivedNotificationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearNotificationImplCopyWith<$Res> {
  factory _$$ClearNotificationImplCopyWith(_$ClearNotificationImpl value,
          $Res Function(_$ClearNotificationImpl) then) =
      __$$ClearNotificationImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearNotificationImplCopyWithImpl<$Res>
    extends _$ReceiverNotificationEventCopyWithImpl<$Res,
        _$ClearNotificationImpl>
    implements _$$ClearNotificationImplCopyWith<$Res> {
  __$$ClearNotificationImplCopyWithImpl(_$ClearNotificationImpl _value,
      $Res Function(_$ClearNotificationImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearNotificationImpl implements _ClearNotification {
  const _$ClearNotificationImpl();

  @override
  String toString() {
    return 'ReceiverNotificationEvent.clearNotification()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearNotificationImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RemoteMessage firebaseMessage)
        receivedNotification,
    required TResult Function() clearNotification,
    required TResult Function() initializeLocalNotification,
    required TResult Function(RemoteMessage firebaseMessage)
        showNotificationInApp,
    required TResult Function() dispose,
  }) {
    return clearNotification();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult? Function()? clearNotification,
    TResult? Function()? initializeLocalNotification,
    TResult? Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult? Function()? dispose,
  }) {
    return clearNotification?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult Function()? clearNotification,
    TResult Function()? initializeLocalNotification,
    TResult Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (clearNotification != null) {
      return clearNotification();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ReceivedNotification value) receivedNotification,
    required TResult Function(_ClearNotification value) clearNotification,
    required TResult Function(_Initialize value) initializeLocalNotification,
    required TResult Function(_ShowNotificationInApp value)
        showNotificationInApp,
    required TResult Function(_Dispose value) dispose,
  }) {
    return clearNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ReceivedNotification value)? receivedNotification,
    TResult? Function(_ClearNotification value)? clearNotification,
    TResult? Function(_Initialize value)? initializeLocalNotification,
    TResult? Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return clearNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ReceivedNotification value)? receivedNotification,
    TResult Function(_ClearNotification value)? clearNotification,
    TResult Function(_Initialize value)? initializeLocalNotification,
    TResult Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (clearNotification != null) {
      return clearNotification(this);
    }
    return orElse();
  }
}

abstract class _ClearNotification implements ReceiverNotificationEvent {
  const factory _ClearNotification() = _$ClearNotificationImpl;
}

/// @nodoc
abstract class _$$InitializeImplCopyWith<$Res> {
  factory _$$InitializeImplCopyWith(
          _$InitializeImpl value, $Res Function(_$InitializeImpl) then) =
      __$$InitializeImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitializeImplCopyWithImpl<$Res>
    extends _$ReceiverNotificationEventCopyWithImpl<$Res, _$InitializeImpl>
    implements _$$InitializeImplCopyWith<$Res> {
  __$$InitializeImplCopyWithImpl(
      _$InitializeImpl _value, $Res Function(_$InitializeImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$InitializeImpl implements _Initialize {
  const _$InitializeImpl();

  @override
  String toString() {
    return 'ReceiverNotificationEvent.initializeLocalNotification()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitializeImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RemoteMessage firebaseMessage)
        receivedNotification,
    required TResult Function() clearNotification,
    required TResult Function() initializeLocalNotification,
    required TResult Function(RemoteMessage firebaseMessage)
        showNotificationInApp,
    required TResult Function() dispose,
  }) {
    return initializeLocalNotification();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult? Function()? clearNotification,
    TResult? Function()? initializeLocalNotification,
    TResult? Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult? Function()? dispose,
  }) {
    return initializeLocalNotification?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult Function()? clearNotification,
    TResult Function()? initializeLocalNotification,
    TResult Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (initializeLocalNotification != null) {
      return initializeLocalNotification();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ReceivedNotification value) receivedNotification,
    required TResult Function(_ClearNotification value) clearNotification,
    required TResult Function(_Initialize value) initializeLocalNotification,
    required TResult Function(_ShowNotificationInApp value)
        showNotificationInApp,
    required TResult Function(_Dispose value) dispose,
  }) {
    return initializeLocalNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ReceivedNotification value)? receivedNotification,
    TResult? Function(_ClearNotification value)? clearNotification,
    TResult? Function(_Initialize value)? initializeLocalNotification,
    TResult? Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return initializeLocalNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ReceivedNotification value)? receivedNotification,
    TResult Function(_ClearNotification value)? clearNotification,
    TResult Function(_Initialize value)? initializeLocalNotification,
    TResult Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (initializeLocalNotification != null) {
      return initializeLocalNotification(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements ReceiverNotificationEvent {
  const factory _Initialize() = _$InitializeImpl;
}

/// @nodoc
abstract class _$$ShowNotificationInAppImplCopyWith<$Res> {
  factory _$$ShowNotificationInAppImplCopyWith(
          _$ShowNotificationInAppImpl value,
          $Res Function(_$ShowNotificationInAppImpl) then) =
      __$$ShowNotificationInAppImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RemoteMessage firebaseMessage});
}

/// @nodoc
class __$$ShowNotificationInAppImplCopyWithImpl<$Res>
    extends _$ReceiverNotificationEventCopyWithImpl<$Res,
        _$ShowNotificationInAppImpl>
    implements _$$ShowNotificationInAppImplCopyWith<$Res> {
  __$$ShowNotificationInAppImplCopyWithImpl(_$ShowNotificationInAppImpl _value,
      $Res Function(_$ShowNotificationInAppImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firebaseMessage = null,
  }) {
    return _then(_$ShowNotificationInAppImpl(
      firebaseMessage: null == firebaseMessage
          ? _value.firebaseMessage
          : firebaseMessage // ignore: cast_nullable_to_non_nullable
              as RemoteMessage,
    ));
  }
}

/// @nodoc

class _$ShowNotificationInAppImpl implements _ShowNotificationInApp {
  const _$ShowNotificationInAppImpl({required this.firebaseMessage});

  @override
  final RemoteMessage firebaseMessage;

  @override
  String toString() {
    return 'ReceiverNotificationEvent.showNotificationInApp(firebaseMessage: $firebaseMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShowNotificationInAppImpl &&
            (identical(other.firebaseMessage, firebaseMessage) ||
                other.firebaseMessage == firebaseMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, firebaseMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ShowNotificationInAppImplCopyWith<_$ShowNotificationInAppImpl>
      get copyWith => __$$ShowNotificationInAppImplCopyWithImpl<
          _$ShowNotificationInAppImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RemoteMessage firebaseMessage)
        receivedNotification,
    required TResult Function() clearNotification,
    required TResult Function() initializeLocalNotification,
    required TResult Function(RemoteMessage firebaseMessage)
        showNotificationInApp,
    required TResult Function() dispose,
  }) {
    return showNotificationInApp(firebaseMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult? Function()? clearNotification,
    TResult? Function()? initializeLocalNotification,
    TResult? Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult? Function()? dispose,
  }) {
    return showNotificationInApp?.call(firebaseMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult Function()? clearNotification,
    TResult Function()? initializeLocalNotification,
    TResult Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (showNotificationInApp != null) {
      return showNotificationInApp(firebaseMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ReceivedNotification value) receivedNotification,
    required TResult Function(_ClearNotification value) clearNotification,
    required TResult Function(_Initialize value) initializeLocalNotification,
    required TResult Function(_ShowNotificationInApp value)
        showNotificationInApp,
    required TResult Function(_Dispose value) dispose,
  }) {
    return showNotificationInApp(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ReceivedNotification value)? receivedNotification,
    TResult? Function(_ClearNotification value)? clearNotification,
    TResult? Function(_Initialize value)? initializeLocalNotification,
    TResult? Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return showNotificationInApp?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ReceivedNotification value)? receivedNotification,
    TResult Function(_ClearNotification value)? clearNotification,
    TResult Function(_Initialize value)? initializeLocalNotification,
    TResult Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (showNotificationInApp != null) {
      return showNotificationInApp(this);
    }
    return orElse();
  }
}

abstract class _ShowNotificationInApp implements ReceiverNotificationEvent {
  const factory _ShowNotificationInApp(
          {required final RemoteMessage firebaseMessage}) =
      _$ShowNotificationInAppImpl;

  RemoteMessage get firebaseMessage;
  @JsonKey(ignore: true)
  _$$ShowNotificationInAppImplCopyWith<_$ShowNotificationInAppImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DisposeImplCopyWith<$Res> {
  factory _$$DisposeImplCopyWith(
          _$DisposeImpl value, $Res Function(_$DisposeImpl) then) =
      __$$DisposeImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DisposeImplCopyWithImpl<$Res>
    extends _$ReceiverNotificationEventCopyWithImpl<$Res, _$DisposeImpl>
    implements _$$DisposeImplCopyWith<$Res> {
  __$$DisposeImplCopyWithImpl(
      _$DisposeImpl _value, $Res Function(_$DisposeImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DisposeImpl implements _Dispose {
  const _$DisposeImpl();

  @override
  String toString() {
    return 'ReceiverNotificationEvent.dispose()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DisposeImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RemoteMessage firebaseMessage)
        receivedNotification,
    required TResult Function() clearNotification,
    required TResult Function() initializeLocalNotification,
    required TResult Function(RemoteMessage firebaseMessage)
        showNotificationInApp,
    required TResult Function() dispose,
  }) {
    return dispose();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult? Function()? clearNotification,
    TResult? Function()? initializeLocalNotification,
    TResult? Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult? Function()? dispose,
  }) {
    return dispose?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RemoteMessage firebaseMessage)? receivedNotification,
    TResult Function()? clearNotification,
    TResult Function()? initializeLocalNotification,
    TResult Function(RemoteMessage firebaseMessage)? showNotificationInApp,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (dispose != null) {
      return dispose();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ReceivedNotification value) receivedNotification,
    required TResult Function(_ClearNotification value) clearNotification,
    required TResult Function(_Initialize value) initializeLocalNotification,
    required TResult Function(_ShowNotificationInApp value)
        showNotificationInApp,
    required TResult Function(_Dispose value) dispose,
  }) {
    return dispose(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ReceivedNotification value)? receivedNotification,
    TResult? Function(_ClearNotification value)? clearNotification,
    TResult? Function(_Initialize value)? initializeLocalNotification,
    TResult? Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return dispose?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ReceivedNotification value)? receivedNotification,
    TResult Function(_ClearNotification value)? clearNotification,
    TResult Function(_Initialize value)? initializeLocalNotification,
    TResult Function(_ShowNotificationInApp value)? showNotificationInApp,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (dispose != null) {
      return dispose(this);
    }
    return orElse();
  }
}

abstract class _Dispose implements ReceiverNotificationEvent {
  const factory _Dispose() = _$DisposeImpl;
}

/// @nodoc
mixin _$ReceiverNotificationState {
  List<RemoteMessage> get listFirebaseMessage =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ReceiverNotificationStateCopyWith<ReceiverNotificationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReceiverNotificationStateCopyWith<$Res> {
  factory $ReceiverNotificationStateCopyWith(ReceiverNotificationState value,
          $Res Function(ReceiverNotificationState) then) =
      _$ReceiverNotificationStateCopyWithImpl<$Res, ReceiverNotificationState>;
  @useResult
  $Res call({List<RemoteMessage> listFirebaseMessage});
}

/// @nodoc
class _$ReceiverNotificationStateCopyWithImpl<$Res,
        $Val extends ReceiverNotificationState>
    implements $ReceiverNotificationStateCopyWith<$Res> {
  _$ReceiverNotificationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listFirebaseMessage = null,
  }) {
    return _then(_value.copyWith(
      listFirebaseMessage: null == listFirebaseMessage
          ? _value.listFirebaseMessage
          : listFirebaseMessage // ignore: cast_nullable_to_non_nullable
              as List<RemoteMessage>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReceiverNotificationStateImplCopyWith<$Res>
    implements $ReceiverNotificationStateCopyWith<$Res> {
  factory _$$ReceiverNotificationStateImplCopyWith(
          _$ReceiverNotificationStateImpl value,
          $Res Function(_$ReceiverNotificationStateImpl) then) =
      __$$ReceiverNotificationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<RemoteMessage> listFirebaseMessage});
}

/// @nodoc
class __$$ReceiverNotificationStateImplCopyWithImpl<$Res>
    extends _$ReceiverNotificationStateCopyWithImpl<$Res,
        _$ReceiverNotificationStateImpl>
    implements _$$ReceiverNotificationStateImplCopyWith<$Res> {
  __$$ReceiverNotificationStateImplCopyWithImpl(
      _$ReceiverNotificationStateImpl _value,
      $Res Function(_$ReceiverNotificationStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listFirebaseMessage = null,
  }) {
    return _then(_$ReceiverNotificationStateImpl(
      listFirebaseMessage: null == listFirebaseMessage
          ? _value._listFirebaseMessage
          : listFirebaseMessage // ignore: cast_nullable_to_non_nullable
              as List<RemoteMessage>,
    ));
  }
}

/// @nodoc

class _$ReceiverNotificationStateImpl implements _ReceiverNotificationState {
  const _$ReceiverNotificationStateImpl(
      {required final List<RemoteMessage> listFirebaseMessage})
      : _listFirebaseMessage = listFirebaseMessage;

  final List<RemoteMessage> _listFirebaseMessage;
  @override
  List<RemoteMessage> get listFirebaseMessage {
    if (_listFirebaseMessage is EqualUnmodifiableListView)
      return _listFirebaseMessage;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listFirebaseMessage);
  }

  @override
  String toString() {
    return 'ReceiverNotificationState(listFirebaseMessage: $listFirebaseMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReceiverNotificationStateImpl &&
            const DeepCollectionEquality()
                .equals(other._listFirebaseMessage, _listFirebaseMessage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_listFirebaseMessage));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReceiverNotificationStateImplCopyWith<_$ReceiverNotificationStateImpl>
      get copyWith => __$$ReceiverNotificationStateImplCopyWithImpl<
          _$ReceiverNotificationStateImpl>(this, _$identity);
}

abstract class _ReceiverNotificationState implements ReceiverNotificationState {
  const factory _ReceiverNotificationState(
          {required final List<RemoteMessage> listFirebaseMessage}) =
      _$ReceiverNotificationStateImpl;

  @override
  List<RemoteMessage> get listFirebaseMessage;
  @override
  @JsonKey(ignore: true)
  _$$ReceiverNotificationStateImplCopyWith<_$ReceiverNotificationStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
