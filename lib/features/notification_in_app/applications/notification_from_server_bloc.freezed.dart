// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_from_server_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$NotificationFromServerEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getAllNotification,
    required TResult Function() readAllNotification,
    required TResult Function(String id) readNotification,
    required TResult Function() clearAll,
    required TResult Function(NotificationFailure failure) failureEvent,
    required TResult Function(List<NotificationInApp> listNotification)
        getAllNotiSuccess,
    required TResult Function() loadMore,
    required TResult Function() resetLoadMore,
    required TResult Function(String? selectDate) getNotiBySelectDay,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getAllNotification,
    TResult? Function()? readAllNotification,
    TResult? Function(String id)? readNotification,
    TResult? Function()? clearAll,
    TResult? Function(NotificationFailure failure)? failureEvent,
    TResult? Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult? Function()? loadMore,
    TResult? Function()? resetLoadMore,
    TResult? Function(String? selectDate)? getNotiBySelectDay,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getAllNotification,
    TResult Function()? readAllNotification,
    TResult Function(String id)? readNotification,
    TResult Function()? clearAll,
    TResult Function(NotificationFailure failure)? failureEvent,
    TResult Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult Function()? loadMore,
    TResult Function()? resetLoadMore,
    TResult Function(String? selectDate)? getNotiBySelectDay,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetAllNotification value) getAllNotification,
    required TResult Function(_ReadAllNotification value) readAllNotification,
    required TResult Function(_ReadNotification value) readNotification,
    required TResult Function(_ClearAll value) clearAll,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_GetAllNotiSuccess value) getAllNotiSuccess,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ResetLoadMore value) resetLoadMore,
    required TResult Function(_GetNotiBySelectDay value) getNotiBySelectDay,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetAllNotification value)? getAllNotification,
    TResult? Function(_ReadAllNotification value)? readAllNotification,
    TResult? Function(_ReadNotification value)? readNotification,
    TResult? Function(_ClearAll value)? clearAll,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ResetLoadMore value)? resetLoadMore,
    TResult? Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetAllNotification value)? getAllNotification,
    TResult Function(_ReadAllNotification value)? readAllNotification,
    TResult Function(_ReadNotification value)? readNotification,
    TResult Function(_ClearAll value)? clearAll,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ResetLoadMore value)? resetLoadMore,
    TResult Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationFromServerEventCopyWith<$Res> {
  factory $NotificationFromServerEventCopyWith(
          NotificationFromServerEvent value,
          $Res Function(NotificationFromServerEvent) then) =
      _$NotificationFromServerEventCopyWithImpl<$Res,
          NotificationFromServerEvent>;
}

/// @nodoc
class _$NotificationFromServerEventCopyWithImpl<$Res,
        $Val extends NotificationFromServerEvent>
    implements $NotificationFromServerEventCopyWith<$Res> {
  _$NotificationFromServerEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$NotificationFromServerEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'NotificationFromServerEvent.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getAllNotification,
    required TResult Function() readAllNotification,
    required TResult Function(String id) readNotification,
    required TResult Function() clearAll,
    required TResult Function(NotificationFailure failure) failureEvent,
    required TResult Function(List<NotificationInApp> listNotification)
        getAllNotiSuccess,
    required TResult Function() loadMore,
    required TResult Function() resetLoadMore,
    required TResult Function(String? selectDate) getNotiBySelectDay,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getAllNotification,
    TResult? Function()? readAllNotification,
    TResult? Function(String id)? readNotification,
    TResult? Function()? clearAll,
    TResult? Function(NotificationFailure failure)? failureEvent,
    TResult? Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult? Function()? loadMore,
    TResult? Function()? resetLoadMore,
    TResult? Function(String? selectDate)? getNotiBySelectDay,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getAllNotification,
    TResult Function()? readAllNotification,
    TResult Function(String id)? readNotification,
    TResult Function()? clearAll,
    TResult Function(NotificationFailure failure)? failureEvent,
    TResult Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult Function()? loadMore,
    TResult Function()? resetLoadMore,
    TResult Function(String? selectDate)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetAllNotification value) getAllNotification,
    required TResult Function(_ReadAllNotification value) readAllNotification,
    required TResult Function(_ReadNotification value) readNotification,
    required TResult Function(_ClearAll value) clearAll,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_GetAllNotiSuccess value) getAllNotiSuccess,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ResetLoadMore value) resetLoadMore,
    required TResult Function(_GetNotiBySelectDay value) getNotiBySelectDay,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetAllNotification value)? getAllNotification,
    TResult? Function(_ReadAllNotification value)? readAllNotification,
    TResult? Function(_ReadNotification value)? readNotification,
    TResult? Function(_ClearAll value)? clearAll,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ResetLoadMore value)? resetLoadMore,
    TResult? Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetAllNotification value)? getAllNotification,
    TResult Function(_ReadAllNotification value)? readAllNotification,
    TResult Function(_ReadNotification value)? readNotification,
    TResult Function(_ClearAll value)? clearAll,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ResetLoadMore value)? resetLoadMore,
    TResult Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements NotificationFromServerEvent {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$GetAllNotificationImplCopyWith<$Res> {
  factory _$$GetAllNotificationImplCopyWith(_$GetAllNotificationImpl value,
          $Res Function(_$GetAllNotificationImpl) then) =
      __$$GetAllNotificationImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetAllNotificationImplCopyWithImpl<$Res>
    extends _$NotificationFromServerEventCopyWithImpl<$Res,
        _$GetAllNotificationImpl>
    implements _$$GetAllNotificationImplCopyWith<$Res> {
  __$$GetAllNotificationImplCopyWithImpl(_$GetAllNotificationImpl _value,
      $Res Function(_$GetAllNotificationImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$GetAllNotificationImpl implements _GetAllNotification {
  const _$GetAllNotificationImpl();

  @override
  String toString() {
    return 'NotificationFromServerEvent.getAllNotification()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetAllNotificationImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getAllNotification,
    required TResult Function() readAllNotification,
    required TResult Function(String id) readNotification,
    required TResult Function() clearAll,
    required TResult Function(NotificationFailure failure) failureEvent,
    required TResult Function(List<NotificationInApp> listNotification)
        getAllNotiSuccess,
    required TResult Function() loadMore,
    required TResult Function() resetLoadMore,
    required TResult Function(String? selectDate) getNotiBySelectDay,
  }) {
    return getAllNotification();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getAllNotification,
    TResult? Function()? readAllNotification,
    TResult? Function(String id)? readNotification,
    TResult? Function()? clearAll,
    TResult? Function(NotificationFailure failure)? failureEvent,
    TResult? Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult? Function()? loadMore,
    TResult? Function()? resetLoadMore,
    TResult? Function(String? selectDate)? getNotiBySelectDay,
  }) {
    return getAllNotification?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getAllNotification,
    TResult Function()? readAllNotification,
    TResult Function(String id)? readNotification,
    TResult Function()? clearAll,
    TResult Function(NotificationFailure failure)? failureEvent,
    TResult Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult Function()? loadMore,
    TResult Function()? resetLoadMore,
    TResult Function(String? selectDate)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (getAllNotification != null) {
      return getAllNotification();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetAllNotification value) getAllNotification,
    required TResult Function(_ReadAllNotification value) readAllNotification,
    required TResult Function(_ReadNotification value) readNotification,
    required TResult Function(_ClearAll value) clearAll,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_GetAllNotiSuccess value) getAllNotiSuccess,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ResetLoadMore value) resetLoadMore,
    required TResult Function(_GetNotiBySelectDay value) getNotiBySelectDay,
  }) {
    return getAllNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetAllNotification value)? getAllNotification,
    TResult? Function(_ReadAllNotification value)? readAllNotification,
    TResult? Function(_ReadNotification value)? readNotification,
    TResult? Function(_ClearAll value)? clearAll,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ResetLoadMore value)? resetLoadMore,
    TResult? Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
  }) {
    return getAllNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetAllNotification value)? getAllNotification,
    TResult Function(_ReadAllNotification value)? readAllNotification,
    TResult Function(_ReadNotification value)? readNotification,
    TResult Function(_ClearAll value)? clearAll,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ResetLoadMore value)? resetLoadMore,
    TResult Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (getAllNotification != null) {
      return getAllNotification(this);
    }
    return orElse();
  }
}

abstract class _GetAllNotification implements NotificationFromServerEvent {
  const factory _GetAllNotification() = _$GetAllNotificationImpl;
}

/// @nodoc
abstract class _$$ReadAllNotificationImplCopyWith<$Res> {
  factory _$$ReadAllNotificationImplCopyWith(_$ReadAllNotificationImpl value,
          $Res Function(_$ReadAllNotificationImpl) then) =
      __$$ReadAllNotificationImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ReadAllNotificationImplCopyWithImpl<$Res>
    extends _$NotificationFromServerEventCopyWithImpl<$Res,
        _$ReadAllNotificationImpl>
    implements _$$ReadAllNotificationImplCopyWith<$Res> {
  __$$ReadAllNotificationImplCopyWithImpl(_$ReadAllNotificationImpl _value,
      $Res Function(_$ReadAllNotificationImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ReadAllNotificationImpl implements _ReadAllNotification {
  const _$ReadAllNotificationImpl();

  @override
  String toString() {
    return 'NotificationFromServerEvent.readAllNotification()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReadAllNotificationImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getAllNotification,
    required TResult Function() readAllNotification,
    required TResult Function(String id) readNotification,
    required TResult Function() clearAll,
    required TResult Function(NotificationFailure failure) failureEvent,
    required TResult Function(List<NotificationInApp> listNotification)
        getAllNotiSuccess,
    required TResult Function() loadMore,
    required TResult Function() resetLoadMore,
    required TResult Function(String? selectDate) getNotiBySelectDay,
  }) {
    return readAllNotification();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getAllNotification,
    TResult? Function()? readAllNotification,
    TResult? Function(String id)? readNotification,
    TResult? Function()? clearAll,
    TResult? Function(NotificationFailure failure)? failureEvent,
    TResult? Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult? Function()? loadMore,
    TResult? Function()? resetLoadMore,
    TResult? Function(String? selectDate)? getNotiBySelectDay,
  }) {
    return readAllNotification?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getAllNotification,
    TResult Function()? readAllNotification,
    TResult Function(String id)? readNotification,
    TResult Function()? clearAll,
    TResult Function(NotificationFailure failure)? failureEvent,
    TResult Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult Function()? loadMore,
    TResult Function()? resetLoadMore,
    TResult Function(String? selectDate)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (readAllNotification != null) {
      return readAllNotification();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetAllNotification value) getAllNotification,
    required TResult Function(_ReadAllNotification value) readAllNotification,
    required TResult Function(_ReadNotification value) readNotification,
    required TResult Function(_ClearAll value) clearAll,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_GetAllNotiSuccess value) getAllNotiSuccess,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ResetLoadMore value) resetLoadMore,
    required TResult Function(_GetNotiBySelectDay value) getNotiBySelectDay,
  }) {
    return readAllNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetAllNotification value)? getAllNotification,
    TResult? Function(_ReadAllNotification value)? readAllNotification,
    TResult? Function(_ReadNotification value)? readNotification,
    TResult? Function(_ClearAll value)? clearAll,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ResetLoadMore value)? resetLoadMore,
    TResult? Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
  }) {
    return readAllNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetAllNotification value)? getAllNotification,
    TResult Function(_ReadAllNotification value)? readAllNotification,
    TResult Function(_ReadNotification value)? readNotification,
    TResult Function(_ClearAll value)? clearAll,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ResetLoadMore value)? resetLoadMore,
    TResult Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (readAllNotification != null) {
      return readAllNotification(this);
    }
    return orElse();
  }
}

abstract class _ReadAllNotification implements NotificationFromServerEvent {
  const factory _ReadAllNotification() = _$ReadAllNotificationImpl;
}

/// @nodoc
abstract class _$$ReadNotificationImplCopyWith<$Res> {
  factory _$$ReadNotificationImplCopyWith(_$ReadNotificationImpl value,
          $Res Function(_$ReadNotificationImpl) then) =
      __$$ReadNotificationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$ReadNotificationImplCopyWithImpl<$Res>
    extends _$NotificationFromServerEventCopyWithImpl<$Res,
        _$ReadNotificationImpl>
    implements _$$ReadNotificationImplCopyWith<$Res> {
  __$$ReadNotificationImplCopyWithImpl(_$ReadNotificationImpl _value,
      $Res Function(_$ReadNotificationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$ReadNotificationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ReadNotificationImpl implements _ReadNotification {
  const _$ReadNotificationImpl({required this.id});

  @override
  final String id;

  @override
  String toString() {
    return 'NotificationFromServerEvent.readNotification(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReadNotificationImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReadNotificationImplCopyWith<_$ReadNotificationImpl> get copyWith =>
      __$$ReadNotificationImplCopyWithImpl<_$ReadNotificationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getAllNotification,
    required TResult Function() readAllNotification,
    required TResult Function(String id) readNotification,
    required TResult Function() clearAll,
    required TResult Function(NotificationFailure failure) failureEvent,
    required TResult Function(List<NotificationInApp> listNotification)
        getAllNotiSuccess,
    required TResult Function() loadMore,
    required TResult Function() resetLoadMore,
    required TResult Function(String? selectDate) getNotiBySelectDay,
  }) {
    return readNotification(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getAllNotification,
    TResult? Function()? readAllNotification,
    TResult? Function(String id)? readNotification,
    TResult? Function()? clearAll,
    TResult? Function(NotificationFailure failure)? failureEvent,
    TResult? Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult? Function()? loadMore,
    TResult? Function()? resetLoadMore,
    TResult? Function(String? selectDate)? getNotiBySelectDay,
  }) {
    return readNotification?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getAllNotification,
    TResult Function()? readAllNotification,
    TResult Function(String id)? readNotification,
    TResult Function()? clearAll,
    TResult Function(NotificationFailure failure)? failureEvent,
    TResult Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult Function()? loadMore,
    TResult Function()? resetLoadMore,
    TResult Function(String? selectDate)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (readNotification != null) {
      return readNotification(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetAllNotification value) getAllNotification,
    required TResult Function(_ReadAllNotification value) readAllNotification,
    required TResult Function(_ReadNotification value) readNotification,
    required TResult Function(_ClearAll value) clearAll,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_GetAllNotiSuccess value) getAllNotiSuccess,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ResetLoadMore value) resetLoadMore,
    required TResult Function(_GetNotiBySelectDay value) getNotiBySelectDay,
  }) {
    return readNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetAllNotification value)? getAllNotification,
    TResult? Function(_ReadAllNotification value)? readAllNotification,
    TResult? Function(_ReadNotification value)? readNotification,
    TResult? Function(_ClearAll value)? clearAll,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ResetLoadMore value)? resetLoadMore,
    TResult? Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
  }) {
    return readNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetAllNotification value)? getAllNotification,
    TResult Function(_ReadAllNotification value)? readAllNotification,
    TResult Function(_ReadNotification value)? readNotification,
    TResult Function(_ClearAll value)? clearAll,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ResetLoadMore value)? resetLoadMore,
    TResult Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (readNotification != null) {
      return readNotification(this);
    }
    return orElse();
  }
}

abstract class _ReadNotification implements NotificationFromServerEvent {
  const factory _ReadNotification({required final String id}) =
      _$ReadNotificationImpl;

  String get id;
  @JsonKey(ignore: true)
  _$$ReadNotificationImplCopyWith<_$ReadNotificationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearAllImplCopyWith<$Res> {
  factory _$$ClearAllImplCopyWith(
          _$ClearAllImpl value, $Res Function(_$ClearAllImpl) then) =
      __$$ClearAllImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearAllImplCopyWithImpl<$Res>
    extends _$NotificationFromServerEventCopyWithImpl<$Res, _$ClearAllImpl>
    implements _$$ClearAllImplCopyWith<$Res> {
  __$$ClearAllImplCopyWithImpl(
      _$ClearAllImpl _value, $Res Function(_$ClearAllImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearAllImpl implements _ClearAll {
  const _$ClearAllImpl();

  @override
  String toString() {
    return 'NotificationFromServerEvent.clearAll()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearAllImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getAllNotification,
    required TResult Function() readAllNotification,
    required TResult Function(String id) readNotification,
    required TResult Function() clearAll,
    required TResult Function(NotificationFailure failure) failureEvent,
    required TResult Function(List<NotificationInApp> listNotification)
        getAllNotiSuccess,
    required TResult Function() loadMore,
    required TResult Function() resetLoadMore,
    required TResult Function(String? selectDate) getNotiBySelectDay,
  }) {
    return clearAll();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getAllNotification,
    TResult? Function()? readAllNotification,
    TResult? Function(String id)? readNotification,
    TResult? Function()? clearAll,
    TResult? Function(NotificationFailure failure)? failureEvent,
    TResult? Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult? Function()? loadMore,
    TResult? Function()? resetLoadMore,
    TResult? Function(String? selectDate)? getNotiBySelectDay,
  }) {
    return clearAll?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getAllNotification,
    TResult Function()? readAllNotification,
    TResult Function(String id)? readNotification,
    TResult Function()? clearAll,
    TResult Function(NotificationFailure failure)? failureEvent,
    TResult Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult Function()? loadMore,
    TResult Function()? resetLoadMore,
    TResult Function(String? selectDate)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (clearAll != null) {
      return clearAll();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetAllNotification value) getAllNotification,
    required TResult Function(_ReadAllNotification value) readAllNotification,
    required TResult Function(_ReadNotification value) readNotification,
    required TResult Function(_ClearAll value) clearAll,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_GetAllNotiSuccess value) getAllNotiSuccess,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ResetLoadMore value) resetLoadMore,
    required TResult Function(_GetNotiBySelectDay value) getNotiBySelectDay,
  }) {
    return clearAll(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetAllNotification value)? getAllNotification,
    TResult? Function(_ReadAllNotification value)? readAllNotification,
    TResult? Function(_ReadNotification value)? readNotification,
    TResult? Function(_ClearAll value)? clearAll,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ResetLoadMore value)? resetLoadMore,
    TResult? Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
  }) {
    return clearAll?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetAllNotification value)? getAllNotification,
    TResult Function(_ReadAllNotification value)? readAllNotification,
    TResult Function(_ReadNotification value)? readNotification,
    TResult Function(_ClearAll value)? clearAll,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ResetLoadMore value)? resetLoadMore,
    TResult Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (clearAll != null) {
      return clearAll(this);
    }
    return orElse();
  }
}

abstract class _ClearAll implements NotificationFromServerEvent {
  const factory _ClearAll() = _$ClearAllImpl;
}

/// @nodoc
abstract class _$$FailureEventImplCopyWith<$Res> {
  factory _$$FailureEventImplCopyWith(
          _$FailureEventImpl value, $Res Function(_$FailureEventImpl) then) =
      __$$FailureEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationFailure failure});

  $NotificationFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureEventImplCopyWithImpl<$Res>
    extends _$NotificationFromServerEventCopyWithImpl<$Res, _$FailureEventImpl>
    implements _$$FailureEventImplCopyWith<$Res> {
  __$$FailureEventImplCopyWithImpl(
      _$FailureEventImpl _value, $Res Function(_$FailureEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$FailureEventImpl(
      failure: null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as NotificationFailure,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $NotificationFailureCopyWith<$Res> get failure {
    return $NotificationFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureEventImpl implements _FailureEvent {
  const _$FailureEventImpl({required this.failure});

  @override
  final NotificationFailure failure;

  @override
  String toString() {
    return 'NotificationFromServerEvent.failureEvent(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureEventImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureEventImplCopyWith<_$FailureEventImpl> get copyWith =>
      __$$FailureEventImplCopyWithImpl<_$FailureEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getAllNotification,
    required TResult Function() readAllNotification,
    required TResult Function(String id) readNotification,
    required TResult Function() clearAll,
    required TResult Function(NotificationFailure failure) failureEvent,
    required TResult Function(List<NotificationInApp> listNotification)
        getAllNotiSuccess,
    required TResult Function() loadMore,
    required TResult Function() resetLoadMore,
    required TResult Function(String? selectDate) getNotiBySelectDay,
  }) {
    return failureEvent(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getAllNotification,
    TResult? Function()? readAllNotification,
    TResult? Function(String id)? readNotification,
    TResult? Function()? clearAll,
    TResult? Function(NotificationFailure failure)? failureEvent,
    TResult? Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult? Function()? loadMore,
    TResult? Function()? resetLoadMore,
    TResult? Function(String? selectDate)? getNotiBySelectDay,
  }) {
    return failureEvent?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getAllNotification,
    TResult Function()? readAllNotification,
    TResult Function(String id)? readNotification,
    TResult Function()? clearAll,
    TResult Function(NotificationFailure failure)? failureEvent,
    TResult Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult Function()? loadMore,
    TResult Function()? resetLoadMore,
    TResult Function(String? selectDate)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (failureEvent != null) {
      return failureEvent(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetAllNotification value) getAllNotification,
    required TResult Function(_ReadAllNotification value) readAllNotification,
    required TResult Function(_ReadNotification value) readNotification,
    required TResult Function(_ClearAll value) clearAll,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_GetAllNotiSuccess value) getAllNotiSuccess,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ResetLoadMore value) resetLoadMore,
    required TResult Function(_GetNotiBySelectDay value) getNotiBySelectDay,
  }) {
    return failureEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetAllNotification value)? getAllNotification,
    TResult? Function(_ReadAllNotification value)? readAllNotification,
    TResult? Function(_ReadNotification value)? readNotification,
    TResult? Function(_ClearAll value)? clearAll,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ResetLoadMore value)? resetLoadMore,
    TResult? Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
  }) {
    return failureEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetAllNotification value)? getAllNotification,
    TResult Function(_ReadAllNotification value)? readAllNotification,
    TResult Function(_ReadNotification value)? readNotification,
    TResult Function(_ClearAll value)? clearAll,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ResetLoadMore value)? resetLoadMore,
    TResult Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (failureEvent != null) {
      return failureEvent(this);
    }
    return orElse();
  }
}

abstract class _FailureEvent implements NotificationFromServerEvent {
  const factory _FailureEvent({required final NotificationFailure failure}) =
      _$FailureEventImpl;

  NotificationFailure get failure;
  @JsonKey(ignore: true)
  _$$FailureEventImplCopyWith<_$FailureEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetAllNotiSuccessImplCopyWith<$Res> {
  factory _$$GetAllNotiSuccessImplCopyWith(_$GetAllNotiSuccessImpl value,
          $Res Function(_$GetAllNotiSuccessImpl) then) =
      __$$GetAllNotiSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<NotificationInApp> listNotification});
}

/// @nodoc
class __$$GetAllNotiSuccessImplCopyWithImpl<$Res>
    extends _$NotificationFromServerEventCopyWithImpl<$Res,
        _$GetAllNotiSuccessImpl>
    implements _$$GetAllNotiSuccessImplCopyWith<$Res> {
  __$$GetAllNotiSuccessImplCopyWithImpl(_$GetAllNotiSuccessImpl _value,
      $Res Function(_$GetAllNotiSuccessImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listNotification = null,
  }) {
    return _then(_$GetAllNotiSuccessImpl(
      listNotification: null == listNotification
          ? _value._listNotification
          : listNotification // ignore: cast_nullable_to_non_nullable
              as List<NotificationInApp>,
    ));
  }
}

/// @nodoc

class _$GetAllNotiSuccessImpl implements _GetAllNotiSuccess {
  const _$GetAllNotiSuccessImpl(
      {required final List<NotificationInApp> listNotification})
      : _listNotification = listNotification;

  final List<NotificationInApp> _listNotification;
  @override
  List<NotificationInApp> get listNotification {
    if (_listNotification is EqualUnmodifiableListView)
      return _listNotification;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listNotification);
  }

  @override
  String toString() {
    return 'NotificationFromServerEvent.getAllNotiSuccess(listNotification: $listNotification)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetAllNotiSuccessImpl &&
            const DeepCollectionEquality()
                .equals(other._listNotification, _listNotification));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_listNotification));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetAllNotiSuccessImplCopyWith<_$GetAllNotiSuccessImpl> get copyWith =>
      __$$GetAllNotiSuccessImplCopyWithImpl<_$GetAllNotiSuccessImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getAllNotification,
    required TResult Function() readAllNotification,
    required TResult Function(String id) readNotification,
    required TResult Function() clearAll,
    required TResult Function(NotificationFailure failure) failureEvent,
    required TResult Function(List<NotificationInApp> listNotification)
        getAllNotiSuccess,
    required TResult Function() loadMore,
    required TResult Function() resetLoadMore,
    required TResult Function(String? selectDate) getNotiBySelectDay,
  }) {
    return getAllNotiSuccess(listNotification);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getAllNotification,
    TResult? Function()? readAllNotification,
    TResult? Function(String id)? readNotification,
    TResult? Function()? clearAll,
    TResult? Function(NotificationFailure failure)? failureEvent,
    TResult? Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult? Function()? loadMore,
    TResult? Function()? resetLoadMore,
    TResult? Function(String? selectDate)? getNotiBySelectDay,
  }) {
    return getAllNotiSuccess?.call(listNotification);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getAllNotification,
    TResult Function()? readAllNotification,
    TResult Function(String id)? readNotification,
    TResult Function()? clearAll,
    TResult Function(NotificationFailure failure)? failureEvent,
    TResult Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult Function()? loadMore,
    TResult Function()? resetLoadMore,
    TResult Function(String? selectDate)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (getAllNotiSuccess != null) {
      return getAllNotiSuccess(listNotification);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetAllNotification value) getAllNotification,
    required TResult Function(_ReadAllNotification value) readAllNotification,
    required TResult Function(_ReadNotification value) readNotification,
    required TResult Function(_ClearAll value) clearAll,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_GetAllNotiSuccess value) getAllNotiSuccess,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ResetLoadMore value) resetLoadMore,
    required TResult Function(_GetNotiBySelectDay value) getNotiBySelectDay,
  }) {
    return getAllNotiSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetAllNotification value)? getAllNotification,
    TResult? Function(_ReadAllNotification value)? readAllNotification,
    TResult? Function(_ReadNotification value)? readNotification,
    TResult? Function(_ClearAll value)? clearAll,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ResetLoadMore value)? resetLoadMore,
    TResult? Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
  }) {
    return getAllNotiSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetAllNotification value)? getAllNotification,
    TResult Function(_ReadAllNotification value)? readAllNotification,
    TResult Function(_ReadNotification value)? readNotification,
    TResult Function(_ClearAll value)? clearAll,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ResetLoadMore value)? resetLoadMore,
    TResult Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (getAllNotiSuccess != null) {
      return getAllNotiSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetAllNotiSuccess implements NotificationFromServerEvent {
  const factory _GetAllNotiSuccess(
          {required final List<NotificationInApp> listNotification}) =
      _$GetAllNotiSuccessImpl;

  List<NotificationInApp> get listNotification;
  @JsonKey(ignore: true)
  _$$GetAllNotiSuccessImplCopyWith<_$GetAllNotiSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadMoreImplCopyWith<$Res> {
  factory _$$LoadMoreImplCopyWith(
          _$LoadMoreImpl value, $Res Function(_$LoadMoreImpl) then) =
      __$$LoadMoreImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadMoreImplCopyWithImpl<$Res>
    extends _$NotificationFromServerEventCopyWithImpl<$Res, _$LoadMoreImpl>
    implements _$$LoadMoreImplCopyWith<$Res> {
  __$$LoadMoreImplCopyWithImpl(
      _$LoadMoreImpl _value, $Res Function(_$LoadMoreImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LoadMoreImpl implements _LoadMore {
  const _$LoadMoreImpl();

  @override
  String toString() {
    return 'NotificationFromServerEvent.loadMore()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadMoreImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getAllNotification,
    required TResult Function() readAllNotification,
    required TResult Function(String id) readNotification,
    required TResult Function() clearAll,
    required TResult Function(NotificationFailure failure) failureEvent,
    required TResult Function(List<NotificationInApp> listNotification)
        getAllNotiSuccess,
    required TResult Function() loadMore,
    required TResult Function() resetLoadMore,
    required TResult Function(String? selectDate) getNotiBySelectDay,
  }) {
    return loadMore();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getAllNotification,
    TResult? Function()? readAllNotification,
    TResult? Function(String id)? readNotification,
    TResult? Function()? clearAll,
    TResult? Function(NotificationFailure failure)? failureEvent,
    TResult? Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult? Function()? loadMore,
    TResult? Function()? resetLoadMore,
    TResult? Function(String? selectDate)? getNotiBySelectDay,
  }) {
    return loadMore?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getAllNotification,
    TResult Function()? readAllNotification,
    TResult Function(String id)? readNotification,
    TResult Function()? clearAll,
    TResult Function(NotificationFailure failure)? failureEvent,
    TResult Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult Function()? loadMore,
    TResult Function()? resetLoadMore,
    TResult Function(String? selectDate)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (loadMore != null) {
      return loadMore();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetAllNotification value) getAllNotification,
    required TResult Function(_ReadAllNotification value) readAllNotification,
    required TResult Function(_ReadNotification value) readNotification,
    required TResult Function(_ClearAll value) clearAll,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_GetAllNotiSuccess value) getAllNotiSuccess,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ResetLoadMore value) resetLoadMore,
    required TResult Function(_GetNotiBySelectDay value) getNotiBySelectDay,
  }) {
    return loadMore(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetAllNotification value)? getAllNotification,
    TResult? Function(_ReadAllNotification value)? readAllNotification,
    TResult? Function(_ReadNotification value)? readNotification,
    TResult? Function(_ClearAll value)? clearAll,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ResetLoadMore value)? resetLoadMore,
    TResult? Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
  }) {
    return loadMore?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetAllNotification value)? getAllNotification,
    TResult Function(_ReadAllNotification value)? readAllNotification,
    TResult Function(_ReadNotification value)? readNotification,
    TResult Function(_ClearAll value)? clearAll,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ResetLoadMore value)? resetLoadMore,
    TResult Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (loadMore != null) {
      return loadMore(this);
    }
    return orElse();
  }
}

abstract class _LoadMore implements NotificationFromServerEvent {
  const factory _LoadMore() = _$LoadMoreImpl;
}

/// @nodoc
abstract class _$$ResetLoadMoreImplCopyWith<$Res> {
  factory _$$ResetLoadMoreImplCopyWith(
          _$ResetLoadMoreImpl value, $Res Function(_$ResetLoadMoreImpl) then) =
      __$$ResetLoadMoreImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetLoadMoreImplCopyWithImpl<$Res>
    extends _$NotificationFromServerEventCopyWithImpl<$Res, _$ResetLoadMoreImpl>
    implements _$$ResetLoadMoreImplCopyWith<$Res> {
  __$$ResetLoadMoreImplCopyWithImpl(
      _$ResetLoadMoreImpl _value, $Res Function(_$ResetLoadMoreImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ResetLoadMoreImpl implements _ResetLoadMore {
  const _$ResetLoadMoreImpl();

  @override
  String toString() {
    return 'NotificationFromServerEvent.resetLoadMore()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetLoadMoreImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getAllNotification,
    required TResult Function() readAllNotification,
    required TResult Function(String id) readNotification,
    required TResult Function() clearAll,
    required TResult Function(NotificationFailure failure) failureEvent,
    required TResult Function(List<NotificationInApp> listNotification)
        getAllNotiSuccess,
    required TResult Function() loadMore,
    required TResult Function() resetLoadMore,
    required TResult Function(String? selectDate) getNotiBySelectDay,
  }) {
    return resetLoadMore();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getAllNotification,
    TResult? Function()? readAllNotification,
    TResult? Function(String id)? readNotification,
    TResult? Function()? clearAll,
    TResult? Function(NotificationFailure failure)? failureEvent,
    TResult? Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult? Function()? loadMore,
    TResult? Function()? resetLoadMore,
    TResult? Function(String? selectDate)? getNotiBySelectDay,
  }) {
    return resetLoadMore?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getAllNotification,
    TResult Function()? readAllNotification,
    TResult Function(String id)? readNotification,
    TResult Function()? clearAll,
    TResult Function(NotificationFailure failure)? failureEvent,
    TResult Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult Function()? loadMore,
    TResult Function()? resetLoadMore,
    TResult Function(String? selectDate)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (resetLoadMore != null) {
      return resetLoadMore();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetAllNotification value) getAllNotification,
    required TResult Function(_ReadAllNotification value) readAllNotification,
    required TResult Function(_ReadNotification value) readNotification,
    required TResult Function(_ClearAll value) clearAll,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_GetAllNotiSuccess value) getAllNotiSuccess,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ResetLoadMore value) resetLoadMore,
    required TResult Function(_GetNotiBySelectDay value) getNotiBySelectDay,
  }) {
    return resetLoadMore(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetAllNotification value)? getAllNotification,
    TResult? Function(_ReadAllNotification value)? readAllNotification,
    TResult? Function(_ReadNotification value)? readNotification,
    TResult? Function(_ClearAll value)? clearAll,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ResetLoadMore value)? resetLoadMore,
    TResult? Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
  }) {
    return resetLoadMore?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetAllNotification value)? getAllNotification,
    TResult Function(_ReadAllNotification value)? readAllNotification,
    TResult Function(_ReadNotification value)? readNotification,
    TResult Function(_ClearAll value)? clearAll,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ResetLoadMore value)? resetLoadMore,
    TResult Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (resetLoadMore != null) {
      return resetLoadMore(this);
    }
    return orElse();
  }
}

abstract class _ResetLoadMore implements NotificationFromServerEvent {
  const factory _ResetLoadMore() = _$ResetLoadMoreImpl;
}

/// @nodoc
abstract class _$$GetNotiBySelectDayImplCopyWith<$Res> {
  factory _$$GetNotiBySelectDayImplCopyWith(_$GetNotiBySelectDayImpl value,
          $Res Function(_$GetNotiBySelectDayImpl) then) =
      __$$GetNotiBySelectDayImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? selectDate});
}

/// @nodoc
class __$$GetNotiBySelectDayImplCopyWithImpl<$Res>
    extends _$NotificationFromServerEventCopyWithImpl<$Res,
        _$GetNotiBySelectDayImpl>
    implements _$$GetNotiBySelectDayImplCopyWith<$Res> {
  __$$GetNotiBySelectDayImplCopyWithImpl(_$GetNotiBySelectDayImpl _value,
      $Res Function(_$GetNotiBySelectDayImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectDate = freezed,
  }) {
    return _then(_$GetNotiBySelectDayImpl(
      selectDate: freezed == selectDate
          ? _value.selectDate
          : selectDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$GetNotiBySelectDayImpl implements _GetNotiBySelectDay {
  const _$GetNotiBySelectDayImpl({required this.selectDate});

  @override
  final String? selectDate;

  @override
  String toString() {
    return 'NotificationFromServerEvent.getNotiBySelectDay(selectDate: $selectDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetNotiBySelectDayImpl &&
            (identical(other.selectDate, selectDate) ||
                other.selectDate == selectDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectDate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetNotiBySelectDayImplCopyWith<_$GetNotiBySelectDayImpl> get copyWith =>
      __$$GetNotiBySelectDayImplCopyWithImpl<_$GetNotiBySelectDayImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getAllNotification,
    required TResult Function() readAllNotification,
    required TResult Function(String id) readNotification,
    required TResult Function() clearAll,
    required TResult Function(NotificationFailure failure) failureEvent,
    required TResult Function(List<NotificationInApp> listNotification)
        getAllNotiSuccess,
    required TResult Function() loadMore,
    required TResult Function() resetLoadMore,
    required TResult Function(String? selectDate) getNotiBySelectDay,
  }) {
    return getNotiBySelectDay(selectDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getAllNotification,
    TResult? Function()? readAllNotification,
    TResult? Function(String id)? readNotification,
    TResult? Function()? clearAll,
    TResult? Function(NotificationFailure failure)? failureEvent,
    TResult? Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult? Function()? loadMore,
    TResult? Function()? resetLoadMore,
    TResult? Function(String? selectDate)? getNotiBySelectDay,
  }) {
    return getNotiBySelectDay?.call(selectDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getAllNotification,
    TResult Function()? readAllNotification,
    TResult Function(String id)? readNotification,
    TResult Function()? clearAll,
    TResult Function(NotificationFailure failure)? failureEvent,
    TResult Function(List<NotificationInApp> listNotification)?
        getAllNotiSuccess,
    TResult Function()? loadMore,
    TResult Function()? resetLoadMore,
    TResult Function(String? selectDate)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (getNotiBySelectDay != null) {
      return getNotiBySelectDay(selectDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetAllNotification value) getAllNotification,
    required TResult Function(_ReadAllNotification value) readAllNotification,
    required TResult Function(_ReadNotification value) readNotification,
    required TResult Function(_ClearAll value) clearAll,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_GetAllNotiSuccess value) getAllNotiSuccess,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ResetLoadMore value) resetLoadMore,
    required TResult Function(_GetNotiBySelectDay value) getNotiBySelectDay,
  }) {
    return getNotiBySelectDay(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetAllNotification value)? getAllNotification,
    TResult? Function(_ReadAllNotification value)? readAllNotification,
    TResult? Function(_ReadNotification value)? readNotification,
    TResult? Function(_ClearAll value)? clearAll,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ResetLoadMore value)? resetLoadMore,
    TResult? Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
  }) {
    return getNotiBySelectDay?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetAllNotification value)? getAllNotification,
    TResult Function(_ReadAllNotification value)? readAllNotification,
    TResult Function(_ReadNotification value)? readNotification,
    TResult Function(_ClearAll value)? clearAll,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_GetAllNotiSuccess value)? getAllNotiSuccess,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ResetLoadMore value)? resetLoadMore,
    TResult Function(_GetNotiBySelectDay value)? getNotiBySelectDay,
    required TResult orElse(),
  }) {
    if (getNotiBySelectDay != null) {
      return getNotiBySelectDay(this);
    }
    return orElse();
  }
}

abstract class _GetNotiBySelectDay implements NotificationFromServerEvent {
  const factory _GetNotiBySelectDay({required final String? selectDate}) =
      _$GetNotiBySelectDayImpl;

  String? get selectDate;
  @JsonKey(ignore: true)
  _$$GetNotiBySelectDayImplCopyWith<_$GetNotiBySelectDayImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$NotificationFromServerState {
  /// LOAD MORE
  List<NotificationInApp> get listRaw => throw _privateConstructorUsedError;
  List<NotificationInApp> get listNotification =>
      throw _privateConstructorUsedError;
  List<NotificationInApp> get listNotificationQueryByDay =>
      throw _privateConstructorUsedError;
  int get lengthNow => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  NotificationFailure? get error => throw _privateConstructorUsedError;
  List<String> get listDay => throw _privateConstructorUsedError;
  String? get filterDay => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $NotificationFromServerStateCopyWith<NotificationFromServerState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationFromServerStateCopyWith<$Res> {
  factory $NotificationFromServerStateCopyWith(
          NotificationFromServerState value,
          $Res Function(NotificationFromServerState) then) =
      _$NotificationFromServerStateCopyWithImpl<$Res,
          NotificationFromServerState>;
  @useResult
  $Res call(
      {List<NotificationInApp> listRaw,
      List<NotificationInApp> listNotification,
      List<NotificationInApp> listNotificationQueryByDay,
      int lengthNow,
      bool isLoading,
      NotificationFailure? error,
      List<String> listDay,
      String? filterDay});

  $NotificationFailureCopyWith<$Res>? get error;
}

/// @nodoc
class _$NotificationFromServerStateCopyWithImpl<$Res,
        $Val extends NotificationFromServerState>
    implements $NotificationFromServerStateCopyWith<$Res> {
  _$NotificationFromServerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listRaw = null,
    Object? listNotification = null,
    Object? listNotificationQueryByDay = null,
    Object? lengthNow = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? listDay = null,
    Object? filterDay = freezed,
  }) {
    return _then(_value.copyWith(
      listRaw: null == listRaw
          ? _value.listRaw
          : listRaw // ignore: cast_nullable_to_non_nullable
              as List<NotificationInApp>,
      listNotification: null == listNotification
          ? _value.listNotification
          : listNotification // ignore: cast_nullable_to_non_nullable
              as List<NotificationInApp>,
      listNotificationQueryByDay: null == listNotificationQueryByDay
          ? _value.listNotificationQueryByDay
          : listNotificationQueryByDay // ignore: cast_nullable_to_non_nullable
              as List<NotificationInApp>,
      lengthNow: null == lengthNow
          ? _value.lengthNow
          : lengthNow // ignore: cast_nullable_to_non_nullable
              as int,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as NotificationFailure?,
      listDay: null == listDay
          ? _value.listDay
          : listDay // ignore: cast_nullable_to_non_nullable
              as List<String>,
      filterDay: freezed == filterDay
          ? _value.filterDay
          : filterDay // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $NotificationFailureCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $NotificationFailureCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$NotificationFromServerStateImplCopyWith<$Res>
    implements $NotificationFromServerStateCopyWith<$Res> {
  factory _$$NotificationFromServerStateImplCopyWith(
          _$NotificationFromServerStateImpl value,
          $Res Function(_$NotificationFromServerStateImpl) then) =
      __$$NotificationFromServerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<NotificationInApp> listRaw,
      List<NotificationInApp> listNotification,
      List<NotificationInApp> listNotificationQueryByDay,
      int lengthNow,
      bool isLoading,
      NotificationFailure? error,
      List<String> listDay,
      String? filterDay});

  @override
  $NotificationFailureCopyWith<$Res>? get error;
}

/// @nodoc
class __$$NotificationFromServerStateImplCopyWithImpl<$Res>
    extends _$NotificationFromServerStateCopyWithImpl<$Res,
        _$NotificationFromServerStateImpl>
    implements _$$NotificationFromServerStateImplCopyWith<$Res> {
  __$$NotificationFromServerStateImplCopyWithImpl(
      _$NotificationFromServerStateImpl _value,
      $Res Function(_$NotificationFromServerStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listRaw = null,
    Object? listNotification = null,
    Object? listNotificationQueryByDay = null,
    Object? lengthNow = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? listDay = null,
    Object? filterDay = freezed,
  }) {
    return _then(_$NotificationFromServerStateImpl(
      listRaw: null == listRaw
          ? _value._listRaw
          : listRaw // ignore: cast_nullable_to_non_nullable
              as List<NotificationInApp>,
      listNotification: null == listNotification
          ? _value._listNotification
          : listNotification // ignore: cast_nullable_to_non_nullable
              as List<NotificationInApp>,
      listNotificationQueryByDay: null == listNotificationQueryByDay
          ? _value._listNotificationQueryByDay
          : listNotificationQueryByDay // ignore: cast_nullable_to_non_nullable
              as List<NotificationInApp>,
      lengthNow: null == lengthNow
          ? _value.lengthNow
          : lengthNow // ignore: cast_nullable_to_non_nullable
              as int,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as NotificationFailure?,
      listDay: null == listDay
          ? _value._listDay
          : listDay // ignore: cast_nullable_to_non_nullable
              as List<String>,
      filterDay: freezed == filterDay
          ? _value.filterDay
          : filterDay // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$NotificationFromServerStateImpl
    implements _NotificationFromServerState {
  const _$NotificationFromServerStateImpl(
      {required final List<NotificationInApp> listRaw,
      required final List<NotificationInApp> listNotification,
      required final List<NotificationInApp> listNotificationQueryByDay,
      required this.lengthNow,
      required this.isLoading,
      required this.error,
      required final List<String> listDay,
      required this.filterDay})
      : _listRaw = listRaw,
        _listNotification = listNotification,
        _listNotificationQueryByDay = listNotificationQueryByDay,
        _listDay = listDay;

  /// LOAD MORE
  final List<NotificationInApp> _listRaw;

  /// LOAD MORE
  @override
  List<NotificationInApp> get listRaw {
    if (_listRaw is EqualUnmodifiableListView) return _listRaw;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listRaw);
  }

  final List<NotificationInApp> _listNotification;
  @override
  List<NotificationInApp> get listNotification {
    if (_listNotification is EqualUnmodifiableListView)
      return _listNotification;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listNotification);
  }

  final List<NotificationInApp> _listNotificationQueryByDay;
  @override
  List<NotificationInApp> get listNotificationQueryByDay {
    if (_listNotificationQueryByDay is EqualUnmodifiableListView)
      return _listNotificationQueryByDay;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listNotificationQueryByDay);
  }

  @override
  final int lengthNow;
  @override
  final bool isLoading;
  @override
  final NotificationFailure? error;
  final List<String> _listDay;
  @override
  List<String> get listDay {
    if (_listDay is EqualUnmodifiableListView) return _listDay;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listDay);
  }

  @override
  final String? filterDay;

  @override
  String toString() {
    return 'NotificationFromServerState(listRaw: $listRaw, listNotification: $listNotification, listNotificationQueryByDay: $listNotificationQueryByDay, lengthNow: $lengthNow, isLoading: $isLoading, error: $error, listDay: $listDay, filterDay: $filterDay)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationFromServerStateImpl &&
            const DeepCollectionEquality().equals(other._listRaw, _listRaw) &&
            const DeepCollectionEquality()
                .equals(other._listNotification, _listNotification) &&
            const DeepCollectionEquality().equals(
                other._listNotificationQueryByDay,
                _listNotificationQueryByDay) &&
            (identical(other.lengthNow, lengthNow) ||
                other.lengthNow == lengthNow) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error) &&
            const DeepCollectionEquality().equals(other._listDay, _listDay) &&
            (identical(other.filterDay, filterDay) ||
                other.filterDay == filterDay));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_listRaw),
      const DeepCollectionEquality().hash(_listNotification),
      const DeepCollectionEquality().hash(_listNotificationQueryByDay),
      lengthNow,
      isLoading,
      error,
      const DeepCollectionEquality().hash(_listDay),
      filterDay);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationFromServerStateImplCopyWith<_$NotificationFromServerStateImpl>
      get copyWith => __$$NotificationFromServerStateImplCopyWithImpl<
          _$NotificationFromServerStateImpl>(this, _$identity);
}

abstract class _NotificationFromServerState
    implements NotificationFromServerState {
  const factory _NotificationFromServerState(
      {required final List<NotificationInApp> listRaw,
      required final List<NotificationInApp> listNotification,
      required final List<NotificationInApp> listNotificationQueryByDay,
      required final int lengthNow,
      required final bool isLoading,
      required final NotificationFailure? error,
      required final List<String> listDay,
      required final String? filterDay}) = _$NotificationFromServerStateImpl;

  @override

  /// LOAD MORE
  List<NotificationInApp> get listRaw;
  @override
  List<NotificationInApp> get listNotification;
  @override
  List<NotificationInApp> get listNotificationQueryByDay;
  @override
  int get lengthNow;
  @override
  bool get isLoading;
  @override
  NotificationFailure? get error;
  @override
  List<String> get listDay;
  @override
  String? get filterDay;
  @override
  @JsonKey(ignore: true)
  _$$NotificationFromServerStateImplCopyWith<_$NotificationFromServerStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
