// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'curl_helper_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CurlHelperEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)
        update,
    required TResult Function(CurlRepresentation curl) add,
    required TResult Function() clearAll,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)?
        update,
    TResult? Function(CurlRepresentation curl)? add,
    TResult? Function()? clearAll,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)?
        update,
    TResult Function(CurlRepresentation curl)? add,
    TResult Function()? clearAll,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Update value) update,
    required TResult Function(_Add value) add,
    required TResult Function(_ClearAll value) clearAll,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Update value)? update,
    TResult? Function(_Add value)? add,
    TResult? Function(_ClearAll value)? clearAll,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Update value)? update,
    TResult Function(_Add value)? add,
    TResult Function(_ClearAll value)? clearAll,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CurlHelperEventCopyWith<$Res> {
  factory $CurlHelperEventCopyWith(
          CurlHelperEvent value, $Res Function(CurlHelperEvent) then) =
      _$CurlHelperEventCopyWithImpl<$Res, CurlHelperEvent>;
}

/// @nodoc
class _$CurlHelperEventCopyWithImpl<$Res, $Val extends CurlHelperEvent>
    implements $CurlHelperEventCopyWith<$Res> {
  _$CurlHelperEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$UpdateImplCopyWith<$Res> {
  factory _$$UpdateImplCopyWith(
          _$UpdateImpl value, $Res Function(_$UpdateImpl) then) =
      __$$UpdateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {int statusCode,
      String message,
      String apiId,
      Map<String, dynamic> response});
}

/// @nodoc
class __$$UpdateImplCopyWithImpl<$Res>
    extends _$CurlHelperEventCopyWithImpl<$Res, _$UpdateImpl>
    implements _$$UpdateImplCopyWith<$Res> {
  __$$UpdateImplCopyWithImpl(
      _$UpdateImpl _value, $Res Function(_$UpdateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statusCode = null,
    Object? message = null,
    Object? apiId = null,
    Object? response = null,
  }) {
    return _then(_$UpdateImpl(
      statusCode: null == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      apiId: null == apiId
          ? _value.apiId
          : apiId // ignore: cast_nullable_to_non_nullable
              as String,
      response: null == response
          ? _value._response
          : response // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateImpl implements _Update {
  const _$UpdateImpl(
      {required this.statusCode,
      required this.message,
      required this.apiId,
      required final Map<String, dynamic> response})
      : _response = response;

  @override
  final int statusCode;
  @override
  final String message;
  @override
  final String apiId;
  final Map<String, dynamic> _response;
  @override
  Map<String, dynamic> get response {
    if (_response is EqualUnmodifiableMapView) return _response;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_response);
  }

  @override
  String toString() {
    return 'CurlHelperEvent.update(statusCode: $statusCode, message: $message, apiId: $apiId, response: $response)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateImpl &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.apiId, apiId) || other.apiId == apiId) &&
            const DeepCollectionEquality().equals(other._response, _response));
  }

  @override
  int get hashCode => Object.hash(runtimeType, statusCode, message, apiId,
      const DeepCollectionEquality().hash(_response));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateImplCopyWith<_$UpdateImpl> get copyWith =>
      __$$UpdateImplCopyWithImpl<_$UpdateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)
        update,
    required TResult Function(CurlRepresentation curl) add,
    required TResult Function() clearAll,
  }) {
    return update(statusCode, message, apiId, response);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)?
        update,
    TResult? Function(CurlRepresentation curl)? add,
    TResult? Function()? clearAll,
  }) {
    return update?.call(statusCode, message, apiId, response);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)?
        update,
    TResult Function(CurlRepresentation curl)? add,
    TResult Function()? clearAll,
    required TResult orElse(),
  }) {
    if (update != null) {
      return update(statusCode, message, apiId, response);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Update value) update,
    required TResult Function(_Add value) add,
    required TResult Function(_ClearAll value) clearAll,
  }) {
    return update(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Update value)? update,
    TResult? Function(_Add value)? add,
    TResult? Function(_ClearAll value)? clearAll,
  }) {
    return update?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Update value)? update,
    TResult Function(_Add value)? add,
    TResult Function(_ClearAll value)? clearAll,
    required TResult orElse(),
  }) {
    if (update != null) {
      return update(this);
    }
    return orElse();
  }
}

abstract class _Update implements CurlHelperEvent {
  const factory _Update(
      {required final int statusCode,
      required final String message,
      required final String apiId,
      required final Map<String, dynamic> response}) = _$UpdateImpl;

  int get statusCode;
  String get message;
  String get apiId;
  Map<String, dynamic> get response;
  @JsonKey(ignore: true)
  _$$UpdateImplCopyWith<_$UpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddImplCopyWith<$Res> {
  factory _$$AddImplCopyWith(_$AddImpl value, $Res Function(_$AddImpl) then) =
      __$$AddImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CurlRepresentation curl});

  $CurlRepresentationCopyWith<$Res> get curl;
}

/// @nodoc
class __$$AddImplCopyWithImpl<$Res>
    extends _$CurlHelperEventCopyWithImpl<$Res, _$AddImpl>
    implements _$$AddImplCopyWith<$Res> {
  __$$AddImplCopyWithImpl(_$AddImpl _value, $Res Function(_$AddImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? curl = null,
  }) {
    return _then(_$AddImpl(
      curl: null == curl
          ? _value.curl
          : curl // ignore: cast_nullable_to_non_nullable
              as CurlRepresentation,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $CurlRepresentationCopyWith<$Res> get curl {
    return $CurlRepresentationCopyWith<$Res>(_value.curl, (value) {
      return _then(_value.copyWith(curl: value));
    });
  }
}

/// @nodoc

class _$AddImpl implements _Add {
  const _$AddImpl({required this.curl});

  @override
  final CurlRepresentation curl;

  @override
  String toString() {
    return 'CurlHelperEvent.add(curl: $curl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddImpl &&
            (identical(other.curl, curl) || other.curl == curl));
  }

  @override
  int get hashCode => Object.hash(runtimeType, curl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AddImplCopyWith<_$AddImpl> get copyWith =>
      __$$AddImplCopyWithImpl<_$AddImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)
        update,
    required TResult Function(CurlRepresentation curl) add,
    required TResult Function() clearAll,
  }) {
    return add(curl);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)?
        update,
    TResult? Function(CurlRepresentation curl)? add,
    TResult? Function()? clearAll,
  }) {
    return add?.call(curl);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)?
        update,
    TResult Function(CurlRepresentation curl)? add,
    TResult Function()? clearAll,
    required TResult orElse(),
  }) {
    if (add != null) {
      return add(curl);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Update value) update,
    required TResult Function(_Add value) add,
    required TResult Function(_ClearAll value) clearAll,
  }) {
    return add(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Update value)? update,
    TResult? Function(_Add value)? add,
    TResult? Function(_ClearAll value)? clearAll,
  }) {
    return add?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Update value)? update,
    TResult Function(_Add value)? add,
    TResult Function(_ClearAll value)? clearAll,
    required TResult orElse(),
  }) {
    if (add != null) {
      return add(this);
    }
    return orElse();
  }
}

abstract class _Add implements CurlHelperEvent {
  const factory _Add({required final CurlRepresentation curl}) = _$AddImpl;

  CurlRepresentation get curl;
  @JsonKey(ignore: true)
  _$$AddImplCopyWith<_$AddImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearAllImplCopyWith<$Res> {
  factory _$$ClearAllImplCopyWith(
          _$ClearAllImpl value, $Res Function(_$ClearAllImpl) then) =
      __$$ClearAllImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearAllImplCopyWithImpl<$Res>
    extends _$CurlHelperEventCopyWithImpl<$Res, _$ClearAllImpl>
    implements _$$ClearAllImplCopyWith<$Res> {
  __$$ClearAllImplCopyWithImpl(
      _$ClearAllImpl _value, $Res Function(_$ClearAllImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearAllImpl implements _ClearAll {
  const _$ClearAllImpl();

  @override
  String toString() {
    return 'CurlHelperEvent.clearAll()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearAllImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)
        update,
    required TResult Function(CurlRepresentation curl) add,
    required TResult Function() clearAll,
  }) {
    return clearAll();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)?
        update,
    TResult? Function(CurlRepresentation curl)? add,
    TResult? Function()? clearAll,
  }) {
    return clearAll?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int statusCode, String message, String apiId,
            Map<String, dynamic> response)?
        update,
    TResult Function(CurlRepresentation curl)? add,
    TResult Function()? clearAll,
    required TResult orElse(),
  }) {
    if (clearAll != null) {
      return clearAll();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Update value) update,
    required TResult Function(_Add value) add,
    required TResult Function(_ClearAll value) clearAll,
  }) {
    return clearAll(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Update value)? update,
    TResult? Function(_Add value)? add,
    TResult? Function(_ClearAll value)? clearAll,
  }) {
    return clearAll?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Update value)? update,
    TResult Function(_Add value)? add,
    TResult Function(_ClearAll value)? clearAll,
    required TResult orElse(),
  }) {
    if (clearAll != null) {
      return clearAll(this);
    }
    return orElse();
  }
}

abstract class _ClearAll implements CurlHelperEvent {
  const factory _ClearAll() = _$ClearAllImpl;
}

/// @nodoc
mixin _$CurlHelperState {
  List<CurlRepresentation> get listCurl => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CurlHelperStateCopyWith<CurlHelperState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CurlHelperStateCopyWith<$Res> {
  factory $CurlHelperStateCopyWith(
          CurlHelperState value, $Res Function(CurlHelperState) then) =
      _$CurlHelperStateCopyWithImpl<$Res, CurlHelperState>;
  @useResult
  $Res call({List<CurlRepresentation> listCurl});
}

/// @nodoc
class _$CurlHelperStateCopyWithImpl<$Res, $Val extends CurlHelperState>
    implements $CurlHelperStateCopyWith<$Res> {
  _$CurlHelperStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listCurl = null,
  }) {
    return _then(_value.copyWith(
      listCurl: null == listCurl
          ? _value.listCurl
          : listCurl // ignore: cast_nullable_to_non_nullable
              as List<CurlRepresentation>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CurlHelperStateImplCopyWith<$Res>
    implements $CurlHelperStateCopyWith<$Res> {
  factory _$$CurlHelperStateImplCopyWith(_$CurlHelperStateImpl value,
          $Res Function(_$CurlHelperStateImpl) then) =
      __$$CurlHelperStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CurlRepresentation> listCurl});
}

/// @nodoc
class __$$CurlHelperStateImplCopyWithImpl<$Res>
    extends _$CurlHelperStateCopyWithImpl<$Res, _$CurlHelperStateImpl>
    implements _$$CurlHelperStateImplCopyWith<$Res> {
  __$$CurlHelperStateImplCopyWithImpl(
      _$CurlHelperStateImpl _value, $Res Function(_$CurlHelperStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listCurl = null,
  }) {
    return _then(_$CurlHelperStateImpl(
      listCurl: null == listCurl
          ? _value._listCurl
          : listCurl // ignore: cast_nullable_to_non_nullable
              as List<CurlRepresentation>,
    ));
  }
}

/// @nodoc

class _$CurlHelperStateImpl implements _CurlHelperState {
  const _$CurlHelperStateImpl(
      {required final List<CurlRepresentation> listCurl})
      : _listCurl = listCurl;

  final List<CurlRepresentation> _listCurl;
  @override
  List<CurlRepresentation> get listCurl {
    if (_listCurl is EqualUnmodifiableListView) return _listCurl;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listCurl);
  }

  @override
  String toString() {
    return 'CurlHelperState(listCurl: $listCurl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CurlHelperStateImpl &&
            const DeepCollectionEquality().equals(other._listCurl, _listCurl));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_listCurl));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CurlHelperStateImplCopyWith<_$CurlHelperStateImpl> get copyWith =>
      __$$CurlHelperStateImplCopyWithImpl<_$CurlHelperStateImpl>(
          this, _$identity);
}

abstract class _CurlHelperState implements CurlHelperState {
  const factory _CurlHelperState(
          {required final List<CurlRepresentation> listCurl}) =
      _$CurlHelperStateImpl;

  @override
  List<CurlRepresentation> get listCurl;
  @override
  @JsonKey(ignore: true)
  _$$CurlHelperStateImplCopyWith<_$CurlHelperStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
