// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_image_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$VehicleImageEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Vehicle vehicleId, int time, bool delay)
        loadAllImage,
    required TResult Function(
            List<VehicleImage> listVehicleImage, Vehicle vehicle)
        loadImageSuccess,
    required TResult Function(VehicleImageFailure failure) getImageFail,
    required TResult Function() takeImageSuccess,
    required TResult Function(VehicleImageFailure failure) takeImageFail,
    required TResult Function(String id) takeImage,
    required TResult Function() clearAllImage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult? Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult? Function(VehicleImageFailure failure)? getImageFail,
    TResult? Function()? takeImageSuccess,
    TResult? Function(VehicleImageFailure failure)? takeImageFail,
    TResult? Function(String id)? takeImage,
    TResult? Function()? clearAllImage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult Function(VehicleImageFailure failure)? getImageFail,
    TResult Function()? takeImageSuccess,
    TResult Function(VehicleImageFailure failure)? takeImageFail,
    TResult Function(String id)? takeImage,
    TResult Function()? clearAllImage,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllImage value) loadAllImage,
    required TResult Function(_LoadImageSuccess value) loadImageSuccess,
    required TResult Function(_GetImageFail value) getImageFail,
    required TResult Function(_TakeImageSuccess value) takeImageSuccess,
    required TResult Function(_TakeImageFail value) takeImageFail,
    required TResult Function(_TakeImage value) takeImage,
    required TResult Function(_ClearAllImage value) clearAllImage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllImage value)? loadAllImage,
    TResult? Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult? Function(_GetImageFail value)? getImageFail,
    TResult? Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult? Function(_TakeImageFail value)? takeImageFail,
    TResult? Function(_TakeImage value)? takeImage,
    TResult? Function(_ClearAllImage value)? clearAllImage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllImage value)? loadAllImage,
    TResult Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult Function(_GetImageFail value)? getImageFail,
    TResult Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult Function(_TakeImageFail value)? takeImageFail,
    TResult Function(_TakeImage value)? takeImage,
    TResult Function(_ClearAllImage value)? clearAllImage,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleImageEventCopyWith<$Res> {
  factory $VehicleImageEventCopyWith(
          VehicleImageEvent value, $Res Function(VehicleImageEvent) then) =
      _$VehicleImageEventCopyWithImpl<$Res, VehicleImageEvent>;
}

/// @nodoc
class _$VehicleImageEventCopyWithImpl<$Res, $Val extends VehicleImageEvent>
    implements $VehicleImageEventCopyWith<$Res> {
  _$VehicleImageEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$LoadAllImageImplCopyWith<$Res> {
  factory _$$LoadAllImageImplCopyWith(
          _$LoadAllImageImpl value, $Res Function(_$LoadAllImageImpl) then) =
      __$$LoadAllImageImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Vehicle vehicleId, int time, bool delay});

  $VehicleCopyWith<$Res> get vehicleId;
}

/// @nodoc
class __$$LoadAllImageImplCopyWithImpl<$Res>
    extends _$VehicleImageEventCopyWithImpl<$Res, _$LoadAllImageImpl>
    implements _$$LoadAllImageImplCopyWith<$Res> {
  __$$LoadAllImageImplCopyWithImpl(
      _$LoadAllImageImpl _value, $Res Function(_$LoadAllImageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? time = null,
    Object? delay = null,
  }) {
    return _then(_$LoadAllImageImpl(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as Vehicle,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      delay: null == delay
          ? _value.delay
          : delay // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleCopyWith<$Res> get vehicleId {
    return $VehicleCopyWith<$Res>(_value.vehicleId, (value) {
      return _then(_value.copyWith(vehicleId: value));
    });
  }
}

/// @nodoc

class _$LoadAllImageImpl implements _LoadAllImage {
  const _$LoadAllImageImpl(
      {required this.vehicleId, required this.time, required this.delay});

  @override
  final Vehicle vehicleId;
  @override
  final int time;
  @override
  final bool delay;

  @override
  String toString() {
    return 'VehicleImageEvent.loadAllImage(vehicleId: $vehicleId, time: $time, delay: $delay)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadAllImageImpl &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.time, time) || other.time == time) &&
            (identical(other.delay, delay) || other.delay == delay));
  }

  @override
  int get hashCode => Object.hash(runtimeType, vehicleId, time, delay);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadAllImageImplCopyWith<_$LoadAllImageImpl> get copyWith =>
      __$$LoadAllImageImplCopyWithImpl<_$LoadAllImageImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Vehicle vehicleId, int time, bool delay)
        loadAllImage,
    required TResult Function(
            List<VehicleImage> listVehicleImage, Vehicle vehicle)
        loadImageSuccess,
    required TResult Function(VehicleImageFailure failure) getImageFail,
    required TResult Function() takeImageSuccess,
    required TResult Function(VehicleImageFailure failure) takeImageFail,
    required TResult Function(String id) takeImage,
    required TResult Function() clearAllImage,
  }) {
    return loadAllImage(vehicleId, time, delay);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult? Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult? Function(VehicleImageFailure failure)? getImageFail,
    TResult? Function()? takeImageSuccess,
    TResult? Function(VehicleImageFailure failure)? takeImageFail,
    TResult? Function(String id)? takeImage,
    TResult? Function()? clearAllImage,
  }) {
    return loadAllImage?.call(vehicleId, time, delay);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult Function(VehicleImageFailure failure)? getImageFail,
    TResult Function()? takeImageSuccess,
    TResult Function(VehicleImageFailure failure)? takeImageFail,
    TResult Function(String id)? takeImage,
    TResult Function()? clearAllImage,
    required TResult orElse(),
  }) {
    if (loadAllImage != null) {
      return loadAllImage(vehicleId, time, delay);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllImage value) loadAllImage,
    required TResult Function(_LoadImageSuccess value) loadImageSuccess,
    required TResult Function(_GetImageFail value) getImageFail,
    required TResult Function(_TakeImageSuccess value) takeImageSuccess,
    required TResult Function(_TakeImageFail value) takeImageFail,
    required TResult Function(_TakeImage value) takeImage,
    required TResult Function(_ClearAllImage value) clearAllImage,
  }) {
    return loadAllImage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllImage value)? loadAllImage,
    TResult? Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult? Function(_GetImageFail value)? getImageFail,
    TResult? Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult? Function(_TakeImageFail value)? takeImageFail,
    TResult? Function(_TakeImage value)? takeImage,
    TResult? Function(_ClearAllImage value)? clearAllImage,
  }) {
    return loadAllImage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllImage value)? loadAllImage,
    TResult Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult Function(_GetImageFail value)? getImageFail,
    TResult Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult Function(_TakeImageFail value)? takeImageFail,
    TResult Function(_TakeImage value)? takeImage,
    TResult Function(_ClearAllImage value)? clearAllImage,
    required TResult orElse(),
  }) {
    if (loadAllImage != null) {
      return loadAllImage(this);
    }
    return orElse();
  }
}

abstract class _LoadAllImage implements VehicleImageEvent {
  const factory _LoadAllImage(
      {required final Vehicle vehicleId,
      required final int time,
      required final bool delay}) = _$LoadAllImageImpl;

  Vehicle get vehicleId;
  int get time;
  bool get delay;
  @JsonKey(ignore: true)
  _$$LoadAllImageImplCopyWith<_$LoadAllImageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadImageSuccessImplCopyWith<$Res> {
  factory _$$LoadImageSuccessImplCopyWith(_$LoadImageSuccessImpl value,
          $Res Function(_$LoadImageSuccessImpl) then) =
      __$$LoadImageSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<VehicleImage> listVehicleImage, Vehicle vehicle});

  $VehicleCopyWith<$Res> get vehicle;
}

/// @nodoc
class __$$LoadImageSuccessImplCopyWithImpl<$Res>
    extends _$VehicleImageEventCopyWithImpl<$Res, _$LoadImageSuccessImpl>
    implements _$$LoadImageSuccessImplCopyWith<$Res> {
  __$$LoadImageSuccessImplCopyWithImpl(_$LoadImageSuccessImpl _value,
      $Res Function(_$LoadImageSuccessImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicleImage = null,
    Object? vehicle = null,
  }) {
    return _then(_$LoadImageSuccessImpl(
      listVehicleImage: null == listVehicleImage
          ? _value._listVehicleImage
          : listVehicleImage // ignore: cast_nullable_to_non_nullable
              as List<VehicleImage>,
      vehicle: null == vehicle
          ? _value.vehicle
          : vehicle // ignore: cast_nullable_to_non_nullable
              as Vehicle,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleCopyWith<$Res> get vehicle {
    return $VehicleCopyWith<$Res>(_value.vehicle, (value) {
      return _then(_value.copyWith(vehicle: value));
    });
  }
}

/// @nodoc

class _$LoadImageSuccessImpl implements _LoadImageSuccess {
  const _$LoadImageSuccessImpl(
      {required final List<VehicleImage> listVehicleImage,
      required this.vehicle})
      : _listVehicleImage = listVehicleImage;

  final List<VehicleImage> _listVehicleImage;
  @override
  List<VehicleImage> get listVehicleImage {
    if (_listVehicleImage is EqualUnmodifiableListView)
      return _listVehicleImage;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicleImage);
  }

  @override
  final Vehicle vehicle;

  @override
  String toString() {
    return 'VehicleImageEvent.loadImageSuccess(listVehicleImage: $listVehicleImage, vehicle: $vehicle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadImageSuccessImpl &&
            const DeepCollectionEquality()
                .equals(other._listVehicleImage, _listVehicleImage) &&
            (identical(other.vehicle, vehicle) || other.vehicle == vehicle));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_listVehicleImage), vehicle);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadImageSuccessImplCopyWith<_$LoadImageSuccessImpl> get copyWith =>
      __$$LoadImageSuccessImplCopyWithImpl<_$LoadImageSuccessImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Vehicle vehicleId, int time, bool delay)
        loadAllImage,
    required TResult Function(
            List<VehicleImage> listVehicleImage, Vehicle vehicle)
        loadImageSuccess,
    required TResult Function(VehicleImageFailure failure) getImageFail,
    required TResult Function() takeImageSuccess,
    required TResult Function(VehicleImageFailure failure) takeImageFail,
    required TResult Function(String id) takeImage,
    required TResult Function() clearAllImage,
  }) {
    return loadImageSuccess(listVehicleImage, vehicle);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult? Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult? Function(VehicleImageFailure failure)? getImageFail,
    TResult? Function()? takeImageSuccess,
    TResult? Function(VehicleImageFailure failure)? takeImageFail,
    TResult? Function(String id)? takeImage,
    TResult? Function()? clearAllImage,
  }) {
    return loadImageSuccess?.call(listVehicleImage, vehicle);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult Function(VehicleImageFailure failure)? getImageFail,
    TResult Function()? takeImageSuccess,
    TResult Function(VehicleImageFailure failure)? takeImageFail,
    TResult Function(String id)? takeImage,
    TResult Function()? clearAllImage,
    required TResult orElse(),
  }) {
    if (loadImageSuccess != null) {
      return loadImageSuccess(listVehicleImage, vehicle);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllImage value) loadAllImage,
    required TResult Function(_LoadImageSuccess value) loadImageSuccess,
    required TResult Function(_GetImageFail value) getImageFail,
    required TResult Function(_TakeImageSuccess value) takeImageSuccess,
    required TResult Function(_TakeImageFail value) takeImageFail,
    required TResult Function(_TakeImage value) takeImage,
    required TResult Function(_ClearAllImage value) clearAllImage,
  }) {
    return loadImageSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllImage value)? loadAllImage,
    TResult? Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult? Function(_GetImageFail value)? getImageFail,
    TResult? Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult? Function(_TakeImageFail value)? takeImageFail,
    TResult? Function(_TakeImage value)? takeImage,
    TResult? Function(_ClearAllImage value)? clearAllImage,
  }) {
    return loadImageSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllImage value)? loadAllImage,
    TResult Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult Function(_GetImageFail value)? getImageFail,
    TResult Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult Function(_TakeImageFail value)? takeImageFail,
    TResult Function(_TakeImage value)? takeImage,
    TResult Function(_ClearAllImage value)? clearAllImage,
    required TResult orElse(),
  }) {
    if (loadImageSuccess != null) {
      return loadImageSuccess(this);
    }
    return orElse();
  }
}

abstract class _LoadImageSuccess implements VehicleImageEvent {
  const factory _LoadImageSuccess(
      {required final List<VehicleImage> listVehicleImage,
      required final Vehicle vehicle}) = _$LoadImageSuccessImpl;

  List<VehicleImage> get listVehicleImage;
  Vehicle get vehicle;
  @JsonKey(ignore: true)
  _$$LoadImageSuccessImplCopyWith<_$LoadImageSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetImageFailImplCopyWith<$Res> {
  factory _$$GetImageFailImplCopyWith(
          _$GetImageFailImpl value, $Res Function(_$GetImageFailImpl) then) =
      __$$GetImageFailImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VehicleImageFailure failure});

  $VehicleImageFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$GetImageFailImplCopyWithImpl<$Res>
    extends _$VehicleImageEventCopyWithImpl<$Res, _$GetImageFailImpl>
    implements _$$GetImageFailImplCopyWith<$Res> {
  __$$GetImageFailImplCopyWithImpl(
      _$GetImageFailImpl _value, $Res Function(_$GetImageFailImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$GetImageFailImpl(
      failure: null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as VehicleImageFailure,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleImageFailureCopyWith<$Res> get failure {
    return $VehicleImageFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$GetImageFailImpl implements _GetImageFail {
  const _$GetImageFailImpl({required this.failure});

  @override
  final VehicleImageFailure failure;

  @override
  String toString() {
    return 'VehicleImageEvent.getImageFail(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetImageFailImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetImageFailImplCopyWith<_$GetImageFailImpl> get copyWith =>
      __$$GetImageFailImplCopyWithImpl<_$GetImageFailImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Vehicle vehicleId, int time, bool delay)
        loadAllImage,
    required TResult Function(
            List<VehicleImage> listVehicleImage, Vehicle vehicle)
        loadImageSuccess,
    required TResult Function(VehicleImageFailure failure) getImageFail,
    required TResult Function() takeImageSuccess,
    required TResult Function(VehicleImageFailure failure) takeImageFail,
    required TResult Function(String id) takeImage,
    required TResult Function() clearAllImage,
  }) {
    return getImageFail(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult? Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult? Function(VehicleImageFailure failure)? getImageFail,
    TResult? Function()? takeImageSuccess,
    TResult? Function(VehicleImageFailure failure)? takeImageFail,
    TResult? Function(String id)? takeImage,
    TResult? Function()? clearAllImage,
  }) {
    return getImageFail?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult Function(VehicleImageFailure failure)? getImageFail,
    TResult Function()? takeImageSuccess,
    TResult Function(VehicleImageFailure failure)? takeImageFail,
    TResult Function(String id)? takeImage,
    TResult Function()? clearAllImage,
    required TResult orElse(),
  }) {
    if (getImageFail != null) {
      return getImageFail(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllImage value) loadAllImage,
    required TResult Function(_LoadImageSuccess value) loadImageSuccess,
    required TResult Function(_GetImageFail value) getImageFail,
    required TResult Function(_TakeImageSuccess value) takeImageSuccess,
    required TResult Function(_TakeImageFail value) takeImageFail,
    required TResult Function(_TakeImage value) takeImage,
    required TResult Function(_ClearAllImage value) clearAllImage,
  }) {
    return getImageFail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllImage value)? loadAllImage,
    TResult? Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult? Function(_GetImageFail value)? getImageFail,
    TResult? Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult? Function(_TakeImageFail value)? takeImageFail,
    TResult? Function(_TakeImage value)? takeImage,
    TResult? Function(_ClearAllImage value)? clearAllImage,
  }) {
    return getImageFail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllImage value)? loadAllImage,
    TResult Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult Function(_GetImageFail value)? getImageFail,
    TResult Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult Function(_TakeImageFail value)? takeImageFail,
    TResult Function(_TakeImage value)? takeImage,
    TResult Function(_ClearAllImage value)? clearAllImage,
    required TResult orElse(),
  }) {
    if (getImageFail != null) {
      return getImageFail(this);
    }
    return orElse();
  }
}

abstract class _GetImageFail implements VehicleImageEvent {
  const factory _GetImageFail({required final VehicleImageFailure failure}) =
      _$GetImageFailImpl;

  VehicleImageFailure get failure;
  @JsonKey(ignore: true)
  _$$GetImageFailImplCopyWith<_$GetImageFailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TakeImageSuccessImplCopyWith<$Res> {
  factory _$$TakeImageSuccessImplCopyWith(_$TakeImageSuccessImpl value,
          $Res Function(_$TakeImageSuccessImpl) then) =
      __$$TakeImageSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TakeImageSuccessImplCopyWithImpl<$Res>
    extends _$VehicleImageEventCopyWithImpl<$Res, _$TakeImageSuccessImpl>
    implements _$$TakeImageSuccessImplCopyWith<$Res> {
  __$$TakeImageSuccessImplCopyWithImpl(_$TakeImageSuccessImpl _value,
      $Res Function(_$TakeImageSuccessImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$TakeImageSuccessImpl implements _TakeImageSuccess {
  const _$TakeImageSuccessImpl();

  @override
  String toString() {
    return 'VehicleImageEvent.takeImageSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$TakeImageSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Vehicle vehicleId, int time, bool delay)
        loadAllImage,
    required TResult Function(
            List<VehicleImage> listVehicleImage, Vehicle vehicle)
        loadImageSuccess,
    required TResult Function(VehicleImageFailure failure) getImageFail,
    required TResult Function() takeImageSuccess,
    required TResult Function(VehicleImageFailure failure) takeImageFail,
    required TResult Function(String id) takeImage,
    required TResult Function() clearAllImage,
  }) {
    return takeImageSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult? Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult? Function(VehicleImageFailure failure)? getImageFail,
    TResult? Function()? takeImageSuccess,
    TResult? Function(VehicleImageFailure failure)? takeImageFail,
    TResult? Function(String id)? takeImage,
    TResult? Function()? clearAllImage,
  }) {
    return takeImageSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult Function(VehicleImageFailure failure)? getImageFail,
    TResult Function()? takeImageSuccess,
    TResult Function(VehicleImageFailure failure)? takeImageFail,
    TResult Function(String id)? takeImage,
    TResult Function()? clearAllImage,
    required TResult orElse(),
  }) {
    if (takeImageSuccess != null) {
      return takeImageSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllImage value) loadAllImage,
    required TResult Function(_LoadImageSuccess value) loadImageSuccess,
    required TResult Function(_GetImageFail value) getImageFail,
    required TResult Function(_TakeImageSuccess value) takeImageSuccess,
    required TResult Function(_TakeImageFail value) takeImageFail,
    required TResult Function(_TakeImage value) takeImage,
    required TResult Function(_ClearAllImage value) clearAllImage,
  }) {
    return takeImageSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllImage value)? loadAllImage,
    TResult? Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult? Function(_GetImageFail value)? getImageFail,
    TResult? Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult? Function(_TakeImageFail value)? takeImageFail,
    TResult? Function(_TakeImage value)? takeImage,
    TResult? Function(_ClearAllImage value)? clearAllImage,
  }) {
    return takeImageSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllImage value)? loadAllImage,
    TResult Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult Function(_GetImageFail value)? getImageFail,
    TResult Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult Function(_TakeImageFail value)? takeImageFail,
    TResult Function(_TakeImage value)? takeImage,
    TResult Function(_ClearAllImage value)? clearAllImage,
    required TResult orElse(),
  }) {
    if (takeImageSuccess != null) {
      return takeImageSuccess(this);
    }
    return orElse();
  }
}

abstract class _TakeImageSuccess implements VehicleImageEvent {
  const factory _TakeImageSuccess() = _$TakeImageSuccessImpl;
}

/// @nodoc
abstract class _$$TakeImageFailImplCopyWith<$Res> {
  factory _$$TakeImageFailImplCopyWith(
          _$TakeImageFailImpl value, $Res Function(_$TakeImageFailImpl) then) =
      __$$TakeImageFailImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VehicleImageFailure failure});

  $VehicleImageFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$TakeImageFailImplCopyWithImpl<$Res>
    extends _$VehicleImageEventCopyWithImpl<$Res, _$TakeImageFailImpl>
    implements _$$TakeImageFailImplCopyWith<$Res> {
  __$$TakeImageFailImplCopyWithImpl(
      _$TakeImageFailImpl _value, $Res Function(_$TakeImageFailImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$TakeImageFailImpl(
      failure: null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as VehicleImageFailure,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleImageFailureCopyWith<$Res> get failure {
    return $VehicleImageFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$TakeImageFailImpl implements _TakeImageFail {
  const _$TakeImageFailImpl({required this.failure});

  @override
  final VehicleImageFailure failure;

  @override
  String toString() {
    return 'VehicleImageEvent.takeImageFail(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TakeImageFailImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TakeImageFailImplCopyWith<_$TakeImageFailImpl> get copyWith =>
      __$$TakeImageFailImplCopyWithImpl<_$TakeImageFailImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Vehicle vehicleId, int time, bool delay)
        loadAllImage,
    required TResult Function(
            List<VehicleImage> listVehicleImage, Vehicle vehicle)
        loadImageSuccess,
    required TResult Function(VehicleImageFailure failure) getImageFail,
    required TResult Function() takeImageSuccess,
    required TResult Function(VehicleImageFailure failure) takeImageFail,
    required TResult Function(String id) takeImage,
    required TResult Function() clearAllImage,
  }) {
    return takeImageFail(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult? Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult? Function(VehicleImageFailure failure)? getImageFail,
    TResult? Function()? takeImageSuccess,
    TResult? Function(VehicleImageFailure failure)? takeImageFail,
    TResult? Function(String id)? takeImage,
    TResult? Function()? clearAllImage,
  }) {
    return takeImageFail?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult Function(VehicleImageFailure failure)? getImageFail,
    TResult Function()? takeImageSuccess,
    TResult Function(VehicleImageFailure failure)? takeImageFail,
    TResult Function(String id)? takeImage,
    TResult Function()? clearAllImage,
    required TResult orElse(),
  }) {
    if (takeImageFail != null) {
      return takeImageFail(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllImage value) loadAllImage,
    required TResult Function(_LoadImageSuccess value) loadImageSuccess,
    required TResult Function(_GetImageFail value) getImageFail,
    required TResult Function(_TakeImageSuccess value) takeImageSuccess,
    required TResult Function(_TakeImageFail value) takeImageFail,
    required TResult Function(_TakeImage value) takeImage,
    required TResult Function(_ClearAllImage value) clearAllImage,
  }) {
    return takeImageFail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllImage value)? loadAllImage,
    TResult? Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult? Function(_GetImageFail value)? getImageFail,
    TResult? Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult? Function(_TakeImageFail value)? takeImageFail,
    TResult? Function(_TakeImage value)? takeImage,
    TResult? Function(_ClearAllImage value)? clearAllImage,
  }) {
    return takeImageFail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllImage value)? loadAllImage,
    TResult Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult Function(_GetImageFail value)? getImageFail,
    TResult Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult Function(_TakeImageFail value)? takeImageFail,
    TResult Function(_TakeImage value)? takeImage,
    TResult Function(_ClearAllImage value)? clearAllImage,
    required TResult orElse(),
  }) {
    if (takeImageFail != null) {
      return takeImageFail(this);
    }
    return orElse();
  }
}

abstract class _TakeImageFail implements VehicleImageEvent {
  const factory _TakeImageFail({required final VehicleImageFailure failure}) =
      _$TakeImageFailImpl;

  VehicleImageFailure get failure;
  @JsonKey(ignore: true)
  _$$TakeImageFailImplCopyWith<_$TakeImageFailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TakeImageImplCopyWith<$Res> {
  factory _$$TakeImageImplCopyWith(
          _$TakeImageImpl value, $Res Function(_$TakeImageImpl) then) =
      __$$TakeImageImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$TakeImageImplCopyWithImpl<$Res>
    extends _$VehicleImageEventCopyWithImpl<$Res, _$TakeImageImpl>
    implements _$$TakeImageImplCopyWith<$Res> {
  __$$TakeImageImplCopyWithImpl(
      _$TakeImageImpl _value, $Res Function(_$TakeImageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$TakeImageImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$TakeImageImpl implements _TakeImage {
  const _$TakeImageImpl({required this.id});

  @override
  final String id;

  @override
  String toString() {
    return 'VehicleImageEvent.takeImage(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TakeImageImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TakeImageImplCopyWith<_$TakeImageImpl> get copyWith =>
      __$$TakeImageImplCopyWithImpl<_$TakeImageImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Vehicle vehicleId, int time, bool delay)
        loadAllImage,
    required TResult Function(
            List<VehicleImage> listVehicleImage, Vehicle vehicle)
        loadImageSuccess,
    required TResult Function(VehicleImageFailure failure) getImageFail,
    required TResult Function() takeImageSuccess,
    required TResult Function(VehicleImageFailure failure) takeImageFail,
    required TResult Function(String id) takeImage,
    required TResult Function() clearAllImage,
  }) {
    return takeImage(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult? Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult? Function(VehicleImageFailure failure)? getImageFail,
    TResult? Function()? takeImageSuccess,
    TResult? Function(VehicleImageFailure failure)? takeImageFail,
    TResult? Function(String id)? takeImage,
    TResult? Function()? clearAllImage,
  }) {
    return takeImage?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult Function(VehicleImageFailure failure)? getImageFail,
    TResult Function()? takeImageSuccess,
    TResult Function(VehicleImageFailure failure)? takeImageFail,
    TResult Function(String id)? takeImage,
    TResult Function()? clearAllImage,
    required TResult orElse(),
  }) {
    if (takeImage != null) {
      return takeImage(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllImage value) loadAllImage,
    required TResult Function(_LoadImageSuccess value) loadImageSuccess,
    required TResult Function(_GetImageFail value) getImageFail,
    required TResult Function(_TakeImageSuccess value) takeImageSuccess,
    required TResult Function(_TakeImageFail value) takeImageFail,
    required TResult Function(_TakeImage value) takeImage,
    required TResult Function(_ClearAllImage value) clearAllImage,
  }) {
    return takeImage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllImage value)? loadAllImage,
    TResult? Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult? Function(_GetImageFail value)? getImageFail,
    TResult? Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult? Function(_TakeImageFail value)? takeImageFail,
    TResult? Function(_TakeImage value)? takeImage,
    TResult? Function(_ClearAllImage value)? clearAllImage,
  }) {
    return takeImage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllImage value)? loadAllImage,
    TResult Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult Function(_GetImageFail value)? getImageFail,
    TResult Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult Function(_TakeImageFail value)? takeImageFail,
    TResult Function(_TakeImage value)? takeImage,
    TResult Function(_ClearAllImage value)? clearAllImage,
    required TResult orElse(),
  }) {
    if (takeImage != null) {
      return takeImage(this);
    }
    return orElse();
  }
}

abstract class _TakeImage implements VehicleImageEvent {
  const factory _TakeImage({required final String id}) = _$TakeImageImpl;

  String get id;
  @JsonKey(ignore: true)
  _$$TakeImageImplCopyWith<_$TakeImageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearAllImageImplCopyWith<$Res> {
  factory _$$ClearAllImageImplCopyWith(
          _$ClearAllImageImpl value, $Res Function(_$ClearAllImageImpl) then) =
      __$$ClearAllImageImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearAllImageImplCopyWithImpl<$Res>
    extends _$VehicleImageEventCopyWithImpl<$Res, _$ClearAllImageImpl>
    implements _$$ClearAllImageImplCopyWith<$Res> {
  __$$ClearAllImageImplCopyWithImpl(
      _$ClearAllImageImpl _value, $Res Function(_$ClearAllImageImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearAllImageImpl implements _ClearAllImage {
  const _$ClearAllImageImpl();

  @override
  String toString() {
    return 'VehicleImageEvent.clearAllImage()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearAllImageImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Vehicle vehicleId, int time, bool delay)
        loadAllImage,
    required TResult Function(
            List<VehicleImage> listVehicleImage, Vehicle vehicle)
        loadImageSuccess,
    required TResult Function(VehicleImageFailure failure) getImageFail,
    required TResult Function() takeImageSuccess,
    required TResult Function(VehicleImageFailure failure) takeImageFail,
    required TResult Function(String id) takeImage,
    required TResult Function() clearAllImage,
  }) {
    return clearAllImage();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult? Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult? Function(VehicleImageFailure failure)? getImageFail,
    TResult? Function()? takeImageSuccess,
    TResult? Function(VehicleImageFailure failure)? takeImageFail,
    TResult? Function(String id)? takeImage,
    TResult? Function()? clearAllImage,
  }) {
    return clearAllImage?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Vehicle vehicleId, int time, bool delay)? loadAllImage,
    TResult Function(List<VehicleImage> listVehicleImage, Vehicle vehicle)?
        loadImageSuccess,
    TResult Function(VehicleImageFailure failure)? getImageFail,
    TResult Function()? takeImageSuccess,
    TResult Function(VehicleImageFailure failure)? takeImageFail,
    TResult Function(String id)? takeImage,
    TResult Function()? clearAllImage,
    required TResult orElse(),
  }) {
    if (clearAllImage != null) {
      return clearAllImage();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadAllImage value) loadAllImage,
    required TResult Function(_LoadImageSuccess value) loadImageSuccess,
    required TResult Function(_GetImageFail value) getImageFail,
    required TResult Function(_TakeImageSuccess value) takeImageSuccess,
    required TResult Function(_TakeImageFail value) takeImageFail,
    required TResult Function(_TakeImage value) takeImage,
    required TResult Function(_ClearAllImage value) clearAllImage,
  }) {
    return clearAllImage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadAllImage value)? loadAllImage,
    TResult? Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult? Function(_GetImageFail value)? getImageFail,
    TResult? Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult? Function(_TakeImageFail value)? takeImageFail,
    TResult? Function(_TakeImage value)? takeImage,
    TResult? Function(_ClearAllImage value)? clearAllImage,
  }) {
    return clearAllImage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadAllImage value)? loadAllImage,
    TResult Function(_LoadImageSuccess value)? loadImageSuccess,
    TResult Function(_GetImageFail value)? getImageFail,
    TResult Function(_TakeImageSuccess value)? takeImageSuccess,
    TResult Function(_TakeImageFail value)? takeImageFail,
    TResult Function(_TakeImage value)? takeImage,
    TResult Function(_ClearAllImage value)? clearAllImage,
    required TResult orElse(),
  }) {
    if (clearAllImage != null) {
      return clearAllImage(this);
    }
    return orElse();
  }
}

abstract class _ClearAllImage implements VehicleImageEvent {
  const factory _ClearAllImage() = _$ClearAllImageImpl;
}

/// @nodoc
mixin _$VehicleImageState {
  List<VehicleImage> get listVehicleImage => throw _privateConstructorUsedError;
  bool get isGettingImage => throw _privateConstructorUsedError;
  bool get isTakingImage => throw _privateConstructorUsedError;
  bool get isTakeImageSuccess => throw _privateConstructorUsedError;
  VehicleImageFailure? get failureGetAllImage =>
      throw _privateConstructorUsedError;
  VehicleImageFailure? get failureTakeImage =>
      throw _privateConstructorUsedError;
  List<VehicleImage> get listCacheImage => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $VehicleImageStateCopyWith<VehicleImageState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleImageStateCopyWith<$Res> {
  factory $VehicleImageStateCopyWith(
          VehicleImageState value, $Res Function(VehicleImageState) then) =
      _$VehicleImageStateCopyWithImpl<$Res, VehicleImageState>;
  @useResult
  $Res call(
      {List<VehicleImage> listVehicleImage,
      bool isGettingImage,
      bool isTakingImage,
      bool isTakeImageSuccess,
      VehicleImageFailure? failureGetAllImage,
      VehicleImageFailure? failureTakeImage,
      List<VehicleImage> listCacheImage});

  $VehicleImageFailureCopyWith<$Res>? get failureGetAllImage;
  $VehicleImageFailureCopyWith<$Res>? get failureTakeImage;
}

/// @nodoc
class _$VehicleImageStateCopyWithImpl<$Res, $Val extends VehicleImageState>
    implements $VehicleImageStateCopyWith<$Res> {
  _$VehicleImageStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicleImage = null,
    Object? isGettingImage = null,
    Object? isTakingImage = null,
    Object? isTakeImageSuccess = null,
    Object? failureGetAllImage = freezed,
    Object? failureTakeImage = freezed,
    Object? listCacheImage = null,
  }) {
    return _then(_value.copyWith(
      listVehicleImage: null == listVehicleImage
          ? _value.listVehicleImage
          : listVehicleImage // ignore: cast_nullable_to_non_nullable
              as List<VehicleImage>,
      isGettingImage: null == isGettingImage
          ? _value.isGettingImage
          : isGettingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      isTakingImage: null == isTakingImage
          ? _value.isTakingImage
          : isTakingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      isTakeImageSuccess: null == isTakeImageSuccess
          ? _value.isTakeImageSuccess
          : isTakeImageSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      failureGetAllImage: freezed == failureGetAllImage
          ? _value.failureGetAllImage
          : failureGetAllImage // ignore: cast_nullable_to_non_nullable
              as VehicleImageFailure?,
      failureTakeImage: freezed == failureTakeImage
          ? _value.failureTakeImage
          : failureTakeImage // ignore: cast_nullable_to_non_nullable
              as VehicleImageFailure?,
      listCacheImage: null == listCacheImage
          ? _value.listCacheImage
          : listCacheImage // ignore: cast_nullable_to_non_nullable
              as List<VehicleImage>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleImageFailureCopyWith<$Res>? get failureGetAllImage {
    if (_value.failureGetAllImage == null) {
      return null;
    }

    return $VehicleImageFailureCopyWith<$Res>(_value.failureGetAllImage!,
        (value) {
      return _then(_value.copyWith(failureGetAllImage: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleImageFailureCopyWith<$Res>? get failureTakeImage {
    if (_value.failureTakeImage == null) {
      return null;
    }

    return $VehicleImageFailureCopyWith<$Res>(_value.failureTakeImage!,
        (value) {
      return _then(_value.copyWith(failureTakeImage: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VehicleImageStateImplCopyWith<$Res>
    implements $VehicleImageStateCopyWith<$Res> {
  factory _$$VehicleImageStateImplCopyWith(_$VehicleImageStateImpl value,
          $Res Function(_$VehicleImageStateImpl) then) =
      __$$VehicleImageStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<VehicleImage> listVehicleImage,
      bool isGettingImage,
      bool isTakingImage,
      bool isTakeImageSuccess,
      VehicleImageFailure? failureGetAllImage,
      VehicleImageFailure? failureTakeImage,
      List<VehicleImage> listCacheImage});

  @override
  $VehicleImageFailureCopyWith<$Res>? get failureGetAllImage;
  @override
  $VehicleImageFailureCopyWith<$Res>? get failureTakeImage;
}

/// @nodoc
class __$$VehicleImageStateImplCopyWithImpl<$Res>
    extends _$VehicleImageStateCopyWithImpl<$Res, _$VehicleImageStateImpl>
    implements _$$VehicleImageStateImplCopyWith<$Res> {
  __$$VehicleImageStateImplCopyWithImpl(_$VehicleImageStateImpl _value,
      $Res Function(_$VehicleImageStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicleImage = null,
    Object? isGettingImage = null,
    Object? isTakingImage = null,
    Object? isTakeImageSuccess = null,
    Object? failureGetAllImage = freezed,
    Object? failureTakeImage = freezed,
    Object? listCacheImage = null,
  }) {
    return _then(_$VehicleImageStateImpl(
      listVehicleImage: null == listVehicleImage
          ? _value._listVehicleImage
          : listVehicleImage // ignore: cast_nullable_to_non_nullable
              as List<VehicleImage>,
      isGettingImage: null == isGettingImage
          ? _value.isGettingImage
          : isGettingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      isTakingImage: null == isTakingImage
          ? _value.isTakingImage
          : isTakingImage // ignore: cast_nullable_to_non_nullable
              as bool,
      isTakeImageSuccess: null == isTakeImageSuccess
          ? _value.isTakeImageSuccess
          : isTakeImageSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      failureGetAllImage: freezed == failureGetAllImage
          ? _value.failureGetAllImage
          : failureGetAllImage // ignore: cast_nullable_to_non_nullable
              as VehicleImageFailure?,
      failureTakeImage: freezed == failureTakeImage
          ? _value.failureTakeImage
          : failureTakeImage // ignore: cast_nullable_to_non_nullable
              as VehicleImageFailure?,
      listCacheImage: null == listCacheImage
          ? _value._listCacheImage
          : listCacheImage // ignore: cast_nullable_to_non_nullable
              as List<VehicleImage>,
    ));
  }
}

/// @nodoc

class _$VehicleImageStateImpl implements _VehicleImageState {
  const _$VehicleImageStateImpl(
      {required final List<VehicleImage> listVehicleImage,
      required this.isGettingImage,
      required this.isTakingImage,
      required this.isTakeImageSuccess,
      required this.failureGetAllImage,
      required this.failureTakeImage,
      required final List<VehicleImage> listCacheImage})
      : _listVehicleImage = listVehicleImage,
        _listCacheImage = listCacheImage;

  final List<VehicleImage> _listVehicleImage;
  @override
  List<VehicleImage> get listVehicleImage {
    if (_listVehicleImage is EqualUnmodifiableListView)
      return _listVehicleImage;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicleImage);
  }

  @override
  final bool isGettingImage;
  @override
  final bool isTakingImage;
  @override
  final bool isTakeImageSuccess;
  @override
  final VehicleImageFailure? failureGetAllImage;
  @override
  final VehicleImageFailure? failureTakeImage;
  final List<VehicleImage> _listCacheImage;
  @override
  List<VehicleImage> get listCacheImage {
    if (_listCacheImage is EqualUnmodifiableListView) return _listCacheImage;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listCacheImage);
  }

  @override
  String toString() {
    return 'VehicleImageState(listVehicleImage: $listVehicleImage, isGettingImage: $isGettingImage, isTakingImage: $isTakingImage, isTakeImageSuccess: $isTakeImageSuccess, failureGetAllImage: $failureGetAllImage, failureTakeImage: $failureTakeImage, listCacheImage: $listCacheImage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImageStateImpl &&
            const DeepCollectionEquality()
                .equals(other._listVehicleImage, _listVehicleImage) &&
            (identical(other.isGettingImage, isGettingImage) ||
                other.isGettingImage == isGettingImage) &&
            (identical(other.isTakingImage, isTakingImage) ||
                other.isTakingImage == isTakingImage) &&
            (identical(other.isTakeImageSuccess, isTakeImageSuccess) ||
                other.isTakeImageSuccess == isTakeImageSuccess) &&
            (identical(other.failureGetAllImage, failureGetAllImage) ||
                other.failureGetAllImage == failureGetAllImage) &&
            (identical(other.failureTakeImage, failureTakeImage) ||
                other.failureTakeImage == failureTakeImage) &&
            const DeepCollectionEquality()
                .equals(other._listCacheImage, _listCacheImage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_listVehicleImage),
      isGettingImage,
      isTakingImage,
      isTakeImageSuccess,
      failureGetAllImage,
      failureTakeImage,
      const DeepCollectionEquality().hash(_listCacheImage));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleImageStateImplCopyWith<_$VehicleImageStateImpl> get copyWith =>
      __$$VehicleImageStateImplCopyWithImpl<_$VehicleImageStateImpl>(
          this, _$identity);
}

abstract class _VehicleImageState implements VehicleImageState {
  const factory _VehicleImageState(
          {required final List<VehicleImage> listVehicleImage,
          required final bool isGettingImage,
          required final bool isTakingImage,
          required final bool isTakeImageSuccess,
          required final VehicleImageFailure? failureGetAllImage,
          required final VehicleImageFailure? failureTakeImage,
          required final List<VehicleImage> listCacheImage}) =
      _$VehicleImageStateImpl;

  @override
  List<VehicleImage> get listVehicleImage;
  @override
  bool get isGettingImage;
  @override
  bool get isTakingImage;
  @override
  bool get isTakeImageSuccess;
  @override
  VehicleImageFailure? get failureGetAllImage;
  @override
  VehicleImageFailure? get failureTakeImage;
  @override
  List<VehicleImage> get listCacheImage;
  @override
  @JsonKey(ignore: true)
  _$$VehicleImageStateImplCopyWith<_$VehicleImageStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
