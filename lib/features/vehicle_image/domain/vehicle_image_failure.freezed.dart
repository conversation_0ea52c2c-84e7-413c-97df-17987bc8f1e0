// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_image_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$VehicleImageFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_VehicleImageUnexpected value) unexpected,
    required TResult Function(_VehicleImageUnauthorized value) unauthorized,
    required TResult Function(_VehicleImageUnauthenticated value)
        unauthenticated,
    required TResult Function(_VehicleImageServerError value) serverError,
    required TResult Function(_VehicleImageEmptyRoute value) emptyRoute,
    required TResult Function(_VehicleImageFailureNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_VehicleImageUnexpected value)? unexpected,
    TResult? Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult? Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult? Function(_VehicleImageServerError value)? serverError,
    TResult? Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult? Function(_VehicleImageFailureNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_VehicleImageUnexpected value)? unexpected,
    TResult Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult Function(_VehicleImageServerError value)? serverError,
    TResult Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult Function(_VehicleImageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleImageFailureCopyWith<$Res> {
  factory $VehicleImageFailureCopyWith(
          VehicleImageFailure value, $Res Function(VehicleImageFailure) then) =
      _$VehicleImageFailureCopyWithImpl<$Res, VehicleImageFailure>;
}

/// @nodoc
class _$VehicleImageFailureCopyWithImpl<$Res, $Val extends VehicleImageFailure>
    implements $VehicleImageFailureCopyWith<$Res> {
  _$VehicleImageFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$VehicleImageUnexpectedImplCopyWith<$Res> {
  factory _$$VehicleImageUnexpectedImplCopyWith(
          _$VehicleImageUnexpectedImpl value,
          $Res Function(_$VehicleImageUnexpectedImpl) then) =
      __$$VehicleImageUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$VehicleImageUnexpectedImplCopyWithImpl<$Res>
    extends _$VehicleImageFailureCopyWithImpl<$Res,
        _$VehicleImageUnexpectedImpl>
    implements _$$VehicleImageUnexpectedImplCopyWith<$Res> {
  __$$VehicleImageUnexpectedImplCopyWithImpl(
      _$VehicleImageUnexpectedImpl _value,
      $Res Function(_$VehicleImageUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$VehicleImageUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$VehicleImageUnexpectedImpl implements _VehicleImageUnexpected {
  const _$VehicleImageUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'VehicleImageFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImageUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleImageUnexpectedImplCopyWith<_$VehicleImageUnexpectedImpl>
      get copyWith => __$$VehicleImageUnexpectedImplCopyWithImpl<
          _$VehicleImageUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_VehicleImageUnexpected value) unexpected,
    required TResult Function(_VehicleImageUnauthorized value) unauthorized,
    required TResult Function(_VehicleImageUnauthenticated value)
        unauthenticated,
    required TResult Function(_VehicleImageServerError value) serverError,
    required TResult Function(_VehicleImageEmptyRoute value) emptyRoute,
    required TResult Function(_VehicleImageFailureNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_VehicleImageUnexpected value)? unexpected,
    TResult? Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult? Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult? Function(_VehicleImageServerError value)? serverError,
    TResult? Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult? Function(_VehicleImageFailureNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_VehicleImageUnexpected value)? unexpected,
    TResult Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult Function(_VehicleImageServerError value)? serverError,
    TResult Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult Function(_VehicleImageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _VehicleImageUnexpected implements VehicleImageFailure {
  const factory _VehicleImageUnexpected({required final String error}) =
      _$VehicleImageUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$VehicleImageUnexpectedImplCopyWith<_$VehicleImageUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$VehicleImageUnauthorizedImplCopyWith<$Res> {
  factory _$$VehicleImageUnauthorizedImplCopyWith(
          _$VehicleImageUnauthorizedImpl value,
          $Res Function(_$VehicleImageUnauthorizedImpl) then) =
      __$$VehicleImageUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VehicleImageUnauthorizedImplCopyWithImpl<$Res>
    extends _$VehicleImageFailureCopyWithImpl<$Res,
        _$VehicleImageUnauthorizedImpl>
    implements _$$VehicleImageUnauthorizedImplCopyWith<$Res> {
  __$$VehicleImageUnauthorizedImplCopyWithImpl(
      _$VehicleImageUnauthorizedImpl _value,
      $Res Function(_$VehicleImageUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$VehicleImageUnauthorizedImpl implements _VehicleImageUnauthorized {
  const _$VehicleImageUnauthorizedImpl();

  @override
  String toString() {
    return 'VehicleImageFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImageUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_VehicleImageUnexpected value) unexpected,
    required TResult Function(_VehicleImageUnauthorized value) unauthorized,
    required TResult Function(_VehicleImageUnauthenticated value)
        unauthenticated,
    required TResult Function(_VehicleImageServerError value) serverError,
    required TResult Function(_VehicleImageEmptyRoute value) emptyRoute,
    required TResult Function(_VehicleImageFailureNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_VehicleImageUnexpected value)? unexpected,
    TResult? Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult? Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult? Function(_VehicleImageServerError value)? serverError,
    TResult? Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult? Function(_VehicleImageFailureNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_VehicleImageUnexpected value)? unexpected,
    TResult Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult Function(_VehicleImageServerError value)? serverError,
    TResult Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult Function(_VehicleImageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _VehicleImageUnauthorized implements VehicleImageFailure {
  const factory _VehicleImageUnauthorized() = _$VehicleImageUnauthorizedImpl;
}

/// @nodoc
abstract class _$$VehicleImageUnauthenticatedImplCopyWith<$Res> {
  factory _$$VehicleImageUnauthenticatedImplCopyWith(
          _$VehicleImageUnauthenticatedImpl value,
          $Res Function(_$VehicleImageUnauthenticatedImpl) then) =
      __$$VehicleImageUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VehicleImageUnauthenticatedImplCopyWithImpl<$Res>
    extends _$VehicleImageFailureCopyWithImpl<$Res,
        _$VehicleImageUnauthenticatedImpl>
    implements _$$VehicleImageUnauthenticatedImplCopyWith<$Res> {
  __$$VehicleImageUnauthenticatedImplCopyWithImpl(
      _$VehicleImageUnauthenticatedImpl _value,
      $Res Function(_$VehicleImageUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$VehicleImageUnauthenticatedImpl
    implements _VehicleImageUnauthenticated {
  const _$VehicleImageUnauthenticatedImpl();

  @override
  String toString() {
    return 'VehicleImageFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImageUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_VehicleImageUnexpected value) unexpected,
    required TResult Function(_VehicleImageUnauthorized value) unauthorized,
    required TResult Function(_VehicleImageUnauthenticated value)
        unauthenticated,
    required TResult Function(_VehicleImageServerError value) serverError,
    required TResult Function(_VehicleImageEmptyRoute value) emptyRoute,
    required TResult Function(_VehicleImageFailureNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_VehicleImageUnexpected value)? unexpected,
    TResult? Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult? Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult? Function(_VehicleImageServerError value)? serverError,
    TResult? Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult? Function(_VehicleImageFailureNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_VehicleImageUnexpected value)? unexpected,
    TResult Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult Function(_VehicleImageServerError value)? serverError,
    TResult Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult Function(_VehicleImageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _VehicleImageUnauthenticated implements VehicleImageFailure {
  const factory _VehicleImageUnauthenticated() =
      _$VehicleImageUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$VehicleImageServerErrorImplCopyWith<$Res> {
  factory _$$VehicleImageServerErrorImplCopyWith(
          _$VehicleImageServerErrorImpl value,
          $Res Function(_$VehicleImageServerErrorImpl) then) =
      __$$VehicleImageServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VehicleImageServerErrorImplCopyWithImpl<$Res>
    extends _$VehicleImageFailureCopyWithImpl<$Res,
        _$VehicleImageServerErrorImpl>
    implements _$$VehicleImageServerErrorImplCopyWith<$Res> {
  __$$VehicleImageServerErrorImplCopyWithImpl(
      _$VehicleImageServerErrorImpl _value,
      $Res Function(_$VehicleImageServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$VehicleImageServerErrorImpl implements _VehicleImageServerError {
  const _$VehicleImageServerErrorImpl();

  @override
  String toString() {
    return 'VehicleImageFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImageServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_VehicleImageUnexpected value) unexpected,
    required TResult Function(_VehicleImageUnauthorized value) unauthorized,
    required TResult Function(_VehicleImageUnauthenticated value)
        unauthenticated,
    required TResult Function(_VehicleImageServerError value) serverError,
    required TResult Function(_VehicleImageEmptyRoute value) emptyRoute,
    required TResult Function(_VehicleImageFailureNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_VehicleImageUnexpected value)? unexpected,
    TResult? Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult? Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult? Function(_VehicleImageServerError value)? serverError,
    TResult? Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult? Function(_VehicleImageFailureNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_VehicleImageUnexpected value)? unexpected,
    TResult Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult Function(_VehicleImageServerError value)? serverError,
    TResult Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult Function(_VehicleImageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _VehicleImageServerError implements VehicleImageFailure {
  const factory _VehicleImageServerError() = _$VehicleImageServerErrorImpl;
}

/// @nodoc
abstract class _$$VehicleImageEmptyRouteImplCopyWith<$Res> {
  factory _$$VehicleImageEmptyRouteImplCopyWith(
          _$VehicleImageEmptyRouteImpl value,
          $Res Function(_$VehicleImageEmptyRouteImpl) then) =
      __$$VehicleImageEmptyRouteImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VehicleImageEmptyRouteImplCopyWithImpl<$Res>
    extends _$VehicleImageFailureCopyWithImpl<$Res,
        _$VehicleImageEmptyRouteImpl>
    implements _$$VehicleImageEmptyRouteImplCopyWith<$Res> {
  __$$VehicleImageEmptyRouteImplCopyWithImpl(
      _$VehicleImageEmptyRouteImpl _value,
      $Res Function(_$VehicleImageEmptyRouteImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$VehicleImageEmptyRouteImpl implements _VehicleImageEmptyRoute {
  const _$VehicleImageEmptyRouteImpl();

  @override
  String toString() {
    return 'VehicleImageFailure.emptyRoute()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImageEmptyRouteImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return emptyRoute();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return emptyRoute?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (emptyRoute != null) {
      return emptyRoute();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_VehicleImageUnexpected value) unexpected,
    required TResult Function(_VehicleImageUnauthorized value) unauthorized,
    required TResult Function(_VehicleImageUnauthenticated value)
        unauthenticated,
    required TResult Function(_VehicleImageServerError value) serverError,
    required TResult Function(_VehicleImageEmptyRoute value) emptyRoute,
    required TResult Function(_VehicleImageFailureNoInternet value) noInternet,
  }) {
    return emptyRoute(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_VehicleImageUnexpected value)? unexpected,
    TResult? Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult? Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult? Function(_VehicleImageServerError value)? serverError,
    TResult? Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult? Function(_VehicleImageFailureNoInternet value)? noInternet,
  }) {
    return emptyRoute?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_VehicleImageUnexpected value)? unexpected,
    TResult Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult Function(_VehicleImageServerError value)? serverError,
    TResult Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult Function(_VehicleImageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (emptyRoute != null) {
      return emptyRoute(this);
    }
    return orElse();
  }
}

abstract class _VehicleImageEmptyRoute implements VehicleImageFailure {
  const factory _VehicleImageEmptyRoute() = _$VehicleImageEmptyRouteImpl;
}

/// @nodoc
abstract class _$$VehicleImageFailureNoInternetImplCopyWith<$Res> {
  factory _$$VehicleImageFailureNoInternetImplCopyWith(
          _$VehicleImageFailureNoInternetImpl value,
          $Res Function(_$VehicleImageFailureNoInternetImpl) then) =
      __$$VehicleImageFailureNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VehicleImageFailureNoInternetImplCopyWithImpl<$Res>
    extends _$VehicleImageFailureCopyWithImpl<$Res,
        _$VehicleImageFailureNoInternetImpl>
    implements _$$VehicleImageFailureNoInternetImplCopyWith<$Res> {
  __$$VehicleImageFailureNoInternetImplCopyWithImpl(
      _$VehicleImageFailureNoInternetImpl _value,
      $Res Function(_$VehicleImageFailureNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$VehicleImageFailureNoInternetImpl
    implements _VehicleImageFailureNoInternet {
  const _$VehicleImageFailureNoInternetImpl();

  @override
  String toString() {
    return 'VehicleImageFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImageFailureNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_VehicleImageUnexpected value) unexpected,
    required TResult Function(_VehicleImageUnauthorized value) unauthorized,
    required TResult Function(_VehicleImageUnauthenticated value)
        unauthenticated,
    required TResult Function(_VehicleImageServerError value) serverError,
    required TResult Function(_VehicleImageEmptyRoute value) emptyRoute,
    required TResult Function(_VehicleImageFailureNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_VehicleImageUnexpected value)? unexpected,
    TResult? Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult? Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult? Function(_VehicleImageServerError value)? serverError,
    TResult? Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult? Function(_VehicleImageFailureNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_VehicleImageUnexpected value)? unexpected,
    TResult Function(_VehicleImageUnauthorized value)? unauthorized,
    TResult Function(_VehicleImageUnauthenticated value)? unauthenticated,
    TResult Function(_VehicleImageServerError value)? serverError,
    TResult Function(_VehicleImageEmptyRoute value)? emptyRoute,
    TResult Function(_VehicleImageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _VehicleImageFailureNoInternet implements VehicleImageFailure {
  const factory _VehicleImageFailureNoInternet() =
      _$VehicleImageFailureNoInternetImpl;
}
