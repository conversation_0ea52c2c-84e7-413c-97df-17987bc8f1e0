// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VehicleResponse _$VehicleResponseFromJson(Map<String, dynamic> json) {
  return _VehicleResponse.fromJson(json);
}

/// @nodoc
mixin _$VehicleResponse {
  String get id => throw _privateConstructorUsedError;
  String get plate => throw _privateConstructorUsedError;
  String get icon => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VehicleResponseCopyWith<VehicleResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleResponseCopyWith<$Res> {
  factory $VehicleResponseCopyWith(
          VehicleResponse value, $Res Function(VehicleResponse) then) =
      _$VehicleResponseCopyWithImpl<$Res, VehicleResponse>;
  @useResult
  $Res call({String id, String plate, String icon});
}

/// @nodoc
class _$VehicleResponseCopyWithImpl<$Res, $Val extends VehicleResponse>
    implements $VehicleResponseCopyWith<$Res> {
  _$VehicleResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? icon = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleResponseImplCopyWith<$Res>
    implements $VehicleResponseCopyWith<$Res> {
  factory _$$VehicleResponseImplCopyWith(_$VehicleResponseImpl value,
          $Res Function(_$VehicleResponseImpl) then) =
      __$$VehicleResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String plate, String icon});
}

/// @nodoc
class __$$VehicleResponseImplCopyWithImpl<$Res>
    extends _$VehicleResponseCopyWithImpl<$Res, _$VehicleResponseImpl>
    implements _$$VehicleResponseImplCopyWith<$Res> {
  __$$VehicleResponseImplCopyWithImpl(
      _$VehicleResponseImpl _value, $Res Function(_$VehicleResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? icon = null,
  }) {
    return _then(_$VehicleResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleResponseImpl extends _VehicleResponse {
  const _$VehicleResponseImpl(
      {required this.id, required this.plate, required this.icon})
      : super._();

  factory _$VehicleResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleResponseImplFromJson(json);

  @override
  final String id;
  @override
  final String plate;
  @override
  final String icon;

  @override
  String toString() {
    return 'VehicleResponse(id: $id, plate: $plate, icon: $icon)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.plate, plate) || other.plate == plate) &&
            (identical(other.icon, icon) || other.icon == icon));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, plate, icon);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleResponseImplCopyWith<_$VehicleResponseImpl> get copyWith =>
      __$$VehicleResponseImplCopyWithImpl<_$VehicleResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleResponseImplToJson(
      this,
    );
  }
}

abstract class _VehicleResponse extends VehicleResponse {
  const factory _VehicleResponse(
      {required final String id,
      required final String plate,
      required final String icon}) = _$VehicleResponseImpl;
  const _VehicleResponse._() : super._();

  factory _VehicleResponse.fromJson(Map<String, dynamic> json) =
      _$VehicleResponseImpl.fromJson;

  @override
  String get id;
  @override
  String get plate;
  @override
  String get icon;
  @override
  @JsonKey(ignore: true)
  _$$VehicleResponseImplCopyWith<_$VehicleResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
