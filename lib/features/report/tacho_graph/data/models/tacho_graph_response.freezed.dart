// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tacho_graph_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TachoGraphResponse _$TachoGraphResponseFromJson(Map<String, dynamic> json) {
  return _TachoGraphResponse.fromJson(json);
}

/// @nodoc
mixin _$TachoGraphResponse {
  bool get success => throw _privateConstructorUsedError;
  int get status => throw _privateConstructorUsedError;
  String get token => throw _privateConstructorUsedError;
  Detail? get detail => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TachoGraphResponseCopyWith<TachoGraphResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TachoGraphResponseCopyWith<$Res> {
  factory $TachoGraphResponseCopyWith(
          TachoGraphResponse value, $Res Function(TachoGraphResponse) then) =
      _$TachoGraphResponseCopyWithImpl<$Res, TachoGraphResponse>;
  @useResult
  $Res call({bool success, int status, String token, Detail? detail});

  $DetailCopyWith<$Res>? get detail;
}

/// @nodoc
class _$TachoGraphResponseCopyWithImpl<$Res, $Val extends TachoGraphResponse>
    implements $TachoGraphResponseCopyWith<$Res> {
  _$TachoGraphResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? status = null,
    Object? token = null,
    Object? detail = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      detail: freezed == detail
          ? _value.detail
          : detail // ignore: cast_nullable_to_non_nullable
              as Detail?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $DetailCopyWith<$Res>? get detail {
    if (_value.detail == null) {
      return null;
    }

    return $DetailCopyWith<$Res>(_value.detail!, (value) {
      return _then(_value.copyWith(detail: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TachoGraphResponseImplCopyWith<$Res>
    implements $TachoGraphResponseCopyWith<$Res> {
  factory _$$TachoGraphResponseImplCopyWith(_$TachoGraphResponseImpl value,
          $Res Function(_$TachoGraphResponseImpl) then) =
      __$$TachoGraphResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, int status, String token, Detail? detail});

  @override
  $DetailCopyWith<$Res>? get detail;
}

/// @nodoc
class __$$TachoGraphResponseImplCopyWithImpl<$Res>
    extends _$TachoGraphResponseCopyWithImpl<$Res, _$TachoGraphResponseImpl>
    implements _$$TachoGraphResponseImplCopyWith<$Res> {
  __$$TachoGraphResponseImplCopyWithImpl(_$TachoGraphResponseImpl _value,
      $Res Function(_$TachoGraphResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? status = null,
    Object? token = null,
    Object? detail = freezed,
  }) {
    return _then(_$TachoGraphResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      detail: freezed == detail
          ? _value.detail
          : detail // ignore: cast_nullable_to_non_nullable
              as Detail?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TachoGraphResponseImpl implements _TachoGraphResponse {
  const _$TachoGraphResponseImpl(
      {required this.success,
      required this.status,
      required this.token,
      this.detail});

  factory _$TachoGraphResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$TachoGraphResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final int status;
  @override
  final String token;
  @override
  final Detail? detail;

  @override
  String toString() {
    return 'TachoGraphResponse(success: $success, status: $status, token: $token, detail: $detail)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TachoGraphResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.detail, detail) || other.detail == detail));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, success, status, token, detail);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TachoGraphResponseImplCopyWith<_$TachoGraphResponseImpl> get copyWith =>
      __$$TachoGraphResponseImplCopyWithImpl<_$TachoGraphResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TachoGraphResponseImplToJson(
      this,
    );
  }
}

abstract class _TachoGraphResponse implements TachoGraphResponse {
  const factory _TachoGraphResponse(
      {required final bool success,
      required final int status,
      required final String token,
      final Detail? detail}) = _$TachoGraphResponseImpl;

  factory _TachoGraphResponse.fromJson(Map<String, dynamic> json) =
      _$TachoGraphResponseImpl.fromJson;

  @override
  bool get success;
  @override
  int get status;
  @override
  String get token;
  @override
  Detail? get detail;
  @override
  @JsonKey(ignore: true)
  _$$TachoGraphResponseImplCopyWith<_$TachoGraphResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
