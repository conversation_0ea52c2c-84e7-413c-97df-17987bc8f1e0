// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tacho_graph_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TachoGraphRequest _$TachoGraphRequestFromJson(Map<String, dynamic> json) {
  return _TachoGraphRequest.fromJson(json);
}

/// @nodoc
mixin _$TachoGraphRequest {
  String get id => throw _privateConstructorUsedError;
  String get date => throw _privateConstructorUsedError;
  String? get token => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TachoGraphRequestCopyWith<TachoGraphRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TachoGraphRequestCopyWith<$Res> {
  factory $TachoGraphRequestCopyWith(
          TachoGraphRequest value, $Res Function(TachoGraphRequest) then) =
      _$TachoGraphRequestCopyWithImpl<$Res, TachoGraphRequest>;
  @useResult
  $Res call({String id, String date, String? token});
}

/// @nodoc
class _$TachoGraphRequestCopyWithImpl<$Res, $Val extends TachoGraphRequest>
    implements $TachoGraphRequestCopyWith<$Res> {
  _$TachoGraphRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? date = null,
    Object? token = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TachoGraphRequestImplCopyWith<$Res>
    implements $TachoGraphRequestCopyWith<$Res> {
  factory _$$TachoGraphRequestImplCopyWith(_$TachoGraphRequestImpl value,
          $Res Function(_$TachoGraphRequestImpl) then) =
      __$$TachoGraphRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String date, String? token});
}

/// @nodoc
class __$$TachoGraphRequestImplCopyWithImpl<$Res>
    extends _$TachoGraphRequestCopyWithImpl<$Res, _$TachoGraphRequestImpl>
    implements _$$TachoGraphRequestImplCopyWith<$Res> {
  __$$TachoGraphRequestImplCopyWithImpl(_$TachoGraphRequestImpl _value,
      $Res Function(_$TachoGraphRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? date = null,
    Object? token = freezed,
  }) {
    return _then(_$TachoGraphRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TachoGraphRequestImpl implements _TachoGraphRequest {
  const _$TachoGraphRequestImpl(
      {required this.id, required this.date, this.token = ""});

  factory _$TachoGraphRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$TachoGraphRequestImplFromJson(json);

  @override
  final String id;
  @override
  final String date;
  @override
  @JsonKey()
  final String? token;

  @override
  String toString() {
    return 'TachoGraphRequest(id: $id, date: $date, token: $token)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TachoGraphRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.token, token) || other.token == token));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, date, token);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TachoGraphRequestImplCopyWith<_$TachoGraphRequestImpl> get copyWith =>
      __$$TachoGraphRequestImplCopyWithImpl<_$TachoGraphRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TachoGraphRequestImplToJson(
      this,
    );
  }
}

abstract class _TachoGraphRequest implements TachoGraphRequest {
  const factory _TachoGraphRequest(
      {required final String id,
      required final String date,
      final String? token}) = _$TachoGraphRequestImpl;

  factory _TachoGraphRequest.fromJson(Map<String, dynamic> json) =
      _$TachoGraphRequestImpl.fromJson;

  @override
  String get id;
  @override
  String get date;
  @override
  String? get token;
  @override
  @JsonKey(ignore: true)
  _$$TachoGraphRequestImplCopyWith<_$TachoGraphRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
