// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DataImpl _$$DataImplFromJson(Map<String, dynamic> json) => _$DataImpl(
      time: json['time'] as String,
      speed: (json['speed'] as num).toInt(),
      distance: (json['distance'] as num).toDouble(),
      distance2: (json['distance2'] as num).toDouble(),
      minusMileage: (json['minusMileage'] as num).toDouble(),
    );

Map<String, dynamic> _$$DataImplToJson(_$DataImpl instance) =>
    <String, dynamic>{
      'time': instance.time,
      'speed': instance.speed,
      'distance': instance.distance,
      'distance2': instance.distance2,
      'minusMileage': instance.minusMileage,
    };

_$DetailImpl _$$DetailImplFromJson(Map<String, dynamic> json) => _$DetailImpl(
      date: json['date'] as String,
      plateNumber: json['plateNumber'] as String,
      driver: json['driver'] as String,
      rides: (json['rides'] as List<dynamic>)
          .map((e) => Data.fromJson(e as Map<String, dynamic>))
          .toList(),
      value1: (json['value1'] as num).toInt(),
      value2: (json['value2'] as num).toInt(),
    );

Map<String, dynamic> _$$DetailImplToJson(_$DetailImpl instance) =>
    <String, dynamic>{
      'date': instance.date,
      'plateNumber': instance.plateNumber,
      'driver': instance.driver,
      'rides': instance.rides,
      'value1': instance.value1,
      'value2': instance.value2,
    };
