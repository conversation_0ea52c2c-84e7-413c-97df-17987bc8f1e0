// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tacho_graph_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TachoGraphFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TachoGraphFailureUnexpected value) unexpected,
    required TResult Function(_TachoGraphFailureUnauthorized value)
        unauthorized,
    required TResult Function(_TachoGraphFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_TachoGraphFailureServerError value) serverError,
    required TResult Function(_TachoGraphFailureNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult? Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult? Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_TachoGraphFailureServerError value)? serverError,
    TResult? Function(_TachoGraphFailureNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult Function(_TachoGraphFailureServerError value)? serverError,
    TResult Function(_TachoGraphFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TachoGraphFailureCopyWith<$Res> {
  factory $TachoGraphFailureCopyWith(
          TachoGraphFailure value, $Res Function(TachoGraphFailure) then) =
      _$TachoGraphFailureCopyWithImpl<$Res, TachoGraphFailure>;
}

/// @nodoc
class _$TachoGraphFailureCopyWithImpl<$Res, $Val extends TachoGraphFailure>
    implements $TachoGraphFailureCopyWith<$Res> {
  _$TachoGraphFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$TachoGraphFailureUnexpectedImplCopyWith<$Res> {
  factory _$$TachoGraphFailureUnexpectedImplCopyWith(
          _$TachoGraphFailureUnexpectedImpl value,
          $Res Function(_$TachoGraphFailureUnexpectedImpl) then) =
      __$$TachoGraphFailureUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$TachoGraphFailureUnexpectedImplCopyWithImpl<$Res>
    extends _$TachoGraphFailureCopyWithImpl<$Res,
        _$TachoGraphFailureUnexpectedImpl>
    implements _$$TachoGraphFailureUnexpectedImplCopyWith<$Res> {
  __$$TachoGraphFailureUnexpectedImplCopyWithImpl(
      _$TachoGraphFailureUnexpectedImpl _value,
      $Res Function(_$TachoGraphFailureUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$TachoGraphFailureUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$TachoGraphFailureUnexpectedImpl
    implements _TachoGraphFailureUnexpected {
  const _$TachoGraphFailureUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'TachoGraphFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TachoGraphFailureUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TachoGraphFailureUnexpectedImplCopyWith<_$TachoGraphFailureUnexpectedImpl>
      get copyWith => __$$TachoGraphFailureUnexpectedImplCopyWithImpl<
          _$TachoGraphFailureUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TachoGraphFailureUnexpected value) unexpected,
    required TResult Function(_TachoGraphFailureUnauthorized value)
        unauthorized,
    required TResult Function(_TachoGraphFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_TachoGraphFailureServerError value) serverError,
    required TResult Function(_TachoGraphFailureNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult? Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult? Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_TachoGraphFailureServerError value)? serverError,
    TResult? Function(_TachoGraphFailureNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult Function(_TachoGraphFailureServerError value)? serverError,
    TResult Function(_TachoGraphFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _TachoGraphFailureUnexpected implements TachoGraphFailure {
  const factory _TachoGraphFailureUnexpected({required final String error}) =
      _$TachoGraphFailureUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$TachoGraphFailureUnexpectedImplCopyWith<_$TachoGraphFailureUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TachoGraphFailureUnauthorizedImplCopyWith<$Res> {
  factory _$$TachoGraphFailureUnauthorizedImplCopyWith(
          _$TachoGraphFailureUnauthorizedImpl value,
          $Res Function(_$TachoGraphFailureUnauthorizedImpl) then) =
      __$$TachoGraphFailureUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TachoGraphFailureUnauthorizedImplCopyWithImpl<$Res>
    extends _$TachoGraphFailureCopyWithImpl<$Res,
        _$TachoGraphFailureUnauthorizedImpl>
    implements _$$TachoGraphFailureUnauthorizedImplCopyWith<$Res> {
  __$$TachoGraphFailureUnauthorizedImplCopyWithImpl(
      _$TachoGraphFailureUnauthorizedImpl _value,
      $Res Function(_$TachoGraphFailureUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$TachoGraphFailureUnauthorizedImpl
    implements _TachoGraphFailureUnauthorized {
  const _$TachoGraphFailureUnauthorizedImpl();

  @override
  String toString() {
    return 'TachoGraphFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TachoGraphFailureUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TachoGraphFailureUnexpected value) unexpected,
    required TResult Function(_TachoGraphFailureUnauthorized value)
        unauthorized,
    required TResult Function(_TachoGraphFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_TachoGraphFailureServerError value) serverError,
    required TResult Function(_TachoGraphFailureNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult? Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult? Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_TachoGraphFailureServerError value)? serverError,
    TResult? Function(_TachoGraphFailureNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult Function(_TachoGraphFailureServerError value)? serverError,
    TResult Function(_TachoGraphFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _TachoGraphFailureUnauthorized implements TachoGraphFailure {
  const factory _TachoGraphFailureUnauthorized() =
      _$TachoGraphFailureUnauthorizedImpl;
}

/// @nodoc
abstract class _$$TachoGraphFailureUnauthenticatedImplCopyWith<$Res> {
  factory _$$TachoGraphFailureUnauthenticatedImplCopyWith(
          _$TachoGraphFailureUnauthenticatedImpl value,
          $Res Function(_$TachoGraphFailureUnauthenticatedImpl) then) =
      __$$TachoGraphFailureUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TachoGraphFailureUnauthenticatedImplCopyWithImpl<$Res>
    extends _$TachoGraphFailureCopyWithImpl<$Res,
        _$TachoGraphFailureUnauthenticatedImpl>
    implements _$$TachoGraphFailureUnauthenticatedImplCopyWith<$Res> {
  __$$TachoGraphFailureUnauthenticatedImplCopyWithImpl(
      _$TachoGraphFailureUnauthenticatedImpl _value,
      $Res Function(_$TachoGraphFailureUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$TachoGraphFailureUnauthenticatedImpl
    implements _TachoGraphFailureUnauthenticated {
  const _$TachoGraphFailureUnauthenticatedImpl();

  @override
  String toString() {
    return 'TachoGraphFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TachoGraphFailureUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TachoGraphFailureUnexpected value) unexpected,
    required TResult Function(_TachoGraphFailureUnauthorized value)
        unauthorized,
    required TResult Function(_TachoGraphFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_TachoGraphFailureServerError value) serverError,
    required TResult Function(_TachoGraphFailureNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult? Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult? Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_TachoGraphFailureServerError value)? serverError,
    TResult? Function(_TachoGraphFailureNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult Function(_TachoGraphFailureServerError value)? serverError,
    TResult Function(_TachoGraphFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _TachoGraphFailureUnauthenticated implements TachoGraphFailure {
  const factory _TachoGraphFailureUnauthenticated() =
      _$TachoGraphFailureUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$TachoGraphFailureServerErrorImplCopyWith<$Res> {
  factory _$$TachoGraphFailureServerErrorImplCopyWith(
          _$TachoGraphFailureServerErrorImpl value,
          $Res Function(_$TachoGraphFailureServerErrorImpl) then) =
      __$$TachoGraphFailureServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TachoGraphFailureServerErrorImplCopyWithImpl<$Res>
    extends _$TachoGraphFailureCopyWithImpl<$Res,
        _$TachoGraphFailureServerErrorImpl>
    implements _$$TachoGraphFailureServerErrorImplCopyWith<$Res> {
  __$$TachoGraphFailureServerErrorImplCopyWithImpl(
      _$TachoGraphFailureServerErrorImpl _value,
      $Res Function(_$TachoGraphFailureServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$TachoGraphFailureServerErrorImpl
    implements _TachoGraphFailureServerError {
  const _$TachoGraphFailureServerErrorImpl();

  @override
  String toString() {
    return 'TachoGraphFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TachoGraphFailureServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TachoGraphFailureUnexpected value) unexpected,
    required TResult Function(_TachoGraphFailureUnauthorized value)
        unauthorized,
    required TResult Function(_TachoGraphFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_TachoGraphFailureServerError value) serverError,
    required TResult Function(_TachoGraphFailureNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult? Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult? Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_TachoGraphFailureServerError value)? serverError,
    TResult? Function(_TachoGraphFailureNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult Function(_TachoGraphFailureServerError value)? serverError,
    TResult Function(_TachoGraphFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _TachoGraphFailureServerError implements TachoGraphFailure {
  const factory _TachoGraphFailureServerError() =
      _$TachoGraphFailureServerErrorImpl;
}

/// @nodoc
abstract class _$$TachoGraphFailureNoInternetImplCopyWith<$Res> {
  factory _$$TachoGraphFailureNoInternetImplCopyWith(
          _$TachoGraphFailureNoInternetImpl value,
          $Res Function(_$TachoGraphFailureNoInternetImpl) then) =
      __$$TachoGraphFailureNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TachoGraphFailureNoInternetImplCopyWithImpl<$Res>
    extends _$TachoGraphFailureCopyWithImpl<$Res,
        _$TachoGraphFailureNoInternetImpl>
    implements _$$TachoGraphFailureNoInternetImplCopyWith<$Res> {
  __$$TachoGraphFailureNoInternetImplCopyWithImpl(
      _$TachoGraphFailureNoInternetImpl _value,
      $Res Function(_$TachoGraphFailureNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$TachoGraphFailureNoInternetImpl
    implements _TachoGraphFailureNoInternet {
  const _$TachoGraphFailureNoInternetImpl();

  @override
  String toString() {
    return 'TachoGraphFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TachoGraphFailureNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TachoGraphFailureUnexpected value) unexpected,
    required TResult Function(_TachoGraphFailureUnauthorized value)
        unauthorized,
    required TResult Function(_TachoGraphFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_TachoGraphFailureServerError value) serverError,
    required TResult Function(_TachoGraphFailureNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult? Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult? Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_TachoGraphFailureServerError value)? serverError,
    TResult? Function(_TachoGraphFailureNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TachoGraphFailureUnexpected value)? unexpected,
    TResult Function(_TachoGraphFailureUnauthorized value)? unauthorized,
    TResult Function(_TachoGraphFailureUnauthenticated value)? unauthenticated,
    TResult Function(_TachoGraphFailureServerError value)? serverError,
    TResult Function(_TachoGraphFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _TachoGraphFailureNoInternet implements TachoGraphFailure {
  const factory _TachoGraphFailureNoInternet() =
      _$TachoGraphFailureNoInternetImpl;
}
