// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'driver_rfid_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DriverRifdResponse _$DriverRifdResponseFromJson(Map<String, dynamic> json) {
  return _DriverRifdResponse.fromJson(json);
}

/// @nodoc
mixin _$DriverRifdResponse {
  String get key => throw _privateConstructorUsedError;
  String get label => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DriverRifdResponseCopyWith<DriverRifdResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverRifdResponseCopyWith<$Res> {
  factory $DriverRifdResponseCopyWith(
          DriverRifdResponse value, $Res Function(DriverRifdResponse) then) =
      _$DriverRifdResponseCopyWithImpl<$Res, DriverRifdResponse>;
  @useResult
  $Res call({String key, String label, String? icon});
}

/// @nodoc
class _$DriverRifdResponseCopyWithImpl<$Res, $Val extends DriverRifdResponse>
    implements $DriverRifdResponseCopyWith<$Res> {
  _$DriverRifdResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? label = null,
    Object? icon = freezed,
  }) {
    return _then(_value.copyWith(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DriverRifdResponseImplCopyWith<$Res>
    implements $DriverRifdResponseCopyWith<$Res> {
  factory _$$DriverRifdResponseImplCopyWith(_$DriverRifdResponseImpl value,
          $Res Function(_$DriverRifdResponseImpl) then) =
      __$$DriverRifdResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String key, String label, String? icon});
}

/// @nodoc
class __$$DriverRifdResponseImplCopyWithImpl<$Res>
    extends _$DriverRifdResponseCopyWithImpl<$Res, _$DriverRifdResponseImpl>
    implements _$$DriverRifdResponseImplCopyWith<$Res> {
  __$$DriverRifdResponseImplCopyWithImpl(_$DriverRifdResponseImpl _value,
      $Res Function(_$DriverRifdResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? label = null,
    Object? icon = freezed,
  }) {
    return _then(_$DriverRifdResponseImpl(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverRifdResponseImpl implements _DriverRifdResponse {
  const _$DriverRifdResponseImpl(
      {required this.key, required this.label, this.icon});

  factory _$DriverRifdResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverRifdResponseImplFromJson(json);

  @override
  final String key;
  @override
  final String label;
  @override
  final String? icon;

  @override
  String toString() {
    return 'DriverRifdResponse(key: $key, label: $label, icon: $icon)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverRifdResponseImpl &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.icon, icon) || other.icon == icon));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, key, label, icon);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverRifdResponseImplCopyWith<_$DriverRifdResponseImpl> get copyWith =>
      __$$DriverRifdResponseImplCopyWithImpl<_$DriverRifdResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverRifdResponseImplToJson(
      this,
    );
  }
}

abstract class _DriverRifdResponse implements DriverRifdResponse {
  const factory _DriverRifdResponse(
      {required final String key,
      required final String label,
      final String? icon}) = _$DriverRifdResponseImpl;

  factory _DriverRifdResponse.fromJson(Map<String, dynamic> json) =
      _$DriverRifdResponseImpl.fromJson;

  @override
  String get key;
  @override
  String get label;
  @override
  String? get icon;
  @override
  @JsonKey(ignore: true)
  _$$DriverRifdResponseImplCopyWith<_$DriverRifdResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
