// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rfid_respone.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RfidResponseImpl _$$RfidResponseImplFromJson(Map<String, dynamic> json) =>
    _$RfidResponseImpl(
      isGroup: json['isGroup'] as bool,
      aggregate: json['aggregate'] as String?,
      data: json['data'] as String,
    );

Map<String, dynamic> _$$RfidResponseImplToJson(_$RfidResponseImpl instance) =>
    <String, dynamic>{
      'isGroup': instance.isGroup,
      'aggregate': instance.aggregate,
      'data': instance.data,
    };

_$ColumnDataImpl _$$ColumnDataImplFromJson(Map<String, dynamic> json) =>
    _$ColumnDataImpl(
      title: json['title'] as String?,
      field: json['field'] as String?,
    );

Map<String, dynamic> _$$ColumnDataImplToJson(_$ColumnDataImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'field': instance.field,
    };
