// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rfid_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RfidRequest _$RfidRequestFromJson(Map<String, dynamic> json) {
  return _RfidRequest.fromJson(json);
}

/// @nodoc
mixin _$RfidRequest {
  List<String> get vehicles => throw _privateConstructorUsedError;
  String get from => throw _privateConstructorUsedError;
  String get to => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RfidRequestCopyWith<RfidRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RfidRequestCopyWith<$Res> {
  factory $RfidRequestCopyWith(
          RfidRequest value, $Res Function(RfidRequest) then) =
      _$RfidRequestCopyWithImpl<$Res, RfidRequest>;
  @useResult
  $Res call({List<String> vehicles, String from, String to});
}

/// @nodoc
class _$RfidRequestCopyWithImpl<$Res, $Val extends RfidRequest>
    implements $RfidRequestCopyWith<$Res> {
  _$RfidRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicles = null,
    Object? from = null,
    Object? to = null,
  }) {
    return _then(_value.copyWith(
      vehicles: null == vehicles
          ? _value.vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as List<String>,
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RfidRequestImplCopyWith<$Res>
    implements $RfidRequestCopyWith<$Res> {
  factory _$$RfidRequestImplCopyWith(
          _$RfidRequestImpl value, $Res Function(_$RfidRequestImpl) then) =
      __$$RfidRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<String> vehicles, String from, String to});
}

/// @nodoc
class __$$RfidRequestImplCopyWithImpl<$Res>
    extends _$RfidRequestCopyWithImpl<$Res, _$RfidRequestImpl>
    implements _$$RfidRequestImplCopyWith<$Res> {
  __$$RfidRequestImplCopyWithImpl(
      _$RfidRequestImpl _value, $Res Function(_$RfidRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicles = null,
    Object? from = null,
    Object? to = null,
  }) {
    return _then(_$RfidRequestImpl(
      vehicles: null == vehicles
          ? _value._vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as List<String>,
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RfidRequestImpl implements _RfidRequest {
  const _$RfidRequestImpl(
      {required final List<String> vehicles,
      required this.from,
      required this.to})
      : _vehicles = vehicles;

  factory _$RfidRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$RfidRequestImplFromJson(json);

  final List<String> _vehicles;
  @override
  List<String> get vehicles {
    if (_vehicles is EqualUnmodifiableListView) return _vehicles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vehicles);
  }

  @override
  final String from;
  @override
  final String to;

  @override
  String toString() {
    return 'RfidRequest(vehicles: $vehicles, from: $from, to: $to)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RfidRequestImpl &&
            const DeepCollectionEquality().equals(other._vehicles, _vehicles) &&
            (identical(other.from, from) || other.from == from) &&
            (identical(other.to, to) || other.to == to));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_vehicles), from, to);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RfidRequestImplCopyWith<_$RfidRequestImpl> get copyWith =>
      __$$RfidRequestImplCopyWithImpl<_$RfidRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RfidRequestImplToJson(
      this,
    );
  }
}

abstract class _RfidRequest implements RfidRequest {
  const factory _RfidRequest(
      {required final List<String> vehicles,
      required final String from,
      required final String to}) = _$RfidRequestImpl;

  factory _RfidRequest.fromJson(Map<String, dynamic> json) =
      _$RfidRequestImpl.fromJson;

  @override
  List<String> get vehicles;
  @override
  String get from;
  @override
  String get to;
  @override
  @JsonKey(ignore: true)
  _$$RfidRequestImplCopyWith<_$RfidRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
