// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rfid_respone.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RfidResponse _$RfidResponseFromJson(Map<String, dynamic> json) {
  return _RfidResponse.fromJson(json);
}

/// @nodoc
mixin _$RfidResponse {
  bool get isGroup => throw _privateConstructorUsedError;
  String? get aggregate => throw _privateConstructorUsedError;
  String get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RfidResponseCopyWith<RfidResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RfidResponseCopyWith<$Res> {
  factory $RfidResponseCopyWith(
          RfidResponse value, $Res Function(RfidResponse) then) =
      _$RfidResponseCopyWithImpl<$Res, RfidResponse>;
  @useResult
  $Res call({bool isGroup, String? aggregate, String data});
}

/// @nodoc
class _$RfidResponseCopyWithImpl<$Res, $Val extends RfidResponse>
    implements $RfidResponseCopyWith<$Res> {
  _$RfidResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isGroup = null,
    Object? aggregate = freezed,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      isGroup: null == isGroup
          ? _value.isGroup
          : isGroup // ignore: cast_nullable_to_non_nullable
              as bool,
      aggregate: freezed == aggregate
          ? _value.aggregate
          : aggregate // ignore: cast_nullable_to_non_nullable
              as String?,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RfidResponseImplCopyWith<$Res>
    implements $RfidResponseCopyWith<$Res> {
  factory _$$RfidResponseImplCopyWith(
          _$RfidResponseImpl value, $Res Function(_$RfidResponseImpl) then) =
      __$$RfidResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isGroup, String? aggregate, String data});
}

/// @nodoc
class __$$RfidResponseImplCopyWithImpl<$Res>
    extends _$RfidResponseCopyWithImpl<$Res, _$RfidResponseImpl>
    implements _$$RfidResponseImplCopyWith<$Res> {
  __$$RfidResponseImplCopyWithImpl(
      _$RfidResponseImpl _value, $Res Function(_$RfidResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isGroup = null,
    Object? aggregate = freezed,
    Object? data = null,
  }) {
    return _then(_$RfidResponseImpl(
      isGroup: null == isGroup
          ? _value.isGroup
          : isGroup // ignore: cast_nullable_to_non_nullable
              as bool,
      aggregate: freezed == aggregate
          ? _value.aggregate
          : aggregate // ignore: cast_nullable_to_non_nullable
              as String?,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RfidResponseImpl implements _RfidResponse {
  const _$RfidResponseImpl(
      {required this.isGroup, this.aggregate, required this.data});

  factory _$RfidResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$RfidResponseImplFromJson(json);

  @override
  final bool isGroup;
  @override
  final String? aggregate;
  @override
  final String data;

  @override
  String toString() {
    return 'RfidResponse(isGroup: $isGroup, aggregate: $aggregate, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RfidResponseImpl &&
            (identical(other.isGroup, isGroup) || other.isGroup == isGroup) &&
            (identical(other.aggregate, aggregate) ||
                other.aggregate == aggregate) &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, isGroup, aggregate, data);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RfidResponseImplCopyWith<_$RfidResponseImpl> get copyWith =>
      __$$RfidResponseImplCopyWithImpl<_$RfidResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RfidResponseImplToJson(
      this,
    );
  }
}

abstract class _RfidResponse implements RfidResponse {
  const factory _RfidResponse(
      {required final bool isGroup,
      final String? aggregate,
      required final String data}) = _$RfidResponseImpl;

  factory _RfidResponse.fromJson(Map<String, dynamic> json) =
      _$RfidResponseImpl.fromJson;

  @override
  bool get isGroup;
  @override
  String? get aggregate;
  @override
  String get data;
  @override
  @JsonKey(ignore: true)
  _$$RfidResponseImplCopyWith<_$RfidResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ColumnData _$ColumnDataFromJson(Map<String, dynamic> json) {
  return _ColumnData.fromJson(json);
}

/// @nodoc
mixin _$ColumnData {
  String? get title => throw _privateConstructorUsedError;
  String? get field => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ColumnDataCopyWith<ColumnData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ColumnDataCopyWith<$Res> {
  factory $ColumnDataCopyWith(
          ColumnData value, $Res Function(ColumnData) then) =
      _$ColumnDataCopyWithImpl<$Res, ColumnData>;
  @useResult
  $Res call({String? title, String? field});
}

/// @nodoc
class _$ColumnDataCopyWithImpl<$Res, $Val extends ColumnData>
    implements $ColumnDataCopyWith<$Res> {
  _$ColumnDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? field = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      field: freezed == field
          ? _value.field
          : field // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ColumnDataImplCopyWith<$Res>
    implements $ColumnDataCopyWith<$Res> {
  factory _$$ColumnDataImplCopyWith(
          _$ColumnDataImpl value, $Res Function(_$ColumnDataImpl) then) =
      __$$ColumnDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? title, String? field});
}

/// @nodoc
class __$$ColumnDataImplCopyWithImpl<$Res>
    extends _$ColumnDataCopyWithImpl<$Res, _$ColumnDataImpl>
    implements _$$ColumnDataImplCopyWith<$Res> {
  __$$ColumnDataImplCopyWithImpl(
      _$ColumnDataImpl _value, $Res Function(_$ColumnDataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? field = freezed,
  }) {
    return _then(_$ColumnDataImpl(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      field: freezed == field
          ? _value.field
          : field // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ColumnDataImpl implements _ColumnData {
  const _$ColumnDataImpl({this.title, this.field});

  factory _$ColumnDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ColumnDataImplFromJson(json);

  @override
  final String? title;
  @override
  final String? field;

  @override
  String toString() {
    return 'ColumnData(title: $title, field: $field)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ColumnDataImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.field, field) || other.field == field));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, field);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ColumnDataImplCopyWith<_$ColumnDataImpl> get copyWith =>
      __$$ColumnDataImplCopyWithImpl<_$ColumnDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ColumnDataImplToJson(
      this,
    );
  }
}

abstract class _ColumnData implements ColumnData {
  const factory _ColumnData({final String? title, final String? field}) =
      _$ColumnDataImpl;

  factory _ColumnData.fromJson(Map<String, dynamic> json) =
      _$ColumnDataImpl.fromJson;

  @override
  String? get title;
  @override
  String? get field;
  @override
  @JsonKey(ignore: true)
  _$$ColumnDataImplCopyWith<_$ColumnDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
