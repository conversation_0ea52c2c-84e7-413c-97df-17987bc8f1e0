// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rfid_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RfidFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RfidFailureUnexpected value) unexpected,
    required TResult Function(_RfidFailureUnauthorized value) unauthorized,
    required TResult Function(_RfidFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_RfidFailureServerError value) serverError,
    required TResult Function(_RfidFailureNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RfidFailureUnexpected value)? unexpected,
    TResult? Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult? Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_RfidFailureServerError value)? serverError,
    TResult? Function(_RfidFailureNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RfidFailureUnexpected value)? unexpected,
    TResult Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult Function(_RfidFailureServerError value)? serverError,
    TResult Function(_RfidFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RfidFailureCopyWith<$Res> {
  factory $RfidFailureCopyWith(
          RfidFailure value, $Res Function(RfidFailure) then) =
      _$RfidFailureCopyWithImpl<$Res, RfidFailure>;
}

/// @nodoc
class _$RfidFailureCopyWithImpl<$Res, $Val extends RfidFailure>
    implements $RfidFailureCopyWith<$Res> {
  _$RfidFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$RfidFailureUnexpectedImplCopyWith<$Res> {
  factory _$$RfidFailureUnexpectedImplCopyWith(
          _$RfidFailureUnexpectedImpl value,
          $Res Function(_$RfidFailureUnexpectedImpl) then) =
      __$$RfidFailureUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$RfidFailureUnexpectedImplCopyWithImpl<$Res>
    extends _$RfidFailureCopyWithImpl<$Res, _$RfidFailureUnexpectedImpl>
    implements _$$RfidFailureUnexpectedImplCopyWith<$Res> {
  __$$RfidFailureUnexpectedImplCopyWithImpl(_$RfidFailureUnexpectedImpl _value,
      $Res Function(_$RfidFailureUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$RfidFailureUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RfidFailureUnexpectedImpl implements _RfidFailureUnexpected {
  const _$RfidFailureUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'RfidFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RfidFailureUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RfidFailureUnexpectedImplCopyWith<_$RfidFailureUnexpectedImpl>
      get copyWith => __$$RfidFailureUnexpectedImplCopyWithImpl<
          _$RfidFailureUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RfidFailureUnexpected value) unexpected,
    required TResult Function(_RfidFailureUnauthorized value) unauthorized,
    required TResult Function(_RfidFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_RfidFailureServerError value) serverError,
    required TResult Function(_RfidFailureNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RfidFailureUnexpected value)? unexpected,
    TResult? Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult? Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_RfidFailureServerError value)? serverError,
    TResult? Function(_RfidFailureNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RfidFailureUnexpected value)? unexpected,
    TResult Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult Function(_RfidFailureServerError value)? serverError,
    TResult Function(_RfidFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _RfidFailureUnexpected implements RfidFailure {
  const factory _RfidFailureUnexpected({required final String error}) =
      _$RfidFailureUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$RfidFailureUnexpectedImplCopyWith<_$RfidFailureUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RfidFailureUnauthorizedImplCopyWith<$Res> {
  factory _$$RfidFailureUnauthorizedImplCopyWith(
          _$RfidFailureUnauthorizedImpl value,
          $Res Function(_$RfidFailureUnauthorizedImpl) then) =
      __$$RfidFailureUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RfidFailureUnauthorizedImplCopyWithImpl<$Res>
    extends _$RfidFailureCopyWithImpl<$Res, _$RfidFailureUnauthorizedImpl>
    implements _$$RfidFailureUnauthorizedImplCopyWith<$Res> {
  __$$RfidFailureUnauthorizedImplCopyWithImpl(
      _$RfidFailureUnauthorizedImpl _value,
      $Res Function(_$RfidFailureUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$RfidFailureUnauthorizedImpl implements _RfidFailureUnauthorized {
  const _$RfidFailureUnauthorizedImpl();

  @override
  String toString() {
    return 'RfidFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RfidFailureUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RfidFailureUnexpected value) unexpected,
    required TResult Function(_RfidFailureUnauthorized value) unauthorized,
    required TResult Function(_RfidFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_RfidFailureServerError value) serverError,
    required TResult Function(_RfidFailureNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RfidFailureUnexpected value)? unexpected,
    TResult? Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult? Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_RfidFailureServerError value)? serverError,
    TResult? Function(_RfidFailureNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RfidFailureUnexpected value)? unexpected,
    TResult Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult Function(_RfidFailureServerError value)? serverError,
    TResult Function(_RfidFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _RfidFailureUnauthorized implements RfidFailure {
  const factory _RfidFailureUnauthorized() = _$RfidFailureUnauthorizedImpl;
}

/// @nodoc
abstract class _$$RfidFailureUnauthenticatedImplCopyWith<$Res> {
  factory _$$RfidFailureUnauthenticatedImplCopyWith(
          _$RfidFailureUnauthenticatedImpl value,
          $Res Function(_$RfidFailureUnauthenticatedImpl) then) =
      __$$RfidFailureUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RfidFailureUnauthenticatedImplCopyWithImpl<$Res>
    extends _$RfidFailureCopyWithImpl<$Res, _$RfidFailureUnauthenticatedImpl>
    implements _$$RfidFailureUnauthenticatedImplCopyWith<$Res> {
  __$$RfidFailureUnauthenticatedImplCopyWithImpl(
      _$RfidFailureUnauthenticatedImpl _value,
      $Res Function(_$RfidFailureUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$RfidFailureUnauthenticatedImpl implements _RfidFailureUnauthenticated {
  const _$RfidFailureUnauthenticatedImpl();

  @override
  String toString() {
    return 'RfidFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RfidFailureUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RfidFailureUnexpected value) unexpected,
    required TResult Function(_RfidFailureUnauthorized value) unauthorized,
    required TResult Function(_RfidFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_RfidFailureServerError value) serverError,
    required TResult Function(_RfidFailureNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RfidFailureUnexpected value)? unexpected,
    TResult? Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult? Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_RfidFailureServerError value)? serverError,
    TResult? Function(_RfidFailureNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RfidFailureUnexpected value)? unexpected,
    TResult Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult Function(_RfidFailureServerError value)? serverError,
    TResult Function(_RfidFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _RfidFailureUnauthenticated implements RfidFailure {
  const factory _RfidFailureUnauthenticated() =
      _$RfidFailureUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$RfidFailureServerErrorImplCopyWith<$Res> {
  factory _$$RfidFailureServerErrorImplCopyWith(
          _$RfidFailureServerErrorImpl value,
          $Res Function(_$RfidFailureServerErrorImpl) then) =
      __$$RfidFailureServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RfidFailureServerErrorImplCopyWithImpl<$Res>
    extends _$RfidFailureCopyWithImpl<$Res, _$RfidFailureServerErrorImpl>
    implements _$$RfidFailureServerErrorImplCopyWith<$Res> {
  __$$RfidFailureServerErrorImplCopyWithImpl(
      _$RfidFailureServerErrorImpl _value,
      $Res Function(_$RfidFailureServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$RfidFailureServerErrorImpl implements _RfidFailureServerError {
  const _$RfidFailureServerErrorImpl();

  @override
  String toString() {
    return 'RfidFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RfidFailureServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RfidFailureUnexpected value) unexpected,
    required TResult Function(_RfidFailureUnauthorized value) unauthorized,
    required TResult Function(_RfidFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_RfidFailureServerError value) serverError,
    required TResult Function(_RfidFailureNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RfidFailureUnexpected value)? unexpected,
    TResult? Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult? Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_RfidFailureServerError value)? serverError,
    TResult? Function(_RfidFailureNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RfidFailureUnexpected value)? unexpected,
    TResult Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult Function(_RfidFailureServerError value)? serverError,
    TResult Function(_RfidFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _RfidFailureServerError implements RfidFailure {
  const factory _RfidFailureServerError() = _$RfidFailureServerErrorImpl;
}

/// @nodoc
abstract class _$$RfidFailureNoInternetImplCopyWith<$Res> {
  factory _$$RfidFailureNoInternetImplCopyWith(
          _$RfidFailureNoInternetImpl value,
          $Res Function(_$RfidFailureNoInternetImpl) then) =
      __$$RfidFailureNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RfidFailureNoInternetImplCopyWithImpl<$Res>
    extends _$RfidFailureCopyWithImpl<$Res, _$RfidFailureNoInternetImpl>
    implements _$$RfidFailureNoInternetImplCopyWith<$Res> {
  __$$RfidFailureNoInternetImplCopyWithImpl(_$RfidFailureNoInternetImpl _value,
      $Res Function(_$RfidFailureNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$RfidFailureNoInternetImpl implements _RfidFailureNoInternet {
  const _$RfidFailureNoInternetImpl();

  @override
  String toString() {
    return 'RfidFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RfidFailureNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RfidFailureUnexpected value) unexpected,
    required TResult Function(_RfidFailureUnauthorized value) unauthorized,
    required TResult Function(_RfidFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_RfidFailureServerError value) serverError,
    required TResult Function(_RfidFailureNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RfidFailureUnexpected value)? unexpected,
    TResult? Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult? Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_RfidFailureServerError value)? serverError,
    TResult? Function(_RfidFailureNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RfidFailureUnexpected value)? unexpected,
    TResult Function(_RfidFailureUnauthorized value)? unauthorized,
    TResult Function(_RfidFailureUnauthenticated value)? unauthenticated,
    TResult Function(_RfidFailureServerError value)? serverError,
    TResult Function(_RfidFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _RfidFailureNoInternet implements RfidFailure {
  const factory _RfidFailureNoInternet() = _$RfidFailureNoInternetImpl;
}
