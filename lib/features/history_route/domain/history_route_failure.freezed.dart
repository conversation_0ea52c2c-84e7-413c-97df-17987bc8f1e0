// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'history_route_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HistoryRouteFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HistoryRouteUnexpected value) unexpected,
    required TResult Function(_HistoryRouteUnauthorized value) unauthorized,
    required TResult Function(_HistoryRouteUnauthenticated value)
        unauthenticated,
    required TResult Function(_HistoryRouteServerError value) serverError,
    required TResult Function(_HistoryRouteEmptyRoute value) emptyRoute,
    required TResult Function(_HistoryRouteFailureNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HistoryRouteUnexpected value)? unexpected,
    TResult? Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult? Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult? Function(_HistoryRouteServerError value)? serverError,
    TResult? Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult? Function(_HistoryRouteFailureNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HistoryRouteUnexpected value)? unexpected,
    TResult Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult Function(_HistoryRouteServerError value)? serverError,
    TResult Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult Function(_HistoryRouteFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryRouteFailureCopyWith<$Res> {
  factory $HistoryRouteFailureCopyWith(
          HistoryRouteFailure value, $Res Function(HistoryRouteFailure) then) =
      _$HistoryRouteFailureCopyWithImpl<$Res, HistoryRouteFailure>;
}

/// @nodoc
class _$HistoryRouteFailureCopyWithImpl<$Res, $Val extends HistoryRouteFailure>
    implements $HistoryRouteFailureCopyWith<$Res> {
  _$HistoryRouteFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$HistoryRouteUnexpectedImplCopyWith<$Res> {
  factory _$$HistoryRouteUnexpectedImplCopyWith(
          _$HistoryRouteUnexpectedImpl value,
          $Res Function(_$HistoryRouteUnexpectedImpl) then) =
      __$$HistoryRouteUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$HistoryRouteUnexpectedImplCopyWithImpl<$Res>
    extends _$HistoryRouteFailureCopyWithImpl<$Res,
        _$HistoryRouteUnexpectedImpl>
    implements _$$HistoryRouteUnexpectedImplCopyWith<$Res> {
  __$$HistoryRouteUnexpectedImplCopyWithImpl(
      _$HistoryRouteUnexpectedImpl _value,
      $Res Function(_$HistoryRouteUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$HistoryRouteUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$HistoryRouteUnexpectedImpl implements _HistoryRouteUnexpected {
  const _$HistoryRouteUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'HistoryRouteFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryRouteUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoryRouteUnexpectedImplCopyWith<_$HistoryRouteUnexpectedImpl>
      get copyWith => __$$HistoryRouteUnexpectedImplCopyWithImpl<
          _$HistoryRouteUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HistoryRouteUnexpected value) unexpected,
    required TResult Function(_HistoryRouteUnauthorized value) unauthorized,
    required TResult Function(_HistoryRouteUnauthenticated value)
        unauthenticated,
    required TResult Function(_HistoryRouteServerError value) serverError,
    required TResult Function(_HistoryRouteEmptyRoute value) emptyRoute,
    required TResult Function(_HistoryRouteFailureNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HistoryRouteUnexpected value)? unexpected,
    TResult? Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult? Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult? Function(_HistoryRouteServerError value)? serverError,
    TResult? Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult? Function(_HistoryRouteFailureNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HistoryRouteUnexpected value)? unexpected,
    TResult Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult Function(_HistoryRouteServerError value)? serverError,
    TResult Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult Function(_HistoryRouteFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _HistoryRouteUnexpected implements HistoryRouteFailure {
  const factory _HistoryRouteUnexpected({required final String error}) =
      _$HistoryRouteUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$HistoryRouteUnexpectedImplCopyWith<_$HistoryRouteUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$HistoryRouteUnauthorizedImplCopyWith<$Res> {
  factory _$$HistoryRouteUnauthorizedImplCopyWith(
          _$HistoryRouteUnauthorizedImpl value,
          $Res Function(_$HistoryRouteUnauthorizedImpl) then) =
      __$$HistoryRouteUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$HistoryRouteUnauthorizedImplCopyWithImpl<$Res>
    extends _$HistoryRouteFailureCopyWithImpl<$Res,
        _$HistoryRouteUnauthorizedImpl>
    implements _$$HistoryRouteUnauthorizedImplCopyWith<$Res> {
  __$$HistoryRouteUnauthorizedImplCopyWithImpl(
      _$HistoryRouteUnauthorizedImpl _value,
      $Res Function(_$HistoryRouteUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$HistoryRouteUnauthorizedImpl implements _HistoryRouteUnauthorized {
  const _$HistoryRouteUnauthorizedImpl();

  @override
  String toString() {
    return 'HistoryRouteFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryRouteUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HistoryRouteUnexpected value) unexpected,
    required TResult Function(_HistoryRouteUnauthorized value) unauthorized,
    required TResult Function(_HistoryRouteUnauthenticated value)
        unauthenticated,
    required TResult Function(_HistoryRouteServerError value) serverError,
    required TResult Function(_HistoryRouteEmptyRoute value) emptyRoute,
    required TResult Function(_HistoryRouteFailureNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HistoryRouteUnexpected value)? unexpected,
    TResult? Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult? Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult? Function(_HistoryRouteServerError value)? serverError,
    TResult? Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult? Function(_HistoryRouteFailureNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HistoryRouteUnexpected value)? unexpected,
    TResult Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult Function(_HistoryRouteServerError value)? serverError,
    TResult Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult Function(_HistoryRouteFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _HistoryRouteUnauthorized implements HistoryRouteFailure {
  const factory _HistoryRouteUnauthorized() = _$HistoryRouteUnauthorizedImpl;
}

/// @nodoc
abstract class _$$HistoryRouteUnauthenticatedImplCopyWith<$Res> {
  factory _$$HistoryRouteUnauthenticatedImplCopyWith(
          _$HistoryRouteUnauthenticatedImpl value,
          $Res Function(_$HistoryRouteUnauthenticatedImpl) then) =
      __$$HistoryRouteUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$HistoryRouteUnauthenticatedImplCopyWithImpl<$Res>
    extends _$HistoryRouteFailureCopyWithImpl<$Res,
        _$HistoryRouteUnauthenticatedImpl>
    implements _$$HistoryRouteUnauthenticatedImplCopyWith<$Res> {
  __$$HistoryRouteUnauthenticatedImplCopyWithImpl(
      _$HistoryRouteUnauthenticatedImpl _value,
      $Res Function(_$HistoryRouteUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$HistoryRouteUnauthenticatedImpl
    implements _HistoryRouteUnauthenticated {
  const _$HistoryRouteUnauthenticatedImpl();

  @override
  String toString() {
    return 'HistoryRouteFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryRouteUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HistoryRouteUnexpected value) unexpected,
    required TResult Function(_HistoryRouteUnauthorized value) unauthorized,
    required TResult Function(_HistoryRouteUnauthenticated value)
        unauthenticated,
    required TResult Function(_HistoryRouteServerError value) serverError,
    required TResult Function(_HistoryRouteEmptyRoute value) emptyRoute,
    required TResult Function(_HistoryRouteFailureNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HistoryRouteUnexpected value)? unexpected,
    TResult? Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult? Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult? Function(_HistoryRouteServerError value)? serverError,
    TResult? Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult? Function(_HistoryRouteFailureNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HistoryRouteUnexpected value)? unexpected,
    TResult Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult Function(_HistoryRouteServerError value)? serverError,
    TResult Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult Function(_HistoryRouteFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _HistoryRouteUnauthenticated implements HistoryRouteFailure {
  const factory _HistoryRouteUnauthenticated() =
      _$HistoryRouteUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$HistoryRouteServerErrorImplCopyWith<$Res> {
  factory _$$HistoryRouteServerErrorImplCopyWith(
          _$HistoryRouteServerErrorImpl value,
          $Res Function(_$HistoryRouteServerErrorImpl) then) =
      __$$HistoryRouteServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$HistoryRouteServerErrorImplCopyWithImpl<$Res>
    extends _$HistoryRouteFailureCopyWithImpl<$Res,
        _$HistoryRouteServerErrorImpl>
    implements _$$HistoryRouteServerErrorImplCopyWith<$Res> {
  __$$HistoryRouteServerErrorImplCopyWithImpl(
      _$HistoryRouteServerErrorImpl _value,
      $Res Function(_$HistoryRouteServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$HistoryRouteServerErrorImpl implements _HistoryRouteServerError {
  const _$HistoryRouteServerErrorImpl();

  @override
  String toString() {
    return 'HistoryRouteFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryRouteServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HistoryRouteUnexpected value) unexpected,
    required TResult Function(_HistoryRouteUnauthorized value) unauthorized,
    required TResult Function(_HistoryRouteUnauthenticated value)
        unauthenticated,
    required TResult Function(_HistoryRouteServerError value) serverError,
    required TResult Function(_HistoryRouteEmptyRoute value) emptyRoute,
    required TResult Function(_HistoryRouteFailureNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HistoryRouteUnexpected value)? unexpected,
    TResult? Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult? Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult? Function(_HistoryRouteServerError value)? serverError,
    TResult? Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult? Function(_HistoryRouteFailureNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HistoryRouteUnexpected value)? unexpected,
    TResult Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult Function(_HistoryRouteServerError value)? serverError,
    TResult Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult Function(_HistoryRouteFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _HistoryRouteServerError implements HistoryRouteFailure {
  const factory _HistoryRouteServerError() = _$HistoryRouteServerErrorImpl;
}

/// @nodoc
abstract class _$$HistoryRouteEmptyRouteImplCopyWith<$Res> {
  factory _$$HistoryRouteEmptyRouteImplCopyWith(
          _$HistoryRouteEmptyRouteImpl value,
          $Res Function(_$HistoryRouteEmptyRouteImpl) then) =
      __$$HistoryRouteEmptyRouteImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$HistoryRouteEmptyRouteImplCopyWithImpl<$Res>
    extends _$HistoryRouteFailureCopyWithImpl<$Res,
        _$HistoryRouteEmptyRouteImpl>
    implements _$$HistoryRouteEmptyRouteImplCopyWith<$Res> {
  __$$HistoryRouteEmptyRouteImplCopyWithImpl(
      _$HistoryRouteEmptyRouteImpl _value,
      $Res Function(_$HistoryRouteEmptyRouteImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$HistoryRouteEmptyRouteImpl implements _HistoryRouteEmptyRoute {
  const _$HistoryRouteEmptyRouteImpl();

  @override
  String toString() {
    return 'HistoryRouteFailure.emptyRoute()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryRouteEmptyRouteImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return emptyRoute();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return emptyRoute?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (emptyRoute != null) {
      return emptyRoute();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HistoryRouteUnexpected value) unexpected,
    required TResult Function(_HistoryRouteUnauthorized value) unauthorized,
    required TResult Function(_HistoryRouteUnauthenticated value)
        unauthenticated,
    required TResult Function(_HistoryRouteServerError value) serverError,
    required TResult Function(_HistoryRouteEmptyRoute value) emptyRoute,
    required TResult Function(_HistoryRouteFailureNoInternet value) noInternet,
  }) {
    return emptyRoute(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HistoryRouteUnexpected value)? unexpected,
    TResult? Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult? Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult? Function(_HistoryRouteServerError value)? serverError,
    TResult? Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult? Function(_HistoryRouteFailureNoInternet value)? noInternet,
  }) {
    return emptyRoute?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HistoryRouteUnexpected value)? unexpected,
    TResult Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult Function(_HistoryRouteServerError value)? serverError,
    TResult Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult Function(_HistoryRouteFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (emptyRoute != null) {
      return emptyRoute(this);
    }
    return orElse();
  }
}

abstract class _HistoryRouteEmptyRoute implements HistoryRouteFailure {
  const factory _HistoryRouteEmptyRoute() = _$HistoryRouteEmptyRouteImpl;
}

/// @nodoc
abstract class _$$HistoryRouteFailureNoInternetImplCopyWith<$Res> {
  factory _$$HistoryRouteFailureNoInternetImplCopyWith(
          _$HistoryRouteFailureNoInternetImpl value,
          $Res Function(_$HistoryRouteFailureNoInternetImpl) then) =
      __$$HistoryRouteFailureNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$HistoryRouteFailureNoInternetImplCopyWithImpl<$Res>
    extends _$HistoryRouteFailureCopyWithImpl<$Res,
        _$HistoryRouteFailureNoInternetImpl>
    implements _$$HistoryRouteFailureNoInternetImplCopyWith<$Res> {
  __$$HistoryRouteFailureNoInternetImplCopyWithImpl(
      _$HistoryRouteFailureNoInternetImpl _value,
      $Res Function(_$HistoryRouteFailureNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$HistoryRouteFailureNoInternetImpl
    implements _HistoryRouteFailureNoInternet {
  const _$HistoryRouteFailureNoInternetImpl();

  @override
  String toString() {
    return 'HistoryRouteFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryRouteFailureNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() emptyRoute,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? emptyRoute,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? emptyRoute,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HistoryRouteUnexpected value) unexpected,
    required TResult Function(_HistoryRouteUnauthorized value) unauthorized,
    required TResult Function(_HistoryRouteUnauthenticated value)
        unauthenticated,
    required TResult Function(_HistoryRouteServerError value) serverError,
    required TResult Function(_HistoryRouteEmptyRoute value) emptyRoute,
    required TResult Function(_HistoryRouteFailureNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HistoryRouteUnexpected value)? unexpected,
    TResult? Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult? Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult? Function(_HistoryRouteServerError value)? serverError,
    TResult? Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult? Function(_HistoryRouteFailureNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HistoryRouteUnexpected value)? unexpected,
    TResult Function(_HistoryRouteUnauthorized value)? unauthorized,
    TResult Function(_HistoryRouteUnauthenticated value)? unauthenticated,
    TResult Function(_HistoryRouteServerError value)? serverError,
    TResult Function(_HistoryRouteEmptyRoute value)? emptyRoute,
    TResult Function(_HistoryRouteFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _HistoryRouteFailureNoInternet implements HistoryRouteFailure {
  const factory _HistoryRouteFailureNoInternet() =
      _$HistoryRouteFailureNoInternetImpl;
}
