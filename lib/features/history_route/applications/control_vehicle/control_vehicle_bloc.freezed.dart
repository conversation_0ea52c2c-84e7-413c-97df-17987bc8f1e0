// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'control_vehicle_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ControlVehicleEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int lengthRoute) start,
    required TResult Function(ControlVehicleState newPlaybackState)
        changePlaybackType,
    required TResult Function() cancelPlayback,
    required TResult Function(bool selectedIndex, bool manuelSliding, int index)
        goToIndex,
    required TResult Function() play,
    required TResult Function() pause,
    required TResult Function() changePlaybackSpeed,
    required TResult Function() restartPlayback,
    required TResult Function() dispose,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int lengthRoute)? start,
    TResult? Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult? Function()? cancelPlayback,
    TResult? Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult? Function()? play,
    TResult? Function()? pause,
    TResult? Function()? changePlaybackSpeed,
    TResult? Function()? restartPlayback,
    TResult? Function()? dispose,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int lengthRoute)? start,
    TResult Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult Function()? cancelPlayback,
    TResult Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult Function()? play,
    TResult Function()? pause,
    TResult Function()? changePlaybackSpeed,
    TResult Function()? restartPlayback,
    TResult Function()? dispose,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Start value) start,
    required TResult Function(_ChangePlaybackType value) changePlaybackType,
    required TResult Function(_CancelPlayback value) cancelPlayback,
    required TResult Function(_GoToIndex value) goToIndex,
    required TResult Function(_Play value) play,
    required TResult Function(_Pause value) pause,
    required TResult Function(_ChangePlaybackSpeed value) changePlaybackSpeed,
    required TResult Function(_RestartPlayback value) restartPlayback,
    required TResult Function(_Dispose value) dispose,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Start value)? start,
    TResult? Function(_ChangePlaybackType value)? changePlaybackType,
    TResult? Function(_CancelPlayback value)? cancelPlayback,
    TResult? Function(_GoToIndex value)? goToIndex,
    TResult? Function(_Play value)? play,
    TResult? Function(_Pause value)? pause,
    TResult? Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult? Function(_RestartPlayback value)? restartPlayback,
    TResult? Function(_Dispose value)? dispose,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Start value)? start,
    TResult Function(_ChangePlaybackType value)? changePlaybackType,
    TResult Function(_CancelPlayback value)? cancelPlayback,
    TResult Function(_GoToIndex value)? goToIndex,
    TResult Function(_Play value)? play,
    TResult Function(_Pause value)? pause,
    TResult Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult Function(_RestartPlayback value)? restartPlayback,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ControlVehicleEventCopyWith<$Res> {
  factory $ControlVehicleEventCopyWith(
          ControlVehicleEvent value, $Res Function(ControlVehicleEvent) then) =
      _$ControlVehicleEventCopyWithImpl<$Res, ControlVehicleEvent>;
}

/// @nodoc
class _$ControlVehicleEventCopyWithImpl<$Res, $Val extends ControlVehicleEvent>
    implements $ControlVehicleEventCopyWith<$Res> {
  _$ControlVehicleEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$StartImplCopyWith<$Res> {
  factory _$$StartImplCopyWith(
          _$StartImpl value, $Res Function(_$StartImpl) then) =
      __$$StartImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int lengthRoute});
}

/// @nodoc
class __$$StartImplCopyWithImpl<$Res>
    extends _$ControlVehicleEventCopyWithImpl<$Res, _$StartImpl>
    implements _$$StartImplCopyWith<$Res> {
  __$$StartImplCopyWithImpl(
      _$StartImpl _value, $Res Function(_$StartImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lengthRoute = null,
  }) {
    return _then(_$StartImpl(
      lengthRoute: null == lengthRoute
          ? _value.lengthRoute
          : lengthRoute // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$StartImpl implements _Start {
  const _$StartImpl({required this.lengthRoute});

  @override
  final int lengthRoute;

  @override
  String toString() {
    return 'ControlVehicleEvent.start(lengthRoute: $lengthRoute)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartImpl &&
            (identical(other.lengthRoute, lengthRoute) ||
                other.lengthRoute == lengthRoute));
  }

  @override
  int get hashCode => Object.hash(runtimeType, lengthRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$StartImplCopyWith<_$StartImpl> get copyWith =>
      __$$StartImplCopyWithImpl<_$StartImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int lengthRoute) start,
    required TResult Function(ControlVehicleState newPlaybackState)
        changePlaybackType,
    required TResult Function() cancelPlayback,
    required TResult Function(bool selectedIndex, bool manuelSliding, int index)
        goToIndex,
    required TResult Function() play,
    required TResult Function() pause,
    required TResult Function() changePlaybackSpeed,
    required TResult Function() restartPlayback,
    required TResult Function() dispose,
  }) {
    return start(lengthRoute);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int lengthRoute)? start,
    TResult? Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult? Function()? cancelPlayback,
    TResult? Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult? Function()? play,
    TResult? Function()? pause,
    TResult? Function()? changePlaybackSpeed,
    TResult? Function()? restartPlayback,
    TResult? Function()? dispose,
  }) {
    return start?.call(lengthRoute);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int lengthRoute)? start,
    TResult Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult Function()? cancelPlayback,
    TResult Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult Function()? play,
    TResult Function()? pause,
    TResult Function()? changePlaybackSpeed,
    TResult Function()? restartPlayback,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (start != null) {
      return start(lengthRoute);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Start value) start,
    required TResult Function(_ChangePlaybackType value) changePlaybackType,
    required TResult Function(_CancelPlayback value) cancelPlayback,
    required TResult Function(_GoToIndex value) goToIndex,
    required TResult Function(_Play value) play,
    required TResult Function(_Pause value) pause,
    required TResult Function(_ChangePlaybackSpeed value) changePlaybackSpeed,
    required TResult Function(_RestartPlayback value) restartPlayback,
    required TResult Function(_Dispose value) dispose,
  }) {
    return start(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Start value)? start,
    TResult? Function(_ChangePlaybackType value)? changePlaybackType,
    TResult? Function(_CancelPlayback value)? cancelPlayback,
    TResult? Function(_GoToIndex value)? goToIndex,
    TResult? Function(_Play value)? play,
    TResult? Function(_Pause value)? pause,
    TResult? Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult? Function(_RestartPlayback value)? restartPlayback,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return start?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Start value)? start,
    TResult Function(_ChangePlaybackType value)? changePlaybackType,
    TResult Function(_CancelPlayback value)? cancelPlayback,
    TResult Function(_GoToIndex value)? goToIndex,
    TResult Function(_Play value)? play,
    TResult Function(_Pause value)? pause,
    TResult Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult Function(_RestartPlayback value)? restartPlayback,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (start != null) {
      return start(this);
    }
    return orElse();
  }
}

abstract class _Start implements ControlVehicleEvent {
  const factory _Start({required final int lengthRoute}) = _$StartImpl;

  int get lengthRoute;
  @JsonKey(ignore: true)
  _$$StartImplCopyWith<_$StartImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangePlaybackTypeImplCopyWith<$Res> {
  factory _$$ChangePlaybackTypeImplCopyWith(_$ChangePlaybackTypeImpl value,
          $Res Function(_$ChangePlaybackTypeImpl) then) =
      __$$ChangePlaybackTypeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({ControlVehicleState newPlaybackState});

  $ControlVehicleStateCopyWith<$Res> get newPlaybackState;
}

/// @nodoc
class __$$ChangePlaybackTypeImplCopyWithImpl<$Res>
    extends _$ControlVehicleEventCopyWithImpl<$Res, _$ChangePlaybackTypeImpl>
    implements _$$ChangePlaybackTypeImplCopyWith<$Res> {
  __$$ChangePlaybackTypeImplCopyWithImpl(_$ChangePlaybackTypeImpl _value,
      $Res Function(_$ChangePlaybackTypeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newPlaybackState = null,
  }) {
    return _then(_$ChangePlaybackTypeImpl(
      newPlaybackState: null == newPlaybackState
          ? _value.newPlaybackState
          : newPlaybackState // ignore: cast_nullable_to_non_nullable
              as ControlVehicleState,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $ControlVehicleStateCopyWith<$Res> get newPlaybackState {
    return $ControlVehicleStateCopyWith<$Res>(_value.newPlaybackState, (value) {
      return _then(_value.copyWith(newPlaybackState: value));
    });
  }
}

/// @nodoc

class _$ChangePlaybackTypeImpl implements _ChangePlaybackType {
  const _$ChangePlaybackTypeImpl({required this.newPlaybackState});

//
  @override
  final ControlVehicleState newPlaybackState;

  @override
  String toString() {
    return 'ControlVehicleEvent.changePlaybackType(newPlaybackState: $newPlaybackState)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangePlaybackTypeImpl &&
            (identical(other.newPlaybackState, newPlaybackState) ||
                other.newPlaybackState == newPlaybackState));
  }

  @override
  int get hashCode => Object.hash(runtimeType, newPlaybackState);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangePlaybackTypeImplCopyWith<_$ChangePlaybackTypeImpl> get copyWith =>
      __$$ChangePlaybackTypeImplCopyWithImpl<_$ChangePlaybackTypeImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int lengthRoute) start,
    required TResult Function(ControlVehicleState newPlaybackState)
        changePlaybackType,
    required TResult Function() cancelPlayback,
    required TResult Function(bool selectedIndex, bool manuelSliding, int index)
        goToIndex,
    required TResult Function() play,
    required TResult Function() pause,
    required TResult Function() changePlaybackSpeed,
    required TResult Function() restartPlayback,
    required TResult Function() dispose,
  }) {
    return changePlaybackType(newPlaybackState);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int lengthRoute)? start,
    TResult? Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult? Function()? cancelPlayback,
    TResult? Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult? Function()? play,
    TResult? Function()? pause,
    TResult? Function()? changePlaybackSpeed,
    TResult? Function()? restartPlayback,
    TResult? Function()? dispose,
  }) {
    return changePlaybackType?.call(newPlaybackState);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int lengthRoute)? start,
    TResult Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult Function()? cancelPlayback,
    TResult Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult Function()? play,
    TResult Function()? pause,
    TResult Function()? changePlaybackSpeed,
    TResult Function()? restartPlayback,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (changePlaybackType != null) {
      return changePlaybackType(newPlaybackState);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Start value) start,
    required TResult Function(_ChangePlaybackType value) changePlaybackType,
    required TResult Function(_CancelPlayback value) cancelPlayback,
    required TResult Function(_GoToIndex value) goToIndex,
    required TResult Function(_Play value) play,
    required TResult Function(_Pause value) pause,
    required TResult Function(_ChangePlaybackSpeed value) changePlaybackSpeed,
    required TResult Function(_RestartPlayback value) restartPlayback,
    required TResult Function(_Dispose value) dispose,
  }) {
    return changePlaybackType(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Start value)? start,
    TResult? Function(_ChangePlaybackType value)? changePlaybackType,
    TResult? Function(_CancelPlayback value)? cancelPlayback,
    TResult? Function(_GoToIndex value)? goToIndex,
    TResult? Function(_Play value)? play,
    TResult? Function(_Pause value)? pause,
    TResult? Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult? Function(_RestartPlayback value)? restartPlayback,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return changePlaybackType?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Start value)? start,
    TResult Function(_ChangePlaybackType value)? changePlaybackType,
    TResult Function(_CancelPlayback value)? cancelPlayback,
    TResult Function(_GoToIndex value)? goToIndex,
    TResult Function(_Play value)? play,
    TResult Function(_Pause value)? pause,
    TResult Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult Function(_RestartPlayback value)? restartPlayback,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (changePlaybackType != null) {
      return changePlaybackType(this);
    }
    return orElse();
  }
}

abstract class _ChangePlaybackType implements ControlVehicleEvent {
  const factory _ChangePlaybackType(
          {required final ControlVehicleState newPlaybackState}) =
      _$ChangePlaybackTypeImpl;

//
  ControlVehicleState get newPlaybackState;
  @JsonKey(ignore: true)
  _$$ChangePlaybackTypeImplCopyWith<_$ChangePlaybackTypeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CancelPlaybackImplCopyWith<$Res> {
  factory _$$CancelPlaybackImplCopyWith(_$CancelPlaybackImpl value,
          $Res Function(_$CancelPlaybackImpl) then) =
      __$$CancelPlaybackImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CancelPlaybackImplCopyWithImpl<$Res>
    extends _$ControlVehicleEventCopyWithImpl<$Res, _$CancelPlaybackImpl>
    implements _$$CancelPlaybackImplCopyWith<$Res> {
  __$$CancelPlaybackImplCopyWithImpl(
      _$CancelPlaybackImpl _value, $Res Function(_$CancelPlaybackImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$CancelPlaybackImpl implements _CancelPlayback {
  const _$CancelPlaybackImpl();

  @override
  String toString() {
    return 'ControlVehicleEvent.cancelPlayback()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CancelPlaybackImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int lengthRoute) start,
    required TResult Function(ControlVehicleState newPlaybackState)
        changePlaybackType,
    required TResult Function() cancelPlayback,
    required TResult Function(bool selectedIndex, bool manuelSliding, int index)
        goToIndex,
    required TResult Function() play,
    required TResult Function() pause,
    required TResult Function() changePlaybackSpeed,
    required TResult Function() restartPlayback,
    required TResult Function() dispose,
  }) {
    return cancelPlayback();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int lengthRoute)? start,
    TResult? Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult? Function()? cancelPlayback,
    TResult? Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult? Function()? play,
    TResult? Function()? pause,
    TResult? Function()? changePlaybackSpeed,
    TResult? Function()? restartPlayback,
    TResult? Function()? dispose,
  }) {
    return cancelPlayback?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int lengthRoute)? start,
    TResult Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult Function()? cancelPlayback,
    TResult Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult Function()? play,
    TResult Function()? pause,
    TResult Function()? changePlaybackSpeed,
    TResult Function()? restartPlayback,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (cancelPlayback != null) {
      return cancelPlayback();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Start value) start,
    required TResult Function(_ChangePlaybackType value) changePlaybackType,
    required TResult Function(_CancelPlayback value) cancelPlayback,
    required TResult Function(_GoToIndex value) goToIndex,
    required TResult Function(_Play value) play,
    required TResult Function(_Pause value) pause,
    required TResult Function(_ChangePlaybackSpeed value) changePlaybackSpeed,
    required TResult Function(_RestartPlayback value) restartPlayback,
    required TResult Function(_Dispose value) dispose,
  }) {
    return cancelPlayback(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Start value)? start,
    TResult? Function(_ChangePlaybackType value)? changePlaybackType,
    TResult? Function(_CancelPlayback value)? cancelPlayback,
    TResult? Function(_GoToIndex value)? goToIndex,
    TResult? Function(_Play value)? play,
    TResult? Function(_Pause value)? pause,
    TResult? Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult? Function(_RestartPlayback value)? restartPlayback,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return cancelPlayback?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Start value)? start,
    TResult Function(_ChangePlaybackType value)? changePlaybackType,
    TResult Function(_CancelPlayback value)? cancelPlayback,
    TResult Function(_GoToIndex value)? goToIndex,
    TResult Function(_Play value)? play,
    TResult Function(_Pause value)? pause,
    TResult Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult Function(_RestartPlayback value)? restartPlayback,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (cancelPlayback != null) {
      return cancelPlayback(this);
    }
    return orElse();
  }
}

abstract class _CancelPlayback implements ControlVehicleEvent {
  const factory _CancelPlayback() = _$CancelPlaybackImpl;
}

/// @nodoc
abstract class _$$GoToIndexImplCopyWith<$Res> {
  factory _$$GoToIndexImplCopyWith(
          _$GoToIndexImpl value, $Res Function(_$GoToIndexImpl) then) =
      __$$GoToIndexImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool selectedIndex, bool manuelSliding, int index});
}

/// @nodoc
class __$$GoToIndexImplCopyWithImpl<$Res>
    extends _$ControlVehicleEventCopyWithImpl<$Res, _$GoToIndexImpl>
    implements _$$GoToIndexImplCopyWith<$Res> {
  __$$GoToIndexImplCopyWithImpl(
      _$GoToIndexImpl _value, $Res Function(_$GoToIndexImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedIndex = null,
    Object? manuelSliding = null,
    Object? index = null,
  }) {
    return _then(_$GoToIndexImpl(
      selectedIndex: null == selectedIndex
          ? _value.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as bool,
      manuelSliding: null == manuelSliding
          ? _value.manuelSliding
          : manuelSliding // ignore: cast_nullable_to_non_nullable
              as bool,
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$GoToIndexImpl implements _GoToIndex {
  const _$GoToIndexImpl(
      {this.selectedIndex = false,
      this.manuelSliding = false,
      required this.index});

//
  @override
  @JsonKey()
  final bool selectedIndex;
  @override
  @JsonKey()
  final bool manuelSliding;
  @override
  final int index;

  @override
  String toString() {
    return 'ControlVehicleEvent.goToIndex(selectedIndex: $selectedIndex, manuelSliding: $manuelSliding, index: $index)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GoToIndexImpl &&
            (identical(other.selectedIndex, selectedIndex) ||
                other.selectedIndex == selectedIndex) &&
            (identical(other.manuelSliding, manuelSliding) ||
                other.manuelSliding == manuelSliding) &&
            (identical(other.index, index) || other.index == index));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, selectedIndex, manuelSliding, index);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GoToIndexImplCopyWith<_$GoToIndexImpl> get copyWith =>
      __$$GoToIndexImplCopyWithImpl<_$GoToIndexImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int lengthRoute) start,
    required TResult Function(ControlVehicleState newPlaybackState)
        changePlaybackType,
    required TResult Function() cancelPlayback,
    required TResult Function(bool selectedIndex, bool manuelSliding, int index)
        goToIndex,
    required TResult Function() play,
    required TResult Function() pause,
    required TResult Function() changePlaybackSpeed,
    required TResult Function() restartPlayback,
    required TResult Function() dispose,
  }) {
    return goToIndex(selectedIndex, manuelSliding, index);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int lengthRoute)? start,
    TResult? Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult? Function()? cancelPlayback,
    TResult? Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult? Function()? play,
    TResult? Function()? pause,
    TResult? Function()? changePlaybackSpeed,
    TResult? Function()? restartPlayback,
    TResult? Function()? dispose,
  }) {
    return goToIndex?.call(selectedIndex, manuelSliding, index);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int lengthRoute)? start,
    TResult Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult Function()? cancelPlayback,
    TResult Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult Function()? play,
    TResult Function()? pause,
    TResult Function()? changePlaybackSpeed,
    TResult Function()? restartPlayback,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (goToIndex != null) {
      return goToIndex(selectedIndex, manuelSliding, index);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Start value) start,
    required TResult Function(_ChangePlaybackType value) changePlaybackType,
    required TResult Function(_CancelPlayback value) cancelPlayback,
    required TResult Function(_GoToIndex value) goToIndex,
    required TResult Function(_Play value) play,
    required TResult Function(_Pause value) pause,
    required TResult Function(_ChangePlaybackSpeed value) changePlaybackSpeed,
    required TResult Function(_RestartPlayback value) restartPlayback,
    required TResult Function(_Dispose value) dispose,
  }) {
    return goToIndex(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Start value)? start,
    TResult? Function(_ChangePlaybackType value)? changePlaybackType,
    TResult? Function(_CancelPlayback value)? cancelPlayback,
    TResult? Function(_GoToIndex value)? goToIndex,
    TResult? Function(_Play value)? play,
    TResult? Function(_Pause value)? pause,
    TResult? Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult? Function(_RestartPlayback value)? restartPlayback,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return goToIndex?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Start value)? start,
    TResult Function(_ChangePlaybackType value)? changePlaybackType,
    TResult Function(_CancelPlayback value)? cancelPlayback,
    TResult Function(_GoToIndex value)? goToIndex,
    TResult Function(_Play value)? play,
    TResult Function(_Pause value)? pause,
    TResult Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult Function(_RestartPlayback value)? restartPlayback,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (goToIndex != null) {
      return goToIndex(this);
    }
    return orElse();
  }
}

abstract class _GoToIndex implements ControlVehicleEvent {
  const factory _GoToIndex(
      {final bool selectedIndex,
      final bool manuelSliding,
      required final int index}) = _$GoToIndexImpl;

//
  bool get selectedIndex;
  bool get manuelSliding;
  int get index;
  @JsonKey(ignore: true)
  _$$GoToIndexImplCopyWith<_$GoToIndexImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PlayImplCopyWith<$Res> {
  factory _$$PlayImplCopyWith(
          _$PlayImpl value, $Res Function(_$PlayImpl) then) =
      __$$PlayImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PlayImplCopyWithImpl<$Res>
    extends _$ControlVehicleEventCopyWithImpl<$Res, _$PlayImpl>
    implements _$$PlayImplCopyWith<$Res> {
  __$$PlayImplCopyWithImpl(_$PlayImpl _value, $Res Function(_$PlayImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$PlayImpl implements _Play {
  const _$PlayImpl();

  @override
  String toString() {
    return 'ControlVehicleEvent.play()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PlayImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int lengthRoute) start,
    required TResult Function(ControlVehicleState newPlaybackState)
        changePlaybackType,
    required TResult Function() cancelPlayback,
    required TResult Function(bool selectedIndex, bool manuelSliding, int index)
        goToIndex,
    required TResult Function() play,
    required TResult Function() pause,
    required TResult Function() changePlaybackSpeed,
    required TResult Function() restartPlayback,
    required TResult Function() dispose,
  }) {
    return play();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int lengthRoute)? start,
    TResult? Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult? Function()? cancelPlayback,
    TResult? Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult? Function()? play,
    TResult? Function()? pause,
    TResult? Function()? changePlaybackSpeed,
    TResult? Function()? restartPlayback,
    TResult? Function()? dispose,
  }) {
    return play?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int lengthRoute)? start,
    TResult Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult Function()? cancelPlayback,
    TResult Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult Function()? play,
    TResult Function()? pause,
    TResult Function()? changePlaybackSpeed,
    TResult Function()? restartPlayback,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (play != null) {
      return play();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Start value) start,
    required TResult Function(_ChangePlaybackType value) changePlaybackType,
    required TResult Function(_CancelPlayback value) cancelPlayback,
    required TResult Function(_GoToIndex value) goToIndex,
    required TResult Function(_Play value) play,
    required TResult Function(_Pause value) pause,
    required TResult Function(_ChangePlaybackSpeed value) changePlaybackSpeed,
    required TResult Function(_RestartPlayback value) restartPlayback,
    required TResult Function(_Dispose value) dispose,
  }) {
    return play(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Start value)? start,
    TResult? Function(_ChangePlaybackType value)? changePlaybackType,
    TResult? Function(_CancelPlayback value)? cancelPlayback,
    TResult? Function(_GoToIndex value)? goToIndex,
    TResult? Function(_Play value)? play,
    TResult? Function(_Pause value)? pause,
    TResult? Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult? Function(_RestartPlayback value)? restartPlayback,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return play?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Start value)? start,
    TResult Function(_ChangePlaybackType value)? changePlaybackType,
    TResult Function(_CancelPlayback value)? cancelPlayback,
    TResult Function(_GoToIndex value)? goToIndex,
    TResult Function(_Play value)? play,
    TResult Function(_Pause value)? pause,
    TResult Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult Function(_RestartPlayback value)? restartPlayback,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (play != null) {
      return play(this);
    }
    return orElse();
  }
}

abstract class _Play implements ControlVehicleEvent {
  const factory _Play() = _$PlayImpl;
}

/// @nodoc
abstract class _$$PauseImplCopyWith<$Res> {
  factory _$$PauseImplCopyWith(
          _$PauseImpl value, $Res Function(_$PauseImpl) then) =
      __$$PauseImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PauseImplCopyWithImpl<$Res>
    extends _$ControlVehicleEventCopyWithImpl<$Res, _$PauseImpl>
    implements _$$PauseImplCopyWith<$Res> {
  __$$PauseImplCopyWithImpl(
      _$PauseImpl _value, $Res Function(_$PauseImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$PauseImpl implements _Pause {
  const _$PauseImpl();

  @override
  String toString() {
    return 'ControlVehicleEvent.pause()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PauseImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int lengthRoute) start,
    required TResult Function(ControlVehicleState newPlaybackState)
        changePlaybackType,
    required TResult Function() cancelPlayback,
    required TResult Function(bool selectedIndex, bool manuelSliding, int index)
        goToIndex,
    required TResult Function() play,
    required TResult Function() pause,
    required TResult Function() changePlaybackSpeed,
    required TResult Function() restartPlayback,
    required TResult Function() dispose,
  }) {
    return pause();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int lengthRoute)? start,
    TResult? Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult? Function()? cancelPlayback,
    TResult? Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult? Function()? play,
    TResult? Function()? pause,
    TResult? Function()? changePlaybackSpeed,
    TResult? Function()? restartPlayback,
    TResult? Function()? dispose,
  }) {
    return pause?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int lengthRoute)? start,
    TResult Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult Function()? cancelPlayback,
    TResult Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult Function()? play,
    TResult Function()? pause,
    TResult Function()? changePlaybackSpeed,
    TResult Function()? restartPlayback,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (pause != null) {
      return pause();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Start value) start,
    required TResult Function(_ChangePlaybackType value) changePlaybackType,
    required TResult Function(_CancelPlayback value) cancelPlayback,
    required TResult Function(_GoToIndex value) goToIndex,
    required TResult Function(_Play value) play,
    required TResult Function(_Pause value) pause,
    required TResult Function(_ChangePlaybackSpeed value) changePlaybackSpeed,
    required TResult Function(_RestartPlayback value) restartPlayback,
    required TResult Function(_Dispose value) dispose,
  }) {
    return pause(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Start value)? start,
    TResult? Function(_ChangePlaybackType value)? changePlaybackType,
    TResult? Function(_CancelPlayback value)? cancelPlayback,
    TResult? Function(_GoToIndex value)? goToIndex,
    TResult? Function(_Play value)? play,
    TResult? Function(_Pause value)? pause,
    TResult? Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult? Function(_RestartPlayback value)? restartPlayback,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return pause?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Start value)? start,
    TResult Function(_ChangePlaybackType value)? changePlaybackType,
    TResult Function(_CancelPlayback value)? cancelPlayback,
    TResult Function(_GoToIndex value)? goToIndex,
    TResult Function(_Play value)? play,
    TResult Function(_Pause value)? pause,
    TResult Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult Function(_RestartPlayback value)? restartPlayback,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (pause != null) {
      return pause(this);
    }
    return orElse();
  }
}

abstract class _Pause implements ControlVehicleEvent {
  const factory _Pause() = _$PauseImpl;
}

/// @nodoc
abstract class _$$ChangePlaybackSpeedImplCopyWith<$Res> {
  factory _$$ChangePlaybackSpeedImplCopyWith(_$ChangePlaybackSpeedImpl value,
          $Res Function(_$ChangePlaybackSpeedImpl) then) =
      __$$ChangePlaybackSpeedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ChangePlaybackSpeedImplCopyWithImpl<$Res>
    extends _$ControlVehicleEventCopyWithImpl<$Res, _$ChangePlaybackSpeedImpl>
    implements _$$ChangePlaybackSpeedImplCopyWith<$Res> {
  __$$ChangePlaybackSpeedImplCopyWithImpl(_$ChangePlaybackSpeedImpl _value,
      $Res Function(_$ChangePlaybackSpeedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ChangePlaybackSpeedImpl implements _ChangePlaybackSpeed {
  const _$ChangePlaybackSpeedImpl();

  @override
  String toString() {
    return 'ControlVehicleEvent.changePlaybackSpeed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangePlaybackSpeedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int lengthRoute) start,
    required TResult Function(ControlVehicleState newPlaybackState)
        changePlaybackType,
    required TResult Function() cancelPlayback,
    required TResult Function(bool selectedIndex, bool manuelSliding, int index)
        goToIndex,
    required TResult Function() play,
    required TResult Function() pause,
    required TResult Function() changePlaybackSpeed,
    required TResult Function() restartPlayback,
    required TResult Function() dispose,
  }) {
    return changePlaybackSpeed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int lengthRoute)? start,
    TResult? Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult? Function()? cancelPlayback,
    TResult? Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult? Function()? play,
    TResult? Function()? pause,
    TResult? Function()? changePlaybackSpeed,
    TResult? Function()? restartPlayback,
    TResult? Function()? dispose,
  }) {
    return changePlaybackSpeed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int lengthRoute)? start,
    TResult Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult Function()? cancelPlayback,
    TResult Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult Function()? play,
    TResult Function()? pause,
    TResult Function()? changePlaybackSpeed,
    TResult Function()? restartPlayback,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (changePlaybackSpeed != null) {
      return changePlaybackSpeed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Start value) start,
    required TResult Function(_ChangePlaybackType value) changePlaybackType,
    required TResult Function(_CancelPlayback value) cancelPlayback,
    required TResult Function(_GoToIndex value) goToIndex,
    required TResult Function(_Play value) play,
    required TResult Function(_Pause value) pause,
    required TResult Function(_ChangePlaybackSpeed value) changePlaybackSpeed,
    required TResult Function(_RestartPlayback value) restartPlayback,
    required TResult Function(_Dispose value) dispose,
  }) {
    return changePlaybackSpeed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Start value)? start,
    TResult? Function(_ChangePlaybackType value)? changePlaybackType,
    TResult? Function(_CancelPlayback value)? cancelPlayback,
    TResult? Function(_GoToIndex value)? goToIndex,
    TResult? Function(_Play value)? play,
    TResult? Function(_Pause value)? pause,
    TResult? Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult? Function(_RestartPlayback value)? restartPlayback,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return changePlaybackSpeed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Start value)? start,
    TResult Function(_ChangePlaybackType value)? changePlaybackType,
    TResult Function(_CancelPlayback value)? cancelPlayback,
    TResult Function(_GoToIndex value)? goToIndex,
    TResult Function(_Play value)? play,
    TResult Function(_Pause value)? pause,
    TResult Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult Function(_RestartPlayback value)? restartPlayback,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (changePlaybackSpeed != null) {
      return changePlaybackSpeed(this);
    }
    return orElse();
  }
}

abstract class _ChangePlaybackSpeed implements ControlVehicleEvent {
  const factory _ChangePlaybackSpeed() = _$ChangePlaybackSpeedImpl;
}

/// @nodoc
abstract class _$$RestartPlaybackImplCopyWith<$Res> {
  factory _$$RestartPlaybackImplCopyWith(_$RestartPlaybackImpl value,
          $Res Function(_$RestartPlaybackImpl) then) =
      __$$RestartPlaybackImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RestartPlaybackImplCopyWithImpl<$Res>
    extends _$ControlVehicleEventCopyWithImpl<$Res, _$RestartPlaybackImpl>
    implements _$$RestartPlaybackImplCopyWith<$Res> {
  __$$RestartPlaybackImplCopyWithImpl(
      _$RestartPlaybackImpl _value, $Res Function(_$RestartPlaybackImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$RestartPlaybackImpl implements _RestartPlayback {
  const _$RestartPlaybackImpl();

  @override
  String toString() {
    return 'ControlVehicleEvent.restartPlayback()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RestartPlaybackImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int lengthRoute) start,
    required TResult Function(ControlVehicleState newPlaybackState)
        changePlaybackType,
    required TResult Function() cancelPlayback,
    required TResult Function(bool selectedIndex, bool manuelSliding, int index)
        goToIndex,
    required TResult Function() play,
    required TResult Function() pause,
    required TResult Function() changePlaybackSpeed,
    required TResult Function() restartPlayback,
    required TResult Function() dispose,
  }) {
    return restartPlayback();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int lengthRoute)? start,
    TResult? Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult? Function()? cancelPlayback,
    TResult? Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult? Function()? play,
    TResult? Function()? pause,
    TResult? Function()? changePlaybackSpeed,
    TResult? Function()? restartPlayback,
    TResult? Function()? dispose,
  }) {
    return restartPlayback?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int lengthRoute)? start,
    TResult Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult Function()? cancelPlayback,
    TResult Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult Function()? play,
    TResult Function()? pause,
    TResult Function()? changePlaybackSpeed,
    TResult Function()? restartPlayback,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (restartPlayback != null) {
      return restartPlayback();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Start value) start,
    required TResult Function(_ChangePlaybackType value) changePlaybackType,
    required TResult Function(_CancelPlayback value) cancelPlayback,
    required TResult Function(_GoToIndex value) goToIndex,
    required TResult Function(_Play value) play,
    required TResult Function(_Pause value) pause,
    required TResult Function(_ChangePlaybackSpeed value) changePlaybackSpeed,
    required TResult Function(_RestartPlayback value) restartPlayback,
    required TResult Function(_Dispose value) dispose,
  }) {
    return restartPlayback(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Start value)? start,
    TResult? Function(_ChangePlaybackType value)? changePlaybackType,
    TResult? Function(_CancelPlayback value)? cancelPlayback,
    TResult? Function(_GoToIndex value)? goToIndex,
    TResult? Function(_Play value)? play,
    TResult? Function(_Pause value)? pause,
    TResult? Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult? Function(_RestartPlayback value)? restartPlayback,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return restartPlayback?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Start value)? start,
    TResult Function(_ChangePlaybackType value)? changePlaybackType,
    TResult Function(_CancelPlayback value)? cancelPlayback,
    TResult Function(_GoToIndex value)? goToIndex,
    TResult Function(_Play value)? play,
    TResult Function(_Pause value)? pause,
    TResult Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult Function(_RestartPlayback value)? restartPlayback,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (restartPlayback != null) {
      return restartPlayback(this);
    }
    return orElse();
  }
}

abstract class _RestartPlayback implements ControlVehicleEvent {
  const factory _RestartPlayback() = _$RestartPlaybackImpl;
}

/// @nodoc
abstract class _$$DisposeImplCopyWith<$Res> {
  factory _$$DisposeImplCopyWith(
          _$DisposeImpl value, $Res Function(_$DisposeImpl) then) =
      __$$DisposeImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DisposeImplCopyWithImpl<$Res>
    extends _$ControlVehicleEventCopyWithImpl<$Res, _$DisposeImpl>
    implements _$$DisposeImplCopyWith<$Res> {
  __$$DisposeImplCopyWithImpl(
      _$DisposeImpl _value, $Res Function(_$DisposeImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DisposeImpl implements _Dispose {
  const _$DisposeImpl();

  @override
  String toString() {
    return 'ControlVehicleEvent.dispose()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DisposeImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int lengthRoute) start,
    required TResult Function(ControlVehicleState newPlaybackState)
        changePlaybackType,
    required TResult Function() cancelPlayback,
    required TResult Function(bool selectedIndex, bool manuelSliding, int index)
        goToIndex,
    required TResult Function() play,
    required TResult Function() pause,
    required TResult Function() changePlaybackSpeed,
    required TResult Function() restartPlayback,
    required TResult Function() dispose,
  }) {
    return dispose();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int lengthRoute)? start,
    TResult? Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult? Function()? cancelPlayback,
    TResult? Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult? Function()? play,
    TResult? Function()? pause,
    TResult? Function()? changePlaybackSpeed,
    TResult? Function()? restartPlayback,
    TResult? Function()? dispose,
  }) {
    return dispose?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int lengthRoute)? start,
    TResult Function(ControlVehicleState newPlaybackState)? changePlaybackType,
    TResult Function()? cancelPlayback,
    TResult Function(bool selectedIndex, bool manuelSliding, int index)?
        goToIndex,
    TResult Function()? play,
    TResult Function()? pause,
    TResult Function()? changePlaybackSpeed,
    TResult Function()? restartPlayback,
    TResult Function()? dispose,
    required TResult orElse(),
  }) {
    if (dispose != null) {
      return dispose();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Start value) start,
    required TResult Function(_ChangePlaybackType value) changePlaybackType,
    required TResult Function(_CancelPlayback value) cancelPlayback,
    required TResult Function(_GoToIndex value) goToIndex,
    required TResult Function(_Play value) play,
    required TResult Function(_Pause value) pause,
    required TResult Function(_ChangePlaybackSpeed value) changePlaybackSpeed,
    required TResult Function(_RestartPlayback value) restartPlayback,
    required TResult Function(_Dispose value) dispose,
  }) {
    return dispose(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Start value)? start,
    TResult? Function(_ChangePlaybackType value)? changePlaybackType,
    TResult? Function(_CancelPlayback value)? cancelPlayback,
    TResult? Function(_GoToIndex value)? goToIndex,
    TResult? Function(_Play value)? play,
    TResult? Function(_Pause value)? pause,
    TResult? Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult? Function(_RestartPlayback value)? restartPlayback,
    TResult? Function(_Dispose value)? dispose,
  }) {
    return dispose?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Start value)? start,
    TResult Function(_ChangePlaybackType value)? changePlaybackType,
    TResult Function(_CancelPlayback value)? cancelPlayback,
    TResult Function(_GoToIndex value)? goToIndex,
    TResult Function(_Play value)? play,
    TResult Function(_Pause value)? pause,
    TResult Function(_ChangePlaybackSpeed value)? changePlaybackSpeed,
    TResult Function(_RestartPlayback value)? restartPlayback,
    TResult Function(_Dispose value)? dispose,
    required TResult orElse(),
  }) {
    if (dispose != null) {
      return dispose(this);
    }
    return orElse();
  }
}

abstract class _Dispose implements ControlVehicleEvent {
  const factory _Dispose() = _$DisposeImpl;
}

/// @nodoc
mixin _$ControlVehicleState {
  int get index => throw _privateConstructorUsedError;
  RouteReplayPlayerPlaybackSpeed get playbackSpeed =>
      throw _privateConstructorUsedError;
  int get lengthRoute => throw _privateConstructorUsedError;
  RouteReplayPlayerPlaybackState get playbackState =>
      throw _privateConstructorUsedError;
  bool get selectedIndex => throw _privateConstructorUsedError;
  bool get manuelSliding => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ControlVehicleStateCopyWith<ControlVehicleState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ControlVehicleStateCopyWith<$Res> {
  factory $ControlVehicleStateCopyWith(
          ControlVehicleState value, $Res Function(ControlVehicleState) then) =
      _$ControlVehicleStateCopyWithImpl<$Res, ControlVehicleState>;
  @useResult
  $Res call(
      {int index,
      RouteReplayPlayerPlaybackSpeed playbackSpeed,
      int lengthRoute,
      RouteReplayPlayerPlaybackState playbackState,
      bool selectedIndex,
      bool manuelSliding});
}

/// @nodoc
class _$ControlVehicleStateCopyWithImpl<$Res, $Val extends ControlVehicleState>
    implements $ControlVehicleStateCopyWith<$Res> {
  _$ControlVehicleStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? playbackSpeed = null,
    Object? lengthRoute = null,
    Object? playbackState = null,
    Object? selectedIndex = null,
    Object? manuelSliding = null,
  }) {
    return _then(_value.copyWith(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      playbackSpeed: null == playbackSpeed
          ? _value.playbackSpeed
          : playbackSpeed // ignore: cast_nullable_to_non_nullable
              as RouteReplayPlayerPlaybackSpeed,
      lengthRoute: null == lengthRoute
          ? _value.lengthRoute
          : lengthRoute // ignore: cast_nullable_to_non_nullable
              as int,
      playbackState: null == playbackState
          ? _value.playbackState
          : playbackState // ignore: cast_nullable_to_non_nullable
              as RouteReplayPlayerPlaybackState,
      selectedIndex: null == selectedIndex
          ? _value.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as bool,
      manuelSliding: null == manuelSliding
          ? _value.manuelSliding
          : manuelSliding // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ControlVehicleStateImplCopyWith<$Res>
    implements $ControlVehicleStateCopyWith<$Res> {
  factory _$$ControlVehicleStateImplCopyWith(_$ControlVehicleStateImpl value,
          $Res Function(_$ControlVehicleStateImpl) then) =
      __$$ControlVehicleStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int index,
      RouteReplayPlayerPlaybackSpeed playbackSpeed,
      int lengthRoute,
      RouteReplayPlayerPlaybackState playbackState,
      bool selectedIndex,
      bool manuelSliding});
}

/// @nodoc
class __$$ControlVehicleStateImplCopyWithImpl<$Res>
    extends _$ControlVehicleStateCopyWithImpl<$Res, _$ControlVehicleStateImpl>
    implements _$$ControlVehicleStateImplCopyWith<$Res> {
  __$$ControlVehicleStateImplCopyWithImpl(_$ControlVehicleStateImpl _value,
      $Res Function(_$ControlVehicleStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? playbackSpeed = null,
    Object? lengthRoute = null,
    Object? playbackState = null,
    Object? selectedIndex = null,
    Object? manuelSliding = null,
  }) {
    return _then(_$ControlVehicleStateImpl(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      playbackSpeed: null == playbackSpeed
          ? _value.playbackSpeed
          : playbackSpeed // ignore: cast_nullable_to_non_nullable
              as RouteReplayPlayerPlaybackSpeed,
      lengthRoute: null == lengthRoute
          ? _value.lengthRoute
          : lengthRoute // ignore: cast_nullable_to_non_nullable
              as int,
      playbackState: null == playbackState
          ? _value.playbackState
          : playbackState // ignore: cast_nullable_to_non_nullable
              as RouteReplayPlayerPlaybackState,
      selectedIndex: null == selectedIndex
          ? _value.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as bool,
      manuelSliding: null == manuelSliding
          ? _value.manuelSliding
          : manuelSliding // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ControlVehicleStateImpl implements _ControlVehicleState {
  const _$ControlVehicleStateImpl(
      {required this.index,
      required this.playbackSpeed,
      required this.lengthRoute,
      required this.playbackState,
      required this.selectedIndex,
      required this.manuelSliding});

  @override
  final int index;
  @override
  final RouteReplayPlayerPlaybackSpeed playbackSpeed;
  @override
  final int lengthRoute;
  @override
  final RouteReplayPlayerPlaybackState playbackState;
  @override
  final bool selectedIndex;
  @override
  final bool manuelSliding;

  @override
  String toString() {
    return 'ControlVehicleState(index: $index, playbackSpeed: $playbackSpeed, lengthRoute: $lengthRoute, playbackState: $playbackState, selectedIndex: $selectedIndex, manuelSliding: $manuelSliding)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ControlVehicleStateImpl &&
            (identical(other.index, index) || other.index == index) &&
            (identical(other.playbackSpeed, playbackSpeed) ||
                other.playbackSpeed == playbackSpeed) &&
            (identical(other.lengthRoute, lengthRoute) ||
                other.lengthRoute == lengthRoute) &&
            (identical(other.playbackState, playbackState) ||
                other.playbackState == playbackState) &&
            (identical(other.selectedIndex, selectedIndex) ||
                other.selectedIndex == selectedIndex) &&
            (identical(other.manuelSliding, manuelSliding) ||
                other.manuelSliding == manuelSliding));
  }

  @override
  int get hashCode => Object.hash(runtimeType, index, playbackSpeed,
      lengthRoute, playbackState, selectedIndex, manuelSliding);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ControlVehicleStateImplCopyWith<_$ControlVehicleStateImpl> get copyWith =>
      __$$ControlVehicleStateImplCopyWithImpl<_$ControlVehicleStateImpl>(
          this, _$identity);
}

abstract class _ControlVehicleState implements ControlVehicleState {
  const factory _ControlVehicleState(
      {required final int index,
      required final RouteReplayPlayerPlaybackSpeed playbackSpeed,
      required final int lengthRoute,
      required final RouteReplayPlayerPlaybackState playbackState,
      required final bool selectedIndex,
      required final bool manuelSliding}) = _$ControlVehicleStateImpl;

  @override
  int get index;
  @override
  RouteReplayPlayerPlaybackSpeed get playbackSpeed;
  @override
  int get lengthRoute;
  @override
  RouteReplayPlayerPlaybackState get playbackState;
  @override
  bool get selectedIndex;
  @override
  bool get manuelSliding;
  @override
  @JsonKey(ignore: true)
  _$$ControlVehicleStateImplCopyWith<_$ControlVehicleStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
