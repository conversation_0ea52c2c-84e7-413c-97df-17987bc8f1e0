// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'moving_map_history_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MovingMapHistoryEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isMovingMap) changeVarible,
    required TResult Function(bool isResetCluster) resetCluster,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isMovingMap)? changeVarible,
    TResult? Function(bool isResetCluster)? resetCluster,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isMovingMap)? changeVarible,
    TResult Function(bool isResetCluster)? resetCluster,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeVarible value) changeVarible,
    required TResult Function(_ResetCluster value) resetCluster,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeVarible value)? changeVarible,
    TResult? Function(_ResetCluster value)? resetCluster,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeVarible value)? changeVarible,
    TResult Function(_ResetCluster value)? resetCluster,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MovingMapHistoryEventCopyWith<$Res> {
  factory $MovingMapHistoryEventCopyWith(MovingMapHistoryEvent value,
          $Res Function(MovingMapHistoryEvent) then) =
      _$MovingMapHistoryEventCopyWithImpl<$Res, MovingMapHistoryEvent>;
}

/// @nodoc
class _$MovingMapHistoryEventCopyWithImpl<$Res,
        $Val extends MovingMapHistoryEvent>
    implements $MovingMapHistoryEventCopyWith<$Res> {
  _$MovingMapHistoryEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$ChangeVaribleImplCopyWith<$Res> {
  factory _$$ChangeVaribleImplCopyWith(
          _$ChangeVaribleImpl value, $Res Function(_$ChangeVaribleImpl) then) =
      __$$ChangeVaribleImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isMovingMap});
}

/// @nodoc
class __$$ChangeVaribleImplCopyWithImpl<$Res>
    extends _$MovingMapHistoryEventCopyWithImpl<$Res, _$ChangeVaribleImpl>
    implements _$$ChangeVaribleImplCopyWith<$Res> {
  __$$ChangeVaribleImplCopyWithImpl(
      _$ChangeVaribleImpl _value, $Res Function(_$ChangeVaribleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isMovingMap = null,
  }) {
    return _then(_$ChangeVaribleImpl(
      isMovingMap: null == isMovingMap
          ? _value.isMovingMap
          : isMovingMap // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ChangeVaribleImpl implements _ChangeVarible {
  const _$ChangeVaribleImpl({required this.isMovingMap});

  @override
  final bool isMovingMap;

  @override
  String toString() {
    return 'MovingMapHistoryEvent.changeVarible(isMovingMap: $isMovingMap)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeVaribleImpl &&
            (identical(other.isMovingMap, isMovingMap) ||
                other.isMovingMap == isMovingMap));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isMovingMap);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeVaribleImplCopyWith<_$ChangeVaribleImpl> get copyWith =>
      __$$ChangeVaribleImplCopyWithImpl<_$ChangeVaribleImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isMovingMap) changeVarible,
    required TResult Function(bool isResetCluster) resetCluster,
  }) {
    return changeVarible(isMovingMap);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isMovingMap)? changeVarible,
    TResult? Function(bool isResetCluster)? resetCluster,
  }) {
    return changeVarible?.call(isMovingMap);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isMovingMap)? changeVarible,
    TResult Function(bool isResetCluster)? resetCluster,
    required TResult orElse(),
  }) {
    if (changeVarible != null) {
      return changeVarible(isMovingMap);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeVarible value) changeVarible,
    required TResult Function(_ResetCluster value) resetCluster,
  }) {
    return changeVarible(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeVarible value)? changeVarible,
    TResult? Function(_ResetCluster value)? resetCluster,
  }) {
    return changeVarible?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeVarible value)? changeVarible,
    TResult Function(_ResetCluster value)? resetCluster,
    required TResult orElse(),
  }) {
    if (changeVarible != null) {
      return changeVarible(this);
    }
    return orElse();
  }
}

abstract class _ChangeVarible implements MovingMapHistoryEvent {
  const factory _ChangeVarible({required final bool isMovingMap}) =
      _$ChangeVaribleImpl;

  bool get isMovingMap;
  @JsonKey(ignore: true)
  _$$ChangeVaribleImplCopyWith<_$ChangeVaribleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetClusterImplCopyWith<$Res> {
  factory _$$ResetClusterImplCopyWith(
          _$ResetClusterImpl value, $Res Function(_$ResetClusterImpl) then) =
      __$$ResetClusterImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isResetCluster});
}

/// @nodoc
class __$$ResetClusterImplCopyWithImpl<$Res>
    extends _$MovingMapHistoryEventCopyWithImpl<$Res, _$ResetClusterImpl>
    implements _$$ResetClusterImplCopyWith<$Res> {
  __$$ResetClusterImplCopyWithImpl(
      _$ResetClusterImpl _value, $Res Function(_$ResetClusterImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isResetCluster = null,
  }) {
    return _then(_$ResetClusterImpl(
      isResetCluster: null == isResetCluster
          ? _value.isResetCluster
          : isResetCluster // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ResetClusterImpl implements _ResetCluster {
  const _$ResetClusterImpl({required this.isResetCluster});

  @override
  final bool isResetCluster;

  @override
  String toString() {
    return 'MovingMapHistoryEvent.resetCluster(isResetCluster: $isResetCluster)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResetClusterImpl &&
            (identical(other.isResetCluster, isResetCluster) ||
                other.isResetCluster == isResetCluster));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isResetCluster);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ResetClusterImplCopyWith<_$ResetClusterImpl> get copyWith =>
      __$$ResetClusterImplCopyWithImpl<_$ResetClusterImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isMovingMap) changeVarible,
    required TResult Function(bool isResetCluster) resetCluster,
  }) {
    return resetCluster(isResetCluster);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isMovingMap)? changeVarible,
    TResult? Function(bool isResetCluster)? resetCluster,
  }) {
    return resetCluster?.call(isResetCluster);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isMovingMap)? changeVarible,
    TResult Function(bool isResetCluster)? resetCluster,
    required TResult orElse(),
  }) {
    if (resetCluster != null) {
      return resetCluster(isResetCluster);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeVarible value) changeVarible,
    required TResult Function(_ResetCluster value) resetCluster,
  }) {
    return resetCluster(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeVarible value)? changeVarible,
    TResult? Function(_ResetCluster value)? resetCluster,
  }) {
    return resetCluster?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeVarible value)? changeVarible,
    TResult Function(_ResetCluster value)? resetCluster,
    required TResult orElse(),
  }) {
    if (resetCluster != null) {
      return resetCluster(this);
    }
    return orElse();
  }
}

abstract class _ResetCluster implements MovingMapHistoryEvent {
  const factory _ResetCluster({required final bool isResetCluster}) =
      _$ResetClusterImpl;

  bool get isResetCluster;
  @JsonKey(ignore: true)
  _$$ResetClusterImplCopyWith<_$ResetClusterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$MovingMapHistoryState {
  bool get isMovingMap => throw _privateConstructorUsedError;
  bool get isResetCluster => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MovingMapHistoryStateCopyWith<MovingMapHistoryState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MovingMapHistoryStateCopyWith<$Res> {
  factory $MovingMapHistoryStateCopyWith(MovingMapHistoryState value,
          $Res Function(MovingMapHistoryState) then) =
      _$MovingMapHistoryStateCopyWithImpl<$Res, MovingMapHistoryState>;
  @useResult
  $Res call({bool isMovingMap, bool isResetCluster});
}

/// @nodoc
class _$MovingMapHistoryStateCopyWithImpl<$Res,
        $Val extends MovingMapHistoryState>
    implements $MovingMapHistoryStateCopyWith<$Res> {
  _$MovingMapHistoryStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isMovingMap = null,
    Object? isResetCluster = null,
  }) {
    return _then(_value.copyWith(
      isMovingMap: null == isMovingMap
          ? _value.isMovingMap
          : isMovingMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isResetCluster: null == isResetCluster
          ? _value.isResetCluster
          : isResetCluster // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MovingMapHistoryStateImplCopyWith<$Res>
    implements $MovingMapHistoryStateCopyWith<$Res> {
  factory _$$MovingMapHistoryStateImplCopyWith(
          _$MovingMapHistoryStateImpl value,
          $Res Function(_$MovingMapHistoryStateImpl) then) =
      __$$MovingMapHistoryStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isMovingMap, bool isResetCluster});
}

/// @nodoc
class __$$MovingMapHistoryStateImplCopyWithImpl<$Res>
    extends _$MovingMapHistoryStateCopyWithImpl<$Res,
        _$MovingMapHistoryStateImpl>
    implements _$$MovingMapHistoryStateImplCopyWith<$Res> {
  __$$MovingMapHistoryStateImplCopyWithImpl(_$MovingMapHistoryStateImpl _value,
      $Res Function(_$MovingMapHistoryStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isMovingMap = null,
    Object? isResetCluster = null,
  }) {
    return _then(_$MovingMapHistoryStateImpl(
      isMovingMap: null == isMovingMap
          ? _value.isMovingMap
          : isMovingMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isResetCluster: null == isResetCluster
          ? _value.isResetCluster
          : isResetCluster // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$MovingMapHistoryStateImpl implements _MovingMapHistoryState {
  const _$MovingMapHistoryStateImpl(
      {this.isMovingMap = false, this.isResetCluster = false});

  @override
  @JsonKey()
  final bool isMovingMap;
  @override
  @JsonKey()
  final bool isResetCluster;

  @override
  String toString() {
    return 'MovingMapHistoryState(isMovingMap: $isMovingMap, isResetCluster: $isResetCluster)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MovingMapHistoryStateImpl &&
            (identical(other.isMovingMap, isMovingMap) ||
                other.isMovingMap == isMovingMap) &&
            (identical(other.isResetCluster, isResetCluster) ||
                other.isResetCluster == isResetCluster));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isMovingMap, isResetCluster);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MovingMapHistoryStateImplCopyWith<_$MovingMapHistoryStateImpl>
      get copyWith => __$$MovingMapHistoryStateImplCopyWithImpl<
          _$MovingMapHistoryStateImpl>(this, _$identity);
}

abstract class _MovingMapHistoryState implements MovingMapHistoryState {
  const factory _MovingMapHistoryState(
      {final bool isMovingMap,
      final bool isResetCluster}) = _$MovingMapHistoryStateImpl;

  @override
  bool get isMovingMap;
  @override
  bool get isResetCluster;
  @override
  @JsonKey(ignore: true)
  _$$MovingMapHistoryStateImplCopyWith<_$MovingMapHistoryStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
