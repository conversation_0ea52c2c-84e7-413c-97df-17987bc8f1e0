// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'history_route_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HistoryRouteEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)
        getHistoryRoute,
    required TResult Function(HistoryRouteResponse historyRouteResponse)
        getRouteSuccess,
    required TResult Function(HistoryRouteFailure failure) getRouteFail,
    required TResult Function() resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)?
        getHistoryRoute,
    TResult? Function(HistoryRouteResponse historyRouteResponse)?
        getRouteSuccess,
    TResult? Function(HistoryRouteFailure failure)? getRouteFail,
    TResult? Function()? resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)?
        getHistoryRoute,
    TResult Function(HistoryRouteResponse historyRouteResponse)?
        getRouteSuccess,
    TResult Function(HistoryRouteFailure failure)? getRouteFail,
    TResult Function()? resetState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetHistoryRouteEvent value) getHistoryRoute,
    required TResult Function(_GetRouteSuccessEvent value) getRouteSuccess,
    required TResult Function(_GetRouteFailEvent value) getRouteFail,
    required TResult Function(_ResetState value) resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetHistoryRouteEvent value)? getHistoryRoute,
    TResult? Function(_GetRouteSuccessEvent value)? getRouteSuccess,
    TResult? Function(_GetRouteFailEvent value)? getRouteFail,
    TResult? Function(_ResetState value)? resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetHistoryRouteEvent value)? getHistoryRoute,
    TResult Function(_GetRouteSuccessEvent value)? getRouteSuccess,
    TResult Function(_GetRouteFailEvent value)? getRouteFail,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryRouteEventCopyWith<$Res> {
  factory $HistoryRouteEventCopyWith(
          HistoryRouteEvent value, $Res Function(HistoryRouteEvent) then) =
      _$HistoryRouteEventCopyWithImpl<$Res, HistoryRouteEvent>;
}

/// @nodoc
class _$HistoryRouteEventCopyWithImpl<$Res, $Val extends HistoryRouteEvent>
    implements $HistoryRouteEventCopyWith<$Res> {
  _$HistoryRouteEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$GetHistoryRouteEventImplCopyWith<$Res> {
  factory _$$GetHistoryRouteEventImplCopyWith(_$GetHistoryRouteEventImpl value,
          $Res Function(_$GetHistoryRouteEventImpl) then) =
      __$$GetHistoryRouteEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String vehicleId, String from, String to, bool isOverSpeedByRoad});
}

/// @nodoc
class __$$GetHistoryRouteEventImplCopyWithImpl<$Res>
    extends _$HistoryRouteEventCopyWithImpl<$Res, _$GetHistoryRouteEventImpl>
    implements _$$GetHistoryRouteEventImplCopyWith<$Res> {
  __$$GetHistoryRouteEventImplCopyWithImpl(_$GetHistoryRouteEventImpl _value,
      $Res Function(_$GetHistoryRouteEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? from = null,
    Object? to = null,
    Object? isOverSpeedByRoad = null,
  }) {
    return _then(_$GetHistoryRouteEventImpl(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$GetHistoryRouteEventImpl implements _GetHistoryRouteEvent {
  const _$GetHistoryRouteEventImpl(
      {required this.vehicleId,
      required this.from,
      required this.to,
      required this.isOverSpeedByRoad});

  @override
  final String vehicleId;
  @override
  final String from;
  @override
  final String to;
  @override
  final bool isOverSpeedByRoad;

  @override
  String toString() {
    return 'HistoryRouteEvent.getHistoryRoute(vehicleId: $vehicleId, from: $from, to: $to, isOverSpeedByRoad: $isOverSpeedByRoad)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetHistoryRouteEventImpl &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.from, from) || other.from == from) &&
            (identical(other.to, to) || other.to == to) &&
            (identical(other.isOverSpeedByRoad, isOverSpeedByRoad) ||
                other.isOverSpeedByRoad == isOverSpeedByRoad));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, vehicleId, from, to, isOverSpeedByRoad);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetHistoryRouteEventImplCopyWith<_$GetHistoryRouteEventImpl>
      get copyWith =>
          __$$GetHistoryRouteEventImplCopyWithImpl<_$GetHistoryRouteEventImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)
        getHistoryRoute,
    required TResult Function(HistoryRouteResponse historyRouteResponse)
        getRouteSuccess,
    required TResult Function(HistoryRouteFailure failure) getRouteFail,
    required TResult Function() resetState,
  }) {
    return getHistoryRoute(vehicleId, from, to, isOverSpeedByRoad);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)?
        getHistoryRoute,
    TResult? Function(HistoryRouteResponse historyRouteResponse)?
        getRouteSuccess,
    TResult? Function(HistoryRouteFailure failure)? getRouteFail,
    TResult? Function()? resetState,
  }) {
    return getHistoryRoute?.call(vehicleId, from, to, isOverSpeedByRoad);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)?
        getHistoryRoute,
    TResult Function(HistoryRouteResponse historyRouteResponse)?
        getRouteSuccess,
    TResult Function(HistoryRouteFailure failure)? getRouteFail,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (getHistoryRoute != null) {
      return getHistoryRoute(vehicleId, from, to, isOverSpeedByRoad);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetHistoryRouteEvent value) getHistoryRoute,
    required TResult Function(_GetRouteSuccessEvent value) getRouteSuccess,
    required TResult Function(_GetRouteFailEvent value) getRouteFail,
    required TResult Function(_ResetState value) resetState,
  }) {
    return getHistoryRoute(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetHistoryRouteEvent value)? getHistoryRoute,
    TResult? Function(_GetRouteSuccessEvent value)? getRouteSuccess,
    TResult? Function(_GetRouteFailEvent value)? getRouteFail,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return getHistoryRoute?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetHistoryRouteEvent value)? getHistoryRoute,
    TResult Function(_GetRouteSuccessEvent value)? getRouteSuccess,
    TResult Function(_GetRouteFailEvent value)? getRouteFail,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (getHistoryRoute != null) {
      return getHistoryRoute(this);
    }
    return orElse();
  }
}

abstract class _GetHistoryRouteEvent implements HistoryRouteEvent {
  const factory _GetHistoryRouteEvent(
      {required final String vehicleId,
      required final String from,
      required final String to,
      required final bool isOverSpeedByRoad}) = _$GetHistoryRouteEventImpl;

  String get vehicleId;
  String get from;
  String get to;
  bool get isOverSpeedByRoad;
  @JsonKey(ignore: true)
  _$$GetHistoryRouteEventImplCopyWith<_$GetHistoryRouteEventImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetRouteSuccessEventImplCopyWith<$Res> {
  factory _$$GetRouteSuccessEventImplCopyWith(_$GetRouteSuccessEventImpl value,
          $Res Function(_$GetRouteSuccessEventImpl) then) =
      __$$GetRouteSuccessEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({HistoryRouteResponse historyRouteResponse});

  $HistoryRouteResponseCopyWith<$Res> get historyRouteResponse;
}

/// @nodoc
class __$$GetRouteSuccessEventImplCopyWithImpl<$Res>
    extends _$HistoryRouteEventCopyWithImpl<$Res, _$GetRouteSuccessEventImpl>
    implements _$$GetRouteSuccessEventImplCopyWith<$Res> {
  __$$GetRouteSuccessEventImplCopyWithImpl(_$GetRouteSuccessEventImpl _value,
      $Res Function(_$GetRouteSuccessEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? historyRouteResponse = null,
  }) {
    return _then(_$GetRouteSuccessEventImpl(
      historyRouteResponse: null == historyRouteResponse
          ? _value.historyRouteResponse
          : historyRouteResponse // ignore: cast_nullable_to_non_nullable
              as HistoryRouteResponse,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $HistoryRouteResponseCopyWith<$Res> get historyRouteResponse {
    return $HistoryRouteResponseCopyWith<$Res>(_value.historyRouteResponse,
        (value) {
      return _then(_value.copyWith(historyRouteResponse: value));
    });
  }
}

/// @nodoc

class _$GetRouteSuccessEventImpl implements _GetRouteSuccessEvent {
  const _$GetRouteSuccessEventImpl({required this.historyRouteResponse});

  @override
  final HistoryRouteResponse historyRouteResponse;

  @override
  String toString() {
    return 'HistoryRouteEvent.getRouteSuccess(historyRouteResponse: $historyRouteResponse)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetRouteSuccessEventImpl &&
            (identical(other.historyRouteResponse, historyRouteResponse) ||
                other.historyRouteResponse == historyRouteResponse));
  }

  @override
  int get hashCode => Object.hash(runtimeType, historyRouteResponse);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetRouteSuccessEventImplCopyWith<_$GetRouteSuccessEventImpl>
      get copyWith =>
          __$$GetRouteSuccessEventImplCopyWithImpl<_$GetRouteSuccessEventImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)
        getHistoryRoute,
    required TResult Function(HistoryRouteResponse historyRouteResponse)
        getRouteSuccess,
    required TResult Function(HistoryRouteFailure failure) getRouteFail,
    required TResult Function() resetState,
  }) {
    return getRouteSuccess(historyRouteResponse);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)?
        getHistoryRoute,
    TResult? Function(HistoryRouteResponse historyRouteResponse)?
        getRouteSuccess,
    TResult? Function(HistoryRouteFailure failure)? getRouteFail,
    TResult? Function()? resetState,
  }) {
    return getRouteSuccess?.call(historyRouteResponse);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)?
        getHistoryRoute,
    TResult Function(HistoryRouteResponse historyRouteResponse)?
        getRouteSuccess,
    TResult Function(HistoryRouteFailure failure)? getRouteFail,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (getRouteSuccess != null) {
      return getRouteSuccess(historyRouteResponse);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetHistoryRouteEvent value) getHistoryRoute,
    required TResult Function(_GetRouteSuccessEvent value) getRouteSuccess,
    required TResult Function(_GetRouteFailEvent value) getRouteFail,
    required TResult Function(_ResetState value) resetState,
  }) {
    return getRouteSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetHistoryRouteEvent value)? getHistoryRoute,
    TResult? Function(_GetRouteSuccessEvent value)? getRouteSuccess,
    TResult? Function(_GetRouteFailEvent value)? getRouteFail,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return getRouteSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetHistoryRouteEvent value)? getHistoryRoute,
    TResult Function(_GetRouteSuccessEvent value)? getRouteSuccess,
    TResult Function(_GetRouteFailEvent value)? getRouteFail,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (getRouteSuccess != null) {
      return getRouteSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetRouteSuccessEvent implements HistoryRouteEvent {
  const factory _GetRouteSuccessEvent(
          {required final HistoryRouteResponse historyRouteResponse}) =
      _$GetRouteSuccessEventImpl;

  HistoryRouteResponse get historyRouteResponse;
  @JsonKey(ignore: true)
  _$$GetRouteSuccessEventImplCopyWith<_$GetRouteSuccessEventImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetRouteFailEventImplCopyWith<$Res> {
  factory _$$GetRouteFailEventImplCopyWith(_$GetRouteFailEventImpl value,
          $Res Function(_$GetRouteFailEventImpl) then) =
      __$$GetRouteFailEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({HistoryRouteFailure failure});

  $HistoryRouteFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$GetRouteFailEventImplCopyWithImpl<$Res>
    extends _$HistoryRouteEventCopyWithImpl<$Res, _$GetRouteFailEventImpl>
    implements _$$GetRouteFailEventImplCopyWith<$Res> {
  __$$GetRouteFailEventImplCopyWithImpl(_$GetRouteFailEventImpl _value,
      $Res Function(_$GetRouteFailEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$GetRouteFailEventImpl(
      failure: null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as HistoryRouteFailure,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $HistoryRouteFailureCopyWith<$Res> get failure {
    return $HistoryRouteFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$GetRouteFailEventImpl implements _GetRouteFailEvent {
  const _$GetRouteFailEventImpl({required this.failure});

  @override
  final HistoryRouteFailure failure;

  @override
  String toString() {
    return 'HistoryRouteEvent.getRouteFail(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetRouteFailEventImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetRouteFailEventImplCopyWith<_$GetRouteFailEventImpl> get copyWith =>
      __$$GetRouteFailEventImplCopyWithImpl<_$GetRouteFailEventImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)
        getHistoryRoute,
    required TResult Function(HistoryRouteResponse historyRouteResponse)
        getRouteSuccess,
    required TResult Function(HistoryRouteFailure failure) getRouteFail,
    required TResult Function() resetState,
  }) {
    return getRouteFail(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)?
        getHistoryRoute,
    TResult? Function(HistoryRouteResponse historyRouteResponse)?
        getRouteSuccess,
    TResult? Function(HistoryRouteFailure failure)? getRouteFail,
    TResult? Function()? resetState,
  }) {
    return getRouteFail?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)?
        getHistoryRoute,
    TResult Function(HistoryRouteResponse historyRouteResponse)?
        getRouteSuccess,
    TResult Function(HistoryRouteFailure failure)? getRouteFail,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (getRouteFail != null) {
      return getRouteFail(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetHistoryRouteEvent value) getHistoryRoute,
    required TResult Function(_GetRouteSuccessEvent value) getRouteSuccess,
    required TResult Function(_GetRouteFailEvent value) getRouteFail,
    required TResult Function(_ResetState value) resetState,
  }) {
    return getRouteFail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetHistoryRouteEvent value)? getHistoryRoute,
    TResult? Function(_GetRouteSuccessEvent value)? getRouteSuccess,
    TResult? Function(_GetRouteFailEvent value)? getRouteFail,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return getRouteFail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetHistoryRouteEvent value)? getHistoryRoute,
    TResult Function(_GetRouteSuccessEvent value)? getRouteSuccess,
    TResult Function(_GetRouteFailEvent value)? getRouteFail,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (getRouteFail != null) {
      return getRouteFail(this);
    }
    return orElse();
  }
}

abstract class _GetRouteFailEvent implements HistoryRouteEvent {
  const factory _GetRouteFailEvent(
      {required final HistoryRouteFailure failure}) = _$GetRouteFailEventImpl;

  HistoryRouteFailure get failure;
  @JsonKey(ignore: true)
  _$$GetRouteFailEventImplCopyWith<_$GetRouteFailEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetStateImplCopyWith<$Res> {
  factory _$$ResetStateImplCopyWith(
          _$ResetStateImpl value, $Res Function(_$ResetStateImpl) then) =
      __$$ResetStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetStateImplCopyWithImpl<$Res>
    extends _$HistoryRouteEventCopyWithImpl<$Res, _$ResetStateImpl>
    implements _$$ResetStateImplCopyWith<$Res> {
  __$$ResetStateImplCopyWithImpl(
      _$ResetStateImpl _value, $Res Function(_$ResetStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ResetStateImpl implements _ResetState {
  const _$ResetStateImpl();

  @override
  String toString() {
    return 'HistoryRouteEvent.resetState()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)
        getHistoryRoute,
    required TResult Function(HistoryRouteResponse historyRouteResponse)
        getRouteSuccess,
    required TResult Function(HistoryRouteFailure failure) getRouteFail,
    required TResult Function() resetState,
  }) {
    return resetState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)?
        getHistoryRoute,
    TResult? Function(HistoryRouteResponse historyRouteResponse)?
        getRouteSuccess,
    TResult? Function(HistoryRouteFailure failure)? getRouteFail,
    TResult? Function()? resetState,
  }) {
    return resetState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String vehicleId, String from, String to, bool isOverSpeedByRoad)?
        getHistoryRoute,
    TResult Function(HistoryRouteResponse historyRouteResponse)?
        getRouteSuccess,
    TResult Function(HistoryRouteFailure failure)? getRouteFail,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetHistoryRouteEvent value) getHistoryRoute,
    required TResult Function(_GetRouteSuccessEvent value) getRouteSuccess,
    required TResult Function(_GetRouteFailEvent value) getRouteFail,
    required TResult Function(_ResetState value) resetState,
  }) {
    return resetState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetHistoryRouteEvent value)? getHistoryRoute,
    TResult? Function(_GetRouteSuccessEvent value)? getRouteSuccess,
    TResult? Function(_GetRouteFailEvent value)? getRouteFail,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return resetState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetHistoryRouteEvent value)? getHistoryRoute,
    TResult Function(_GetRouteSuccessEvent value)? getRouteSuccess,
    TResult Function(_GetRouteFailEvent value)? getRouteFail,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState(this);
    }
    return orElse();
  }
}

abstract class _ResetState implements HistoryRouteEvent {
  const factory _ResetState() = _$ResetStateImpl;
}

/// @nodoc
mixin _$HistoryRouteState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)
        loaded,
    required TResult Function(HistoryRouteFailure failure) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)?
        loaded,
    TResult? Function(HistoryRouteFailure failure)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)?
        loaded,
    TResult Function(HistoryRouteFailure failure)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Failure value) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Failure value)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryRouteStateCopyWith<$Res> {
  factory $HistoryRouteStateCopyWith(
          HistoryRouteState value, $Res Function(HistoryRouteState) then) =
      _$HistoryRouteStateCopyWithImpl<$Res, HistoryRouteState>;
}

/// @nodoc
class _$HistoryRouteStateCopyWithImpl<$Res, $Val extends HistoryRouteState>
    implements $HistoryRouteStateCopyWith<$Res> {
  _$HistoryRouteStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$HistoryRouteStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'HistoryRouteState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)
        loaded,
    required TResult Function(HistoryRouteFailure failure) failure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)?
        loaded,
    TResult? Function(HistoryRouteFailure failure)? failure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)?
        loaded,
    TResult Function(HistoryRouteFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Failure value) failure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Failure value)? failure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements HistoryRouteState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$HistoryRouteStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'HistoryRouteState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)
        loaded,
    required TResult Function(HistoryRouteFailure failure) failure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)?
        loaded,
    TResult? Function(HistoryRouteFailure failure)? failure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)?
        loaded,
    TResult Function(HistoryRouteFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Failure value) failure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Failure value)? failure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements HistoryRouteState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {HistoryRouteResponse historyRouteResponse,
      List<LatLng> listLatLngForMap});

  $HistoryRouteResponseCopyWith<$Res> get historyRouteResponse;
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$HistoryRouteStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? historyRouteResponse = null,
    Object? listLatLngForMap = null,
  }) {
    return _then(_$LoadedImpl(
      historyRouteResponse: null == historyRouteResponse
          ? _value.historyRouteResponse
          : historyRouteResponse // ignore: cast_nullable_to_non_nullable
              as HistoryRouteResponse,
      listLatLngForMap: null == listLatLngForMap
          ? _value._listLatLngForMap
          : listLatLngForMap // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $HistoryRouteResponseCopyWith<$Res> get historyRouteResponse {
    return $HistoryRouteResponseCopyWith<$Res>(_value.historyRouteResponse,
        (value) {
      return _then(_value.copyWith(historyRouteResponse: value));
    });
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl(
      {required this.historyRouteResponse,
      required final List<LatLng> listLatLngForMap})
      : _listLatLngForMap = listLatLngForMap;

  @override
  final HistoryRouteResponse historyRouteResponse;
  final List<LatLng> _listLatLngForMap;
  @override
  List<LatLng> get listLatLngForMap {
    if (_listLatLngForMap is EqualUnmodifiableListView)
      return _listLatLngForMap;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listLatLngForMap);
  }

  @override
  String toString() {
    return 'HistoryRouteState.loaded(historyRouteResponse: $historyRouteResponse, listLatLngForMap: $listLatLngForMap)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            (identical(other.historyRouteResponse, historyRouteResponse) ||
                other.historyRouteResponse == historyRouteResponse) &&
            const DeepCollectionEquality()
                .equals(other._listLatLngForMap, _listLatLngForMap));
  }

  @override
  int get hashCode => Object.hash(runtimeType, historyRouteResponse,
      const DeepCollectionEquality().hash(_listLatLngForMap));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)
        loaded,
    required TResult Function(HistoryRouteFailure failure) failure,
  }) {
    return loaded(historyRouteResponse, listLatLngForMap);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)?
        loaded,
    TResult? Function(HistoryRouteFailure failure)? failure,
  }) {
    return loaded?.call(historyRouteResponse, listLatLngForMap);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)?
        loaded,
    TResult Function(HistoryRouteFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(historyRouteResponse, listLatLngForMap);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Failure value) failure,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Failure value)? failure,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements HistoryRouteState {
  const factory _Loaded(
      {required final HistoryRouteResponse historyRouteResponse,
      required final List<LatLng> listLatLngForMap}) = _$LoadedImpl;

  HistoryRouteResponse get historyRouteResponse;
  List<LatLng> get listLatLngForMap;
  @JsonKey(ignore: true)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FailureImplCopyWith<$Res> {
  factory _$$FailureImplCopyWith(
          _$FailureImpl value, $Res Function(_$FailureImpl) then) =
      __$$FailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({HistoryRouteFailure failure});

  $HistoryRouteFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureImplCopyWithImpl<$Res>
    extends _$HistoryRouteStateCopyWithImpl<$Res, _$FailureImpl>
    implements _$$FailureImplCopyWith<$Res> {
  __$$FailureImplCopyWithImpl(
      _$FailureImpl _value, $Res Function(_$FailureImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$FailureImpl(
      failure: null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as HistoryRouteFailure,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $HistoryRouteFailureCopyWith<$Res> get failure {
    return $HistoryRouteFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureImpl implements _Failure {
  const _$FailureImpl({required this.failure});

  @override
  final HistoryRouteFailure failure;

  @override
  String toString() {
    return 'HistoryRouteState.failure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      __$$FailureImplCopyWithImpl<_$FailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)
        loaded,
    required TResult Function(HistoryRouteFailure failure) failure,
  }) {
    return failure(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)?
        loaded,
    TResult? Function(HistoryRouteFailure failure)? failure,
  }) {
    return failure?.call(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HistoryRouteResponse historyRouteResponse,
            List<LatLng> listLatLngForMap)?
        loaded,
    TResult Function(HistoryRouteFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this.failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Failure value) failure,
  }) {
    return failure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Failure value)? failure,
  }) {
    return failure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this);
    }
    return orElse();
  }
}

abstract class _Failure implements HistoryRouteState {
  const factory _Failure({required final HistoryRouteFailure failure}) =
      _$FailureImpl;

  HistoryRouteFailure get failure;
  @JsonKey(ignore: true)
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
