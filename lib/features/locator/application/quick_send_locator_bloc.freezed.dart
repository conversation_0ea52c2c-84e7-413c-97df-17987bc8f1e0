// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quick_send_locator_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$QuickSendLocatorEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(LocatorCreateRequest request, String userId)
        getCreateLocator,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function(LocatorFailure error) getCreatefailureEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(LocatorCreateRequest request, String userId)?
        getCreateLocator,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function(LocatorFailure error)? getCreatefailureEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(LocatorCreateRequest request, String userId)?
        getCreateLocator,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function(LocatorFailure error)? getCreatefailureEvent,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetCreateQuickSendLocatorEvent value)
        getCreateLocator,
    required TResult Function(_GetCreateLocatorSuccessEvent value)
        getCreateLocatorSuccess,
    required TResult Function(_GetCreateFailureEvent value)
        getCreatefailureEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetCreateQuickSendLocatorEvent value)? getCreateLocator,
    TResult? Function(_GetCreateLocatorSuccessEvent value)?
        getCreateLocatorSuccess,
    TResult? Function(_GetCreateFailureEvent value)? getCreatefailureEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetCreateQuickSendLocatorEvent value)? getCreateLocator,
    TResult Function(_GetCreateLocatorSuccessEvent value)?
        getCreateLocatorSuccess,
    TResult Function(_GetCreateFailureEvent value)? getCreatefailureEvent,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuickSendLocatorEventCopyWith<$Res> {
  factory $QuickSendLocatorEventCopyWith(QuickSendLocatorEvent value,
          $Res Function(QuickSendLocatorEvent) then) =
      _$QuickSendLocatorEventCopyWithImpl<$Res, QuickSendLocatorEvent>;
}

/// @nodoc
class _$QuickSendLocatorEventCopyWithImpl<$Res,
        $Val extends QuickSendLocatorEvent>
    implements $QuickSendLocatorEventCopyWith<$Res> {
  _$QuickSendLocatorEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$GetCreateQuickSendLocatorEventImplCopyWith<$Res> {
  factory _$$GetCreateQuickSendLocatorEventImplCopyWith(
          _$GetCreateQuickSendLocatorEventImpl value,
          $Res Function(_$GetCreateQuickSendLocatorEventImpl) then) =
      __$$GetCreateQuickSendLocatorEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LocatorCreateRequest request, String userId});

  $LocatorCreateRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$GetCreateQuickSendLocatorEventImplCopyWithImpl<$Res>
    extends _$QuickSendLocatorEventCopyWithImpl<$Res,
        _$GetCreateQuickSendLocatorEventImpl>
    implements _$$GetCreateQuickSendLocatorEventImplCopyWith<$Res> {
  __$$GetCreateQuickSendLocatorEventImplCopyWithImpl(
      _$GetCreateQuickSendLocatorEventImpl _value,
      $Res Function(_$GetCreateQuickSendLocatorEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
    Object? userId = null,
  }) {
    return _then(_$GetCreateQuickSendLocatorEventImpl(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as LocatorCreateRequest,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LocatorCreateRequestCopyWith<$Res> get request {
    return $LocatorCreateRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$GetCreateQuickSendLocatorEventImpl
    implements _GetCreateQuickSendLocatorEvent {
  const _$GetCreateQuickSendLocatorEventImpl(
      {required this.request, required this.userId});

  @override
  final LocatorCreateRequest request;
  @override
  final String userId;

  @override
  String toString() {
    return 'QuickSendLocatorEvent.getCreateLocator(request: $request, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetCreateQuickSendLocatorEventImpl &&
            (identical(other.request, request) || other.request == request) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request, userId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetCreateQuickSendLocatorEventImplCopyWith<
          _$GetCreateQuickSendLocatorEventImpl>
      get copyWith => __$$GetCreateQuickSendLocatorEventImplCopyWithImpl<
          _$GetCreateQuickSendLocatorEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(LocatorCreateRequest request, String userId)
        getCreateLocator,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function(LocatorFailure error) getCreatefailureEvent,
  }) {
    return getCreateLocator(request, userId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(LocatorCreateRequest request, String userId)?
        getCreateLocator,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function(LocatorFailure error)? getCreatefailureEvent,
  }) {
    return getCreateLocator?.call(request, userId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(LocatorCreateRequest request, String userId)?
        getCreateLocator,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function(LocatorFailure error)? getCreatefailureEvent,
    required TResult orElse(),
  }) {
    if (getCreateLocator != null) {
      return getCreateLocator(request, userId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetCreateQuickSendLocatorEvent value)
        getCreateLocator,
    required TResult Function(_GetCreateLocatorSuccessEvent value)
        getCreateLocatorSuccess,
    required TResult Function(_GetCreateFailureEvent value)
        getCreatefailureEvent,
  }) {
    return getCreateLocator(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetCreateQuickSendLocatorEvent value)? getCreateLocator,
    TResult? Function(_GetCreateLocatorSuccessEvent value)?
        getCreateLocatorSuccess,
    TResult? Function(_GetCreateFailureEvent value)? getCreatefailureEvent,
  }) {
    return getCreateLocator?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetCreateQuickSendLocatorEvent value)? getCreateLocator,
    TResult Function(_GetCreateLocatorSuccessEvent value)?
        getCreateLocatorSuccess,
    TResult Function(_GetCreateFailureEvent value)? getCreatefailureEvent,
    required TResult orElse(),
  }) {
    if (getCreateLocator != null) {
      return getCreateLocator(this);
    }
    return orElse();
  }
}

abstract class _GetCreateQuickSendLocatorEvent
    implements QuickSendLocatorEvent {
  const factory _GetCreateQuickSendLocatorEvent(
      {required final LocatorCreateRequest request,
      required final String userId}) = _$GetCreateQuickSendLocatorEventImpl;

  LocatorCreateRequest get request;
  String get userId;
  @JsonKey(ignore: true)
  _$$GetCreateQuickSendLocatorEventImplCopyWith<
          _$GetCreateQuickSendLocatorEventImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetCreateLocatorSuccessEventImplCopyWith<$Res> {
  factory _$$GetCreateLocatorSuccessEventImplCopyWith(
          _$GetCreateLocatorSuccessEventImpl value,
          $Res Function(_$GetCreateLocatorSuccessEventImpl) then) =
      __$$GetCreateLocatorSuccessEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BaseLocatorResponse response, Uint8List? qrCode});

  $BaseLocatorResponseCopyWith<$Res> get response;
}

/// @nodoc
class __$$GetCreateLocatorSuccessEventImplCopyWithImpl<$Res>
    extends _$QuickSendLocatorEventCopyWithImpl<$Res,
        _$GetCreateLocatorSuccessEventImpl>
    implements _$$GetCreateLocatorSuccessEventImplCopyWith<$Res> {
  __$$GetCreateLocatorSuccessEventImplCopyWithImpl(
      _$GetCreateLocatorSuccessEventImpl _value,
      $Res Function(_$GetCreateLocatorSuccessEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? response = null,
    Object? qrCode = freezed,
  }) {
    return _then(_$GetCreateLocatorSuccessEventImpl(
      response: null == response
          ? _value.response
          : response // ignore: cast_nullable_to_non_nullable
              as BaseLocatorResponse,
      qrCode: freezed == qrCode
          ? _value.qrCode
          : qrCode // ignore: cast_nullable_to_non_nullable
              as Uint8List?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $BaseLocatorResponseCopyWith<$Res> get response {
    return $BaseLocatorResponseCopyWith<$Res>(_value.response, (value) {
      return _then(_value.copyWith(response: value));
    });
  }
}

/// @nodoc

class _$GetCreateLocatorSuccessEventImpl
    implements _GetCreateLocatorSuccessEvent {
  const _$GetCreateLocatorSuccessEventImpl(
      {required this.response, required this.qrCode});

  @override
  final BaseLocatorResponse response;
  @override
  final Uint8List? qrCode;

  @override
  String toString() {
    return 'QuickSendLocatorEvent.getCreateLocatorSuccess(response: $response, qrCode: $qrCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetCreateLocatorSuccessEventImpl &&
            (identical(other.response, response) ||
                other.response == response) &&
            const DeepCollectionEquality().equals(other.qrCode, qrCode));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, response, const DeepCollectionEquality().hash(qrCode));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetCreateLocatorSuccessEventImplCopyWith<
          _$GetCreateLocatorSuccessEventImpl>
      get copyWith => __$$GetCreateLocatorSuccessEventImplCopyWithImpl<
          _$GetCreateLocatorSuccessEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(LocatorCreateRequest request, String userId)
        getCreateLocator,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function(LocatorFailure error) getCreatefailureEvent,
  }) {
    return getCreateLocatorSuccess(response, qrCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(LocatorCreateRequest request, String userId)?
        getCreateLocator,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function(LocatorFailure error)? getCreatefailureEvent,
  }) {
    return getCreateLocatorSuccess?.call(response, qrCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(LocatorCreateRequest request, String userId)?
        getCreateLocator,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function(LocatorFailure error)? getCreatefailureEvent,
    required TResult orElse(),
  }) {
    if (getCreateLocatorSuccess != null) {
      return getCreateLocatorSuccess(response, qrCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetCreateQuickSendLocatorEvent value)
        getCreateLocator,
    required TResult Function(_GetCreateLocatorSuccessEvent value)
        getCreateLocatorSuccess,
    required TResult Function(_GetCreateFailureEvent value)
        getCreatefailureEvent,
  }) {
    return getCreateLocatorSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetCreateQuickSendLocatorEvent value)? getCreateLocator,
    TResult? Function(_GetCreateLocatorSuccessEvent value)?
        getCreateLocatorSuccess,
    TResult? Function(_GetCreateFailureEvent value)? getCreatefailureEvent,
  }) {
    return getCreateLocatorSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetCreateQuickSendLocatorEvent value)? getCreateLocator,
    TResult Function(_GetCreateLocatorSuccessEvent value)?
        getCreateLocatorSuccess,
    TResult Function(_GetCreateFailureEvent value)? getCreatefailureEvent,
    required TResult orElse(),
  }) {
    if (getCreateLocatorSuccess != null) {
      return getCreateLocatorSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetCreateLocatorSuccessEvent implements QuickSendLocatorEvent {
  const factory _GetCreateLocatorSuccessEvent(
      {required final BaseLocatorResponse response,
      required final Uint8List? qrCode}) = _$GetCreateLocatorSuccessEventImpl;

  BaseLocatorResponse get response;
  Uint8List? get qrCode;
  @JsonKey(ignore: true)
  _$$GetCreateLocatorSuccessEventImplCopyWith<
          _$GetCreateLocatorSuccessEventImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetCreateFailureEventImplCopyWith<$Res> {
  factory _$$GetCreateFailureEventImplCopyWith(
          _$GetCreateFailureEventImpl value,
          $Res Function(_$GetCreateFailureEventImpl) then) =
      __$$GetCreateFailureEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LocatorFailure error});

  $LocatorFailureCopyWith<$Res> get error;
}

/// @nodoc
class __$$GetCreateFailureEventImplCopyWithImpl<$Res>
    extends _$QuickSendLocatorEventCopyWithImpl<$Res,
        _$GetCreateFailureEventImpl>
    implements _$$GetCreateFailureEventImplCopyWith<$Res> {
  __$$GetCreateFailureEventImplCopyWithImpl(_$GetCreateFailureEventImpl _value,
      $Res Function(_$GetCreateFailureEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$GetCreateFailureEventImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as LocatorFailure,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LocatorFailureCopyWith<$Res> get error {
    return $LocatorFailureCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$GetCreateFailureEventImpl implements _GetCreateFailureEvent {
  const _$GetCreateFailureEventImpl({required this.error});

  @override
  final LocatorFailure error;

  @override
  String toString() {
    return 'QuickSendLocatorEvent.getCreatefailureEvent(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetCreateFailureEventImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetCreateFailureEventImplCopyWith<_$GetCreateFailureEventImpl>
      get copyWith => __$$GetCreateFailureEventImplCopyWithImpl<
          _$GetCreateFailureEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(LocatorCreateRequest request, String userId)
        getCreateLocator,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function(LocatorFailure error) getCreatefailureEvent,
  }) {
    return getCreatefailureEvent(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(LocatorCreateRequest request, String userId)?
        getCreateLocator,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function(LocatorFailure error)? getCreatefailureEvent,
  }) {
    return getCreatefailureEvent?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(LocatorCreateRequest request, String userId)?
        getCreateLocator,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function(LocatorFailure error)? getCreatefailureEvent,
    required TResult orElse(),
  }) {
    if (getCreatefailureEvent != null) {
      return getCreatefailureEvent(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetCreateQuickSendLocatorEvent value)
        getCreateLocator,
    required TResult Function(_GetCreateLocatorSuccessEvent value)
        getCreateLocatorSuccess,
    required TResult Function(_GetCreateFailureEvent value)
        getCreatefailureEvent,
  }) {
    return getCreatefailureEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetCreateQuickSendLocatorEvent value)? getCreateLocator,
    TResult? Function(_GetCreateLocatorSuccessEvent value)?
        getCreateLocatorSuccess,
    TResult? Function(_GetCreateFailureEvent value)? getCreatefailureEvent,
  }) {
    return getCreatefailureEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetCreateQuickSendLocatorEvent value)? getCreateLocator,
    TResult Function(_GetCreateLocatorSuccessEvent value)?
        getCreateLocatorSuccess,
    TResult Function(_GetCreateFailureEvent value)? getCreatefailureEvent,
    required TResult orElse(),
  }) {
    if (getCreatefailureEvent != null) {
      return getCreatefailureEvent(this);
    }
    return orElse();
  }
}

abstract class _GetCreateFailureEvent implements QuickSendLocatorEvent {
  const factory _GetCreateFailureEvent({required final LocatorFailure error}) =
      _$GetCreateFailureEventImpl;

  LocatorFailure get error;
  @JsonKey(ignore: true)
  _$$GetCreateFailureEventImplCopyWith<_$GetCreateFailureEventImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$QuickSendLocatorState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String result) getQRCodeSuccess,
    required TResult Function() gettingQRCode,
    required TResult Function(LocatorFailure error) getQRCodeFail,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function() gettingCreateLocator,
    required TResult Function(LocatorFailure error) getCreateLocatorFail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String result)? getQRCodeSuccess,
    TResult? Function()? gettingQRCode,
    TResult? Function(LocatorFailure error)? getQRCodeFail,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function()? gettingCreateLocator,
    TResult? Function(LocatorFailure error)? getCreateLocatorFail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String result)? getQRCodeSuccess,
    TResult Function()? gettingQRCode,
    TResult Function(LocatorFailure error)? getQRCodeFail,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function()? gettingCreateLocator,
    TResult Function(LocatorFailure error)? getCreateLocatorFail,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEvent value) initial,
    required TResult Function(_GetQRCodeSuccessState value) getQRCodeSuccess,
    required TResult Function(_GettingQRCodeEvent value) gettingQRCode,
    required TResult Function(_GetQRCodeFailState value) getQRCodeFail,
    required TResult Function(_GetCreateLocatorSuccessState value)
        getCreateLocatorSuccess,
    required TResult Function(_GettingCreateQuickSendLocatorState value)
        gettingCreateLocator,
    required TResult Function(_GetCreateLocatorFailState value)
        getCreateLocatorFail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEvent value)? initial,
    TResult? Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult? Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult? Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult? Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult? Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult? Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEvent value)? initial,
    TResult Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuickSendLocatorStateCopyWith<$Res> {
  factory $QuickSendLocatorStateCopyWith(QuickSendLocatorState value,
          $Res Function(QuickSendLocatorState) then) =
      _$QuickSendLocatorStateCopyWithImpl<$Res, QuickSendLocatorState>;
}

/// @nodoc
class _$QuickSendLocatorStateCopyWithImpl<$Res,
        $Val extends QuickSendLocatorState>
    implements $QuickSendLocatorStateCopyWith<$Res> {
  _$QuickSendLocatorStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$InitialEventImplCopyWith<$Res> {
  factory _$$InitialEventImplCopyWith(
          _$InitialEventImpl value, $Res Function(_$InitialEventImpl) then) =
      __$$InitialEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialEventImplCopyWithImpl<$Res>
    extends _$QuickSendLocatorStateCopyWithImpl<$Res, _$InitialEventImpl>
    implements _$$InitialEventImplCopyWith<$Res> {
  __$$InitialEventImplCopyWithImpl(
      _$InitialEventImpl _value, $Res Function(_$InitialEventImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$InitialEventImpl implements _InitialEvent {
  const _$InitialEventImpl();

  @override
  String toString() {
    return 'QuickSendLocatorState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String result) getQRCodeSuccess,
    required TResult Function() gettingQRCode,
    required TResult Function(LocatorFailure error) getQRCodeFail,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function() gettingCreateLocator,
    required TResult Function(LocatorFailure error) getCreateLocatorFail,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String result)? getQRCodeSuccess,
    TResult? Function()? gettingQRCode,
    TResult? Function(LocatorFailure error)? getQRCodeFail,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function()? gettingCreateLocator,
    TResult? Function(LocatorFailure error)? getCreateLocatorFail,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String result)? getQRCodeSuccess,
    TResult Function()? gettingQRCode,
    TResult Function(LocatorFailure error)? getQRCodeFail,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function()? gettingCreateLocator,
    TResult Function(LocatorFailure error)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEvent value) initial,
    required TResult Function(_GetQRCodeSuccessState value) getQRCodeSuccess,
    required TResult Function(_GettingQRCodeEvent value) gettingQRCode,
    required TResult Function(_GetQRCodeFailState value) getQRCodeFail,
    required TResult Function(_GetCreateLocatorSuccessState value)
        getCreateLocatorSuccess,
    required TResult Function(_GettingCreateQuickSendLocatorState value)
        gettingCreateLocator,
    required TResult Function(_GetCreateLocatorFailState value)
        getCreateLocatorFail,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEvent value)? initial,
    TResult? Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult? Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult? Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult? Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult? Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult? Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEvent value)? initial,
    TResult Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _InitialEvent implements QuickSendLocatorState {
  const factory _InitialEvent() = _$InitialEventImpl;
}

/// @nodoc
abstract class _$$GetQRCodeSuccessStateImplCopyWith<$Res> {
  factory _$$GetQRCodeSuccessStateImplCopyWith(
          _$GetQRCodeSuccessStateImpl value,
          $Res Function(_$GetQRCodeSuccessStateImpl) then) =
      __$$GetQRCodeSuccessStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String result});
}

/// @nodoc
class __$$GetQRCodeSuccessStateImplCopyWithImpl<$Res>
    extends _$QuickSendLocatorStateCopyWithImpl<$Res,
        _$GetQRCodeSuccessStateImpl>
    implements _$$GetQRCodeSuccessStateImplCopyWith<$Res> {
  __$$GetQRCodeSuccessStateImplCopyWithImpl(_$GetQRCodeSuccessStateImpl _value,
      $Res Function(_$GetQRCodeSuccessStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? result = null,
  }) {
    return _then(_$GetQRCodeSuccessStateImpl(
      result: null == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetQRCodeSuccessStateImpl implements _GetQRCodeSuccessState {
  const _$GetQRCodeSuccessStateImpl({required this.result});

  @override
  final String result;

  @override
  String toString() {
    return 'QuickSendLocatorState.getQRCodeSuccess(result: $result)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetQRCodeSuccessStateImpl &&
            (identical(other.result, result) || other.result == result));
  }

  @override
  int get hashCode => Object.hash(runtimeType, result);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetQRCodeSuccessStateImplCopyWith<_$GetQRCodeSuccessStateImpl>
      get copyWith => __$$GetQRCodeSuccessStateImplCopyWithImpl<
          _$GetQRCodeSuccessStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String result) getQRCodeSuccess,
    required TResult Function() gettingQRCode,
    required TResult Function(LocatorFailure error) getQRCodeFail,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function() gettingCreateLocator,
    required TResult Function(LocatorFailure error) getCreateLocatorFail,
  }) {
    return getQRCodeSuccess(result);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String result)? getQRCodeSuccess,
    TResult? Function()? gettingQRCode,
    TResult? Function(LocatorFailure error)? getQRCodeFail,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function()? gettingCreateLocator,
    TResult? Function(LocatorFailure error)? getCreateLocatorFail,
  }) {
    return getQRCodeSuccess?.call(result);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String result)? getQRCodeSuccess,
    TResult Function()? gettingQRCode,
    TResult Function(LocatorFailure error)? getQRCodeFail,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function()? gettingCreateLocator,
    TResult Function(LocatorFailure error)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (getQRCodeSuccess != null) {
      return getQRCodeSuccess(result);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEvent value) initial,
    required TResult Function(_GetQRCodeSuccessState value) getQRCodeSuccess,
    required TResult Function(_GettingQRCodeEvent value) gettingQRCode,
    required TResult Function(_GetQRCodeFailState value) getQRCodeFail,
    required TResult Function(_GetCreateLocatorSuccessState value)
        getCreateLocatorSuccess,
    required TResult Function(_GettingCreateQuickSendLocatorState value)
        gettingCreateLocator,
    required TResult Function(_GetCreateLocatorFailState value)
        getCreateLocatorFail,
  }) {
    return getQRCodeSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEvent value)? initial,
    TResult? Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult? Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult? Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult? Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult? Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult? Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
  }) {
    return getQRCodeSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEvent value)? initial,
    TResult Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (getQRCodeSuccess != null) {
      return getQRCodeSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetQRCodeSuccessState implements QuickSendLocatorState {
  const factory _GetQRCodeSuccessState({required final String result}) =
      _$GetQRCodeSuccessStateImpl;

  String get result;
  @JsonKey(ignore: true)
  _$$GetQRCodeSuccessStateImplCopyWith<_$GetQRCodeSuccessStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GettingQRCodeEventImplCopyWith<$Res> {
  factory _$$GettingQRCodeEventImplCopyWith(_$GettingQRCodeEventImpl value,
          $Res Function(_$GettingQRCodeEventImpl) then) =
      __$$GettingQRCodeEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GettingQRCodeEventImplCopyWithImpl<$Res>
    extends _$QuickSendLocatorStateCopyWithImpl<$Res, _$GettingQRCodeEventImpl>
    implements _$$GettingQRCodeEventImplCopyWith<$Res> {
  __$$GettingQRCodeEventImplCopyWithImpl(_$GettingQRCodeEventImpl _value,
      $Res Function(_$GettingQRCodeEventImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$GettingQRCodeEventImpl implements _GettingQRCodeEvent {
  const _$GettingQRCodeEventImpl();

  @override
  String toString() {
    return 'QuickSendLocatorState.gettingQRCode()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GettingQRCodeEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String result) getQRCodeSuccess,
    required TResult Function() gettingQRCode,
    required TResult Function(LocatorFailure error) getQRCodeFail,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function() gettingCreateLocator,
    required TResult Function(LocatorFailure error) getCreateLocatorFail,
  }) {
    return gettingQRCode();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String result)? getQRCodeSuccess,
    TResult? Function()? gettingQRCode,
    TResult? Function(LocatorFailure error)? getQRCodeFail,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function()? gettingCreateLocator,
    TResult? Function(LocatorFailure error)? getCreateLocatorFail,
  }) {
    return gettingQRCode?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String result)? getQRCodeSuccess,
    TResult Function()? gettingQRCode,
    TResult Function(LocatorFailure error)? getQRCodeFail,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function()? gettingCreateLocator,
    TResult Function(LocatorFailure error)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (gettingQRCode != null) {
      return gettingQRCode();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEvent value) initial,
    required TResult Function(_GetQRCodeSuccessState value) getQRCodeSuccess,
    required TResult Function(_GettingQRCodeEvent value) gettingQRCode,
    required TResult Function(_GetQRCodeFailState value) getQRCodeFail,
    required TResult Function(_GetCreateLocatorSuccessState value)
        getCreateLocatorSuccess,
    required TResult Function(_GettingCreateQuickSendLocatorState value)
        gettingCreateLocator,
    required TResult Function(_GetCreateLocatorFailState value)
        getCreateLocatorFail,
  }) {
    return gettingQRCode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEvent value)? initial,
    TResult? Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult? Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult? Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult? Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult? Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult? Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
  }) {
    return gettingQRCode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEvent value)? initial,
    TResult Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (gettingQRCode != null) {
      return gettingQRCode(this);
    }
    return orElse();
  }
}

abstract class _GettingQRCodeEvent implements QuickSendLocatorState {
  const factory _GettingQRCodeEvent() = _$GettingQRCodeEventImpl;
}

/// @nodoc
abstract class _$$GetQRCodeFailStateImplCopyWith<$Res> {
  factory _$$GetQRCodeFailStateImplCopyWith(_$GetQRCodeFailStateImpl value,
          $Res Function(_$GetQRCodeFailStateImpl) then) =
      __$$GetQRCodeFailStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LocatorFailure error});

  $LocatorFailureCopyWith<$Res> get error;
}

/// @nodoc
class __$$GetQRCodeFailStateImplCopyWithImpl<$Res>
    extends _$QuickSendLocatorStateCopyWithImpl<$Res, _$GetQRCodeFailStateImpl>
    implements _$$GetQRCodeFailStateImplCopyWith<$Res> {
  __$$GetQRCodeFailStateImplCopyWithImpl(_$GetQRCodeFailStateImpl _value,
      $Res Function(_$GetQRCodeFailStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$GetQRCodeFailStateImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as LocatorFailure,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LocatorFailureCopyWith<$Res> get error {
    return $LocatorFailureCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$GetQRCodeFailStateImpl implements _GetQRCodeFailState {
  const _$GetQRCodeFailStateImpl({required this.error});

  @override
  final LocatorFailure error;

  @override
  String toString() {
    return 'QuickSendLocatorState.getQRCodeFail(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetQRCodeFailStateImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetQRCodeFailStateImplCopyWith<_$GetQRCodeFailStateImpl> get copyWith =>
      __$$GetQRCodeFailStateImplCopyWithImpl<_$GetQRCodeFailStateImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String result) getQRCodeSuccess,
    required TResult Function() gettingQRCode,
    required TResult Function(LocatorFailure error) getQRCodeFail,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function() gettingCreateLocator,
    required TResult Function(LocatorFailure error) getCreateLocatorFail,
  }) {
    return getQRCodeFail(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String result)? getQRCodeSuccess,
    TResult? Function()? gettingQRCode,
    TResult? Function(LocatorFailure error)? getQRCodeFail,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function()? gettingCreateLocator,
    TResult? Function(LocatorFailure error)? getCreateLocatorFail,
  }) {
    return getQRCodeFail?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String result)? getQRCodeSuccess,
    TResult Function()? gettingQRCode,
    TResult Function(LocatorFailure error)? getQRCodeFail,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function()? gettingCreateLocator,
    TResult Function(LocatorFailure error)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (getQRCodeFail != null) {
      return getQRCodeFail(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEvent value) initial,
    required TResult Function(_GetQRCodeSuccessState value) getQRCodeSuccess,
    required TResult Function(_GettingQRCodeEvent value) gettingQRCode,
    required TResult Function(_GetQRCodeFailState value) getQRCodeFail,
    required TResult Function(_GetCreateLocatorSuccessState value)
        getCreateLocatorSuccess,
    required TResult Function(_GettingCreateQuickSendLocatorState value)
        gettingCreateLocator,
    required TResult Function(_GetCreateLocatorFailState value)
        getCreateLocatorFail,
  }) {
    return getQRCodeFail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEvent value)? initial,
    TResult? Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult? Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult? Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult? Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult? Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult? Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
  }) {
    return getQRCodeFail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEvent value)? initial,
    TResult Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (getQRCodeFail != null) {
      return getQRCodeFail(this);
    }
    return orElse();
  }
}

abstract class _GetQRCodeFailState implements QuickSendLocatorState {
  const factory _GetQRCodeFailState({required final LocatorFailure error}) =
      _$GetQRCodeFailStateImpl;

  LocatorFailure get error;
  @JsonKey(ignore: true)
  _$$GetQRCodeFailStateImplCopyWith<_$GetQRCodeFailStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetCreateLocatorSuccessStateImplCopyWith<$Res> {
  factory _$$GetCreateLocatorSuccessStateImplCopyWith(
          _$GetCreateLocatorSuccessStateImpl value,
          $Res Function(_$GetCreateLocatorSuccessStateImpl) then) =
      __$$GetCreateLocatorSuccessStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BaseLocatorResponse response, Uint8List? qrCode});

  $BaseLocatorResponseCopyWith<$Res> get response;
}

/// @nodoc
class __$$GetCreateLocatorSuccessStateImplCopyWithImpl<$Res>
    extends _$QuickSendLocatorStateCopyWithImpl<$Res,
        _$GetCreateLocatorSuccessStateImpl>
    implements _$$GetCreateLocatorSuccessStateImplCopyWith<$Res> {
  __$$GetCreateLocatorSuccessStateImplCopyWithImpl(
      _$GetCreateLocatorSuccessStateImpl _value,
      $Res Function(_$GetCreateLocatorSuccessStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? response = null,
    Object? qrCode = freezed,
  }) {
    return _then(_$GetCreateLocatorSuccessStateImpl(
      response: null == response
          ? _value.response
          : response // ignore: cast_nullable_to_non_nullable
              as BaseLocatorResponse,
      qrCode: freezed == qrCode
          ? _value.qrCode
          : qrCode // ignore: cast_nullable_to_non_nullable
              as Uint8List?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $BaseLocatorResponseCopyWith<$Res> get response {
    return $BaseLocatorResponseCopyWith<$Res>(_value.response, (value) {
      return _then(_value.copyWith(response: value));
    });
  }
}

/// @nodoc

class _$GetCreateLocatorSuccessStateImpl
    implements _GetCreateLocatorSuccessState {
  const _$GetCreateLocatorSuccessStateImpl(
      {required this.response, required this.qrCode});

  @override
  final BaseLocatorResponse response;
  @override
  final Uint8List? qrCode;

  @override
  String toString() {
    return 'QuickSendLocatorState.getCreateLocatorSuccess(response: $response, qrCode: $qrCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetCreateLocatorSuccessStateImpl &&
            (identical(other.response, response) ||
                other.response == response) &&
            const DeepCollectionEquality().equals(other.qrCode, qrCode));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, response, const DeepCollectionEquality().hash(qrCode));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetCreateLocatorSuccessStateImplCopyWith<
          _$GetCreateLocatorSuccessStateImpl>
      get copyWith => __$$GetCreateLocatorSuccessStateImplCopyWithImpl<
          _$GetCreateLocatorSuccessStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String result) getQRCodeSuccess,
    required TResult Function() gettingQRCode,
    required TResult Function(LocatorFailure error) getQRCodeFail,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function() gettingCreateLocator,
    required TResult Function(LocatorFailure error) getCreateLocatorFail,
  }) {
    return getCreateLocatorSuccess(response, qrCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String result)? getQRCodeSuccess,
    TResult? Function()? gettingQRCode,
    TResult? Function(LocatorFailure error)? getQRCodeFail,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function()? gettingCreateLocator,
    TResult? Function(LocatorFailure error)? getCreateLocatorFail,
  }) {
    return getCreateLocatorSuccess?.call(response, qrCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String result)? getQRCodeSuccess,
    TResult Function()? gettingQRCode,
    TResult Function(LocatorFailure error)? getQRCodeFail,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function()? gettingCreateLocator,
    TResult Function(LocatorFailure error)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (getCreateLocatorSuccess != null) {
      return getCreateLocatorSuccess(response, qrCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEvent value) initial,
    required TResult Function(_GetQRCodeSuccessState value) getQRCodeSuccess,
    required TResult Function(_GettingQRCodeEvent value) gettingQRCode,
    required TResult Function(_GetQRCodeFailState value) getQRCodeFail,
    required TResult Function(_GetCreateLocatorSuccessState value)
        getCreateLocatorSuccess,
    required TResult Function(_GettingCreateQuickSendLocatorState value)
        gettingCreateLocator,
    required TResult Function(_GetCreateLocatorFailState value)
        getCreateLocatorFail,
  }) {
    return getCreateLocatorSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEvent value)? initial,
    TResult? Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult? Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult? Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult? Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult? Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult? Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
  }) {
    return getCreateLocatorSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEvent value)? initial,
    TResult Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (getCreateLocatorSuccess != null) {
      return getCreateLocatorSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetCreateLocatorSuccessState implements QuickSendLocatorState {
  const factory _GetCreateLocatorSuccessState(
      {required final BaseLocatorResponse response,
      required final Uint8List? qrCode}) = _$GetCreateLocatorSuccessStateImpl;

  BaseLocatorResponse get response;
  Uint8List? get qrCode;
  @JsonKey(ignore: true)
  _$$GetCreateLocatorSuccessStateImplCopyWith<
          _$GetCreateLocatorSuccessStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GettingCreateQuickSendLocatorStateImplCopyWith<$Res> {
  factory _$$GettingCreateQuickSendLocatorStateImplCopyWith(
          _$GettingCreateQuickSendLocatorStateImpl value,
          $Res Function(_$GettingCreateQuickSendLocatorStateImpl) then) =
      __$$GettingCreateQuickSendLocatorStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GettingCreateQuickSendLocatorStateImplCopyWithImpl<$Res>
    extends _$QuickSendLocatorStateCopyWithImpl<$Res,
        _$GettingCreateQuickSendLocatorStateImpl>
    implements _$$GettingCreateQuickSendLocatorStateImplCopyWith<$Res> {
  __$$GettingCreateQuickSendLocatorStateImplCopyWithImpl(
      _$GettingCreateQuickSendLocatorStateImpl _value,
      $Res Function(_$GettingCreateQuickSendLocatorStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$GettingCreateQuickSendLocatorStateImpl
    implements _GettingCreateQuickSendLocatorState {
  const _$GettingCreateQuickSendLocatorStateImpl();

  @override
  String toString() {
    return 'QuickSendLocatorState.gettingCreateLocator()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GettingCreateQuickSendLocatorStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String result) getQRCodeSuccess,
    required TResult Function() gettingQRCode,
    required TResult Function(LocatorFailure error) getQRCodeFail,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function() gettingCreateLocator,
    required TResult Function(LocatorFailure error) getCreateLocatorFail,
  }) {
    return gettingCreateLocator();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String result)? getQRCodeSuccess,
    TResult? Function()? gettingQRCode,
    TResult? Function(LocatorFailure error)? getQRCodeFail,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function()? gettingCreateLocator,
    TResult? Function(LocatorFailure error)? getCreateLocatorFail,
  }) {
    return gettingCreateLocator?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String result)? getQRCodeSuccess,
    TResult Function()? gettingQRCode,
    TResult Function(LocatorFailure error)? getQRCodeFail,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function()? gettingCreateLocator,
    TResult Function(LocatorFailure error)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (gettingCreateLocator != null) {
      return gettingCreateLocator();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEvent value) initial,
    required TResult Function(_GetQRCodeSuccessState value) getQRCodeSuccess,
    required TResult Function(_GettingQRCodeEvent value) gettingQRCode,
    required TResult Function(_GetQRCodeFailState value) getQRCodeFail,
    required TResult Function(_GetCreateLocatorSuccessState value)
        getCreateLocatorSuccess,
    required TResult Function(_GettingCreateQuickSendLocatorState value)
        gettingCreateLocator,
    required TResult Function(_GetCreateLocatorFailState value)
        getCreateLocatorFail,
  }) {
    return gettingCreateLocator(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEvent value)? initial,
    TResult? Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult? Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult? Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult? Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult? Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult? Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
  }) {
    return gettingCreateLocator?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEvent value)? initial,
    TResult Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (gettingCreateLocator != null) {
      return gettingCreateLocator(this);
    }
    return orElse();
  }
}

abstract class _GettingCreateQuickSendLocatorState
    implements QuickSendLocatorState {
  const factory _GettingCreateQuickSendLocatorState() =
      _$GettingCreateQuickSendLocatorStateImpl;
}

/// @nodoc
abstract class _$$GetCreateLocatorFailStateImplCopyWith<$Res> {
  factory _$$GetCreateLocatorFailStateImplCopyWith(
          _$GetCreateLocatorFailStateImpl value,
          $Res Function(_$GetCreateLocatorFailStateImpl) then) =
      __$$GetCreateLocatorFailStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LocatorFailure error});

  $LocatorFailureCopyWith<$Res> get error;
}

/// @nodoc
class __$$GetCreateLocatorFailStateImplCopyWithImpl<$Res>
    extends _$QuickSendLocatorStateCopyWithImpl<$Res,
        _$GetCreateLocatorFailStateImpl>
    implements _$$GetCreateLocatorFailStateImplCopyWith<$Res> {
  __$$GetCreateLocatorFailStateImplCopyWithImpl(
      _$GetCreateLocatorFailStateImpl _value,
      $Res Function(_$GetCreateLocatorFailStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$GetCreateLocatorFailStateImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as LocatorFailure,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LocatorFailureCopyWith<$Res> get error {
    return $LocatorFailureCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$GetCreateLocatorFailStateImpl implements _GetCreateLocatorFailState {
  const _$GetCreateLocatorFailStateImpl({required this.error});

  @override
  final LocatorFailure error;

  @override
  String toString() {
    return 'QuickSendLocatorState.getCreateLocatorFail(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetCreateLocatorFailStateImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetCreateLocatorFailStateImplCopyWith<_$GetCreateLocatorFailStateImpl>
      get copyWith => __$$GetCreateLocatorFailStateImplCopyWithImpl<
          _$GetCreateLocatorFailStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String result) getQRCodeSuccess,
    required TResult Function() gettingQRCode,
    required TResult Function(LocatorFailure error) getQRCodeFail,
    required TResult Function(BaseLocatorResponse response, Uint8List? qrCode)
        getCreateLocatorSuccess,
    required TResult Function() gettingCreateLocator,
    required TResult Function(LocatorFailure error) getCreateLocatorFail,
  }) {
    return getCreateLocatorFail(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String result)? getQRCodeSuccess,
    TResult? Function()? gettingQRCode,
    TResult? Function(LocatorFailure error)? getQRCodeFail,
    TResult? Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult? Function()? gettingCreateLocator,
    TResult? Function(LocatorFailure error)? getCreateLocatorFail,
  }) {
    return getCreateLocatorFail?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String result)? getQRCodeSuccess,
    TResult Function()? gettingQRCode,
    TResult Function(LocatorFailure error)? getQRCodeFail,
    TResult Function(BaseLocatorResponse response, Uint8List? qrCode)?
        getCreateLocatorSuccess,
    TResult Function()? gettingCreateLocator,
    TResult Function(LocatorFailure error)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (getCreateLocatorFail != null) {
      return getCreateLocatorFail(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEvent value) initial,
    required TResult Function(_GetQRCodeSuccessState value) getQRCodeSuccess,
    required TResult Function(_GettingQRCodeEvent value) gettingQRCode,
    required TResult Function(_GetQRCodeFailState value) getQRCodeFail,
    required TResult Function(_GetCreateLocatorSuccessState value)
        getCreateLocatorSuccess,
    required TResult Function(_GettingCreateQuickSendLocatorState value)
        gettingCreateLocator,
    required TResult Function(_GetCreateLocatorFailState value)
        getCreateLocatorFail,
  }) {
    return getCreateLocatorFail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEvent value)? initial,
    TResult? Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult? Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult? Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult? Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult? Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult? Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
  }) {
    return getCreateLocatorFail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEvent value)? initial,
    TResult Function(_GetQRCodeSuccessState value)? getQRCodeSuccess,
    TResult Function(_GettingQRCodeEvent value)? gettingQRCode,
    TResult Function(_GetQRCodeFailState value)? getQRCodeFail,
    TResult Function(_GetCreateLocatorSuccessState value)?
        getCreateLocatorSuccess,
    TResult Function(_GettingCreateQuickSendLocatorState value)?
        gettingCreateLocator,
    TResult Function(_GetCreateLocatorFailState value)? getCreateLocatorFail,
    required TResult orElse(),
  }) {
    if (getCreateLocatorFail != null) {
      return getCreateLocatorFail(this);
    }
    return orElse();
  }
}

abstract class _GetCreateLocatorFailState implements QuickSendLocatorState {
  const factory _GetCreateLocatorFailState(
      {required final LocatorFailure error}) = _$GetCreateLocatorFailStateImpl;

  LocatorFailure get error;
  @JsonKey(ignore: true)
  _$$GetCreateLocatorFailStateImplCopyWith<_$GetCreateLocatorFailStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
