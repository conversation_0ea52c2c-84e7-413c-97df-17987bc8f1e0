// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'locator_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LocatorEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(LocatorGetAllRequest request) refreshPage,
    required TResult Function() getLocator,
    required TResult Function(String deleteId) refreshListAfterDelete,
    required TResult Function(String searchKey) searchList,
    required TResult Function() clearSearchList,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(LocatorGetAllRequest request)? refreshPage,
    TResult? Function()? getLocator,
    TResult? Function(String deleteId)? refreshListAfterDelete,
    TResult? Function(String searchKey)? searchList,
    TResult? Function()? clearSearchList,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(LocatorGetAllRequest request)? refreshPage,
    TResult Function()? getLocator,
    TResult Function(String deleteId)? refreshListAfterDelete,
    TResult Function(String searchKey)? searchList,
    TResult Function()? clearSearchList,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_RefreshPage value) refreshPage,
    required TResult Function(_GetAllLocator value) getLocator,
    required TResult Function(_RefreshListAfterDelete value)
        refreshListAfterDelete,
    required TResult Function(_SearchList value) searchList,
    required TResult Function(_ClearSearchList value) clearSearchList,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_RefreshPage value)? refreshPage,
    TResult? Function(_GetAllLocator value)? getLocator,
    TResult? Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult? Function(_SearchList value)? searchList,
    TResult? Function(_ClearSearchList value)? clearSearchList,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_RefreshPage value)? refreshPage,
    TResult Function(_GetAllLocator value)? getLocator,
    TResult Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult Function(_SearchList value)? searchList,
    TResult Function(_ClearSearchList value)? clearSearchList,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorEventCopyWith<$Res> {
  factory $LocatorEventCopyWith(
          LocatorEvent value, $Res Function(LocatorEvent) then) =
      _$LocatorEventCopyWithImpl<$Res, LocatorEvent>;
}

/// @nodoc
class _$LocatorEventCopyWithImpl<$Res, $Val extends LocatorEvent>
    implements $LocatorEventCopyWith<$Res> {
  _$LocatorEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$LocatorEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'LocatorEvent.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(LocatorGetAllRequest request) refreshPage,
    required TResult Function() getLocator,
    required TResult Function(String deleteId) refreshListAfterDelete,
    required TResult Function(String searchKey) searchList,
    required TResult Function() clearSearchList,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(LocatorGetAllRequest request)? refreshPage,
    TResult? Function()? getLocator,
    TResult? Function(String deleteId)? refreshListAfterDelete,
    TResult? Function(String searchKey)? searchList,
    TResult? Function()? clearSearchList,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(LocatorGetAllRequest request)? refreshPage,
    TResult Function()? getLocator,
    TResult Function(String deleteId)? refreshListAfterDelete,
    TResult Function(String searchKey)? searchList,
    TResult Function()? clearSearchList,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_RefreshPage value) refreshPage,
    required TResult Function(_GetAllLocator value) getLocator,
    required TResult Function(_RefreshListAfterDelete value)
        refreshListAfterDelete,
    required TResult Function(_SearchList value) searchList,
    required TResult Function(_ClearSearchList value) clearSearchList,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_RefreshPage value)? refreshPage,
    TResult? Function(_GetAllLocator value)? getLocator,
    TResult? Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult? Function(_SearchList value)? searchList,
    TResult? Function(_ClearSearchList value)? clearSearchList,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_RefreshPage value)? refreshPage,
    TResult Function(_GetAllLocator value)? getLocator,
    TResult Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult Function(_SearchList value)? searchList,
    TResult Function(_ClearSearchList value)? clearSearchList,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements LocatorEvent {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$RefreshPageImplCopyWith<$Res> {
  factory _$$RefreshPageImplCopyWith(
          _$RefreshPageImpl value, $Res Function(_$RefreshPageImpl) then) =
      __$$RefreshPageImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LocatorGetAllRequest request});

  $LocatorGetAllRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$RefreshPageImplCopyWithImpl<$Res>
    extends _$LocatorEventCopyWithImpl<$Res, _$RefreshPageImpl>
    implements _$$RefreshPageImplCopyWith<$Res> {
  __$$RefreshPageImplCopyWithImpl(
      _$RefreshPageImpl _value, $Res Function(_$RefreshPageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$RefreshPageImpl(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as LocatorGetAllRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LocatorGetAllRequestCopyWith<$Res> get request {
    return $LocatorGetAllRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$RefreshPageImpl implements _RefreshPage {
  const _$RefreshPageImpl({required this.request});

  @override
  final LocatorGetAllRequest request;

  @override
  String toString() {
    return 'LocatorEvent.refreshPage(request: $request)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RefreshPageImpl &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RefreshPageImplCopyWith<_$RefreshPageImpl> get copyWith =>
      __$$RefreshPageImplCopyWithImpl<_$RefreshPageImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(LocatorGetAllRequest request) refreshPage,
    required TResult Function() getLocator,
    required TResult Function(String deleteId) refreshListAfterDelete,
    required TResult Function(String searchKey) searchList,
    required TResult Function() clearSearchList,
  }) {
    return refreshPage(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(LocatorGetAllRequest request)? refreshPage,
    TResult? Function()? getLocator,
    TResult? Function(String deleteId)? refreshListAfterDelete,
    TResult? Function(String searchKey)? searchList,
    TResult? Function()? clearSearchList,
  }) {
    return refreshPage?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(LocatorGetAllRequest request)? refreshPage,
    TResult Function()? getLocator,
    TResult Function(String deleteId)? refreshListAfterDelete,
    TResult Function(String searchKey)? searchList,
    TResult Function()? clearSearchList,
    required TResult orElse(),
  }) {
    if (refreshPage != null) {
      return refreshPage(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_RefreshPage value) refreshPage,
    required TResult Function(_GetAllLocator value) getLocator,
    required TResult Function(_RefreshListAfterDelete value)
        refreshListAfterDelete,
    required TResult Function(_SearchList value) searchList,
    required TResult Function(_ClearSearchList value) clearSearchList,
  }) {
    return refreshPage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_RefreshPage value)? refreshPage,
    TResult? Function(_GetAllLocator value)? getLocator,
    TResult? Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult? Function(_SearchList value)? searchList,
    TResult? Function(_ClearSearchList value)? clearSearchList,
  }) {
    return refreshPage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_RefreshPage value)? refreshPage,
    TResult Function(_GetAllLocator value)? getLocator,
    TResult Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult Function(_SearchList value)? searchList,
    TResult Function(_ClearSearchList value)? clearSearchList,
    required TResult orElse(),
  }) {
    if (refreshPage != null) {
      return refreshPage(this);
    }
    return orElse();
  }
}

abstract class _RefreshPage implements LocatorEvent {
  const factory _RefreshPage({required final LocatorGetAllRequest request}) =
      _$RefreshPageImpl;

  LocatorGetAllRequest get request;
  @JsonKey(ignore: true)
  _$$RefreshPageImplCopyWith<_$RefreshPageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetAllLocatorImplCopyWith<$Res> {
  factory _$$GetAllLocatorImplCopyWith(
          _$GetAllLocatorImpl value, $Res Function(_$GetAllLocatorImpl) then) =
      __$$GetAllLocatorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetAllLocatorImplCopyWithImpl<$Res>
    extends _$LocatorEventCopyWithImpl<$Res, _$GetAllLocatorImpl>
    implements _$$GetAllLocatorImplCopyWith<$Res> {
  __$$GetAllLocatorImplCopyWithImpl(
      _$GetAllLocatorImpl _value, $Res Function(_$GetAllLocatorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$GetAllLocatorImpl implements _GetAllLocator {
  const _$GetAllLocatorImpl();

  @override
  String toString() {
    return 'LocatorEvent.getLocator()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetAllLocatorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(LocatorGetAllRequest request) refreshPage,
    required TResult Function() getLocator,
    required TResult Function(String deleteId) refreshListAfterDelete,
    required TResult Function(String searchKey) searchList,
    required TResult Function() clearSearchList,
  }) {
    return getLocator();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(LocatorGetAllRequest request)? refreshPage,
    TResult? Function()? getLocator,
    TResult? Function(String deleteId)? refreshListAfterDelete,
    TResult? Function(String searchKey)? searchList,
    TResult? Function()? clearSearchList,
  }) {
    return getLocator?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(LocatorGetAllRequest request)? refreshPage,
    TResult Function()? getLocator,
    TResult Function(String deleteId)? refreshListAfterDelete,
    TResult Function(String searchKey)? searchList,
    TResult Function()? clearSearchList,
    required TResult orElse(),
  }) {
    if (getLocator != null) {
      return getLocator();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_RefreshPage value) refreshPage,
    required TResult Function(_GetAllLocator value) getLocator,
    required TResult Function(_RefreshListAfterDelete value)
        refreshListAfterDelete,
    required TResult Function(_SearchList value) searchList,
    required TResult Function(_ClearSearchList value) clearSearchList,
  }) {
    return getLocator(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_RefreshPage value)? refreshPage,
    TResult? Function(_GetAllLocator value)? getLocator,
    TResult? Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult? Function(_SearchList value)? searchList,
    TResult? Function(_ClearSearchList value)? clearSearchList,
  }) {
    return getLocator?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_RefreshPage value)? refreshPage,
    TResult Function(_GetAllLocator value)? getLocator,
    TResult Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult Function(_SearchList value)? searchList,
    TResult Function(_ClearSearchList value)? clearSearchList,
    required TResult orElse(),
  }) {
    if (getLocator != null) {
      return getLocator(this);
    }
    return orElse();
  }
}

abstract class _GetAllLocator implements LocatorEvent {
  const factory _GetAllLocator() = _$GetAllLocatorImpl;
}

/// @nodoc
abstract class _$$RefreshListAfterDeleteImplCopyWith<$Res> {
  factory _$$RefreshListAfterDeleteImplCopyWith(
          _$RefreshListAfterDeleteImpl value,
          $Res Function(_$RefreshListAfterDeleteImpl) then) =
      __$$RefreshListAfterDeleteImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String deleteId});
}

/// @nodoc
class __$$RefreshListAfterDeleteImplCopyWithImpl<$Res>
    extends _$LocatorEventCopyWithImpl<$Res, _$RefreshListAfterDeleteImpl>
    implements _$$RefreshListAfterDeleteImplCopyWith<$Res> {
  __$$RefreshListAfterDeleteImplCopyWithImpl(
      _$RefreshListAfterDeleteImpl _value,
      $Res Function(_$RefreshListAfterDeleteImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deleteId = null,
  }) {
    return _then(_$RefreshListAfterDeleteImpl(
      deleteId: null == deleteId
          ? _value.deleteId
          : deleteId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RefreshListAfterDeleteImpl implements _RefreshListAfterDelete {
  const _$RefreshListAfterDeleteImpl({required this.deleteId});

  @override
  final String deleteId;

  @override
  String toString() {
    return 'LocatorEvent.refreshListAfterDelete(deleteId: $deleteId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RefreshListAfterDeleteImpl &&
            (identical(other.deleteId, deleteId) ||
                other.deleteId == deleteId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, deleteId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RefreshListAfterDeleteImplCopyWith<_$RefreshListAfterDeleteImpl>
      get copyWith => __$$RefreshListAfterDeleteImplCopyWithImpl<
          _$RefreshListAfterDeleteImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(LocatorGetAllRequest request) refreshPage,
    required TResult Function() getLocator,
    required TResult Function(String deleteId) refreshListAfterDelete,
    required TResult Function(String searchKey) searchList,
    required TResult Function() clearSearchList,
  }) {
    return refreshListAfterDelete(deleteId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(LocatorGetAllRequest request)? refreshPage,
    TResult? Function()? getLocator,
    TResult? Function(String deleteId)? refreshListAfterDelete,
    TResult? Function(String searchKey)? searchList,
    TResult? Function()? clearSearchList,
  }) {
    return refreshListAfterDelete?.call(deleteId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(LocatorGetAllRequest request)? refreshPage,
    TResult Function()? getLocator,
    TResult Function(String deleteId)? refreshListAfterDelete,
    TResult Function(String searchKey)? searchList,
    TResult Function()? clearSearchList,
    required TResult orElse(),
  }) {
    if (refreshListAfterDelete != null) {
      return refreshListAfterDelete(deleteId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_RefreshPage value) refreshPage,
    required TResult Function(_GetAllLocator value) getLocator,
    required TResult Function(_RefreshListAfterDelete value)
        refreshListAfterDelete,
    required TResult Function(_SearchList value) searchList,
    required TResult Function(_ClearSearchList value) clearSearchList,
  }) {
    return refreshListAfterDelete(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_RefreshPage value)? refreshPage,
    TResult? Function(_GetAllLocator value)? getLocator,
    TResult? Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult? Function(_SearchList value)? searchList,
    TResult? Function(_ClearSearchList value)? clearSearchList,
  }) {
    return refreshListAfterDelete?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_RefreshPage value)? refreshPage,
    TResult Function(_GetAllLocator value)? getLocator,
    TResult Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult Function(_SearchList value)? searchList,
    TResult Function(_ClearSearchList value)? clearSearchList,
    required TResult orElse(),
  }) {
    if (refreshListAfterDelete != null) {
      return refreshListAfterDelete(this);
    }
    return orElse();
  }
}

abstract class _RefreshListAfterDelete implements LocatorEvent {
  const factory _RefreshListAfterDelete({required final String deleteId}) =
      _$RefreshListAfterDeleteImpl;

  String get deleteId;
  @JsonKey(ignore: true)
  _$$RefreshListAfterDeleteImplCopyWith<_$RefreshListAfterDeleteImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchListImplCopyWith<$Res> {
  factory _$$SearchListImplCopyWith(
          _$SearchListImpl value, $Res Function(_$SearchListImpl) then) =
      __$$SearchListImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String searchKey});
}

/// @nodoc
class __$$SearchListImplCopyWithImpl<$Res>
    extends _$LocatorEventCopyWithImpl<$Res, _$SearchListImpl>
    implements _$$SearchListImplCopyWith<$Res> {
  __$$SearchListImplCopyWithImpl(
      _$SearchListImpl _value, $Res Function(_$SearchListImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchKey = null,
  }) {
    return _then(_$SearchListImpl(
      searchKey: null == searchKey
          ? _value.searchKey
          : searchKey // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchListImpl implements _SearchList {
  const _$SearchListImpl({required this.searchKey});

  @override
  final String searchKey;

  @override
  String toString() {
    return 'LocatorEvent.searchList(searchKey: $searchKey)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchListImpl &&
            (identical(other.searchKey, searchKey) ||
                other.searchKey == searchKey));
  }

  @override
  int get hashCode => Object.hash(runtimeType, searchKey);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchListImplCopyWith<_$SearchListImpl> get copyWith =>
      __$$SearchListImplCopyWithImpl<_$SearchListImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(LocatorGetAllRequest request) refreshPage,
    required TResult Function() getLocator,
    required TResult Function(String deleteId) refreshListAfterDelete,
    required TResult Function(String searchKey) searchList,
    required TResult Function() clearSearchList,
  }) {
    return searchList(searchKey);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(LocatorGetAllRequest request)? refreshPage,
    TResult? Function()? getLocator,
    TResult? Function(String deleteId)? refreshListAfterDelete,
    TResult? Function(String searchKey)? searchList,
    TResult? Function()? clearSearchList,
  }) {
    return searchList?.call(searchKey);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(LocatorGetAllRequest request)? refreshPage,
    TResult Function()? getLocator,
    TResult Function(String deleteId)? refreshListAfterDelete,
    TResult Function(String searchKey)? searchList,
    TResult Function()? clearSearchList,
    required TResult orElse(),
  }) {
    if (searchList != null) {
      return searchList(searchKey);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_RefreshPage value) refreshPage,
    required TResult Function(_GetAllLocator value) getLocator,
    required TResult Function(_RefreshListAfterDelete value)
        refreshListAfterDelete,
    required TResult Function(_SearchList value) searchList,
    required TResult Function(_ClearSearchList value) clearSearchList,
  }) {
    return searchList(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_RefreshPage value)? refreshPage,
    TResult? Function(_GetAllLocator value)? getLocator,
    TResult? Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult? Function(_SearchList value)? searchList,
    TResult? Function(_ClearSearchList value)? clearSearchList,
  }) {
    return searchList?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_RefreshPage value)? refreshPage,
    TResult Function(_GetAllLocator value)? getLocator,
    TResult Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult Function(_SearchList value)? searchList,
    TResult Function(_ClearSearchList value)? clearSearchList,
    required TResult orElse(),
  }) {
    if (searchList != null) {
      return searchList(this);
    }
    return orElse();
  }
}

abstract class _SearchList implements LocatorEvent {
  const factory _SearchList({required final String searchKey}) =
      _$SearchListImpl;

  String get searchKey;
  @JsonKey(ignore: true)
  _$$SearchListImplCopyWith<_$SearchListImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearSearchListImplCopyWith<$Res> {
  factory _$$ClearSearchListImplCopyWith(_$ClearSearchListImpl value,
          $Res Function(_$ClearSearchListImpl) then) =
      __$$ClearSearchListImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearSearchListImplCopyWithImpl<$Res>
    extends _$LocatorEventCopyWithImpl<$Res, _$ClearSearchListImpl>
    implements _$$ClearSearchListImplCopyWith<$Res> {
  __$$ClearSearchListImplCopyWithImpl(
      _$ClearSearchListImpl _value, $Res Function(_$ClearSearchListImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearSearchListImpl implements _ClearSearchList {
  const _$ClearSearchListImpl();

  @override
  String toString() {
    return 'LocatorEvent.clearSearchList()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearSearchListImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(LocatorGetAllRequest request) refreshPage,
    required TResult Function() getLocator,
    required TResult Function(String deleteId) refreshListAfterDelete,
    required TResult Function(String searchKey) searchList,
    required TResult Function() clearSearchList,
  }) {
    return clearSearchList();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(LocatorGetAllRequest request)? refreshPage,
    TResult? Function()? getLocator,
    TResult? Function(String deleteId)? refreshListAfterDelete,
    TResult? Function(String searchKey)? searchList,
    TResult? Function()? clearSearchList,
  }) {
    return clearSearchList?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(LocatorGetAllRequest request)? refreshPage,
    TResult Function()? getLocator,
    TResult Function(String deleteId)? refreshListAfterDelete,
    TResult Function(String searchKey)? searchList,
    TResult Function()? clearSearchList,
    required TResult orElse(),
  }) {
    if (clearSearchList != null) {
      return clearSearchList();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_RefreshPage value) refreshPage,
    required TResult Function(_GetAllLocator value) getLocator,
    required TResult Function(_RefreshListAfterDelete value)
        refreshListAfterDelete,
    required TResult Function(_SearchList value) searchList,
    required TResult Function(_ClearSearchList value) clearSearchList,
  }) {
    return clearSearchList(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_RefreshPage value)? refreshPage,
    TResult? Function(_GetAllLocator value)? getLocator,
    TResult? Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult? Function(_SearchList value)? searchList,
    TResult? Function(_ClearSearchList value)? clearSearchList,
  }) {
    return clearSearchList?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_RefreshPage value)? refreshPage,
    TResult Function(_GetAllLocator value)? getLocator,
    TResult Function(_RefreshListAfterDelete value)? refreshListAfterDelete,
    TResult Function(_SearchList value)? searchList,
    TResult Function(_ClearSearchList value)? clearSearchList,
    required TResult orElse(),
  }) {
    if (clearSearchList != null) {
      return clearSearchList(this);
    }
    return orElse();
  }
}

abstract class _ClearSearchList implements LocatorEvent {
  const factory _ClearSearchList() = _$ClearSearchListImpl;
}

/// @nodoc
mixin _$LocatorState {
  List<Locator> get listLocator => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isLoadMore => throw _privateConstructorUsedError;
  LocatorFailure? get readFailure => throw _privateConstructorUsedError;
  int get pageIndex => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError; //Search
  List<Locator>? get listSearchLocator => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $LocatorStateCopyWith<LocatorState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorStateCopyWith<$Res> {
  factory $LocatorStateCopyWith(
          LocatorState value, $Res Function(LocatorState) then) =
      _$LocatorStateCopyWithImpl<$Res, LocatorState>;
  @useResult
  $Res call(
      {List<Locator> listLocator,
      bool isLoading,
      bool isLoadMore,
      LocatorFailure? readFailure,
      int pageIndex,
      int pageSize,
      int total,
      List<Locator>? listSearchLocator});

  $LocatorFailureCopyWith<$Res>? get readFailure;
}

/// @nodoc
class _$LocatorStateCopyWithImpl<$Res, $Val extends LocatorState>
    implements $LocatorStateCopyWith<$Res> {
  _$LocatorStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listLocator = null,
    Object? isLoading = null,
    Object? isLoadMore = null,
    Object? readFailure = freezed,
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? total = null,
    Object? listSearchLocator = freezed,
  }) {
    return _then(_value.copyWith(
      listLocator: null == listLocator
          ? _value.listLocator
          : listLocator // ignore: cast_nullable_to_non_nullable
              as List<Locator>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadMore: null == isLoadMore
          ? _value.isLoadMore
          : isLoadMore // ignore: cast_nullable_to_non_nullable
              as bool,
      readFailure: freezed == readFailure
          ? _value.readFailure
          : readFailure // ignore: cast_nullable_to_non_nullable
              as LocatorFailure?,
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      listSearchLocator: freezed == listSearchLocator
          ? _value.listSearchLocator
          : listSearchLocator // ignore: cast_nullable_to_non_nullable
              as List<Locator>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LocatorFailureCopyWith<$Res>? get readFailure {
    if (_value.readFailure == null) {
      return null;
    }

    return $LocatorFailureCopyWith<$Res>(_value.readFailure!, (value) {
      return _then(_value.copyWith(readFailure: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LocatorStateImplCopyWith<$Res>
    implements $LocatorStateCopyWith<$Res> {
  factory _$$LocatorStateImplCopyWith(
          _$LocatorStateImpl value, $Res Function(_$LocatorStateImpl) then) =
      __$$LocatorStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Locator> listLocator,
      bool isLoading,
      bool isLoadMore,
      LocatorFailure? readFailure,
      int pageIndex,
      int pageSize,
      int total,
      List<Locator>? listSearchLocator});

  @override
  $LocatorFailureCopyWith<$Res>? get readFailure;
}

/// @nodoc
class __$$LocatorStateImplCopyWithImpl<$Res>
    extends _$LocatorStateCopyWithImpl<$Res, _$LocatorStateImpl>
    implements _$$LocatorStateImplCopyWith<$Res> {
  __$$LocatorStateImplCopyWithImpl(
      _$LocatorStateImpl _value, $Res Function(_$LocatorStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listLocator = null,
    Object? isLoading = null,
    Object? isLoadMore = null,
    Object? readFailure = freezed,
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? total = null,
    Object? listSearchLocator = freezed,
  }) {
    return _then(_$LocatorStateImpl(
      listLocator: null == listLocator
          ? _value._listLocator
          : listLocator // ignore: cast_nullable_to_non_nullable
              as List<Locator>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadMore: null == isLoadMore
          ? _value.isLoadMore
          : isLoadMore // ignore: cast_nullable_to_non_nullable
              as bool,
      readFailure: freezed == readFailure
          ? _value.readFailure
          : readFailure // ignore: cast_nullable_to_non_nullable
              as LocatorFailure?,
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      listSearchLocator: freezed == listSearchLocator
          ? _value._listSearchLocator
          : listSearchLocator // ignore: cast_nullable_to_non_nullable
              as List<Locator>?,
    ));
  }
}

/// @nodoc

class _$LocatorStateImpl implements _LocatorState {
  const _$LocatorStateImpl(
      {required final List<Locator> listLocator,
      required this.isLoading,
      required this.isLoadMore,
      required this.readFailure,
      required this.pageIndex,
      required this.pageSize,
      required this.total,
      required final List<Locator>? listSearchLocator})
      : _listLocator = listLocator,
        _listSearchLocator = listSearchLocator;

  final List<Locator> _listLocator;
  @override
  List<Locator> get listLocator {
    if (_listLocator is EqualUnmodifiableListView) return _listLocator;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listLocator);
  }

  @override
  final bool isLoading;
  @override
  final bool isLoadMore;
  @override
  final LocatorFailure? readFailure;
  @override
  final int pageIndex;
  @override
  final int pageSize;
  @override
  final int total;
//Search
  final List<Locator>? _listSearchLocator;
//Search
  @override
  List<Locator>? get listSearchLocator {
    final value = _listSearchLocator;
    if (value == null) return null;
    if (_listSearchLocator is EqualUnmodifiableListView)
      return _listSearchLocator;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'LocatorState(listLocator: $listLocator, isLoading: $isLoading, isLoadMore: $isLoadMore, readFailure: $readFailure, pageIndex: $pageIndex, pageSize: $pageSize, total: $total, listSearchLocator: $listSearchLocator)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorStateImpl &&
            const DeepCollectionEquality()
                .equals(other._listLocator, _listLocator) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isLoadMore, isLoadMore) ||
                other.isLoadMore == isLoadMore) &&
            (identical(other.readFailure, readFailure) ||
                other.readFailure == readFailure) &&
            (identical(other.pageIndex, pageIndex) ||
                other.pageIndex == pageIndex) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality()
                .equals(other._listSearchLocator, _listSearchLocator));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_listLocator),
      isLoading,
      isLoadMore,
      readFailure,
      pageIndex,
      pageSize,
      total,
      const DeepCollectionEquality().hash(_listSearchLocator));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorStateImplCopyWith<_$LocatorStateImpl> get copyWith =>
      __$$LocatorStateImplCopyWithImpl<_$LocatorStateImpl>(this, _$identity);
}

abstract class _LocatorState implements LocatorState {
  const factory _LocatorState(
      {required final List<Locator> listLocator,
      required final bool isLoading,
      required final bool isLoadMore,
      required final LocatorFailure? readFailure,
      required final int pageIndex,
      required final int pageSize,
      required final int total,
      required final List<Locator>? listSearchLocator}) = _$LocatorStateImpl;

  @override
  List<Locator> get listLocator;
  @override
  bool get isLoading;
  @override
  bool get isLoadMore;
  @override
  LocatorFailure? get readFailure;
  @override
  int get pageIndex;
  @override
  int get pageSize;
  @override
  int get total;
  @override //Search
  List<Locator>? get listSearchLocator;
  @override
  @JsonKey(ignore: true)
  _$$LocatorStateImplCopyWith<_$LocatorStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
