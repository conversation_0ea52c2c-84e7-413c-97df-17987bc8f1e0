// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_add_or_edit_locator_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ManageAddOrEditLocatorEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<Vehicle> listVehicle) initial,
    required TResult Function(bool isEdit) getLocatorParams,
    required TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)
        editLocatorTime,
    required TResult Function(String id, LocatorType locatorType)
        editLocatorItem,
    required TResult Function(LocatorType locatorType) selectAllLocatorItem,
    required TResult Function() createNewLocator,
    required TResult Function() clear,
    required TResult Function() syncDataFromEdit,
    required TResult Function(String id) getDetail,
    required TResult Function() editLocator,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<Vehicle> listVehicle)? initial,
    TResult? Function(bool isEdit)? getLocatorParams,
    TResult? Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult? Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult? Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult? Function()? createNewLocator,
    TResult? Function()? clear,
    TResult? Function()? syncDataFromEdit,
    TResult? Function(String id)? getDetail,
    TResult? Function()? editLocator,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<Vehicle> listVehicle)? initial,
    TResult Function(bool isEdit)? getLocatorParams,
    TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult Function()? createNewLocator,
    TResult Function()? clear,
    TResult Function()? syncDataFromEdit,
    TResult Function(String id)? getDetail,
    TResult Function()? editLocator,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetLocatorParams value) getLocatorParams,
    required TResult Function(_EditNameAndDuration value) editLocatorTime,
    required TResult Function(_EditLocatorItem value) editLocatorItem,
    required TResult Function(_SelectAllLocatorItem value) selectAllLocatorItem,
    required TResult Function(_CreateNewLocator value) createNewLocator,
    required TResult Function(_Clear value) clear,
    required TResult Function(_SyncDataFromEdit value) syncDataFromEdit,
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_EditLocator value) editLocator,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetLocatorParams value)? getLocatorParams,
    TResult? Function(_EditNameAndDuration value)? editLocatorTime,
    TResult? Function(_EditLocatorItem value)? editLocatorItem,
    TResult? Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult? Function(_CreateNewLocator value)? createNewLocator,
    TResult? Function(_Clear value)? clear,
    TResult? Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_EditLocator value)? editLocator,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetLocatorParams value)? getLocatorParams,
    TResult Function(_EditNameAndDuration value)? editLocatorTime,
    TResult Function(_EditLocatorItem value)? editLocatorItem,
    TResult Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult Function(_CreateNewLocator value)? createNewLocator,
    TResult Function(_Clear value)? clear,
    TResult Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_EditLocator value)? editLocator,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageAddOrEditLocatorEventCopyWith<$Res> {
  factory $ManageAddOrEditLocatorEventCopyWith(
          ManageAddOrEditLocatorEvent value,
          $Res Function(ManageAddOrEditLocatorEvent) then) =
      _$ManageAddOrEditLocatorEventCopyWithImpl<$Res,
          ManageAddOrEditLocatorEvent>;
}

/// @nodoc
class _$ManageAddOrEditLocatorEventCopyWithImpl<$Res,
        $Val extends ManageAddOrEditLocatorEvent>
    implements $ManageAddOrEditLocatorEventCopyWith<$Res> {
  _$ManageAddOrEditLocatorEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Vehicle> listVehicle});
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$ManageAddOrEditLocatorEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicle = null,
  }) {
    return _then(_$InitialImpl(
      listVehicle: null == listVehicle
          ? _value._listVehicle
          : listVehicle // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
    ));
  }
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl({required final List<Vehicle> listVehicle})
      : _listVehicle = listVehicle;

  final List<Vehicle> _listVehicle;
  @override
  List<Vehicle> get listVehicle {
    if (_listVehicle is EqualUnmodifiableListView) return _listVehicle;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicle);
  }

  @override
  String toString() {
    return 'ManageAddOrEditLocatorEvent.initial(listVehicle: $listVehicle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InitialImpl &&
            const DeepCollectionEquality()
                .equals(other._listVehicle, _listVehicle));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_listVehicle));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InitialImplCopyWith<_$InitialImpl> get copyWith =>
      __$$InitialImplCopyWithImpl<_$InitialImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<Vehicle> listVehicle) initial,
    required TResult Function(bool isEdit) getLocatorParams,
    required TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)
        editLocatorTime,
    required TResult Function(String id, LocatorType locatorType)
        editLocatorItem,
    required TResult Function(LocatorType locatorType) selectAllLocatorItem,
    required TResult Function() createNewLocator,
    required TResult Function() clear,
    required TResult Function() syncDataFromEdit,
    required TResult Function(String id) getDetail,
    required TResult Function() editLocator,
  }) {
    return initial(listVehicle);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<Vehicle> listVehicle)? initial,
    TResult? Function(bool isEdit)? getLocatorParams,
    TResult? Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult? Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult? Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult? Function()? createNewLocator,
    TResult? Function()? clear,
    TResult? Function()? syncDataFromEdit,
    TResult? Function(String id)? getDetail,
    TResult? Function()? editLocator,
  }) {
    return initial?.call(listVehicle);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<Vehicle> listVehicle)? initial,
    TResult Function(bool isEdit)? getLocatorParams,
    TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult Function()? createNewLocator,
    TResult Function()? clear,
    TResult Function()? syncDataFromEdit,
    TResult Function(String id)? getDetail,
    TResult Function()? editLocator,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(listVehicle);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetLocatorParams value) getLocatorParams,
    required TResult Function(_EditNameAndDuration value) editLocatorTime,
    required TResult Function(_EditLocatorItem value) editLocatorItem,
    required TResult Function(_SelectAllLocatorItem value) selectAllLocatorItem,
    required TResult Function(_CreateNewLocator value) createNewLocator,
    required TResult Function(_Clear value) clear,
    required TResult Function(_SyncDataFromEdit value) syncDataFromEdit,
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_EditLocator value) editLocator,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetLocatorParams value)? getLocatorParams,
    TResult? Function(_EditNameAndDuration value)? editLocatorTime,
    TResult? Function(_EditLocatorItem value)? editLocatorItem,
    TResult? Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult? Function(_CreateNewLocator value)? createNewLocator,
    TResult? Function(_Clear value)? clear,
    TResult? Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_EditLocator value)? editLocator,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetLocatorParams value)? getLocatorParams,
    TResult Function(_EditNameAndDuration value)? editLocatorTime,
    TResult Function(_EditLocatorItem value)? editLocatorItem,
    TResult Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult Function(_CreateNewLocator value)? createNewLocator,
    TResult Function(_Clear value)? clear,
    TResult Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_EditLocator value)? editLocator,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements ManageAddOrEditLocatorEvent {
  const factory _Initial({required final List<Vehicle> listVehicle}) =
      _$InitialImpl;

  List<Vehicle> get listVehicle;
  @JsonKey(ignore: true)
  _$$InitialImplCopyWith<_$InitialImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetLocatorParamsImplCopyWith<$Res> {
  factory _$$GetLocatorParamsImplCopyWith(_$GetLocatorParamsImpl value,
          $Res Function(_$GetLocatorParamsImpl) then) =
      __$$GetLocatorParamsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isEdit});
}

/// @nodoc
class __$$GetLocatorParamsImplCopyWithImpl<$Res>
    extends _$ManageAddOrEditLocatorEventCopyWithImpl<$Res,
        _$GetLocatorParamsImpl>
    implements _$$GetLocatorParamsImplCopyWith<$Res> {
  __$$GetLocatorParamsImplCopyWithImpl(_$GetLocatorParamsImpl _value,
      $Res Function(_$GetLocatorParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isEdit = null,
  }) {
    return _then(_$GetLocatorParamsImpl(
      isEdit: null == isEdit
          ? _value.isEdit
          : isEdit // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$GetLocatorParamsImpl implements _GetLocatorParams {
  const _$GetLocatorParamsImpl({required this.isEdit});

  @override
  final bool isEdit;

  @override
  String toString() {
    return 'ManageAddOrEditLocatorEvent.getLocatorParams(isEdit: $isEdit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetLocatorParamsImpl &&
            (identical(other.isEdit, isEdit) || other.isEdit == isEdit));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isEdit);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetLocatorParamsImplCopyWith<_$GetLocatorParamsImpl> get copyWith =>
      __$$GetLocatorParamsImplCopyWithImpl<_$GetLocatorParamsImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<Vehicle> listVehicle) initial,
    required TResult Function(bool isEdit) getLocatorParams,
    required TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)
        editLocatorTime,
    required TResult Function(String id, LocatorType locatorType)
        editLocatorItem,
    required TResult Function(LocatorType locatorType) selectAllLocatorItem,
    required TResult Function() createNewLocator,
    required TResult Function() clear,
    required TResult Function() syncDataFromEdit,
    required TResult Function(String id) getDetail,
    required TResult Function() editLocator,
  }) {
    return getLocatorParams(isEdit);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<Vehicle> listVehicle)? initial,
    TResult? Function(bool isEdit)? getLocatorParams,
    TResult? Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult? Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult? Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult? Function()? createNewLocator,
    TResult? Function()? clear,
    TResult? Function()? syncDataFromEdit,
    TResult? Function(String id)? getDetail,
    TResult? Function()? editLocator,
  }) {
    return getLocatorParams?.call(isEdit);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<Vehicle> listVehicle)? initial,
    TResult Function(bool isEdit)? getLocatorParams,
    TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult Function()? createNewLocator,
    TResult Function()? clear,
    TResult Function()? syncDataFromEdit,
    TResult Function(String id)? getDetail,
    TResult Function()? editLocator,
    required TResult orElse(),
  }) {
    if (getLocatorParams != null) {
      return getLocatorParams(isEdit);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetLocatorParams value) getLocatorParams,
    required TResult Function(_EditNameAndDuration value) editLocatorTime,
    required TResult Function(_EditLocatorItem value) editLocatorItem,
    required TResult Function(_SelectAllLocatorItem value) selectAllLocatorItem,
    required TResult Function(_CreateNewLocator value) createNewLocator,
    required TResult Function(_Clear value) clear,
    required TResult Function(_SyncDataFromEdit value) syncDataFromEdit,
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_EditLocator value) editLocator,
  }) {
    return getLocatorParams(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetLocatorParams value)? getLocatorParams,
    TResult? Function(_EditNameAndDuration value)? editLocatorTime,
    TResult? Function(_EditLocatorItem value)? editLocatorItem,
    TResult? Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult? Function(_CreateNewLocator value)? createNewLocator,
    TResult? Function(_Clear value)? clear,
    TResult? Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_EditLocator value)? editLocator,
  }) {
    return getLocatorParams?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetLocatorParams value)? getLocatorParams,
    TResult Function(_EditNameAndDuration value)? editLocatorTime,
    TResult Function(_EditLocatorItem value)? editLocatorItem,
    TResult Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult Function(_CreateNewLocator value)? createNewLocator,
    TResult Function(_Clear value)? clear,
    TResult Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_EditLocator value)? editLocator,
    required TResult orElse(),
  }) {
    if (getLocatorParams != null) {
      return getLocatorParams(this);
    }
    return orElse();
  }
}

abstract class _GetLocatorParams implements ManageAddOrEditLocatorEvent {
  const factory _GetLocatorParams({required final bool isEdit}) =
      _$GetLocatorParamsImpl;

  bool get isEdit;
  @JsonKey(ignore: true)
  _$$GetLocatorParamsImplCopyWith<_$GetLocatorParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EditNameAndDurationImplCopyWith<$Res> {
  factory _$$EditNameAndDurationImplCopyWith(_$EditNameAndDurationImpl value,
          $Res Function(_$EditNameAndDurationImpl) then) =
      __$$EditNameAndDurationImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String name,
      DateTime fromTime,
      String duration,
      LocatorTimeType locatorTimeType});
}

/// @nodoc
class __$$EditNameAndDurationImplCopyWithImpl<$Res>
    extends _$ManageAddOrEditLocatorEventCopyWithImpl<$Res,
        _$EditNameAndDurationImpl>
    implements _$$EditNameAndDurationImplCopyWith<$Res> {
  __$$EditNameAndDurationImplCopyWithImpl(_$EditNameAndDurationImpl _value,
      $Res Function(_$EditNameAndDurationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? fromTime = null,
    Object? duration = null,
    Object? locatorTimeType = null,
  }) {
    return _then(_$EditNameAndDurationImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      fromTime: null == fromTime
          ? _value.fromTime
          : fromTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String,
      locatorTimeType: null == locatorTimeType
          ? _value.locatorTimeType
          : locatorTimeType // ignore: cast_nullable_to_non_nullable
              as LocatorTimeType,
    ));
  }
}

/// @nodoc

class _$EditNameAndDurationImpl implements _EditNameAndDuration {
  const _$EditNameAndDurationImpl(
      {required this.name,
      required this.fromTime,
      required this.duration,
      required this.locatorTimeType});

  @override
  final String name;
  @override
  final DateTime fromTime;
  @override
  final String duration;
  @override
  final LocatorTimeType locatorTimeType;

  @override
  String toString() {
    return 'ManageAddOrEditLocatorEvent.editLocatorTime(name: $name, fromTime: $fromTime, duration: $duration, locatorTimeType: $locatorTimeType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditNameAndDurationImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.fromTime, fromTime) ||
                other.fromTime == fromTime) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.locatorTimeType, locatorTimeType) ||
                other.locatorTimeType == locatorTimeType));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, name, fromTime, duration, locatorTimeType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$EditNameAndDurationImplCopyWith<_$EditNameAndDurationImpl> get copyWith =>
      __$$EditNameAndDurationImplCopyWithImpl<_$EditNameAndDurationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<Vehicle> listVehicle) initial,
    required TResult Function(bool isEdit) getLocatorParams,
    required TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)
        editLocatorTime,
    required TResult Function(String id, LocatorType locatorType)
        editLocatorItem,
    required TResult Function(LocatorType locatorType) selectAllLocatorItem,
    required TResult Function() createNewLocator,
    required TResult Function() clear,
    required TResult Function() syncDataFromEdit,
    required TResult Function(String id) getDetail,
    required TResult Function() editLocator,
  }) {
    return editLocatorTime(name, fromTime, duration, locatorTimeType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<Vehicle> listVehicle)? initial,
    TResult? Function(bool isEdit)? getLocatorParams,
    TResult? Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult? Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult? Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult? Function()? createNewLocator,
    TResult? Function()? clear,
    TResult? Function()? syncDataFromEdit,
    TResult? Function(String id)? getDetail,
    TResult? Function()? editLocator,
  }) {
    return editLocatorTime?.call(name, fromTime, duration, locatorTimeType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<Vehicle> listVehicle)? initial,
    TResult Function(bool isEdit)? getLocatorParams,
    TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult Function()? createNewLocator,
    TResult Function()? clear,
    TResult Function()? syncDataFromEdit,
    TResult Function(String id)? getDetail,
    TResult Function()? editLocator,
    required TResult orElse(),
  }) {
    if (editLocatorTime != null) {
      return editLocatorTime(name, fromTime, duration, locatorTimeType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetLocatorParams value) getLocatorParams,
    required TResult Function(_EditNameAndDuration value) editLocatorTime,
    required TResult Function(_EditLocatorItem value) editLocatorItem,
    required TResult Function(_SelectAllLocatorItem value) selectAllLocatorItem,
    required TResult Function(_CreateNewLocator value) createNewLocator,
    required TResult Function(_Clear value) clear,
    required TResult Function(_SyncDataFromEdit value) syncDataFromEdit,
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_EditLocator value) editLocator,
  }) {
    return editLocatorTime(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetLocatorParams value)? getLocatorParams,
    TResult? Function(_EditNameAndDuration value)? editLocatorTime,
    TResult? Function(_EditLocatorItem value)? editLocatorItem,
    TResult? Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult? Function(_CreateNewLocator value)? createNewLocator,
    TResult? Function(_Clear value)? clear,
    TResult? Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_EditLocator value)? editLocator,
  }) {
    return editLocatorTime?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetLocatorParams value)? getLocatorParams,
    TResult Function(_EditNameAndDuration value)? editLocatorTime,
    TResult Function(_EditLocatorItem value)? editLocatorItem,
    TResult Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult Function(_CreateNewLocator value)? createNewLocator,
    TResult Function(_Clear value)? clear,
    TResult Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_EditLocator value)? editLocator,
    required TResult orElse(),
  }) {
    if (editLocatorTime != null) {
      return editLocatorTime(this);
    }
    return orElse();
  }
}

abstract class _EditNameAndDuration implements ManageAddOrEditLocatorEvent {
  const factory _EditNameAndDuration(
          {required final String name,
          required final DateTime fromTime,
          required final String duration,
          required final LocatorTimeType locatorTimeType}) =
      _$EditNameAndDurationImpl;

  String get name;
  DateTime get fromTime;
  String get duration;
  LocatorTimeType get locatorTimeType;
  @JsonKey(ignore: true)
  _$$EditNameAndDurationImplCopyWith<_$EditNameAndDurationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EditLocatorItemImplCopyWith<$Res> {
  factory _$$EditLocatorItemImplCopyWith(_$EditLocatorItemImpl value,
          $Res Function(_$EditLocatorItemImpl) then) =
      __$$EditLocatorItemImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, LocatorType locatorType});
}

/// @nodoc
class __$$EditLocatorItemImplCopyWithImpl<$Res>
    extends _$ManageAddOrEditLocatorEventCopyWithImpl<$Res,
        _$EditLocatorItemImpl> implements _$$EditLocatorItemImplCopyWith<$Res> {
  __$$EditLocatorItemImplCopyWithImpl(
      _$EditLocatorItemImpl _value, $Res Function(_$EditLocatorItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? locatorType = null,
  }) {
    return _then(_$EditLocatorItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      locatorType: null == locatorType
          ? _value.locatorType
          : locatorType // ignore: cast_nullable_to_non_nullable
              as LocatorType,
    ));
  }
}

/// @nodoc

class _$EditLocatorItemImpl implements _EditLocatorItem {
  const _$EditLocatorItemImpl({required this.id, required this.locatorType});

  @override
  final String id;
  @override
  final LocatorType locatorType;

  @override
  String toString() {
    return 'ManageAddOrEditLocatorEvent.editLocatorItem(id: $id, locatorType: $locatorType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditLocatorItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.locatorType, locatorType) ||
                other.locatorType == locatorType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, locatorType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$EditLocatorItemImplCopyWith<_$EditLocatorItemImpl> get copyWith =>
      __$$EditLocatorItemImplCopyWithImpl<_$EditLocatorItemImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<Vehicle> listVehicle) initial,
    required TResult Function(bool isEdit) getLocatorParams,
    required TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)
        editLocatorTime,
    required TResult Function(String id, LocatorType locatorType)
        editLocatorItem,
    required TResult Function(LocatorType locatorType) selectAllLocatorItem,
    required TResult Function() createNewLocator,
    required TResult Function() clear,
    required TResult Function() syncDataFromEdit,
    required TResult Function(String id) getDetail,
    required TResult Function() editLocator,
  }) {
    return editLocatorItem(id, locatorType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<Vehicle> listVehicle)? initial,
    TResult? Function(bool isEdit)? getLocatorParams,
    TResult? Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult? Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult? Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult? Function()? createNewLocator,
    TResult? Function()? clear,
    TResult? Function()? syncDataFromEdit,
    TResult? Function(String id)? getDetail,
    TResult? Function()? editLocator,
  }) {
    return editLocatorItem?.call(id, locatorType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<Vehicle> listVehicle)? initial,
    TResult Function(bool isEdit)? getLocatorParams,
    TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult Function()? createNewLocator,
    TResult Function()? clear,
    TResult Function()? syncDataFromEdit,
    TResult Function(String id)? getDetail,
    TResult Function()? editLocator,
    required TResult orElse(),
  }) {
    if (editLocatorItem != null) {
      return editLocatorItem(id, locatorType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetLocatorParams value) getLocatorParams,
    required TResult Function(_EditNameAndDuration value) editLocatorTime,
    required TResult Function(_EditLocatorItem value) editLocatorItem,
    required TResult Function(_SelectAllLocatorItem value) selectAllLocatorItem,
    required TResult Function(_CreateNewLocator value) createNewLocator,
    required TResult Function(_Clear value) clear,
    required TResult Function(_SyncDataFromEdit value) syncDataFromEdit,
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_EditLocator value) editLocator,
  }) {
    return editLocatorItem(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetLocatorParams value)? getLocatorParams,
    TResult? Function(_EditNameAndDuration value)? editLocatorTime,
    TResult? Function(_EditLocatorItem value)? editLocatorItem,
    TResult? Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult? Function(_CreateNewLocator value)? createNewLocator,
    TResult? Function(_Clear value)? clear,
    TResult? Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_EditLocator value)? editLocator,
  }) {
    return editLocatorItem?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetLocatorParams value)? getLocatorParams,
    TResult Function(_EditNameAndDuration value)? editLocatorTime,
    TResult Function(_EditLocatorItem value)? editLocatorItem,
    TResult Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult Function(_CreateNewLocator value)? createNewLocator,
    TResult Function(_Clear value)? clear,
    TResult Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_EditLocator value)? editLocator,
    required TResult orElse(),
  }) {
    if (editLocatorItem != null) {
      return editLocatorItem(this);
    }
    return orElse();
  }
}

abstract class _EditLocatorItem implements ManageAddOrEditLocatorEvent {
  const factory _EditLocatorItem(
      {required final String id,
      required final LocatorType locatorType}) = _$EditLocatorItemImpl;

  String get id;
  LocatorType get locatorType;
  @JsonKey(ignore: true)
  _$$EditLocatorItemImplCopyWith<_$EditLocatorItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectAllLocatorItemImplCopyWith<$Res> {
  factory _$$SelectAllLocatorItemImplCopyWith(_$SelectAllLocatorItemImpl value,
          $Res Function(_$SelectAllLocatorItemImpl) then) =
      __$$SelectAllLocatorItemImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LocatorType locatorType});
}

/// @nodoc
class __$$SelectAllLocatorItemImplCopyWithImpl<$Res>
    extends _$ManageAddOrEditLocatorEventCopyWithImpl<$Res,
        _$SelectAllLocatorItemImpl>
    implements _$$SelectAllLocatorItemImplCopyWith<$Res> {
  __$$SelectAllLocatorItemImplCopyWithImpl(_$SelectAllLocatorItemImpl _value,
      $Res Function(_$SelectAllLocatorItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? locatorType = null,
  }) {
    return _then(_$SelectAllLocatorItemImpl(
      locatorType: null == locatorType
          ? _value.locatorType
          : locatorType // ignore: cast_nullable_to_non_nullable
              as LocatorType,
    ));
  }
}

/// @nodoc

class _$SelectAllLocatorItemImpl implements _SelectAllLocatorItem {
  const _$SelectAllLocatorItemImpl({required this.locatorType});

  @override
  final LocatorType locatorType;

  @override
  String toString() {
    return 'ManageAddOrEditLocatorEvent.selectAllLocatorItem(locatorType: $locatorType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectAllLocatorItemImpl &&
            (identical(other.locatorType, locatorType) ||
                other.locatorType == locatorType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, locatorType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectAllLocatorItemImplCopyWith<_$SelectAllLocatorItemImpl>
      get copyWith =>
          __$$SelectAllLocatorItemImplCopyWithImpl<_$SelectAllLocatorItemImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<Vehicle> listVehicle) initial,
    required TResult Function(bool isEdit) getLocatorParams,
    required TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)
        editLocatorTime,
    required TResult Function(String id, LocatorType locatorType)
        editLocatorItem,
    required TResult Function(LocatorType locatorType) selectAllLocatorItem,
    required TResult Function() createNewLocator,
    required TResult Function() clear,
    required TResult Function() syncDataFromEdit,
    required TResult Function(String id) getDetail,
    required TResult Function() editLocator,
  }) {
    return selectAllLocatorItem(locatorType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<Vehicle> listVehicle)? initial,
    TResult? Function(bool isEdit)? getLocatorParams,
    TResult? Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult? Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult? Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult? Function()? createNewLocator,
    TResult? Function()? clear,
    TResult? Function()? syncDataFromEdit,
    TResult? Function(String id)? getDetail,
    TResult? Function()? editLocator,
  }) {
    return selectAllLocatorItem?.call(locatorType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<Vehicle> listVehicle)? initial,
    TResult Function(bool isEdit)? getLocatorParams,
    TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult Function()? createNewLocator,
    TResult Function()? clear,
    TResult Function()? syncDataFromEdit,
    TResult Function(String id)? getDetail,
    TResult Function()? editLocator,
    required TResult orElse(),
  }) {
    if (selectAllLocatorItem != null) {
      return selectAllLocatorItem(locatorType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetLocatorParams value) getLocatorParams,
    required TResult Function(_EditNameAndDuration value) editLocatorTime,
    required TResult Function(_EditLocatorItem value) editLocatorItem,
    required TResult Function(_SelectAllLocatorItem value) selectAllLocatorItem,
    required TResult Function(_CreateNewLocator value) createNewLocator,
    required TResult Function(_Clear value) clear,
    required TResult Function(_SyncDataFromEdit value) syncDataFromEdit,
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_EditLocator value) editLocator,
  }) {
    return selectAllLocatorItem(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetLocatorParams value)? getLocatorParams,
    TResult? Function(_EditNameAndDuration value)? editLocatorTime,
    TResult? Function(_EditLocatorItem value)? editLocatorItem,
    TResult? Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult? Function(_CreateNewLocator value)? createNewLocator,
    TResult? Function(_Clear value)? clear,
    TResult? Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_EditLocator value)? editLocator,
  }) {
    return selectAllLocatorItem?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetLocatorParams value)? getLocatorParams,
    TResult Function(_EditNameAndDuration value)? editLocatorTime,
    TResult Function(_EditLocatorItem value)? editLocatorItem,
    TResult Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult Function(_CreateNewLocator value)? createNewLocator,
    TResult Function(_Clear value)? clear,
    TResult Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_EditLocator value)? editLocator,
    required TResult orElse(),
  }) {
    if (selectAllLocatorItem != null) {
      return selectAllLocatorItem(this);
    }
    return orElse();
  }
}

abstract class _SelectAllLocatorItem implements ManageAddOrEditLocatorEvent {
  const factory _SelectAllLocatorItem(
      {required final LocatorType locatorType}) = _$SelectAllLocatorItemImpl;

  LocatorType get locatorType;
  @JsonKey(ignore: true)
  _$$SelectAllLocatorItemImplCopyWith<_$SelectAllLocatorItemImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreateNewLocatorImplCopyWith<$Res> {
  factory _$$CreateNewLocatorImplCopyWith(_$CreateNewLocatorImpl value,
          $Res Function(_$CreateNewLocatorImpl) then) =
      __$$CreateNewLocatorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreateNewLocatorImplCopyWithImpl<$Res>
    extends _$ManageAddOrEditLocatorEventCopyWithImpl<$Res,
        _$CreateNewLocatorImpl>
    implements _$$CreateNewLocatorImplCopyWith<$Res> {
  __$$CreateNewLocatorImplCopyWithImpl(_$CreateNewLocatorImpl _value,
      $Res Function(_$CreateNewLocatorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$CreateNewLocatorImpl implements _CreateNewLocator {
  const _$CreateNewLocatorImpl();

  @override
  String toString() {
    return 'ManageAddOrEditLocatorEvent.createNewLocator()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CreateNewLocatorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<Vehicle> listVehicle) initial,
    required TResult Function(bool isEdit) getLocatorParams,
    required TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)
        editLocatorTime,
    required TResult Function(String id, LocatorType locatorType)
        editLocatorItem,
    required TResult Function(LocatorType locatorType) selectAllLocatorItem,
    required TResult Function() createNewLocator,
    required TResult Function() clear,
    required TResult Function() syncDataFromEdit,
    required TResult Function(String id) getDetail,
    required TResult Function() editLocator,
  }) {
    return createNewLocator();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<Vehicle> listVehicle)? initial,
    TResult? Function(bool isEdit)? getLocatorParams,
    TResult? Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult? Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult? Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult? Function()? createNewLocator,
    TResult? Function()? clear,
    TResult? Function()? syncDataFromEdit,
    TResult? Function(String id)? getDetail,
    TResult? Function()? editLocator,
  }) {
    return createNewLocator?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<Vehicle> listVehicle)? initial,
    TResult Function(bool isEdit)? getLocatorParams,
    TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult Function()? createNewLocator,
    TResult Function()? clear,
    TResult Function()? syncDataFromEdit,
    TResult Function(String id)? getDetail,
    TResult Function()? editLocator,
    required TResult orElse(),
  }) {
    if (createNewLocator != null) {
      return createNewLocator();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetLocatorParams value) getLocatorParams,
    required TResult Function(_EditNameAndDuration value) editLocatorTime,
    required TResult Function(_EditLocatorItem value) editLocatorItem,
    required TResult Function(_SelectAllLocatorItem value) selectAllLocatorItem,
    required TResult Function(_CreateNewLocator value) createNewLocator,
    required TResult Function(_Clear value) clear,
    required TResult Function(_SyncDataFromEdit value) syncDataFromEdit,
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_EditLocator value) editLocator,
  }) {
    return createNewLocator(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetLocatorParams value)? getLocatorParams,
    TResult? Function(_EditNameAndDuration value)? editLocatorTime,
    TResult? Function(_EditLocatorItem value)? editLocatorItem,
    TResult? Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult? Function(_CreateNewLocator value)? createNewLocator,
    TResult? Function(_Clear value)? clear,
    TResult? Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_EditLocator value)? editLocator,
  }) {
    return createNewLocator?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetLocatorParams value)? getLocatorParams,
    TResult Function(_EditNameAndDuration value)? editLocatorTime,
    TResult Function(_EditLocatorItem value)? editLocatorItem,
    TResult Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult Function(_CreateNewLocator value)? createNewLocator,
    TResult Function(_Clear value)? clear,
    TResult Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_EditLocator value)? editLocator,
    required TResult orElse(),
  }) {
    if (createNewLocator != null) {
      return createNewLocator(this);
    }
    return orElse();
  }
}

abstract class _CreateNewLocator implements ManageAddOrEditLocatorEvent {
  const factory _CreateNewLocator() = _$CreateNewLocatorImpl;
}

/// @nodoc
abstract class _$$ClearImplCopyWith<$Res> {
  factory _$$ClearImplCopyWith(
          _$ClearImpl value, $Res Function(_$ClearImpl) then) =
      __$$ClearImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearImplCopyWithImpl<$Res>
    extends _$ManageAddOrEditLocatorEventCopyWithImpl<$Res, _$ClearImpl>
    implements _$$ClearImplCopyWith<$Res> {
  __$$ClearImplCopyWithImpl(
      _$ClearImpl _value, $Res Function(_$ClearImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearImpl implements _Clear {
  const _$ClearImpl();

  @override
  String toString() {
    return 'ManageAddOrEditLocatorEvent.clear()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<Vehicle> listVehicle) initial,
    required TResult Function(bool isEdit) getLocatorParams,
    required TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)
        editLocatorTime,
    required TResult Function(String id, LocatorType locatorType)
        editLocatorItem,
    required TResult Function(LocatorType locatorType) selectAllLocatorItem,
    required TResult Function() createNewLocator,
    required TResult Function() clear,
    required TResult Function() syncDataFromEdit,
    required TResult Function(String id) getDetail,
    required TResult Function() editLocator,
  }) {
    return clear();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<Vehicle> listVehicle)? initial,
    TResult? Function(bool isEdit)? getLocatorParams,
    TResult? Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult? Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult? Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult? Function()? createNewLocator,
    TResult? Function()? clear,
    TResult? Function()? syncDataFromEdit,
    TResult? Function(String id)? getDetail,
    TResult? Function()? editLocator,
  }) {
    return clear?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<Vehicle> listVehicle)? initial,
    TResult Function(bool isEdit)? getLocatorParams,
    TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult Function()? createNewLocator,
    TResult Function()? clear,
    TResult Function()? syncDataFromEdit,
    TResult Function(String id)? getDetail,
    TResult Function()? editLocator,
    required TResult orElse(),
  }) {
    if (clear != null) {
      return clear();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetLocatorParams value) getLocatorParams,
    required TResult Function(_EditNameAndDuration value) editLocatorTime,
    required TResult Function(_EditLocatorItem value) editLocatorItem,
    required TResult Function(_SelectAllLocatorItem value) selectAllLocatorItem,
    required TResult Function(_CreateNewLocator value) createNewLocator,
    required TResult Function(_Clear value) clear,
    required TResult Function(_SyncDataFromEdit value) syncDataFromEdit,
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_EditLocator value) editLocator,
  }) {
    return clear(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetLocatorParams value)? getLocatorParams,
    TResult? Function(_EditNameAndDuration value)? editLocatorTime,
    TResult? Function(_EditLocatorItem value)? editLocatorItem,
    TResult? Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult? Function(_CreateNewLocator value)? createNewLocator,
    TResult? Function(_Clear value)? clear,
    TResult? Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_EditLocator value)? editLocator,
  }) {
    return clear?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetLocatorParams value)? getLocatorParams,
    TResult Function(_EditNameAndDuration value)? editLocatorTime,
    TResult Function(_EditLocatorItem value)? editLocatorItem,
    TResult Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult Function(_CreateNewLocator value)? createNewLocator,
    TResult Function(_Clear value)? clear,
    TResult Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_EditLocator value)? editLocator,
    required TResult orElse(),
  }) {
    if (clear != null) {
      return clear(this);
    }
    return orElse();
  }
}

abstract class _Clear implements ManageAddOrEditLocatorEvent {
  const factory _Clear() = _$ClearImpl;
}

/// @nodoc
abstract class _$$SyncDataFromEditImplCopyWith<$Res> {
  factory _$$SyncDataFromEditImplCopyWith(_$SyncDataFromEditImpl value,
          $Res Function(_$SyncDataFromEditImpl) then) =
      __$$SyncDataFromEditImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SyncDataFromEditImplCopyWithImpl<$Res>
    extends _$ManageAddOrEditLocatorEventCopyWithImpl<$Res,
        _$SyncDataFromEditImpl>
    implements _$$SyncDataFromEditImplCopyWith<$Res> {
  __$$SyncDataFromEditImplCopyWithImpl(_$SyncDataFromEditImpl _value,
      $Res Function(_$SyncDataFromEditImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$SyncDataFromEditImpl implements _SyncDataFromEdit {
  const _$SyncDataFromEditImpl();

  @override
  String toString() {
    return 'ManageAddOrEditLocatorEvent.syncDataFromEdit()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SyncDataFromEditImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<Vehicle> listVehicle) initial,
    required TResult Function(bool isEdit) getLocatorParams,
    required TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)
        editLocatorTime,
    required TResult Function(String id, LocatorType locatorType)
        editLocatorItem,
    required TResult Function(LocatorType locatorType) selectAllLocatorItem,
    required TResult Function() createNewLocator,
    required TResult Function() clear,
    required TResult Function() syncDataFromEdit,
    required TResult Function(String id) getDetail,
    required TResult Function() editLocator,
  }) {
    return syncDataFromEdit();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<Vehicle> listVehicle)? initial,
    TResult? Function(bool isEdit)? getLocatorParams,
    TResult? Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult? Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult? Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult? Function()? createNewLocator,
    TResult? Function()? clear,
    TResult? Function()? syncDataFromEdit,
    TResult? Function(String id)? getDetail,
    TResult? Function()? editLocator,
  }) {
    return syncDataFromEdit?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<Vehicle> listVehicle)? initial,
    TResult Function(bool isEdit)? getLocatorParams,
    TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult Function()? createNewLocator,
    TResult Function()? clear,
    TResult Function()? syncDataFromEdit,
    TResult Function(String id)? getDetail,
    TResult Function()? editLocator,
    required TResult orElse(),
  }) {
    if (syncDataFromEdit != null) {
      return syncDataFromEdit();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetLocatorParams value) getLocatorParams,
    required TResult Function(_EditNameAndDuration value) editLocatorTime,
    required TResult Function(_EditLocatorItem value) editLocatorItem,
    required TResult Function(_SelectAllLocatorItem value) selectAllLocatorItem,
    required TResult Function(_CreateNewLocator value) createNewLocator,
    required TResult Function(_Clear value) clear,
    required TResult Function(_SyncDataFromEdit value) syncDataFromEdit,
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_EditLocator value) editLocator,
  }) {
    return syncDataFromEdit(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetLocatorParams value)? getLocatorParams,
    TResult? Function(_EditNameAndDuration value)? editLocatorTime,
    TResult? Function(_EditLocatorItem value)? editLocatorItem,
    TResult? Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult? Function(_CreateNewLocator value)? createNewLocator,
    TResult? Function(_Clear value)? clear,
    TResult? Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_EditLocator value)? editLocator,
  }) {
    return syncDataFromEdit?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetLocatorParams value)? getLocatorParams,
    TResult Function(_EditNameAndDuration value)? editLocatorTime,
    TResult Function(_EditLocatorItem value)? editLocatorItem,
    TResult Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult Function(_CreateNewLocator value)? createNewLocator,
    TResult Function(_Clear value)? clear,
    TResult Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_EditLocator value)? editLocator,
    required TResult orElse(),
  }) {
    if (syncDataFromEdit != null) {
      return syncDataFromEdit(this);
    }
    return orElse();
  }
}

abstract class _SyncDataFromEdit implements ManageAddOrEditLocatorEvent {
  const factory _SyncDataFromEdit() = _$SyncDataFromEditImpl;
}

/// @nodoc
abstract class _$$GetDetailImplCopyWith<$Res> {
  factory _$$GetDetailImplCopyWith(
          _$GetDetailImpl value, $Res Function(_$GetDetailImpl) then) =
      __$$GetDetailImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$GetDetailImplCopyWithImpl<$Res>
    extends _$ManageAddOrEditLocatorEventCopyWithImpl<$Res, _$GetDetailImpl>
    implements _$$GetDetailImplCopyWith<$Res> {
  __$$GetDetailImplCopyWithImpl(
      _$GetDetailImpl _value, $Res Function(_$GetDetailImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$GetDetailImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetDetailImpl implements _GetDetail {
  const _$GetDetailImpl({required this.id});

  @override
  final String id;

  @override
  String toString() {
    return 'ManageAddOrEditLocatorEvent.getDetail(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetDetailImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetDetailImplCopyWith<_$GetDetailImpl> get copyWith =>
      __$$GetDetailImplCopyWithImpl<_$GetDetailImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<Vehicle> listVehicle) initial,
    required TResult Function(bool isEdit) getLocatorParams,
    required TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)
        editLocatorTime,
    required TResult Function(String id, LocatorType locatorType)
        editLocatorItem,
    required TResult Function(LocatorType locatorType) selectAllLocatorItem,
    required TResult Function() createNewLocator,
    required TResult Function() clear,
    required TResult Function() syncDataFromEdit,
    required TResult Function(String id) getDetail,
    required TResult Function() editLocator,
  }) {
    return getDetail(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<Vehicle> listVehicle)? initial,
    TResult? Function(bool isEdit)? getLocatorParams,
    TResult? Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult? Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult? Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult? Function()? createNewLocator,
    TResult? Function()? clear,
    TResult? Function()? syncDataFromEdit,
    TResult? Function(String id)? getDetail,
    TResult? Function()? editLocator,
  }) {
    return getDetail?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<Vehicle> listVehicle)? initial,
    TResult Function(bool isEdit)? getLocatorParams,
    TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult Function()? createNewLocator,
    TResult Function()? clear,
    TResult Function()? syncDataFromEdit,
    TResult Function(String id)? getDetail,
    TResult Function()? editLocator,
    required TResult orElse(),
  }) {
    if (getDetail != null) {
      return getDetail(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetLocatorParams value) getLocatorParams,
    required TResult Function(_EditNameAndDuration value) editLocatorTime,
    required TResult Function(_EditLocatorItem value) editLocatorItem,
    required TResult Function(_SelectAllLocatorItem value) selectAllLocatorItem,
    required TResult Function(_CreateNewLocator value) createNewLocator,
    required TResult Function(_Clear value) clear,
    required TResult Function(_SyncDataFromEdit value) syncDataFromEdit,
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_EditLocator value) editLocator,
  }) {
    return getDetail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetLocatorParams value)? getLocatorParams,
    TResult? Function(_EditNameAndDuration value)? editLocatorTime,
    TResult? Function(_EditLocatorItem value)? editLocatorItem,
    TResult? Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult? Function(_CreateNewLocator value)? createNewLocator,
    TResult? Function(_Clear value)? clear,
    TResult? Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_EditLocator value)? editLocator,
  }) {
    return getDetail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetLocatorParams value)? getLocatorParams,
    TResult Function(_EditNameAndDuration value)? editLocatorTime,
    TResult Function(_EditLocatorItem value)? editLocatorItem,
    TResult Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult Function(_CreateNewLocator value)? createNewLocator,
    TResult Function(_Clear value)? clear,
    TResult Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_EditLocator value)? editLocator,
    required TResult orElse(),
  }) {
    if (getDetail != null) {
      return getDetail(this);
    }
    return orElse();
  }
}

abstract class _GetDetail implements ManageAddOrEditLocatorEvent {
  const factory _GetDetail({required final String id}) = _$GetDetailImpl;

  String get id;
  @JsonKey(ignore: true)
  _$$GetDetailImplCopyWith<_$GetDetailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EditLocatorImplCopyWith<$Res> {
  factory _$$EditLocatorImplCopyWith(
          _$EditLocatorImpl value, $Res Function(_$EditLocatorImpl) then) =
      __$$EditLocatorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EditLocatorImplCopyWithImpl<$Res>
    extends _$ManageAddOrEditLocatorEventCopyWithImpl<$Res, _$EditLocatorImpl>
    implements _$$EditLocatorImplCopyWith<$Res> {
  __$$EditLocatorImplCopyWithImpl(
      _$EditLocatorImpl _value, $Res Function(_$EditLocatorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$EditLocatorImpl implements _EditLocator {
  const _$EditLocatorImpl();

  @override
  String toString() {
    return 'ManageAddOrEditLocatorEvent.editLocator()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EditLocatorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<Vehicle> listVehicle) initial,
    required TResult Function(bool isEdit) getLocatorParams,
    required TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)
        editLocatorTime,
    required TResult Function(String id, LocatorType locatorType)
        editLocatorItem,
    required TResult Function(LocatorType locatorType) selectAllLocatorItem,
    required TResult Function() createNewLocator,
    required TResult Function() clear,
    required TResult Function() syncDataFromEdit,
    required TResult Function(String id) getDetail,
    required TResult Function() editLocator,
  }) {
    return editLocator();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<Vehicle> listVehicle)? initial,
    TResult? Function(bool isEdit)? getLocatorParams,
    TResult? Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult? Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult? Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult? Function()? createNewLocator,
    TResult? Function()? clear,
    TResult? Function()? syncDataFromEdit,
    TResult? Function(String id)? getDetail,
    TResult? Function()? editLocator,
  }) {
    return editLocator?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<Vehicle> listVehicle)? initial,
    TResult Function(bool isEdit)? getLocatorParams,
    TResult Function(String name, DateTime fromTime, String duration,
            LocatorTimeType locatorTimeType)?
        editLocatorTime,
    TResult Function(String id, LocatorType locatorType)? editLocatorItem,
    TResult Function(LocatorType locatorType)? selectAllLocatorItem,
    TResult Function()? createNewLocator,
    TResult Function()? clear,
    TResult Function()? syncDataFromEdit,
    TResult Function(String id)? getDetail,
    TResult Function()? editLocator,
    required TResult orElse(),
  }) {
    if (editLocator != null) {
      return editLocator();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetLocatorParams value) getLocatorParams,
    required TResult Function(_EditNameAndDuration value) editLocatorTime,
    required TResult Function(_EditLocatorItem value) editLocatorItem,
    required TResult Function(_SelectAllLocatorItem value) selectAllLocatorItem,
    required TResult Function(_CreateNewLocator value) createNewLocator,
    required TResult Function(_Clear value) clear,
    required TResult Function(_SyncDataFromEdit value) syncDataFromEdit,
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_EditLocator value) editLocator,
  }) {
    return editLocator(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetLocatorParams value)? getLocatorParams,
    TResult? Function(_EditNameAndDuration value)? editLocatorTime,
    TResult? Function(_EditLocatorItem value)? editLocatorItem,
    TResult? Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult? Function(_CreateNewLocator value)? createNewLocator,
    TResult? Function(_Clear value)? clear,
    TResult? Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_EditLocator value)? editLocator,
  }) {
    return editLocator?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetLocatorParams value)? getLocatorParams,
    TResult Function(_EditNameAndDuration value)? editLocatorTime,
    TResult Function(_EditLocatorItem value)? editLocatorItem,
    TResult Function(_SelectAllLocatorItem value)? selectAllLocatorItem,
    TResult Function(_CreateNewLocator value)? createNewLocator,
    TResult Function(_Clear value)? clear,
    TResult Function(_SyncDataFromEdit value)? syncDataFromEdit,
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_EditLocator value)? editLocator,
    required TResult orElse(),
  }) {
    if (editLocator != null) {
      return editLocator(this);
    }
    return orElse();
  }
}

abstract class _EditLocator implements ManageAddOrEditLocatorEvent {
  const factory _EditLocator() = _$EditLocatorImpl;
}

/// @nodoc
mixin _$ManageAddOrEditLocatorState {
// DATA
  List<Vehicle> get listVehicle => throw _privateConstructorUsedError;
  List<LocatorGeofence> get listGeofence => throw _privateConstructorUsedError;
  List<LocatorStation> get listStation => throw _privateConstructorUsedError;
  List<LocatorOption> get listOption => throw _privateConstructorUsedError;
  List<LocatorStatus> get listStatus =>
      throw _privateConstructorUsedError; // FIELD FOR EDIT OR ADD
  String get name => throw _privateConstructorUsedError;
  String get durationString => throw _privateConstructorUsedError;
  LocatorTimeType get locatorTimeType => throw _privateConstructorUsedError;
  DateTime get fromTime => throw _privateConstructorUsedError;
  List<String> get listVehicleId => throw _privateConstructorUsedError;
  List<String> get listGeofenceId => throw _privateConstructorUsedError;
  List<String> get listStationId => throw _privateConstructorUsedError;
  List<String> get listOptionId => throw _privateConstructorUsedError;
  List<String> get listStatusId => throw _privateConstructorUsedError; // UI
  bool get isGettingParams => throw _privateConstructorUsedError;
  bool get isCreatingNew => throw _privateConstructorUsedError;
  LocatorFailure? get createNewFailure =>
      throw _privateConstructorUsedError; //EDIT
  DetailLocatorResponse? get detailLocatorResponse =>
      throw _privateConstructorUsedError;
  bool get isGettingDetail => throw _privateConstructorUsedError;
  LocatorFailure? get getDetailFailure => throw _privateConstructorUsedError;
  bool get isUpdatingLocator => throw _privateConstructorUsedError;
  LocatorFailure? get updateLocatorFail => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ManageAddOrEditLocatorStateCopyWith<ManageAddOrEditLocatorState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageAddOrEditLocatorStateCopyWith<$Res> {
  factory $ManageAddOrEditLocatorStateCopyWith(
          ManageAddOrEditLocatorState value,
          $Res Function(ManageAddOrEditLocatorState) then) =
      _$ManageAddOrEditLocatorStateCopyWithImpl<$Res,
          ManageAddOrEditLocatorState>;
  @useResult
  $Res call(
      {List<Vehicle> listVehicle,
      List<LocatorGeofence> listGeofence,
      List<LocatorStation> listStation,
      List<LocatorOption> listOption,
      List<LocatorStatus> listStatus,
      String name,
      String durationString,
      LocatorTimeType locatorTimeType,
      DateTime fromTime,
      List<String> listVehicleId,
      List<String> listGeofenceId,
      List<String> listStationId,
      List<String> listOptionId,
      List<String> listStatusId,
      bool isGettingParams,
      bool isCreatingNew,
      LocatorFailure? createNewFailure,
      DetailLocatorResponse? detailLocatorResponse,
      bool isGettingDetail,
      LocatorFailure? getDetailFailure,
      bool isUpdatingLocator,
      LocatorFailure? updateLocatorFail});

  $LocatorFailureCopyWith<$Res>? get createNewFailure;
  $DetailLocatorResponseCopyWith<$Res>? get detailLocatorResponse;
  $LocatorFailureCopyWith<$Res>? get getDetailFailure;
  $LocatorFailureCopyWith<$Res>? get updateLocatorFail;
}

/// @nodoc
class _$ManageAddOrEditLocatorStateCopyWithImpl<$Res,
        $Val extends ManageAddOrEditLocatorState>
    implements $ManageAddOrEditLocatorStateCopyWith<$Res> {
  _$ManageAddOrEditLocatorStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicle = null,
    Object? listGeofence = null,
    Object? listStation = null,
    Object? listOption = null,
    Object? listStatus = null,
    Object? name = null,
    Object? durationString = null,
    Object? locatorTimeType = null,
    Object? fromTime = null,
    Object? listVehicleId = null,
    Object? listGeofenceId = null,
    Object? listStationId = null,
    Object? listOptionId = null,
    Object? listStatusId = null,
    Object? isGettingParams = null,
    Object? isCreatingNew = null,
    Object? createNewFailure = freezed,
    Object? detailLocatorResponse = freezed,
    Object? isGettingDetail = null,
    Object? getDetailFailure = freezed,
    Object? isUpdatingLocator = null,
    Object? updateLocatorFail = freezed,
  }) {
    return _then(_value.copyWith(
      listVehicle: null == listVehicle
          ? _value.listVehicle
          : listVehicle // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      listGeofence: null == listGeofence
          ? _value.listGeofence
          : listGeofence // ignore: cast_nullable_to_non_nullable
              as List<LocatorGeofence>,
      listStation: null == listStation
          ? _value.listStation
          : listStation // ignore: cast_nullable_to_non_nullable
              as List<LocatorStation>,
      listOption: null == listOption
          ? _value.listOption
          : listOption // ignore: cast_nullable_to_non_nullable
              as List<LocatorOption>,
      listStatus: null == listStatus
          ? _value.listStatus
          : listStatus // ignore: cast_nullable_to_non_nullable
              as List<LocatorStatus>,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      durationString: null == durationString
          ? _value.durationString
          : durationString // ignore: cast_nullable_to_non_nullable
              as String,
      locatorTimeType: null == locatorTimeType
          ? _value.locatorTimeType
          : locatorTimeType // ignore: cast_nullable_to_non_nullable
              as LocatorTimeType,
      fromTime: null == fromTime
          ? _value.fromTime
          : fromTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      listVehicleId: null == listVehicleId
          ? _value.listVehicleId
          : listVehicleId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listGeofenceId: null == listGeofenceId
          ? _value.listGeofenceId
          : listGeofenceId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listStationId: null == listStationId
          ? _value.listStationId
          : listStationId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listOptionId: null == listOptionId
          ? _value.listOptionId
          : listOptionId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listStatusId: null == listStatusId
          ? _value.listStatusId
          : listStatusId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isGettingParams: null == isGettingParams
          ? _value.isGettingParams
          : isGettingParams // ignore: cast_nullable_to_non_nullable
              as bool,
      isCreatingNew: null == isCreatingNew
          ? _value.isCreatingNew
          : isCreatingNew // ignore: cast_nullable_to_non_nullable
              as bool,
      createNewFailure: freezed == createNewFailure
          ? _value.createNewFailure
          : createNewFailure // ignore: cast_nullable_to_non_nullable
              as LocatorFailure?,
      detailLocatorResponse: freezed == detailLocatorResponse
          ? _value.detailLocatorResponse
          : detailLocatorResponse // ignore: cast_nullable_to_non_nullable
              as DetailLocatorResponse?,
      isGettingDetail: null == isGettingDetail
          ? _value.isGettingDetail
          : isGettingDetail // ignore: cast_nullable_to_non_nullable
              as bool,
      getDetailFailure: freezed == getDetailFailure
          ? _value.getDetailFailure
          : getDetailFailure // ignore: cast_nullable_to_non_nullable
              as LocatorFailure?,
      isUpdatingLocator: null == isUpdatingLocator
          ? _value.isUpdatingLocator
          : isUpdatingLocator // ignore: cast_nullable_to_non_nullable
              as bool,
      updateLocatorFail: freezed == updateLocatorFail
          ? _value.updateLocatorFail
          : updateLocatorFail // ignore: cast_nullable_to_non_nullable
              as LocatorFailure?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LocatorFailureCopyWith<$Res>? get createNewFailure {
    if (_value.createNewFailure == null) {
      return null;
    }

    return $LocatorFailureCopyWith<$Res>(_value.createNewFailure!, (value) {
      return _then(_value.copyWith(createNewFailure: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $DetailLocatorResponseCopyWith<$Res>? get detailLocatorResponse {
    if (_value.detailLocatorResponse == null) {
      return null;
    }

    return $DetailLocatorResponseCopyWith<$Res>(_value.detailLocatorResponse!,
        (value) {
      return _then(_value.copyWith(detailLocatorResponse: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LocatorFailureCopyWith<$Res>? get getDetailFailure {
    if (_value.getDetailFailure == null) {
      return null;
    }

    return $LocatorFailureCopyWith<$Res>(_value.getDetailFailure!, (value) {
      return _then(_value.copyWith(getDetailFailure: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LocatorFailureCopyWith<$Res>? get updateLocatorFail {
    if (_value.updateLocatorFail == null) {
      return null;
    }

    return $LocatorFailureCopyWith<$Res>(_value.updateLocatorFail!, (value) {
      return _then(_value.copyWith(updateLocatorFail: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ManageAddOrEditLocatorStateImplCopyWith<$Res>
    implements $ManageAddOrEditLocatorStateCopyWith<$Res> {
  factory _$$ManageAddOrEditLocatorStateImplCopyWith(
          _$ManageAddOrEditLocatorStateImpl value,
          $Res Function(_$ManageAddOrEditLocatorStateImpl) then) =
      __$$ManageAddOrEditLocatorStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Vehicle> listVehicle,
      List<LocatorGeofence> listGeofence,
      List<LocatorStation> listStation,
      List<LocatorOption> listOption,
      List<LocatorStatus> listStatus,
      String name,
      String durationString,
      LocatorTimeType locatorTimeType,
      DateTime fromTime,
      List<String> listVehicleId,
      List<String> listGeofenceId,
      List<String> listStationId,
      List<String> listOptionId,
      List<String> listStatusId,
      bool isGettingParams,
      bool isCreatingNew,
      LocatorFailure? createNewFailure,
      DetailLocatorResponse? detailLocatorResponse,
      bool isGettingDetail,
      LocatorFailure? getDetailFailure,
      bool isUpdatingLocator,
      LocatorFailure? updateLocatorFail});

  @override
  $LocatorFailureCopyWith<$Res>? get createNewFailure;
  @override
  $DetailLocatorResponseCopyWith<$Res>? get detailLocatorResponse;
  @override
  $LocatorFailureCopyWith<$Res>? get getDetailFailure;
  @override
  $LocatorFailureCopyWith<$Res>? get updateLocatorFail;
}

/// @nodoc
class __$$ManageAddOrEditLocatorStateImplCopyWithImpl<$Res>
    extends _$ManageAddOrEditLocatorStateCopyWithImpl<$Res,
        _$ManageAddOrEditLocatorStateImpl>
    implements _$$ManageAddOrEditLocatorStateImplCopyWith<$Res> {
  __$$ManageAddOrEditLocatorStateImplCopyWithImpl(
      _$ManageAddOrEditLocatorStateImpl _value,
      $Res Function(_$ManageAddOrEditLocatorStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicle = null,
    Object? listGeofence = null,
    Object? listStation = null,
    Object? listOption = null,
    Object? listStatus = null,
    Object? name = null,
    Object? durationString = null,
    Object? locatorTimeType = null,
    Object? fromTime = null,
    Object? listVehicleId = null,
    Object? listGeofenceId = null,
    Object? listStationId = null,
    Object? listOptionId = null,
    Object? listStatusId = null,
    Object? isGettingParams = null,
    Object? isCreatingNew = null,
    Object? createNewFailure = freezed,
    Object? detailLocatorResponse = freezed,
    Object? isGettingDetail = null,
    Object? getDetailFailure = freezed,
    Object? isUpdatingLocator = null,
    Object? updateLocatorFail = freezed,
  }) {
    return _then(_$ManageAddOrEditLocatorStateImpl(
      listVehicle: null == listVehicle
          ? _value._listVehicle
          : listVehicle // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      listGeofence: null == listGeofence
          ? _value._listGeofence
          : listGeofence // ignore: cast_nullable_to_non_nullable
              as List<LocatorGeofence>,
      listStation: null == listStation
          ? _value._listStation
          : listStation // ignore: cast_nullable_to_non_nullable
              as List<LocatorStation>,
      listOption: null == listOption
          ? _value._listOption
          : listOption // ignore: cast_nullable_to_non_nullable
              as List<LocatorOption>,
      listStatus: null == listStatus
          ? _value._listStatus
          : listStatus // ignore: cast_nullable_to_non_nullable
              as List<LocatorStatus>,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      durationString: null == durationString
          ? _value.durationString
          : durationString // ignore: cast_nullable_to_non_nullable
              as String,
      locatorTimeType: null == locatorTimeType
          ? _value.locatorTimeType
          : locatorTimeType // ignore: cast_nullable_to_non_nullable
              as LocatorTimeType,
      fromTime: null == fromTime
          ? _value.fromTime
          : fromTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      listVehicleId: null == listVehicleId
          ? _value._listVehicleId
          : listVehicleId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listGeofenceId: null == listGeofenceId
          ? _value._listGeofenceId
          : listGeofenceId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listStationId: null == listStationId
          ? _value._listStationId
          : listStationId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listOptionId: null == listOptionId
          ? _value._listOptionId
          : listOptionId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listStatusId: null == listStatusId
          ? _value._listStatusId
          : listStatusId // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isGettingParams: null == isGettingParams
          ? _value.isGettingParams
          : isGettingParams // ignore: cast_nullable_to_non_nullable
              as bool,
      isCreatingNew: null == isCreatingNew
          ? _value.isCreatingNew
          : isCreatingNew // ignore: cast_nullable_to_non_nullable
              as bool,
      createNewFailure: freezed == createNewFailure
          ? _value.createNewFailure
          : createNewFailure // ignore: cast_nullable_to_non_nullable
              as LocatorFailure?,
      detailLocatorResponse: freezed == detailLocatorResponse
          ? _value.detailLocatorResponse
          : detailLocatorResponse // ignore: cast_nullable_to_non_nullable
              as DetailLocatorResponse?,
      isGettingDetail: null == isGettingDetail
          ? _value.isGettingDetail
          : isGettingDetail // ignore: cast_nullable_to_non_nullable
              as bool,
      getDetailFailure: freezed == getDetailFailure
          ? _value.getDetailFailure
          : getDetailFailure // ignore: cast_nullable_to_non_nullable
              as LocatorFailure?,
      isUpdatingLocator: null == isUpdatingLocator
          ? _value.isUpdatingLocator
          : isUpdatingLocator // ignore: cast_nullable_to_non_nullable
              as bool,
      updateLocatorFail: freezed == updateLocatorFail
          ? _value.updateLocatorFail
          : updateLocatorFail // ignore: cast_nullable_to_non_nullable
              as LocatorFailure?,
    ));
  }
}

/// @nodoc

class _$ManageAddOrEditLocatorStateImpl
    implements _ManageAddOrEditLocatorState {
  const _$ManageAddOrEditLocatorStateImpl(
      {required final List<Vehicle> listVehicle,
      required final List<LocatorGeofence> listGeofence,
      required final List<LocatorStation> listStation,
      required final List<LocatorOption> listOption,
      required final List<LocatorStatus> listStatus,
      required this.name,
      required this.durationString,
      required this.locatorTimeType,
      required this.fromTime,
      required final List<String> listVehicleId,
      required final List<String> listGeofenceId,
      required final List<String> listStationId,
      required final List<String> listOptionId,
      required final List<String> listStatusId,
      required this.isGettingParams,
      required this.isCreatingNew,
      required this.createNewFailure,
      required this.detailLocatorResponse,
      required this.isGettingDetail,
      required this.getDetailFailure,
      required this.isUpdatingLocator,
      required this.updateLocatorFail})
      : _listVehicle = listVehicle,
        _listGeofence = listGeofence,
        _listStation = listStation,
        _listOption = listOption,
        _listStatus = listStatus,
        _listVehicleId = listVehicleId,
        _listGeofenceId = listGeofenceId,
        _listStationId = listStationId,
        _listOptionId = listOptionId,
        _listStatusId = listStatusId;

// DATA
  final List<Vehicle> _listVehicle;
// DATA
  @override
  List<Vehicle> get listVehicle {
    if (_listVehicle is EqualUnmodifiableListView) return _listVehicle;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicle);
  }

  final List<LocatorGeofence> _listGeofence;
  @override
  List<LocatorGeofence> get listGeofence {
    if (_listGeofence is EqualUnmodifiableListView) return _listGeofence;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listGeofence);
  }

  final List<LocatorStation> _listStation;
  @override
  List<LocatorStation> get listStation {
    if (_listStation is EqualUnmodifiableListView) return _listStation;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listStation);
  }

  final List<LocatorOption> _listOption;
  @override
  List<LocatorOption> get listOption {
    if (_listOption is EqualUnmodifiableListView) return _listOption;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listOption);
  }

  final List<LocatorStatus> _listStatus;
  @override
  List<LocatorStatus> get listStatus {
    if (_listStatus is EqualUnmodifiableListView) return _listStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listStatus);
  }

// FIELD FOR EDIT OR ADD
  @override
  final String name;
  @override
  final String durationString;
  @override
  final LocatorTimeType locatorTimeType;
  @override
  final DateTime fromTime;
  final List<String> _listVehicleId;
  @override
  List<String> get listVehicleId {
    if (_listVehicleId is EqualUnmodifiableListView) return _listVehicleId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicleId);
  }

  final List<String> _listGeofenceId;
  @override
  List<String> get listGeofenceId {
    if (_listGeofenceId is EqualUnmodifiableListView) return _listGeofenceId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listGeofenceId);
  }

  final List<String> _listStationId;
  @override
  List<String> get listStationId {
    if (_listStationId is EqualUnmodifiableListView) return _listStationId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listStationId);
  }

  final List<String> _listOptionId;
  @override
  List<String> get listOptionId {
    if (_listOptionId is EqualUnmodifiableListView) return _listOptionId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listOptionId);
  }

  final List<String> _listStatusId;
  @override
  List<String> get listStatusId {
    if (_listStatusId is EqualUnmodifiableListView) return _listStatusId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listStatusId);
  }

// UI
  @override
  final bool isGettingParams;
  @override
  final bool isCreatingNew;
  @override
  final LocatorFailure? createNewFailure;
//EDIT
  @override
  final DetailLocatorResponse? detailLocatorResponse;
  @override
  final bool isGettingDetail;
  @override
  final LocatorFailure? getDetailFailure;
  @override
  final bool isUpdatingLocator;
  @override
  final LocatorFailure? updateLocatorFail;

  @override
  String toString() {
    return 'ManageAddOrEditLocatorState(listVehicle: $listVehicle, listGeofence: $listGeofence, listStation: $listStation, listOption: $listOption, listStatus: $listStatus, name: $name, durationString: $durationString, locatorTimeType: $locatorTimeType, fromTime: $fromTime, listVehicleId: $listVehicleId, listGeofenceId: $listGeofenceId, listStationId: $listStationId, listOptionId: $listOptionId, listStatusId: $listStatusId, isGettingParams: $isGettingParams, isCreatingNew: $isCreatingNew, createNewFailure: $createNewFailure, detailLocatorResponse: $detailLocatorResponse, isGettingDetail: $isGettingDetail, getDetailFailure: $getDetailFailure, isUpdatingLocator: $isUpdatingLocator, updateLocatorFail: $updateLocatorFail)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ManageAddOrEditLocatorStateImpl &&
            const DeepCollectionEquality()
                .equals(other._listVehicle, _listVehicle) &&
            const DeepCollectionEquality()
                .equals(other._listGeofence, _listGeofence) &&
            const DeepCollectionEquality()
                .equals(other._listStation, _listStation) &&
            const DeepCollectionEquality()
                .equals(other._listOption, _listOption) &&
            const DeepCollectionEquality()
                .equals(other._listStatus, _listStatus) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.durationString, durationString) ||
                other.durationString == durationString) &&
            (identical(other.locatorTimeType, locatorTimeType) ||
                other.locatorTimeType == locatorTimeType) &&
            (identical(other.fromTime, fromTime) ||
                other.fromTime == fromTime) &&
            const DeepCollectionEquality()
                .equals(other._listVehicleId, _listVehicleId) &&
            const DeepCollectionEquality()
                .equals(other._listGeofenceId, _listGeofenceId) &&
            const DeepCollectionEquality()
                .equals(other._listStationId, _listStationId) &&
            const DeepCollectionEquality()
                .equals(other._listOptionId, _listOptionId) &&
            const DeepCollectionEquality()
                .equals(other._listStatusId, _listStatusId) &&
            (identical(other.isGettingParams, isGettingParams) ||
                other.isGettingParams == isGettingParams) &&
            (identical(other.isCreatingNew, isCreatingNew) ||
                other.isCreatingNew == isCreatingNew) &&
            (identical(other.createNewFailure, createNewFailure) ||
                other.createNewFailure == createNewFailure) &&
            (identical(other.detailLocatorResponse, detailLocatorResponse) ||
                other.detailLocatorResponse == detailLocatorResponse) &&
            (identical(other.isGettingDetail, isGettingDetail) ||
                other.isGettingDetail == isGettingDetail) &&
            (identical(other.getDetailFailure, getDetailFailure) ||
                other.getDetailFailure == getDetailFailure) &&
            (identical(other.isUpdatingLocator, isUpdatingLocator) ||
                other.isUpdatingLocator == isUpdatingLocator) &&
            (identical(other.updateLocatorFail, updateLocatorFail) ||
                other.updateLocatorFail == updateLocatorFail));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        const DeepCollectionEquality().hash(_listVehicle),
        const DeepCollectionEquality().hash(_listGeofence),
        const DeepCollectionEquality().hash(_listStation),
        const DeepCollectionEquality().hash(_listOption),
        const DeepCollectionEquality().hash(_listStatus),
        name,
        durationString,
        locatorTimeType,
        fromTime,
        const DeepCollectionEquality().hash(_listVehicleId),
        const DeepCollectionEquality().hash(_listGeofenceId),
        const DeepCollectionEquality().hash(_listStationId),
        const DeepCollectionEquality().hash(_listOptionId),
        const DeepCollectionEquality().hash(_listStatusId),
        isGettingParams,
        isCreatingNew,
        createNewFailure,
        detailLocatorResponse,
        isGettingDetail,
        getDetailFailure,
        isUpdatingLocator,
        updateLocatorFail
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ManageAddOrEditLocatorStateImplCopyWith<_$ManageAddOrEditLocatorStateImpl>
      get copyWith => __$$ManageAddOrEditLocatorStateImplCopyWithImpl<
          _$ManageAddOrEditLocatorStateImpl>(this, _$identity);
}

abstract class _ManageAddOrEditLocatorState
    implements ManageAddOrEditLocatorState {
  const factory _ManageAddOrEditLocatorState(
          {required final List<Vehicle> listVehicle,
          required final List<LocatorGeofence> listGeofence,
          required final List<LocatorStation> listStation,
          required final List<LocatorOption> listOption,
          required final List<LocatorStatus> listStatus,
          required final String name,
          required final String durationString,
          required final LocatorTimeType locatorTimeType,
          required final DateTime fromTime,
          required final List<String> listVehicleId,
          required final List<String> listGeofenceId,
          required final List<String> listStationId,
          required final List<String> listOptionId,
          required final List<String> listStatusId,
          required final bool isGettingParams,
          required final bool isCreatingNew,
          required final LocatorFailure? createNewFailure,
          required final DetailLocatorResponse? detailLocatorResponse,
          required final bool isGettingDetail,
          required final LocatorFailure? getDetailFailure,
          required final bool isUpdatingLocator,
          required final LocatorFailure? updateLocatorFail}) =
      _$ManageAddOrEditLocatorStateImpl;

  @override // DATA
  List<Vehicle> get listVehicle;
  @override
  List<LocatorGeofence> get listGeofence;
  @override
  List<LocatorStation> get listStation;
  @override
  List<LocatorOption> get listOption;
  @override
  List<LocatorStatus> get listStatus;
  @override // FIELD FOR EDIT OR ADD
  String get name;
  @override
  String get durationString;
  @override
  LocatorTimeType get locatorTimeType;
  @override
  DateTime get fromTime;
  @override
  List<String> get listVehicleId;
  @override
  List<String> get listGeofenceId;
  @override
  List<String> get listStationId;
  @override
  List<String> get listOptionId;
  @override
  List<String> get listStatusId;
  @override // UI
  bool get isGettingParams;
  @override
  bool get isCreatingNew;
  @override
  LocatorFailure? get createNewFailure;
  @override //EDIT
  DetailLocatorResponse? get detailLocatorResponse;
  @override
  bool get isGettingDetail;
  @override
  LocatorFailure? get getDetailFailure;
  @override
  bool get isUpdatingLocator;
  @override
  LocatorFailure? get updateLocatorFail;
  @override
  @JsonKey(ignore: true)
  _$$ManageAddOrEditLocatorStateImplCopyWith<_$ManageAddOrEditLocatorStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
