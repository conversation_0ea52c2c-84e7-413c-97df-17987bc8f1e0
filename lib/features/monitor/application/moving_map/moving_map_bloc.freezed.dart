// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'moving_map_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MovingMapEvent {
  bool get isMovingMap => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isMovingMap) changeVarible,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isMovingMap)? changeVarible,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isMovingMap)? changeVarible,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeVarible value) changeVarible,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeVarible value)? changeVarible,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeVarible value)? changeVarible,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MovingMapEventCopyWith<MovingMapEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MovingMapEventCopyWith<$Res> {
  factory $MovingMapEventCopyWith(
          MovingMapEvent value, $Res Function(MovingMapEvent) then) =
      _$MovingMapEventCopyWithImpl<$Res, MovingMapEvent>;
  @useResult
  $Res call({bool isMovingMap});
}

/// @nodoc
class _$MovingMapEventCopyWithImpl<$Res, $Val extends MovingMapEvent>
    implements $MovingMapEventCopyWith<$Res> {
  _$MovingMapEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isMovingMap = null,
  }) {
    return _then(_value.copyWith(
      isMovingMap: null == isMovingMap
          ? _value.isMovingMap
          : isMovingMap // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChangeVaribleImplCopyWith<$Res>
    implements $MovingMapEventCopyWith<$Res> {
  factory _$$ChangeVaribleImplCopyWith(
          _$ChangeVaribleImpl value, $Res Function(_$ChangeVaribleImpl) then) =
      __$$ChangeVaribleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isMovingMap});
}

/// @nodoc
class __$$ChangeVaribleImplCopyWithImpl<$Res>
    extends _$MovingMapEventCopyWithImpl<$Res, _$ChangeVaribleImpl>
    implements _$$ChangeVaribleImplCopyWith<$Res> {
  __$$ChangeVaribleImplCopyWithImpl(
      _$ChangeVaribleImpl _value, $Res Function(_$ChangeVaribleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isMovingMap = null,
  }) {
    return _then(_$ChangeVaribleImpl(
      isMovingMap: null == isMovingMap
          ? _value.isMovingMap
          : isMovingMap // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ChangeVaribleImpl implements _ChangeVarible {
  const _$ChangeVaribleImpl({required this.isMovingMap});

  @override
  final bool isMovingMap;

  @override
  String toString() {
    return 'MovingMapEvent.changeVarible(isMovingMap: $isMovingMap)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeVaribleImpl &&
            (identical(other.isMovingMap, isMovingMap) ||
                other.isMovingMap == isMovingMap));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isMovingMap);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeVaribleImplCopyWith<_$ChangeVaribleImpl> get copyWith =>
      __$$ChangeVaribleImplCopyWithImpl<_$ChangeVaribleImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isMovingMap) changeVarible,
  }) {
    return changeVarible(isMovingMap);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isMovingMap)? changeVarible,
  }) {
    return changeVarible?.call(isMovingMap);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isMovingMap)? changeVarible,
    required TResult orElse(),
  }) {
    if (changeVarible != null) {
      return changeVarible(isMovingMap);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeVarible value) changeVarible,
  }) {
    return changeVarible(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeVarible value)? changeVarible,
  }) {
    return changeVarible?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeVarible value)? changeVarible,
    required TResult orElse(),
  }) {
    if (changeVarible != null) {
      return changeVarible(this);
    }
    return orElse();
  }
}

abstract class _ChangeVarible implements MovingMapEvent {
  const factory _ChangeVarible({required final bool isMovingMap}) =
      _$ChangeVaribleImpl;

  @override
  bool get isMovingMap;
  @override
  @JsonKey(ignore: true)
  _$$ChangeVaribleImplCopyWith<_$ChangeVaribleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$MovingMapState {
  bool get isMovingMap => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MovingMapStateCopyWith<MovingMapState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MovingMapStateCopyWith<$Res> {
  factory $MovingMapStateCopyWith(
          MovingMapState value, $Res Function(MovingMapState) then) =
      _$MovingMapStateCopyWithImpl<$Res, MovingMapState>;
  @useResult
  $Res call({bool isMovingMap});
}

/// @nodoc
class _$MovingMapStateCopyWithImpl<$Res, $Val extends MovingMapState>
    implements $MovingMapStateCopyWith<$Res> {
  _$MovingMapStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isMovingMap = null,
  }) {
    return _then(_value.copyWith(
      isMovingMap: null == isMovingMap
          ? _value.isMovingMap
          : isMovingMap // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MovingMapStateImplCopyWith<$Res>
    implements $MovingMapStateCopyWith<$Res> {
  factory _$$MovingMapStateImplCopyWith(_$MovingMapStateImpl value,
          $Res Function(_$MovingMapStateImpl) then) =
      __$$MovingMapStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isMovingMap});
}

/// @nodoc
class __$$MovingMapStateImplCopyWithImpl<$Res>
    extends _$MovingMapStateCopyWithImpl<$Res, _$MovingMapStateImpl>
    implements _$$MovingMapStateImplCopyWith<$Res> {
  __$$MovingMapStateImplCopyWithImpl(
      _$MovingMapStateImpl _value, $Res Function(_$MovingMapStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isMovingMap = null,
  }) {
    return _then(_$MovingMapStateImpl(
      isMovingMap: null == isMovingMap
          ? _value.isMovingMap
          : isMovingMap // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$MovingMapStateImpl implements _MovingMapState {
  const _$MovingMapStateImpl({required this.isMovingMap});

  @override
  final bool isMovingMap;

  @override
  String toString() {
    return 'MovingMapState(isMovingMap: $isMovingMap)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MovingMapStateImpl &&
            (identical(other.isMovingMap, isMovingMap) ||
                other.isMovingMap == isMovingMap));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isMovingMap);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MovingMapStateImplCopyWith<_$MovingMapStateImpl> get copyWith =>
      __$$MovingMapStateImplCopyWithImpl<_$MovingMapStateImpl>(
          this, _$identity);
}

abstract class _MovingMapState implements MovingMapState {
  const factory _MovingMapState({required final bool isMovingMap}) =
      _$MovingMapStateImpl;

  @override
  bool get isMovingMap;
  @override
  @JsonKey(ignore: true)
  _$$MovingMapStateImplCopyWith<_$MovingMapStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
