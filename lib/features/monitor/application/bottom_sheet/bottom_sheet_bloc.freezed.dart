// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bottom_sheet_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BottomSheetEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DashboardWidget dashboardWidget) getDefaultMode,
    required TResult Function(Vehicle selectedVehicle) getVehicleMode,
    required TResult Function(
            List<Vehicle> listVehicle, VehicleGroupColor groupColor)
        getSearchMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DashboardWidget dashboardWidget)? getDefaultMode,
    TResult? Function(Vehicle selectedVehicle)? getVehicleMode,
    TResult? Function(List<Vehicle> listVehicle, VehicleGroupColor groupColor)?
        getSearchMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DashboardWidget dashboardWidget)? getDefaultMode,
    TResult Function(Vehicle selectedVehicle)? getVehicleMode,
    TResult Function(List<Vehicle> listVehicle, VehicleGroupColor groupColor)?
        getSearchMode,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDefaultMode value) getDefaultMode,
    required TResult Function(_GetVehicleMode value) getVehicleMode,
    required TResult Function(_GetSearchMode value) getSearchMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDefaultMode value)? getDefaultMode,
    TResult? Function(_GetVehicleMode value)? getVehicleMode,
    TResult? Function(_GetSearchMode value)? getSearchMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDefaultMode value)? getDefaultMode,
    TResult Function(_GetVehicleMode value)? getVehicleMode,
    TResult Function(_GetSearchMode value)? getSearchMode,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BottomSheetEventCopyWith<$Res> {
  factory $BottomSheetEventCopyWith(
          BottomSheetEvent value, $Res Function(BottomSheetEvent) then) =
      _$BottomSheetEventCopyWithImpl<$Res, BottomSheetEvent>;
}

/// @nodoc
class _$BottomSheetEventCopyWithImpl<$Res, $Val extends BottomSheetEvent>
    implements $BottomSheetEventCopyWith<$Res> {
  _$BottomSheetEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$GetDefaultModeImplCopyWith<$Res> {
  factory _$$GetDefaultModeImplCopyWith(_$GetDefaultModeImpl value,
          $Res Function(_$GetDefaultModeImpl) then) =
      __$$GetDefaultModeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DashboardWidget dashboardWidget});
}

/// @nodoc
class __$$GetDefaultModeImplCopyWithImpl<$Res>
    extends _$BottomSheetEventCopyWithImpl<$Res, _$GetDefaultModeImpl>
    implements _$$GetDefaultModeImplCopyWith<$Res> {
  __$$GetDefaultModeImplCopyWithImpl(
      _$GetDefaultModeImpl _value, $Res Function(_$GetDefaultModeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dashboardWidget = null,
  }) {
    return _then(_$GetDefaultModeImpl(
      dashboardWidget: null == dashboardWidget
          ? _value.dashboardWidget
          : dashboardWidget // ignore: cast_nullable_to_non_nullable
              as DashboardWidget,
    ));
  }
}

/// @nodoc

class _$GetDefaultModeImpl implements _GetDefaultMode {
  const _$GetDefaultModeImpl({required this.dashboardWidget});

  @override
  final DashboardWidget dashboardWidget;

  @override
  String toString() {
    return 'BottomSheetEvent.getDefaultMode(dashboardWidget: $dashboardWidget)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetDefaultModeImpl &&
            (identical(other.dashboardWidget, dashboardWidget) ||
                other.dashboardWidget == dashboardWidget));
  }

  @override
  int get hashCode => Object.hash(runtimeType, dashboardWidget);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetDefaultModeImplCopyWith<_$GetDefaultModeImpl> get copyWith =>
      __$$GetDefaultModeImplCopyWithImpl<_$GetDefaultModeImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DashboardWidget dashboardWidget) getDefaultMode,
    required TResult Function(Vehicle selectedVehicle) getVehicleMode,
    required TResult Function(
            List<Vehicle> listVehicle, VehicleGroupColor groupColor)
        getSearchMode,
  }) {
    return getDefaultMode(dashboardWidget);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DashboardWidget dashboardWidget)? getDefaultMode,
    TResult? Function(Vehicle selectedVehicle)? getVehicleMode,
    TResult? Function(List<Vehicle> listVehicle, VehicleGroupColor groupColor)?
        getSearchMode,
  }) {
    return getDefaultMode?.call(dashboardWidget);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DashboardWidget dashboardWidget)? getDefaultMode,
    TResult Function(Vehicle selectedVehicle)? getVehicleMode,
    TResult Function(List<Vehicle> listVehicle, VehicleGroupColor groupColor)?
        getSearchMode,
    required TResult orElse(),
  }) {
    if (getDefaultMode != null) {
      return getDefaultMode(dashboardWidget);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDefaultMode value) getDefaultMode,
    required TResult Function(_GetVehicleMode value) getVehicleMode,
    required TResult Function(_GetSearchMode value) getSearchMode,
  }) {
    return getDefaultMode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDefaultMode value)? getDefaultMode,
    TResult? Function(_GetVehicleMode value)? getVehicleMode,
    TResult? Function(_GetSearchMode value)? getSearchMode,
  }) {
    return getDefaultMode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDefaultMode value)? getDefaultMode,
    TResult Function(_GetVehicleMode value)? getVehicleMode,
    TResult Function(_GetSearchMode value)? getSearchMode,
    required TResult orElse(),
  }) {
    if (getDefaultMode != null) {
      return getDefaultMode(this);
    }
    return orElse();
  }
}

abstract class _GetDefaultMode implements BottomSheetEvent {
  const factory _GetDefaultMode(
      {required final DashboardWidget dashboardWidget}) = _$GetDefaultModeImpl;

  DashboardWidget get dashboardWidget;
  @JsonKey(ignore: true)
  _$$GetDefaultModeImplCopyWith<_$GetDefaultModeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetVehicleModeImplCopyWith<$Res> {
  factory _$$GetVehicleModeImplCopyWith(_$GetVehicleModeImpl value,
          $Res Function(_$GetVehicleModeImpl) then) =
      __$$GetVehicleModeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Vehicle selectedVehicle});

  $VehicleCopyWith<$Res> get selectedVehicle;
}

/// @nodoc
class __$$GetVehicleModeImplCopyWithImpl<$Res>
    extends _$BottomSheetEventCopyWithImpl<$Res, _$GetVehicleModeImpl>
    implements _$$GetVehicleModeImplCopyWith<$Res> {
  __$$GetVehicleModeImplCopyWithImpl(
      _$GetVehicleModeImpl _value, $Res Function(_$GetVehicleModeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedVehicle = null,
  }) {
    return _then(_$GetVehicleModeImpl(
      selectedVehicle: null == selectedVehicle
          ? _value.selectedVehicle
          : selectedVehicle // ignore: cast_nullable_to_non_nullable
              as Vehicle,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleCopyWith<$Res> get selectedVehicle {
    return $VehicleCopyWith<$Res>(_value.selectedVehicle, (value) {
      return _then(_value.copyWith(selectedVehicle: value));
    });
  }
}

/// @nodoc

class _$GetVehicleModeImpl implements _GetVehicleMode {
  const _$GetVehicleModeImpl({required this.selectedVehicle});

  @override
  final Vehicle selectedVehicle;

  @override
  String toString() {
    return 'BottomSheetEvent.getVehicleMode(selectedVehicle: $selectedVehicle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetVehicleModeImpl &&
            (identical(other.selectedVehicle, selectedVehicle) ||
                other.selectedVehicle == selectedVehicle));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedVehicle);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetVehicleModeImplCopyWith<_$GetVehicleModeImpl> get copyWith =>
      __$$GetVehicleModeImplCopyWithImpl<_$GetVehicleModeImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DashboardWidget dashboardWidget) getDefaultMode,
    required TResult Function(Vehicle selectedVehicle) getVehicleMode,
    required TResult Function(
            List<Vehicle> listVehicle, VehicleGroupColor groupColor)
        getSearchMode,
  }) {
    return getVehicleMode(selectedVehicle);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DashboardWidget dashboardWidget)? getDefaultMode,
    TResult? Function(Vehicle selectedVehicle)? getVehicleMode,
    TResult? Function(List<Vehicle> listVehicle, VehicleGroupColor groupColor)?
        getSearchMode,
  }) {
    return getVehicleMode?.call(selectedVehicle);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DashboardWidget dashboardWidget)? getDefaultMode,
    TResult Function(Vehicle selectedVehicle)? getVehicleMode,
    TResult Function(List<Vehicle> listVehicle, VehicleGroupColor groupColor)?
        getSearchMode,
    required TResult orElse(),
  }) {
    if (getVehicleMode != null) {
      return getVehicleMode(selectedVehicle);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDefaultMode value) getDefaultMode,
    required TResult Function(_GetVehicleMode value) getVehicleMode,
    required TResult Function(_GetSearchMode value) getSearchMode,
  }) {
    return getVehicleMode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDefaultMode value)? getDefaultMode,
    TResult? Function(_GetVehicleMode value)? getVehicleMode,
    TResult? Function(_GetSearchMode value)? getSearchMode,
  }) {
    return getVehicleMode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDefaultMode value)? getDefaultMode,
    TResult Function(_GetVehicleMode value)? getVehicleMode,
    TResult Function(_GetSearchMode value)? getSearchMode,
    required TResult orElse(),
  }) {
    if (getVehicleMode != null) {
      return getVehicleMode(this);
    }
    return orElse();
  }
}

abstract class _GetVehicleMode implements BottomSheetEvent {
  const factory _GetVehicleMode({required final Vehicle selectedVehicle}) =
      _$GetVehicleModeImpl;

  Vehicle get selectedVehicle;
  @JsonKey(ignore: true)
  _$$GetVehicleModeImplCopyWith<_$GetVehicleModeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetSearchModeImplCopyWith<$Res> {
  factory _$$GetSearchModeImplCopyWith(
          _$GetSearchModeImpl value, $Res Function(_$GetSearchModeImpl) then) =
      __$$GetSearchModeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Vehicle> listVehicle, VehicleGroupColor groupColor});
}

/// @nodoc
class __$$GetSearchModeImplCopyWithImpl<$Res>
    extends _$BottomSheetEventCopyWithImpl<$Res, _$GetSearchModeImpl>
    implements _$$GetSearchModeImplCopyWith<$Res> {
  __$$GetSearchModeImplCopyWithImpl(
      _$GetSearchModeImpl _value, $Res Function(_$GetSearchModeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicle = null,
    Object? groupColor = null,
  }) {
    return _then(_$GetSearchModeImpl(
      listVehicle: null == listVehicle
          ? _value._listVehicle
          : listVehicle // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      groupColor: null == groupColor
          ? _value.groupColor
          : groupColor // ignore: cast_nullable_to_non_nullable
              as VehicleGroupColor,
    ));
  }
}

/// @nodoc

class _$GetSearchModeImpl implements _GetSearchMode {
  const _$GetSearchModeImpl(
      {required final List<Vehicle> listVehicle, required this.groupColor})
      : _listVehicle = listVehicle;

  final List<Vehicle> _listVehicle;
  @override
  List<Vehicle> get listVehicle {
    if (_listVehicle is EqualUnmodifiableListView) return _listVehicle;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicle);
  }

  @override
  final VehicleGroupColor groupColor;

  @override
  String toString() {
    return 'BottomSheetEvent.getSearchMode(listVehicle: $listVehicle, groupColor: $groupColor)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetSearchModeImpl &&
            const DeepCollectionEquality()
                .equals(other._listVehicle, _listVehicle) &&
            (identical(other.groupColor, groupColor) ||
                other.groupColor == groupColor));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_listVehicle), groupColor);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetSearchModeImplCopyWith<_$GetSearchModeImpl> get copyWith =>
      __$$GetSearchModeImplCopyWithImpl<_$GetSearchModeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DashboardWidget dashboardWidget) getDefaultMode,
    required TResult Function(Vehicle selectedVehicle) getVehicleMode,
    required TResult Function(
            List<Vehicle> listVehicle, VehicleGroupColor groupColor)
        getSearchMode,
  }) {
    return getSearchMode(listVehicle, groupColor);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DashboardWidget dashboardWidget)? getDefaultMode,
    TResult? Function(Vehicle selectedVehicle)? getVehicleMode,
    TResult? Function(List<Vehicle> listVehicle, VehicleGroupColor groupColor)?
        getSearchMode,
  }) {
    return getSearchMode?.call(listVehicle, groupColor);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DashboardWidget dashboardWidget)? getDefaultMode,
    TResult Function(Vehicle selectedVehicle)? getVehicleMode,
    TResult Function(List<Vehicle> listVehicle, VehicleGroupColor groupColor)?
        getSearchMode,
    required TResult orElse(),
  }) {
    if (getSearchMode != null) {
      return getSearchMode(listVehicle, groupColor);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDefaultMode value) getDefaultMode,
    required TResult Function(_GetVehicleMode value) getVehicleMode,
    required TResult Function(_GetSearchMode value) getSearchMode,
  }) {
    return getSearchMode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDefaultMode value)? getDefaultMode,
    TResult? Function(_GetVehicleMode value)? getVehicleMode,
    TResult? Function(_GetSearchMode value)? getSearchMode,
  }) {
    return getSearchMode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDefaultMode value)? getDefaultMode,
    TResult Function(_GetVehicleMode value)? getVehicleMode,
    TResult Function(_GetSearchMode value)? getSearchMode,
    required TResult orElse(),
  }) {
    if (getSearchMode != null) {
      return getSearchMode(this);
    }
    return orElse();
  }
}

abstract class _GetSearchMode implements BottomSheetEvent {
  const factory _GetSearchMode(
      {required final List<Vehicle> listVehicle,
      required final VehicleGroupColor groupColor}) = _$GetSearchModeImpl;

  List<Vehicle> get listVehicle;
  VehicleGroupColor get groupColor;
  @JsonKey(ignore: true)
  _$$GetSearchModeImplCopyWith<_$GetSearchModeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$BottomSheetState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DashboardWidget dashboardWidget) defaultMode,
    required TResult Function(Vehicle vehicle) vehicleMode,
    required TResult Function(
            List<Vehicle> listVehicle, VehicleGroupColor color)
        search,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DashboardWidget dashboardWidget)? defaultMode,
    TResult? Function(Vehicle vehicle)? vehicleMode,
    TResult? Function(List<Vehicle> listVehicle, VehicleGroupColor color)?
        search,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DashboardWidget dashboardWidget)? defaultMode,
    TResult Function(Vehicle vehicle)? vehicleMode,
    TResult Function(List<Vehicle> listVehicle, VehicleGroupColor color)?
        search,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DefaultMode value) defaultMode,
    required TResult Function(_VehicleMode value) vehicleMode,
    required TResult Function(_SearchMode value) search,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DefaultMode value)? defaultMode,
    TResult? Function(_VehicleMode value)? vehicleMode,
    TResult? Function(_SearchMode value)? search,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DefaultMode value)? defaultMode,
    TResult Function(_VehicleMode value)? vehicleMode,
    TResult Function(_SearchMode value)? search,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BottomSheetStateCopyWith<$Res> {
  factory $BottomSheetStateCopyWith(
          BottomSheetState value, $Res Function(BottomSheetState) then) =
      _$BottomSheetStateCopyWithImpl<$Res, BottomSheetState>;
}

/// @nodoc
class _$BottomSheetStateCopyWithImpl<$Res, $Val extends BottomSheetState>
    implements $BottomSheetStateCopyWith<$Res> {
  _$BottomSheetStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$DefaultModeImplCopyWith<$Res> {
  factory _$$DefaultModeImplCopyWith(
          _$DefaultModeImpl value, $Res Function(_$DefaultModeImpl) then) =
      __$$DefaultModeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DashboardWidget dashboardWidget});
}

/// @nodoc
class __$$DefaultModeImplCopyWithImpl<$Res>
    extends _$BottomSheetStateCopyWithImpl<$Res, _$DefaultModeImpl>
    implements _$$DefaultModeImplCopyWith<$Res> {
  __$$DefaultModeImplCopyWithImpl(
      _$DefaultModeImpl _value, $Res Function(_$DefaultModeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dashboardWidget = null,
  }) {
    return _then(_$DefaultModeImpl(
      dashboardWidget: null == dashboardWidget
          ? _value.dashboardWidget
          : dashboardWidget // ignore: cast_nullable_to_non_nullable
              as DashboardWidget,
    ));
  }
}

/// @nodoc

class _$DefaultModeImpl implements _DefaultMode {
  const _$DefaultModeImpl({required this.dashboardWidget});

  @override
  final DashboardWidget dashboardWidget;

  @override
  String toString() {
    return 'BottomSheetState.defaultMode(dashboardWidget: $dashboardWidget)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DefaultModeImpl &&
            (identical(other.dashboardWidget, dashboardWidget) ||
                other.dashboardWidget == dashboardWidget));
  }

  @override
  int get hashCode => Object.hash(runtimeType, dashboardWidget);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DefaultModeImplCopyWith<_$DefaultModeImpl> get copyWith =>
      __$$DefaultModeImplCopyWithImpl<_$DefaultModeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DashboardWidget dashboardWidget) defaultMode,
    required TResult Function(Vehicle vehicle) vehicleMode,
    required TResult Function(
            List<Vehicle> listVehicle, VehicleGroupColor color)
        search,
  }) {
    return defaultMode(dashboardWidget);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DashboardWidget dashboardWidget)? defaultMode,
    TResult? Function(Vehicle vehicle)? vehicleMode,
    TResult? Function(List<Vehicle> listVehicle, VehicleGroupColor color)?
        search,
  }) {
    return defaultMode?.call(dashboardWidget);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DashboardWidget dashboardWidget)? defaultMode,
    TResult Function(Vehicle vehicle)? vehicleMode,
    TResult Function(List<Vehicle> listVehicle, VehicleGroupColor color)?
        search,
    required TResult orElse(),
  }) {
    if (defaultMode != null) {
      return defaultMode(dashboardWidget);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DefaultMode value) defaultMode,
    required TResult Function(_VehicleMode value) vehicleMode,
    required TResult Function(_SearchMode value) search,
  }) {
    return defaultMode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DefaultMode value)? defaultMode,
    TResult? Function(_VehicleMode value)? vehicleMode,
    TResult? Function(_SearchMode value)? search,
  }) {
    return defaultMode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DefaultMode value)? defaultMode,
    TResult Function(_VehicleMode value)? vehicleMode,
    TResult Function(_SearchMode value)? search,
    required TResult orElse(),
  }) {
    if (defaultMode != null) {
      return defaultMode(this);
    }
    return orElse();
  }
}

abstract class _DefaultMode implements BottomSheetState {
  const factory _DefaultMode({required final DashboardWidget dashboardWidget}) =
      _$DefaultModeImpl;

  DashboardWidget get dashboardWidget;
  @JsonKey(ignore: true)
  _$$DefaultModeImplCopyWith<_$DefaultModeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$VehicleModeImplCopyWith<$Res> {
  factory _$$VehicleModeImplCopyWith(
          _$VehicleModeImpl value, $Res Function(_$VehicleModeImpl) then) =
      __$$VehicleModeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Vehicle vehicle});

  $VehicleCopyWith<$Res> get vehicle;
}

/// @nodoc
class __$$VehicleModeImplCopyWithImpl<$Res>
    extends _$BottomSheetStateCopyWithImpl<$Res, _$VehicleModeImpl>
    implements _$$VehicleModeImplCopyWith<$Res> {
  __$$VehicleModeImplCopyWithImpl(
      _$VehicleModeImpl _value, $Res Function(_$VehicleModeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicle = null,
  }) {
    return _then(_$VehicleModeImpl(
      vehicle: null == vehicle
          ? _value.vehicle
          : vehicle // ignore: cast_nullable_to_non_nullable
              as Vehicle,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleCopyWith<$Res> get vehicle {
    return $VehicleCopyWith<$Res>(_value.vehicle, (value) {
      return _then(_value.copyWith(vehicle: value));
    });
  }
}

/// @nodoc

class _$VehicleModeImpl implements _VehicleMode {
  const _$VehicleModeImpl({required this.vehicle});

  @override
  final Vehicle vehicle;

  @override
  String toString() {
    return 'BottomSheetState.vehicleMode(vehicle: $vehicle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleModeImpl &&
            (identical(other.vehicle, vehicle) || other.vehicle == vehicle));
  }

  @override
  int get hashCode => Object.hash(runtimeType, vehicle);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleModeImplCopyWith<_$VehicleModeImpl> get copyWith =>
      __$$VehicleModeImplCopyWithImpl<_$VehicleModeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DashboardWidget dashboardWidget) defaultMode,
    required TResult Function(Vehicle vehicle) vehicleMode,
    required TResult Function(
            List<Vehicle> listVehicle, VehicleGroupColor color)
        search,
  }) {
    return vehicleMode(vehicle);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DashboardWidget dashboardWidget)? defaultMode,
    TResult? Function(Vehicle vehicle)? vehicleMode,
    TResult? Function(List<Vehicle> listVehicle, VehicleGroupColor color)?
        search,
  }) {
    return vehicleMode?.call(vehicle);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DashboardWidget dashboardWidget)? defaultMode,
    TResult Function(Vehicle vehicle)? vehicleMode,
    TResult Function(List<Vehicle> listVehicle, VehicleGroupColor color)?
        search,
    required TResult orElse(),
  }) {
    if (vehicleMode != null) {
      return vehicleMode(vehicle);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DefaultMode value) defaultMode,
    required TResult Function(_VehicleMode value) vehicleMode,
    required TResult Function(_SearchMode value) search,
  }) {
    return vehicleMode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DefaultMode value)? defaultMode,
    TResult? Function(_VehicleMode value)? vehicleMode,
    TResult? Function(_SearchMode value)? search,
  }) {
    return vehicleMode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DefaultMode value)? defaultMode,
    TResult Function(_VehicleMode value)? vehicleMode,
    TResult Function(_SearchMode value)? search,
    required TResult orElse(),
  }) {
    if (vehicleMode != null) {
      return vehicleMode(this);
    }
    return orElse();
  }
}

abstract class _VehicleMode implements BottomSheetState {
  const factory _VehicleMode({required final Vehicle vehicle}) =
      _$VehicleModeImpl;

  Vehicle get vehicle;
  @JsonKey(ignore: true)
  _$$VehicleModeImplCopyWith<_$VehicleModeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchModeImplCopyWith<$Res> {
  factory _$$SearchModeImplCopyWith(
          _$SearchModeImpl value, $Res Function(_$SearchModeImpl) then) =
      __$$SearchModeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Vehicle> listVehicle, VehicleGroupColor color});
}

/// @nodoc
class __$$SearchModeImplCopyWithImpl<$Res>
    extends _$BottomSheetStateCopyWithImpl<$Res, _$SearchModeImpl>
    implements _$$SearchModeImplCopyWith<$Res> {
  __$$SearchModeImplCopyWithImpl(
      _$SearchModeImpl _value, $Res Function(_$SearchModeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicle = null,
    Object? color = null,
  }) {
    return _then(_$SearchModeImpl(
      listVehicle: null == listVehicle
          ? _value._listVehicle
          : listVehicle // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as VehicleGroupColor,
    ));
  }
}

/// @nodoc

class _$SearchModeImpl implements _SearchMode {
  const _$SearchModeImpl(
      {required final List<Vehicle> listVehicle, required this.color})
      : _listVehicle = listVehicle;

  final List<Vehicle> _listVehicle;
  @override
  List<Vehicle> get listVehicle {
    if (_listVehicle is EqualUnmodifiableListView) return _listVehicle;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicle);
  }

  @override
  final VehicleGroupColor color;

  @override
  String toString() {
    return 'BottomSheetState.search(listVehicle: $listVehicle, color: $color)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchModeImpl &&
            const DeepCollectionEquality()
                .equals(other._listVehicle, _listVehicle) &&
            (identical(other.color, color) || other.color == color));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_listVehicle), color);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchModeImplCopyWith<_$SearchModeImpl> get copyWith =>
      __$$SearchModeImplCopyWithImpl<_$SearchModeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DashboardWidget dashboardWidget) defaultMode,
    required TResult Function(Vehicle vehicle) vehicleMode,
    required TResult Function(
            List<Vehicle> listVehicle, VehicleGroupColor color)
        search,
  }) {
    return search(listVehicle, color);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DashboardWidget dashboardWidget)? defaultMode,
    TResult? Function(Vehicle vehicle)? vehicleMode,
    TResult? Function(List<Vehicle> listVehicle, VehicleGroupColor color)?
        search,
  }) {
    return search?.call(listVehicle, color);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DashboardWidget dashboardWidget)? defaultMode,
    TResult Function(Vehicle vehicle)? vehicleMode,
    TResult Function(List<Vehicle> listVehicle, VehicleGroupColor color)?
        search,
    required TResult orElse(),
  }) {
    if (search != null) {
      return search(listVehicle, color);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DefaultMode value) defaultMode,
    required TResult Function(_VehicleMode value) vehicleMode,
    required TResult Function(_SearchMode value) search,
  }) {
    return search(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DefaultMode value)? defaultMode,
    TResult? Function(_VehicleMode value)? vehicleMode,
    TResult? Function(_SearchMode value)? search,
  }) {
    return search?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DefaultMode value)? defaultMode,
    TResult Function(_VehicleMode value)? vehicleMode,
    TResult Function(_SearchMode value)? search,
    required TResult orElse(),
  }) {
    if (search != null) {
      return search(this);
    }
    return orElse();
  }
}

abstract class _SearchMode implements BottomSheetState {
  const factory _SearchMode(
      {required final List<Vehicle> listVehicle,
      required final VehicleGroupColor color}) = _$SearchModeImpl;

  List<Vehicle> get listVehicle;
  VehicleGroupColor get color;
  @JsonKey(ignore: true)
  _$$SearchModeImplCopyWith<_$SearchModeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
