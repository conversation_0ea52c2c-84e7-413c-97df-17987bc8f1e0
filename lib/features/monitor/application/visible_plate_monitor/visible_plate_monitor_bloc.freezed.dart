// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'visible_plate_monitor_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$VisiblePlateMonitorEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) changeValue,
    required TResult Function(bool isResetCluster) resetClusterToMarkers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? changeValue,
    TResult? Function(bool isResetCluster)? resetClusterToMarkers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? changeValue,
    TResult Function(bool isResetCluster)? resetClusterToMarkers,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeValue value) changeValue,
    required TResult Function(_ResetClusterToMarkers value)
        resetClusterToMarkers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeValue value)? changeValue,
    TResult? Function(_ResetClusterToMarkers value)? resetClusterToMarkers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeValue value)? changeValue,
    TResult Function(_ResetClusterToMarkers value)? resetClusterToMarkers,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VisiblePlateMonitorEventCopyWith<$Res> {
  factory $VisiblePlateMonitorEventCopyWith(VisiblePlateMonitorEvent value,
          $Res Function(VisiblePlateMonitorEvent) then) =
      _$VisiblePlateMonitorEventCopyWithImpl<$Res, VisiblePlateMonitorEvent>;
}

/// @nodoc
class _$VisiblePlateMonitorEventCopyWithImpl<$Res,
        $Val extends VisiblePlateMonitorEvent>
    implements $VisiblePlateMonitorEventCopyWith<$Res> {
  _$VisiblePlateMonitorEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$ChangeValueImplCopyWith<$Res> {
  factory _$$ChangeValueImplCopyWith(
          _$ChangeValueImpl value, $Res Function(_$ChangeValueImpl) then) =
      __$$ChangeValueImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool value});
}

/// @nodoc
class __$$ChangeValueImplCopyWithImpl<$Res>
    extends _$VisiblePlateMonitorEventCopyWithImpl<$Res, _$ChangeValueImpl>
    implements _$$ChangeValueImplCopyWith<$Res> {
  __$$ChangeValueImplCopyWithImpl(
      _$ChangeValueImpl _value, $Res Function(_$ChangeValueImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeValueImpl(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ChangeValueImpl implements _ChangeValue {
  const _$ChangeValueImpl({required this.value});

  @override
  final bool value;

  @override
  String toString() {
    return 'VisiblePlateMonitorEvent.changeValue(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeValueImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeValueImplCopyWith<_$ChangeValueImpl> get copyWith =>
      __$$ChangeValueImplCopyWithImpl<_$ChangeValueImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) changeValue,
    required TResult Function(bool isResetCluster) resetClusterToMarkers,
  }) {
    return changeValue(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? changeValue,
    TResult? Function(bool isResetCluster)? resetClusterToMarkers,
  }) {
    return changeValue?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? changeValue,
    TResult Function(bool isResetCluster)? resetClusterToMarkers,
    required TResult orElse(),
  }) {
    if (changeValue != null) {
      return changeValue(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeValue value) changeValue,
    required TResult Function(_ResetClusterToMarkers value)
        resetClusterToMarkers,
  }) {
    return changeValue(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeValue value)? changeValue,
    TResult? Function(_ResetClusterToMarkers value)? resetClusterToMarkers,
  }) {
    return changeValue?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeValue value)? changeValue,
    TResult Function(_ResetClusterToMarkers value)? resetClusterToMarkers,
    required TResult orElse(),
  }) {
    if (changeValue != null) {
      return changeValue(this);
    }
    return orElse();
  }
}

abstract class _ChangeValue implements VisiblePlateMonitorEvent {
  const factory _ChangeValue({required final bool value}) = _$ChangeValueImpl;

  bool get value;
  @JsonKey(ignore: true)
  _$$ChangeValueImplCopyWith<_$ChangeValueImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetClusterToMarkersImplCopyWith<$Res> {
  factory _$$ResetClusterToMarkersImplCopyWith(
          _$ResetClusterToMarkersImpl value,
          $Res Function(_$ResetClusterToMarkersImpl) then) =
      __$$ResetClusterToMarkersImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isResetCluster});
}

/// @nodoc
class __$$ResetClusterToMarkersImplCopyWithImpl<$Res>
    extends _$VisiblePlateMonitorEventCopyWithImpl<$Res,
        _$ResetClusterToMarkersImpl>
    implements _$$ResetClusterToMarkersImplCopyWith<$Res> {
  __$$ResetClusterToMarkersImplCopyWithImpl(_$ResetClusterToMarkersImpl _value,
      $Res Function(_$ResetClusterToMarkersImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isResetCluster = null,
  }) {
    return _then(_$ResetClusterToMarkersImpl(
      isResetCluster: null == isResetCluster
          ? _value.isResetCluster
          : isResetCluster // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ResetClusterToMarkersImpl implements _ResetClusterToMarkers {
  const _$ResetClusterToMarkersImpl({required this.isResetCluster});

  @override
  final bool isResetCluster;

  @override
  String toString() {
    return 'VisiblePlateMonitorEvent.resetClusterToMarkers(isResetCluster: $isResetCluster)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResetClusterToMarkersImpl &&
            (identical(other.isResetCluster, isResetCluster) ||
                other.isResetCluster == isResetCluster));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isResetCluster);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ResetClusterToMarkersImplCopyWith<_$ResetClusterToMarkersImpl>
      get copyWith => __$$ResetClusterToMarkersImplCopyWithImpl<
          _$ResetClusterToMarkersImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) changeValue,
    required TResult Function(bool isResetCluster) resetClusterToMarkers,
  }) {
    return resetClusterToMarkers(isResetCluster);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? changeValue,
    TResult? Function(bool isResetCluster)? resetClusterToMarkers,
  }) {
    return resetClusterToMarkers?.call(isResetCluster);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? changeValue,
    TResult Function(bool isResetCluster)? resetClusterToMarkers,
    required TResult orElse(),
  }) {
    if (resetClusterToMarkers != null) {
      return resetClusterToMarkers(isResetCluster);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeValue value) changeValue,
    required TResult Function(_ResetClusterToMarkers value)
        resetClusterToMarkers,
  }) {
    return resetClusterToMarkers(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeValue value)? changeValue,
    TResult? Function(_ResetClusterToMarkers value)? resetClusterToMarkers,
  }) {
    return resetClusterToMarkers?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeValue value)? changeValue,
    TResult Function(_ResetClusterToMarkers value)? resetClusterToMarkers,
    required TResult orElse(),
  }) {
    if (resetClusterToMarkers != null) {
      return resetClusterToMarkers(this);
    }
    return orElse();
  }
}

abstract class _ResetClusterToMarkers implements VisiblePlateMonitorEvent {
  const factory _ResetClusterToMarkers({required final bool isResetCluster}) =
      _$ResetClusterToMarkersImpl;

  bool get isResetCluster;
  @JsonKey(ignore: true)
  _$$ResetClusterToMarkersImplCopyWith<_$ResetClusterToMarkersImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$VisiblePlateMonitorState {
  bool get visiblePlate => throw _privateConstructorUsedError;
  bool get isResetCluster => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $VisiblePlateMonitorStateCopyWith<VisiblePlateMonitorState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VisiblePlateMonitorStateCopyWith<$Res> {
  factory $VisiblePlateMonitorStateCopyWith(VisiblePlateMonitorState value,
          $Res Function(VisiblePlateMonitorState) then) =
      _$VisiblePlateMonitorStateCopyWithImpl<$Res, VisiblePlateMonitorState>;
  @useResult
  $Res call({bool visiblePlate, bool isResetCluster});
}

/// @nodoc
class _$VisiblePlateMonitorStateCopyWithImpl<$Res,
        $Val extends VisiblePlateMonitorState>
    implements $VisiblePlateMonitorStateCopyWith<$Res> {
  _$VisiblePlateMonitorStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? visiblePlate = null,
    Object? isResetCluster = null,
  }) {
    return _then(_value.copyWith(
      visiblePlate: null == visiblePlate
          ? _value.visiblePlate
          : visiblePlate // ignore: cast_nullable_to_non_nullable
              as bool,
      isResetCluster: null == isResetCluster
          ? _value.isResetCluster
          : isResetCluster // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VisiblePlateMonitorStateImplCopyWith<$Res>
    implements $VisiblePlateMonitorStateCopyWith<$Res> {
  factory _$$VisiblePlateMonitorStateImplCopyWith(
          _$VisiblePlateMonitorStateImpl value,
          $Res Function(_$VisiblePlateMonitorStateImpl) then) =
      __$$VisiblePlateMonitorStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool visiblePlate, bool isResetCluster});
}

/// @nodoc
class __$$VisiblePlateMonitorStateImplCopyWithImpl<$Res>
    extends _$VisiblePlateMonitorStateCopyWithImpl<$Res,
        _$VisiblePlateMonitorStateImpl>
    implements _$$VisiblePlateMonitorStateImplCopyWith<$Res> {
  __$$VisiblePlateMonitorStateImplCopyWithImpl(
      _$VisiblePlateMonitorStateImpl _value,
      $Res Function(_$VisiblePlateMonitorStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? visiblePlate = null,
    Object? isResetCluster = null,
  }) {
    return _then(_$VisiblePlateMonitorStateImpl(
      visiblePlate: null == visiblePlate
          ? _value.visiblePlate
          : visiblePlate // ignore: cast_nullable_to_non_nullable
              as bool,
      isResetCluster: null == isResetCluster
          ? _value.isResetCluster
          : isResetCluster // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$VisiblePlateMonitorStateImpl implements _VisiblePlateMonitorState {
  const _$VisiblePlateMonitorStateImpl(
      {this.visiblePlate = false, this.isResetCluster = false});

  @override
  @JsonKey()
  final bool visiblePlate;
  @override
  @JsonKey()
  final bool isResetCluster;

  @override
  String toString() {
    return 'VisiblePlateMonitorState(visiblePlate: $visiblePlate, isResetCluster: $isResetCluster)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VisiblePlateMonitorStateImpl &&
            (identical(other.visiblePlate, visiblePlate) ||
                other.visiblePlate == visiblePlate) &&
            (identical(other.isResetCluster, isResetCluster) ||
                other.isResetCluster == isResetCluster));
  }

  @override
  int get hashCode => Object.hash(runtimeType, visiblePlate, isResetCluster);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VisiblePlateMonitorStateImplCopyWith<_$VisiblePlateMonitorStateImpl>
      get copyWith => __$$VisiblePlateMonitorStateImplCopyWithImpl<
          _$VisiblePlateMonitorStateImpl>(this, _$identity);
}

abstract class _VisiblePlateMonitorState implements VisiblePlateMonitorState {
  const factory _VisiblePlateMonitorState(
      {final bool visiblePlate,
      final bool isResetCluster}) = _$VisiblePlateMonitorStateImpl;

  @override
  bool get visiblePlate;
  @override
  bool get isResetCluster;
  @override
  @JsonKey(ignore: true)
  _$$VisiblePlateMonitorStateImplCopyWith<_$VisiblePlateMonitorStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
