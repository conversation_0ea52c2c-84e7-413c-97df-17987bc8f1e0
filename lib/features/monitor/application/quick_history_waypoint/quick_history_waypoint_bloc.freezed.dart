// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quick_history_waypoint_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$QuickHistoryWaypointEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() clearWaypointEvent,
    required TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)
        getWaypoint,
    required TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)
        getWaypointSuccess,
    required TResult Function(HistoryRouteFailure failure) failureEvent,
    required TResult Function(List<LatLng> latLngExtra) addCurrentUpdatePoint,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? clearWaypointEvent,
    TResult? Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult? Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult? Function(HistoryRouteFailure failure)? failureEvent,
    TResult? Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? clearWaypointEvent,
    TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult Function(HistoryRouteFailure failure)? failureEvent,
    TResult Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ClearWaypointEvent value) clearWaypointEvent,
    required TResult Function(_GetWaypoint value) getWaypoint,
    required TResult Function(_GetWaypointSuccess value) getWaypointSuccess,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_AddCurrentUpdatePoint value)
        addCurrentUpdatePoint,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult? Function(_GetWaypoint value)? getWaypoint,
    TResult? Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult Function(_GetWaypoint value)? getWaypoint,
    TResult Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuickHistoryWaypointEventCopyWith<$Res> {
  factory $QuickHistoryWaypointEventCopyWith(QuickHistoryWaypointEvent value,
          $Res Function(QuickHistoryWaypointEvent) then) =
      _$QuickHistoryWaypointEventCopyWithImpl<$Res, QuickHistoryWaypointEvent>;
}

/// @nodoc
class _$QuickHistoryWaypointEventCopyWithImpl<$Res,
        $Val extends QuickHistoryWaypointEvent>
    implements $QuickHistoryWaypointEventCopyWith<$Res> {
  _$QuickHistoryWaypointEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$ClearWaypointEventImplCopyWith<$Res> {
  factory _$$ClearWaypointEventImplCopyWith(_$ClearWaypointEventImpl value,
          $Res Function(_$ClearWaypointEventImpl) then) =
      __$$ClearWaypointEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearWaypointEventImplCopyWithImpl<$Res>
    extends _$QuickHistoryWaypointEventCopyWithImpl<$Res,
        _$ClearWaypointEventImpl>
    implements _$$ClearWaypointEventImplCopyWith<$Res> {
  __$$ClearWaypointEventImplCopyWithImpl(_$ClearWaypointEventImpl _value,
      $Res Function(_$ClearWaypointEventImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearWaypointEventImpl implements _ClearWaypointEvent {
  const _$ClearWaypointEventImpl();

  @override
  String toString() {
    return 'QuickHistoryWaypointEvent.clearWaypointEvent()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearWaypointEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() clearWaypointEvent,
    required TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)
        getWaypoint,
    required TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)
        getWaypointSuccess,
    required TResult Function(HistoryRouteFailure failure) failureEvent,
    required TResult Function(List<LatLng> latLngExtra) addCurrentUpdatePoint,
  }) {
    return clearWaypointEvent();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? clearWaypointEvent,
    TResult? Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult? Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult? Function(HistoryRouteFailure failure)? failureEvent,
    TResult? Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
  }) {
    return clearWaypointEvent?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? clearWaypointEvent,
    TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult Function(HistoryRouteFailure failure)? failureEvent,
    TResult Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) {
    if (clearWaypointEvent != null) {
      return clearWaypointEvent();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ClearWaypointEvent value) clearWaypointEvent,
    required TResult Function(_GetWaypoint value) getWaypoint,
    required TResult Function(_GetWaypointSuccess value) getWaypointSuccess,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_AddCurrentUpdatePoint value)
        addCurrentUpdatePoint,
  }) {
    return clearWaypointEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult? Function(_GetWaypoint value)? getWaypoint,
    TResult? Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
  }) {
    return clearWaypointEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult Function(_GetWaypoint value)? getWaypoint,
    TResult Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) {
    if (clearWaypointEvent != null) {
      return clearWaypointEvent(this);
    }
    return orElse();
  }
}

abstract class _ClearWaypointEvent implements QuickHistoryWaypointEvent {
  const factory _ClearWaypointEvent() = _$ClearWaypointEventImpl;
}

/// @nodoc
abstract class _$$GetWaypointImplCopyWith<$Res> {
  factory _$$GetWaypointImplCopyWith(
          _$GetWaypointImpl value, $Res Function(_$GetWaypointImpl) then) =
      __$$GetWaypointImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String vehicleId,
      String from,
      String to,
      bool isOverSpeedByRoad,
      LatLng currentWaypoint});
}

/// @nodoc
class __$$GetWaypointImplCopyWithImpl<$Res>
    extends _$QuickHistoryWaypointEventCopyWithImpl<$Res, _$GetWaypointImpl>
    implements _$$GetWaypointImplCopyWith<$Res> {
  __$$GetWaypointImplCopyWithImpl(
      _$GetWaypointImpl _value, $Res Function(_$GetWaypointImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? from = null,
    Object? to = null,
    Object? isOverSpeedByRoad = null,
    Object? currentWaypoint = null,
  }) {
    return _then(_$GetWaypointImpl(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
      currentWaypoint: null == currentWaypoint
          ? _value.currentWaypoint
          : currentWaypoint // ignore: cast_nullable_to_non_nullable
              as LatLng,
    ));
  }
}

/// @nodoc

class _$GetWaypointImpl implements _GetWaypoint {
  const _$GetWaypointImpl(
      {required this.vehicleId,
      required this.from,
      required this.to,
      required this.isOverSpeedByRoad,
      required this.currentWaypoint});

  @override
  final String vehicleId;
  @override
  final String from;
  @override
  final String to;
  @override
  final bool isOverSpeedByRoad;
  @override
  final LatLng currentWaypoint;

  @override
  String toString() {
    return 'QuickHistoryWaypointEvent.getWaypoint(vehicleId: $vehicleId, from: $from, to: $to, isOverSpeedByRoad: $isOverSpeedByRoad, currentWaypoint: $currentWaypoint)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetWaypointImpl &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.from, from) || other.from == from) &&
            (identical(other.to, to) || other.to == to) &&
            (identical(other.isOverSpeedByRoad, isOverSpeedByRoad) ||
                other.isOverSpeedByRoad == isOverSpeedByRoad) &&
            (identical(other.currentWaypoint, currentWaypoint) ||
                other.currentWaypoint == currentWaypoint));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, vehicleId, from, to, isOverSpeedByRoad, currentWaypoint);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetWaypointImplCopyWith<_$GetWaypointImpl> get copyWith =>
      __$$GetWaypointImplCopyWithImpl<_$GetWaypointImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() clearWaypointEvent,
    required TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)
        getWaypoint,
    required TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)
        getWaypointSuccess,
    required TResult Function(HistoryRouteFailure failure) failureEvent,
    required TResult Function(List<LatLng> latLngExtra) addCurrentUpdatePoint,
  }) {
    return getWaypoint(vehicleId, from, to, isOverSpeedByRoad, currentWaypoint);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? clearWaypointEvent,
    TResult? Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult? Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult? Function(HistoryRouteFailure failure)? failureEvent,
    TResult? Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
  }) {
    return getWaypoint?.call(
        vehicleId, from, to, isOverSpeedByRoad, currentWaypoint);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? clearWaypointEvent,
    TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult Function(HistoryRouteFailure failure)? failureEvent,
    TResult Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) {
    if (getWaypoint != null) {
      return getWaypoint(
          vehicleId, from, to, isOverSpeedByRoad, currentWaypoint);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ClearWaypointEvent value) clearWaypointEvent,
    required TResult Function(_GetWaypoint value) getWaypoint,
    required TResult Function(_GetWaypointSuccess value) getWaypointSuccess,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_AddCurrentUpdatePoint value)
        addCurrentUpdatePoint,
  }) {
    return getWaypoint(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult? Function(_GetWaypoint value)? getWaypoint,
    TResult? Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
  }) {
    return getWaypoint?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult Function(_GetWaypoint value)? getWaypoint,
    TResult Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) {
    if (getWaypoint != null) {
      return getWaypoint(this);
    }
    return orElse();
  }
}

abstract class _GetWaypoint implements QuickHistoryWaypointEvent {
  const factory _GetWaypoint(
      {required final String vehicleId,
      required final String from,
      required final String to,
      required final bool isOverSpeedByRoad,
      required final LatLng currentWaypoint}) = _$GetWaypointImpl;

  String get vehicleId;
  String get from;
  String get to;
  bool get isOverSpeedByRoad;
  LatLng get currentWaypoint;
  @JsonKey(ignore: true)
  _$$GetWaypointImplCopyWith<_$GetWaypointImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetWaypointSuccessImplCopyWith<$Res> {
  factory _$$GetWaypointSuccessImplCopyWith(_$GetWaypointSuccessImpl value,
          $Res Function(_$GetWaypointSuccessImpl) then) =
      __$$GetWaypointSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {HistoryRouteResponse historyRouteResponse,
      String vehicleId,
      LatLng currentWaypoint});

  $HistoryRouteResponseCopyWith<$Res> get historyRouteResponse;
}

/// @nodoc
class __$$GetWaypointSuccessImplCopyWithImpl<$Res>
    extends _$QuickHistoryWaypointEventCopyWithImpl<$Res,
        _$GetWaypointSuccessImpl>
    implements _$$GetWaypointSuccessImplCopyWith<$Res> {
  __$$GetWaypointSuccessImplCopyWithImpl(_$GetWaypointSuccessImpl _value,
      $Res Function(_$GetWaypointSuccessImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? historyRouteResponse = null,
    Object? vehicleId = null,
    Object? currentWaypoint = null,
  }) {
    return _then(_$GetWaypointSuccessImpl(
      historyRouteResponse: null == historyRouteResponse
          ? _value.historyRouteResponse
          : historyRouteResponse // ignore: cast_nullable_to_non_nullable
              as HistoryRouteResponse,
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      currentWaypoint: null == currentWaypoint
          ? _value.currentWaypoint
          : currentWaypoint // ignore: cast_nullable_to_non_nullable
              as LatLng,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $HistoryRouteResponseCopyWith<$Res> get historyRouteResponse {
    return $HistoryRouteResponseCopyWith<$Res>(_value.historyRouteResponse,
        (value) {
      return _then(_value.copyWith(historyRouteResponse: value));
    });
  }
}

/// @nodoc

class _$GetWaypointSuccessImpl implements _GetWaypointSuccess {
  const _$GetWaypointSuccessImpl(
      {required this.historyRouteResponse,
      required this.vehicleId,
      required this.currentWaypoint});

  @override
  final HistoryRouteResponse historyRouteResponse;
  @override
  final String vehicleId;
  @override
  final LatLng currentWaypoint;

  @override
  String toString() {
    return 'QuickHistoryWaypointEvent.getWaypointSuccess(historyRouteResponse: $historyRouteResponse, vehicleId: $vehicleId, currentWaypoint: $currentWaypoint)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetWaypointSuccessImpl &&
            (identical(other.historyRouteResponse, historyRouteResponse) ||
                other.historyRouteResponse == historyRouteResponse) &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.currentWaypoint, currentWaypoint) ||
                other.currentWaypoint == currentWaypoint));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, historyRouteResponse, vehicleId, currentWaypoint);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetWaypointSuccessImplCopyWith<_$GetWaypointSuccessImpl> get copyWith =>
      __$$GetWaypointSuccessImplCopyWithImpl<_$GetWaypointSuccessImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() clearWaypointEvent,
    required TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)
        getWaypoint,
    required TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)
        getWaypointSuccess,
    required TResult Function(HistoryRouteFailure failure) failureEvent,
    required TResult Function(List<LatLng> latLngExtra) addCurrentUpdatePoint,
  }) {
    return getWaypointSuccess(historyRouteResponse, vehicleId, currentWaypoint);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? clearWaypointEvent,
    TResult? Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult? Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult? Function(HistoryRouteFailure failure)? failureEvent,
    TResult? Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
  }) {
    return getWaypointSuccess?.call(
        historyRouteResponse, vehicleId, currentWaypoint);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? clearWaypointEvent,
    TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult Function(HistoryRouteFailure failure)? failureEvent,
    TResult Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) {
    if (getWaypointSuccess != null) {
      return getWaypointSuccess(
          historyRouteResponse, vehicleId, currentWaypoint);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ClearWaypointEvent value) clearWaypointEvent,
    required TResult Function(_GetWaypoint value) getWaypoint,
    required TResult Function(_GetWaypointSuccess value) getWaypointSuccess,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_AddCurrentUpdatePoint value)
        addCurrentUpdatePoint,
  }) {
    return getWaypointSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult? Function(_GetWaypoint value)? getWaypoint,
    TResult? Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
  }) {
    return getWaypointSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult Function(_GetWaypoint value)? getWaypoint,
    TResult Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) {
    if (getWaypointSuccess != null) {
      return getWaypointSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetWaypointSuccess implements QuickHistoryWaypointEvent {
  const factory _GetWaypointSuccess(
      {required final HistoryRouteResponse historyRouteResponse,
      required final String vehicleId,
      required final LatLng currentWaypoint}) = _$GetWaypointSuccessImpl;

  HistoryRouteResponse get historyRouteResponse;
  String get vehicleId;
  LatLng get currentWaypoint;
  @JsonKey(ignore: true)
  _$$GetWaypointSuccessImplCopyWith<_$GetWaypointSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FailureEventImplCopyWith<$Res> {
  factory _$$FailureEventImplCopyWith(
          _$FailureEventImpl value, $Res Function(_$FailureEventImpl) then) =
      __$$FailureEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({HistoryRouteFailure failure});

  $HistoryRouteFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureEventImplCopyWithImpl<$Res>
    extends _$QuickHistoryWaypointEventCopyWithImpl<$Res, _$FailureEventImpl>
    implements _$$FailureEventImplCopyWith<$Res> {
  __$$FailureEventImplCopyWithImpl(
      _$FailureEventImpl _value, $Res Function(_$FailureEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$FailureEventImpl(
      failure: null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as HistoryRouteFailure,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $HistoryRouteFailureCopyWith<$Res> get failure {
    return $HistoryRouteFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureEventImpl implements _FailureEvent {
  const _$FailureEventImpl({required this.failure});

  @override
  final HistoryRouteFailure failure;

  @override
  String toString() {
    return 'QuickHistoryWaypointEvent.failureEvent(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureEventImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureEventImplCopyWith<_$FailureEventImpl> get copyWith =>
      __$$FailureEventImplCopyWithImpl<_$FailureEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() clearWaypointEvent,
    required TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)
        getWaypoint,
    required TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)
        getWaypointSuccess,
    required TResult Function(HistoryRouteFailure failure) failureEvent,
    required TResult Function(List<LatLng> latLngExtra) addCurrentUpdatePoint,
  }) {
    return failureEvent(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? clearWaypointEvent,
    TResult? Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult? Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult? Function(HistoryRouteFailure failure)? failureEvent,
    TResult? Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
  }) {
    return failureEvent?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? clearWaypointEvent,
    TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult Function(HistoryRouteFailure failure)? failureEvent,
    TResult Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) {
    if (failureEvent != null) {
      return failureEvent(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ClearWaypointEvent value) clearWaypointEvent,
    required TResult Function(_GetWaypoint value) getWaypoint,
    required TResult Function(_GetWaypointSuccess value) getWaypointSuccess,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_AddCurrentUpdatePoint value)
        addCurrentUpdatePoint,
  }) {
    return failureEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult? Function(_GetWaypoint value)? getWaypoint,
    TResult? Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
  }) {
    return failureEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult Function(_GetWaypoint value)? getWaypoint,
    TResult Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) {
    if (failureEvent != null) {
      return failureEvent(this);
    }
    return orElse();
  }
}

abstract class _FailureEvent implements QuickHistoryWaypointEvent {
  const factory _FailureEvent({required final HistoryRouteFailure failure}) =
      _$FailureEventImpl;

  HistoryRouteFailure get failure;
  @JsonKey(ignore: true)
  _$$FailureEventImplCopyWith<_$FailureEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddCurrentUpdatePointImplCopyWith<$Res> {
  factory _$$AddCurrentUpdatePointImplCopyWith(
          _$AddCurrentUpdatePointImpl value,
          $Res Function(_$AddCurrentUpdatePointImpl) then) =
      __$$AddCurrentUpdatePointImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<LatLng> latLngExtra});
}

/// @nodoc
class __$$AddCurrentUpdatePointImplCopyWithImpl<$Res>
    extends _$QuickHistoryWaypointEventCopyWithImpl<$Res,
        _$AddCurrentUpdatePointImpl>
    implements _$$AddCurrentUpdatePointImplCopyWith<$Res> {
  __$$AddCurrentUpdatePointImplCopyWithImpl(_$AddCurrentUpdatePointImpl _value,
      $Res Function(_$AddCurrentUpdatePointImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latLngExtra = null,
  }) {
    return _then(_$AddCurrentUpdatePointImpl(
      latLngExtra: null == latLngExtra
          ? _value._latLngExtra
          : latLngExtra // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
    ));
  }
}

/// @nodoc

class _$AddCurrentUpdatePointImpl implements _AddCurrentUpdatePoint {
  const _$AddCurrentUpdatePointImpl({required final List<LatLng> latLngExtra})
      : _latLngExtra = latLngExtra;

  final List<LatLng> _latLngExtra;
  @override
  List<LatLng> get latLngExtra {
    if (_latLngExtra is EqualUnmodifiableListView) return _latLngExtra;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_latLngExtra);
  }

  @override
  String toString() {
    return 'QuickHistoryWaypointEvent.addCurrentUpdatePoint(latLngExtra: $latLngExtra)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddCurrentUpdatePointImpl &&
            const DeepCollectionEquality()
                .equals(other._latLngExtra, _latLngExtra));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_latLngExtra));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AddCurrentUpdatePointImplCopyWith<_$AddCurrentUpdatePointImpl>
      get copyWith => __$$AddCurrentUpdatePointImplCopyWithImpl<
          _$AddCurrentUpdatePointImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() clearWaypointEvent,
    required TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)
        getWaypoint,
    required TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)
        getWaypointSuccess,
    required TResult Function(HistoryRouteFailure failure) failureEvent,
    required TResult Function(List<LatLng> latLngExtra) addCurrentUpdatePoint,
  }) {
    return addCurrentUpdatePoint(latLngExtra);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? clearWaypointEvent,
    TResult? Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult? Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult? Function(HistoryRouteFailure failure)? failureEvent,
    TResult? Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
  }) {
    return addCurrentUpdatePoint?.call(latLngExtra);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? clearWaypointEvent,
    TResult Function(String vehicleId, String from, String to,
            bool isOverSpeedByRoad, LatLng currentWaypoint)?
        getWaypoint,
    TResult Function(HistoryRouteResponse historyRouteResponse,
            String vehicleId, LatLng currentWaypoint)?
        getWaypointSuccess,
    TResult Function(HistoryRouteFailure failure)? failureEvent,
    TResult Function(List<LatLng> latLngExtra)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) {
    if (addCurrentUpdatePoint != null) {
      return addCurrentUpdatePoint(latLngExtra);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ClearWaypointEvent value) clearWaypointEvent,
    required TResult Function(_GetWaypoint value) getWaypoint,
    required TResult Function(_GetWaypointSuccess value) getWaypointSuccess,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_AddCurrentUpdatePoint value)
        addCurrentUpdatePoint,
  }) {
    return addCurrentUpdatePoint(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult? Function(_GetWaypoint value)? getWaypoint,
    TResult? Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
  }) {
    return addCurrentUpdatePoint?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ClearWaypointEvent value)? clearWaypointEvent,
    TResult Function(_GetWaypoint value)? getWaypoint,
    TResult Function(_GetWaypointSuccess value)? getWaypointSuccess,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_AddCurrentUpdatePoint value)? addCurrentUpdatePoint,
    required TResult orElse(),
  }) {
    if (addCurrentUpdatePoint != null) {
      return addCurrentUpdatePoint(this);
    }
    return orElse();
  }
}

abstract class _AddCurrentUpdatePoint implements QuickHistoryWaypointEvent {
  const factory _AddCurrentUpdatePoint(
      {required final List<LatLng> latLngExtra}) = _$AddCurrentUpdatePointImpl;

  List<LatLng> get latLngExtra;
  @JsonKey(ignore: true)
  _$$AddCurrentUpdatePointImplCopyWith<_$AddCurrentUpdatePointImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$QuickHistoryWaypointState {
  bool get isLoading => throw _privateConstructorUsedError;
  HistoryRouteResponse? get historyRouteResponse =>
      throw _privateConstructorUsedError;
  List<LatLng> get listLatLngForMap => throw _privateConstructorUsedError;
  HistoryRouteFailure? get failure => throw _privateConstructorUsedError;
  String? get vehicleId => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $QuickHistoryWaypointStateCopyWith<QuickHistoryWaypointState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuickHistoryWaypointStateCopyWith<$Res> {
  factory $QuickHistoryWaypointStateCopyWith(QuickHistoryWaypointState value,
          $Res Function(QuickHistoryWaypointState) then) =
      _$QuickHistoryWaypointStateCopyWithImpl<$Res, QuickHistoryWaypointState>;
  @useResult
  $Res call(
      {bool isLoading,
      HistoryRouteResponse? historyRouteResponse,
      List<LatLng> listLatLngForMap,
      HistoryRouteFailure? failure,
      String? vehicleId});

  $HistoryRouteResponseCopyWith<$Res>? get historyRouteResponse;
  $HistoryRouteFailureCopyWith<$Res>? get failure;
}

/// @nodoc
class _$QuickHistoryWaypointStateCopyWithImpl<$Res,
        $Val extends QuickHistoryWaypointState>
    implements $QuickHistoryWaypointStateCopyWith<$Res> {
  _$QuickHistoryWaypointStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? historyRouteResponse = freezed,
    Object? listLatLngForMap = null,
    Object? failure = freezed,
    Object? vehicleId = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      historyRouteResponse: freezed == historyRouteResponse
          ? _value.historyRouteResponse
          : historyRouteResponse // ignore: cast_nullable_to_non_nullable
              as HistoryRouteResponse?,
      listLatLngForMap: null == listLatLngForMap
          ? _value.listLatLngForMap
          : listLatLngForMap // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
      failure: freezed == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as HistoryRouteFailure?,
      vehicleId: freezed == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $HistoryRouteResponseCopyWith<$Res>? get historyRouteResponse {
    if (_value.historyRouteResponse == null) {
      return null;
    }

    return $HistoryRouteResponseCopyWith<$Res>(_value.historyRouteResponse!,
        (value) {
      return _then(_value.copyWith(historyRouteResponse: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $HistoryRouteFailureCopyWith<$Res>? get failure {
    if (_value.failure == null) {
      return null;
    }

    return $HistoryRouteFailureCopyWith<$Res>(_value.failure!, (value) {
      return _then(_value.copyWith(failure: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$QuickHistoryWaypointStateImplCopyWith<$Res>
    implements $QuickHistoryWaypointStateCopyWith<$Res> {
  factory _$$QuickHistoryWaypointStateImplCopyWith(
          _$QuickHistoryWaypointStateImpl value,
          $Res Function(_$QuickHistoryWaypointStateImpl) then) =
      __$$QuickHistoryWaypointStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      HistoryRouteResponse? historyRouteResponse,
      List<LatLng> listLatLngForMap,
      HistoryRouteFailure? failure,
      String? vehicleId});

  @override
  $HistoryRouteResponseCopyWith<$Res>? get historyRouteResponse;
  @override
  $HistoryRouteFailureCopyWith<$Res>? get failure;
}

/// @nodoc
class __$$QuickHistoryWaypointStateImplCopyWithImpl<$Res>
    extends _$QuickHistoryWaypointStateCopyWithImpl<$Res,
        _$QuickHistoryWaypointStateImpl>
    implements _$$QuickHistoryWaypointStateImplCopyWith<$Res> {
  __$$QuickHistoryWaypointStateImplCopyWithImpl(
      _$QuickHistoryWaypointStateImpl _value,
      $Res Function(_$QuickHistoryWaypointStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? historyRouteResponse = freezed,
    Object? listLatLngForMap = null,
    Object? failure = freezed,
    Object? vehicleId = freezed,
  }) {
    return _then(_$QuickHistoryWaypointStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      historyRouteResponse: freezed == historyRouteResponse
          ? _value.historyRouteResponse
          : historyRouteResponse // ignore: cast_nullable_to_non_nullable
              as HistoryRouteResponse?,
      listLatLngForMap: null == listLatLngForMap
          ? _value._listLatLngForMap
          : listLatLngForMap // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
      failure: freezed == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as HistoryRouteFailure?,
      vehicleId: freezed == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$QuickHistoryWaypointStateImpl implements _QuickHistoryWaypointState {
  const _$QuickHistoryWaypointStateImpl(
      {required this.isLoading,
      required this.historyRouteResponse,
      required final List<LatLng> listLatLngForMap,
      required this.failure,
      required this.vehicleId})
      : _listLatLngForMap = listLatLngForMap;

  @override
  final bool isLoading;
  @override
  final HistoryRouteResponse? historyRouteResponse;
  final List<LatLng> _listLatLngForMap;
  @override
  List<LatLng> get listLatLngForMap {
    if (_listLatLngForMap is EqualUnmodifiableListView)
      return _listLatLngForMap;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listLatLngForMap);
  }

  @override
  final HistoryRouteFailure? failure;
  @override
  final String? vehicleId;

  @override
  String toString() {
    return 'QuickHistoryWaypointState(isLoading: $isLoading, historyRouteResponse: $historyRouteResponse, listLatLngForMap: $listLatLngForMap, failure: $failure, vehicleId: $vehicleId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuickHistoryWaypointStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.historyRouteResponse, historyRouteResponse) ||
                other.historyRouteResponse == historyRouteResponse) &&
            const DeepCollectionEquality()
                .equals(other._listLatLngForMap, _listLatLngForMap) &&
            (identical(other.failure, failure) || other.failure == failure) &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      historyRouteResponse,
      const DeepCollectionEquality().hash(_listLatLngForMap),
      failure,
      vehicleId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuickHistoryWaypointStateImplCopyWith<_$QuickHistoryWaypointStateImpl>
      get copyWith => __$$QuickHistoryWaypointStateImplCopyWithImpl<
          _$QuickHistoryWaypointStateImpl>(this, _$identity);
}

abstract class _QuickHistoryWaypointState implements QuickHistoryWaypointState {
  const factory _QuickHistoryWaypointState(
      {required final bool isLoading,
      required final HistoryRouteResponse? historyRouteResponse,
      required final List<LatLng> listLatLngForMap,
      required final HistoryRouteFailure? failure,
      required final String? vehicleId}) = _$QuickHistoryWaypointStateImpl;

  @override
  bool get isLoading;
  @override
  HistoryRouteResponse? get historyRouteResponse;
  @override
  List<LatLng> get listLatLngForMap;
  @override
  HistoryRouteFailure? get failure;
  @override
  String? get vehicleId;
  @override
  @JsonKey(ignore: true)
  _$$QuickHistoryWaypointStateImplCopyWith<_$QuickHistoryWaypointStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
