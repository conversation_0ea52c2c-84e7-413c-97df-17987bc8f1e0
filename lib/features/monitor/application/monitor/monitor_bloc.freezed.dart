// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'monitor_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MonitorEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListGroupAndStation,
    required TResult Function() getLiveStreamSetting,
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListVehicle,
    required TResult Function() stopGetVehicleList,
    required TResult Function(int countDownGetVehicleList,
            bool isOverSpeedByRoad, List<String> listFilterVehicleId)
        updateTimerGetVehicleList,
    required TResult Function(Vehicle? selectedVehicle) selectedVehicle,
    required TResult Function() resetBloc,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult? Function()? getLiveStreamSetting,
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult? Function()? stopGetVehicleList,
    TResult? Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult? Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult? Function()? resetBloc,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult Function()? getLiveStreamSetting,
    TResult Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult Function()? stopGetVehicleList,
    TResult Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult Function()? resetBloc,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetShortVehicleResponse value)
        getListGroupAndStation,
    required TResult Function(_GetLiveStreamSetting value) getLiveStreamSetting,
    required TResult Function(_GetListVehicle value) getListVehicle,
    required TResult Function(_StopGetVehicleList value) stopGetVehicleList,
    required TResult Function(_UpdateTimerGetVehicleList value)
        updateTimerGetVehicleList,
    required TResult Function(_UpdateSelectedVehicle value) selectedVehicle,
    required TResult Function(_ResetBloc value) resetBloc,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult? Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult? Function(_GetListVehicle value)? getListVehicle,
    TResult? Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult? Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult? Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult? Function(_ResetBloc value)? resetBloc,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult Function(_GetListVehicle value)? getListVehicle,
    TResult Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult Function(_ResetBloc value)? resetBloc,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MonitorEventCopyWith<$Res> {
  factory $MonitorEventCopyWith(
          MonitorEvent value, $Res Function(MonitorEvent) then) =
      _$MonitorEventCopyWithImpl<$Res, MonitorEvent>;
}

/// @nodoc
class _$MonitorEventCopyWithImpl<$Res, $Val extends MonitorEvent>
    implements $MonitorEventCopyWith<$Res> {
  _$MonitorEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$GetShortVehicleResponseImplCopyWith<$Res> {
  factory _$$GetShortVehicleResponseImplCopyWith(
          _$GetShortVehicleResponseImpl value,
          $Res Function(_$GetShortVehicleResponseImpl) then) =
      __$$GetShortVehicleResponseImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> ids, bool isOverSpeedByRoad});
}

/// @nodoc
class __$$GetShortVehicleResponseImplCopyWithImpl<$Res>
    extends _$MonitorEventCopyWithImpl<$Res, _$GetShortVehicleResponseImpl>
    implements _$$GetShortVehicleResponseImplCopyWith<$Res> {
  __$$GetShortVehicleResponseImplCopyWithImpl(
      _$GetShortVehicleResponseImpl _value,
      $Res Function(_$GetShortVehicleResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ids = null,
    Object? isOverSpeedByRoad = null,
  }) {
    return _then(_$GetShortVehicleResponseImpl(
      ids: null == ids
          ? _value._ids
          : ids // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$GetShortVehicleResponseImpl implements _GetShortVehicleResponse {
  const _$GetShortVehicleResponseImpl(
      {required final List<String> ids, required this.isOverSpeedByRoad})
      : _ids = ids;

  final List<String> _ids;
  @override
  List<String> get ids {
    if (_ids is EqualUnmodifiableListView) return _ids;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_ids);
  }

  @override
  final bool isOverSpeedByRoad;

  @override
  String toString() {
    return 'MonitorEvent.getListGroupAndStation(ids: $ids, isOverSpeedByRoad: $isOverSpeedByRoad)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetShortVehicleResponseImpl &&
            const DeepCollectionEquality().equals(other._ids, _ids) &&
            (identical(other.isOverSpeedByRoad, isOverSpeedByRoad) ||
                other.isOverSpeedByRoad == isOverSpeedByRoad));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_ids), isOverSpeedByRoad);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetShortVehicleResponseImplCopyWith<_$GetShortVehicleResponseImpl>
      get copyWith => __$$GetShortVehicleResponseImplCopyWithImpl<
          _$GetShortVehicleResponseImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListGroupAndStation,
    required TResult Function() getLiveStreamSetting,
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListVehicle,
    required TResult Function() stopGetVehicleList,
    required TResult Function(int countDownGetVehicleList,
            bool isOverSpeedByRoad, List<String> listFilterVehicleId)
        updateTimerGetVehicleList,
    required TResult Function(Vehicle? selectedVehicle) selectedVehicle,
    required TResult Function() resetBloc,
  }) {
    return getListGroupAndStation(ids, isOverSpeedByRoad);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult? Function()? getLiveStreamSetting,
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult? Function()? stopGetVehicleList,
    TResult? Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult? Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult? Function()? resetBloc,
  }) {
    return getListGroupAndStation?.call(ids, isOverSpeedByRoad);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult Function()? getLiveStreamSetting,
    TResult Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult Function()? stopGetVehicleList,
    TResult Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult Function()? resetBloc,
    required TResult orElse(),
  }) {
    if (getListGroupAndStation != null) {
      return getListGroupAndStation(ids, isOverSpeedByRoad);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetShortVehicleResponse value)
        getListGroupAndStation,
    required TResult Function(_GetLiveStreamSetting value) getLiveStreamSetting,
    required TResult Function(_GetListVehicle value) getListVehicle,
    required TResult Function(_StopGetVehicleList value) stopGetVehicleList,
    required TResult Function(_UpdateTimerGetVehicleList value)
        updateTimerGetVehicleList,
    required TResult Function(_UpdateSelectedVehicle value) selectedVehicle,
    required TResult Function(_ResetBloc value) resetBloc,
  }) {
    return getListGroupAndStation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult? Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult? Function(_GetListVehicle value)? getListVehicle,
    TResult? Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult? Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult? Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult? Function(_ResetBloc value)? resetBloc,
  }) {
    return getListGroupAndStation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult Function(_GetListVehicle value)? getListVehicle,
    TResult Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult Function(_ResetBloc value)? resetBloc,
    required TResult orElse(),
  }) {
    if (getListGroupAndStation != null) {
      return getListGroupAndStation(this);
    }
    return orElse();
  }
}

abstract class _GetShortVehicleResponse implements MonitorEvent {
  const factory _GetShortVehicleResponse(
      {required final List<String> ids,
      required final bool isOverSpeedByRoad}) = _$GetShortVehicleResponseImpl;

  List<String> get ids;
  bool get isOverSpeedByRoad;
  @JsonKey(ignore: true)
  _$$GetShortVehicleResponseImplCopyWith<_$GetShortVehicleResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetLiveStreamSettingImplCopyWith<$Res> {
  factory _$$GetLiveStreamSettingImplCopyWith(_$GetLiveStreamSettingImpl value,
          $Res Function(_$GetLiveStreamSettingImpl) then) =
      __$$GetLiveStreamSettingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetLiveStreamSettingImplCopyWithImpl<$Res>
    extends _$MonitorEventCopyWithImpl<$Res, _$GetLiveStreamSettingImpl>
    implements _$$GetLiveStreamSettingImplCopyWith<$Res> {
  __$$GetLiveStreamSettingImplCopyWithImpl(_$GetLiveStreamSettingImpl _value,
      $Res Function(_$GetLiveStreamSettingImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$GetLiveStreamSettingImpl implements _GetLiveStreamSetting {
  const _$GetLiveStreamSettingImpl();

  @override
  String toString() {
    return 'MonitorEvent.getLiveStreamSetting()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetLiveStreamSettingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListGroupAndStation,
    required TResult Function() getLiveStreamSetting,
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListVehicle,
    required TResult Function() stopGetVehicleList,
    required TResult Function(int countDownGetVehicleList,
            bool isOverSpeedByRoad, List<String> listFilterVehicleId)
        updateTimerGetVehicleList,
    required TResult Function(Vehicle? selectedVehicle) selectedVehicle,
    required TResult Function() resetBloc,
  }) {
    return getLiveStreamSetting();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult? Function()? getLiveStreamSetting,
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult? Function()? stopGetVehicleList,
    TResult? Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult? Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult? Function()? resetBloc,
  }) {
    return getLiveStreamSetting?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult Function()? getLiveStreamSetting,
    TResult Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult Function()? stopGetVehicleList,
    TResult Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult Function()? resetBloc,
    required TResult orElse(),
  }) {
    if (getLiveStreamSetting != null) {
      return getLiveStreamSetting();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetShortVehicleResponse value)
        getListGroupAndStation,
    required TResult Function(_GetLiveStreamSetting value) getLiveStreamSetting,
    required TResult Function(_GetListVehicle value) getListVehicle,
    required TResult Function(_StopGetVehicleList value) stopGetVehicleList,
    required TResult Function(_UpdateTimerGetVehicleList value)
        updateTimerGetVehicleList,
    required TResult Function(_UpdateSelectedVehicle value) selectedVehicle,
    required TResult Function(_ResetBloc value) resetBloc,
  }) {
    return getLiveStreamSetting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult? Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult? Function(_GetListVehicle value)? getListVehicle,
    TResult? Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult? Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult? Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult? Function(_ResetBloc value)? resetBloc,
  }) {
    return getLiveStreamSetting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult Function(_GetListVehicle value)? getListVehicle,
    TResult Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult Function(_ResetBloc value)? resetBloc,
    required TResult orElse(),
  }) {
    if (getLiveStreamSetting != null) {
      return getLiveStreamSetting(this);
    }
    return orElse();
  }
}

abstract class _GetLiveStreamSetting implements MonitorEvent {
  const factory _GetLiveStreamSetting() = _$GetLiveStreamSettingImpl;
}

/// @nodoc
abstract class _$$GetListVehicleImplCopyWith<$Res> {
  factory _$$GetListVehicleImplCopyWith(_$GetListVehicleImpl value,
          $Res Function(_$GetListVehicleImpl) then) =
      __$$GetListVehicleImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> ids, bool isOverSpeedByRoad});
}

/// @nodoc
class __$$GetListVehicleImplCopyWithImpl<$Res>
    extends _$MonitorEventCopyWithImpl<$Res, _$GetListVehicleImpl>
    implements _$$GetListVehicleImplCopyWith<$Res> {
  __$$GetListVehicleImplCopyWithImpl(
      _$GetListVehicleImpl _value, $Res Function(_$GetListVehicleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ids = null,
    Object? isOverSpeedByRoad = null,
  }) {
    return _then(_$GetListVehicleImpl(
      ids: null == ids
          ? _value._ids
          : ids // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$GetListVehicleImpl implements _GetListVehicle {
  const _$GetListVehicleImpl(
      {required final List<String> ids, required this.isOverSpeedByRoad})
      : _ids = ids;

  final List<String> _ids;
  @override
  List<String> get ids {
    if (_ids is EqualUnmodifiableListView) return _ids;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_ids);
  }

  @override
  final bool isOverSpeedByRoad;

  @override
  String toString() {
    return 'MonitorEvent.getListVehicle(ids: $ids, isOverSpeedByRoad: $isOverSpeedByRoad)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetListVehicleImpl &&
            const DeepCollectionEquality().equals(other._ids, _ids) &&
            (identical(other.isOverSpeedByRoad, isOverSpeedByRoad) ||
                other.isOverSpeedByRoad == isOverSpeedByRoad));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_ids), isOverSpeedByRoad);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetListVehicleImplCopyWith<_$GetListVehicleImpl> get copyWith =>
      __$$GetListVehicleImplCopyWithImpl<_$GetListVehicleImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListGroupAndStation,
    required TResult Function() getLiveStreamSetting,
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListVehicle,
    required TResult Function() stopGetVehicleList,
    required TResult Function(int countDownGetVehicleList,
            bool isOverSpeedByRoad, List<String> listFilterVehicleId)
        updateTimerGetVehicleList,
    required TResult Function(Vehicle? selectedVehicle) selectedVehicle,
    required TResult Function() resetBloc,
  }) {
    return getListVehicle(ids, isOverSpeedByRoad);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult? Function()? getLiveStreamSetting,
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult? Function()? stopGetVehicleList,
    TResult? Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult? Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult? Function()? resetBloc,
  }) {
    return getListVehicle?.call(ids, isOverSpeedByRoad);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult Function()? getLiveStreamSetting,
    TResult Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult Function()? stopGetVehicleList,
    TResult Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult Function()? resetBloc,
    required TResult orElse(),
  }) {
    if (getListVehicle != null) {
      return getListVehicle(ids, isOverSpeedByRoad);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetShortVehicleResponse value)
        getListGroupAndStation,
    required TResult Function(_GetLiveStreamSetting value) getLiveStreamSetting,
    required TResult Function(_GetListVehicle value) getListVehicle,
    required TResult Function(_StopGetVehicleList value) stopGetVehicleList,
    required TResult Function(_UpdateTimerGetVehicleList value)
        updateTimerGetVehicleList,
    required TResult Function(_UpdateSelectedVehicle value) selectedVehicle,
    required TResult Function(_ResetBloc value) resetBloc,
  }) {
    return getListVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult? Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult? Function(_GetListVehicle value)? getListVehicle,
    TResult? Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult? Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult? Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult? Function(_ResetBloc value)? resetBloc,
  }) {
    return getListVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult Function(_GetListVehicle value)? getListVehicle,
    TResult Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult Function(_ResetBloc value)? resetBloc,
    required TResult orElse(),
  }) {
    if (getListVehicle != null) {
      return getListVehicle(this);
    }
    return orElse();
  }
}

abstract class _GetListVehicle implements MonitorEvent {
  const factory _GetListVehicle(
      {required final List<String> ids,
      required final bool isOverSpeedByRoad}) = _$GetListVehicleImpl;

  List<String> get ids;
  bool get isOverSpeedByRoad;
  @JsonKey(ignore: true)
  _$$GetListVehicleImplCopyWith<_$GetListVehicleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StopGetVehicleListImplCopyWith<$Res> {
  factory _$$StopGetVehicleListImplCopyWith(_$StopGetVehicleListImpl value,
          $Res Function(_$StopGetVehicleListImpl) then) =
      __$$StopGetVehicleListImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StopGetVehicleListImplCopyWithImpl<$Res>
    extends _$MonitorEventCopyWithImpl<$Res, _$StopGetVehicleListImpl>
    implements _$$StopGetVehicleListImplCopyWith<$Res> {
  __$$StopGetVehicleListImplCopyWithImpl(_$StopGetVehicleListImpl _value,
      $Res Function(_$StopGetVehicleListImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$StopGetVehicleListImpl implements _StopGetVehicleList {
  const _$StopGetVehicleListImpl();

  @override
  String toString() {
    return 'MonitorEvent.stopGetVehicleList()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StopGetVehicleListImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListGroupAndStation,
    required TResult Function() getLiveStreamSetting,
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListVehicle,
    required TResult Function() stopGetVehicleList,
    required TResult Function(int countDownGetVehicleList,
            bool isOverSpeedByRoad, List<String> listFilterVehicleId)
        updateTimerGetVehicleList,
    required TResult Function(Vehicle? selectedVehicle) selectedVehicle,
    required TResult Function() resetBloc,
  }) {
    return stopGetVehicleList();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult? Function()? getLiveStreamSetting,
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult? Function()? stopGetVehicleList,
    TResult? Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult? Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult? Function()? resetBloc,
  }) {
    return stopGetVehicleList?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult Function()? getLiveStreamSetting,
    TResult Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult Function()? stopGetVehicleList,
    TResult Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult Function()? resetBloc,
    required TResult orElse(),
  }) {
    if (stopGetVehicleList != null) {
      return stopGetVehicleList();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetShortVehicleResponse value)
        getListGroupAndStation,
    required TResult Function(_GetLiveStreamSetting value) getLiveStreamSetting,
    required TResult Function(_GetListVehicle value) getListVehicle,
    required TResult Function(_StopGetVehicleList value) stopGetVehicleList,
    required TResult Function(_UpdateTimerGetVehicleList value)
        updateTimerGetVehicleList,
    required TResult Function(_UpdateSelectedVehicle value) selectedVehicle,
    required TResult Function(_ResetBloc value) resetBloc,
  }) {
    return stopGetVehicleList(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult? Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult? Function(_GetListVehicle value)? getListVehicle,
    TResult? Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult? Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult? Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult? Function(_ResetBloc value)? resetBloc,
  }) {
    return stopGetVehicleList?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult Function(_GetListVehicle value)? getListVehicle,
    TResult Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult Function(_ResetBloc value)? resetBloc,
    required TResult orElse(),
  }) {
    if (stopGetVehicleList != null) {
      return stopGetVehicleList(this);
    }
    return orElse();
  }
}

abstract class _StopGetVehicleList implements MonitorEvent {
  const factory _StopGetVehicleList() = _$StopGetVehicleListImpl;
}

/// @nodoc
abstract class _$$UpdateTimerGetVehicleListImplCopyWith<$Res> {
  factory _$$UpdateTimerGetVehicleListImplCopyWith(
          _$UpdateTimerGetVehicleListImpl value,
          $Res Function(_$UpdateTimerGetVehicleListImpl) then) =
      __$$UpdateTimerGetVehicleListImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {int countDownGetVehicleList,
      bool isOverSpeedByRoad,
      List<String> listFilterVehicleId});
}

/// @nodoc
class __$$UpdateTimerGetVehicleListImplCopyWithImpl<$Res>
    extends _$MonitorEventCopyWithImpl<$Res, _$UpdateTimerGetVehicleListImpl>
    implements _$$UpdateTimerGetVehicleListImplCopyWith<$Res> {
  __$$UpdateTimerGetVehicleListImplCopyWithImpl(
      _$UpdateTimerGetVehicleListImpl _value,
      $Res Function(_$UpdateTimerGetVehicleListImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countDownGetVehicleList = null,
    Object? isOverSpeedByRoad = null,
    Object? listFilterVehicleId = null,
  }) {
    return _then(_$UpdateTimerGetVehicleListImpl(
      countDownGetVehicleList: null == countDownGetVehicleList
          ? _value.countDownGetVehicleList
          : countDownGetVehicleList // ignore: cast_nullable_to_non_nullable
              as int,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
      listFilterVehicleId: null == listFilterVehicleId
          ? _value._listFilterVehicleId
          : listFilterVehicleId // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _$UpdateTimerGetVehicleListImpl implements _UpdateTimerGetVehicleList {
  const _$UpdateTimerGetVehicleListImpl(
      {required this.countDownGetVehicleList,
      required this.isOverSpeedByRoad,
      required final List<String> listFilterVehicleId})
      : _listFilterVehicleId = listFilterVehicleId;

  @override
  final int countDownGetVehicleList;
  @override
  final bool isOverSpeedByRoad;
  final List<String> _listFilterVehicleId;
  @override
  List<String> get listFilterVehicleId {
    if (_listFilterVehicleId is EqualUnmodifiableListView)
      return _listFilterVehicleId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listFilterVehicleId);
  }

  @override
  String toString() {
    return 'MonitorEvent.updateTimerGetVehicleList(countDownGetVehicleList: $countDownGetVehicleList, isOverSpeedByRoad: $isOverSpeedByRoad, listFilterVehicleId: $listFilterVehicleId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateTimerGetVehicleListImpl &&
            (identical(
                    other.countDownGetVehicleList, countDownGetVehicleList) ||
                other.countDownGetVehicleList == countDownGetVehicleList) &&
            (identical(other.isOverSpeedByRoad, isOverSpeedByRoad) ||
                other.isOverSpeedByRoad == isOverSpeedByRoad) &&
            const DeepCollectionEquality()
                .equals(other._listFilterVehicleId, _listFilterVehicleId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      countDownGetVehicleList,
      isOverSpeedByRoad,
      const DeepCollectionEquality().hash(_listFilterVehicleId));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateTimerGetVehicleListImplCopyWith<_$UpdateTimerGetVehicleListImpl>
      get copyWith => __$$UpdateTimerGetVehicleListImplCopyWithImpl<
          _$UpdateTimerGetVehicleListImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListGroupAndStation,
    required TResult Function() getLiveStreamSetting,
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListVehicle,
    required TResult Function() stopGetVehicleList,
    required TResult Function(int countDownGetVehicleList,
            bool isOverSpeedByRoad, List<String> listFilterVehicleId)
        updateTimerGetVehicleList,
    required TResult Function(Vehicle? selectedVehicle) selectedVehicle,
    required TResult Function() resetBloc,
  }) {
    return updateTimerGetVehicleList(
        countDownGetVehicleList, isOverSpeedByRoad, listFilterVehicleId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult? Function()? getLiveStreamSetting,
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult? Function()? stopGetVehicleList,
    TResult? Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult? Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult? Function()? resetBloc,
  }) {
    return updateTimerGetVehicleList?.call(
        countDownGetVehicleList, isOverSpeedByRoad, listFilterVehicleId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult Function()? getLiveStreamSetting,
    TResult Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult Function()? stopGetVehicleList,
    TResult Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult Function()? resetBloc,
    required TResult orElse(),
  }) {
    if (updateTimerGetVehicleList != null) {
      return updateTimerGetVehicleList(
          countDownGetVehicleList, isOverSpeedByRoad, listFilterVehicleId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetShortVehicleResponse value)
        getListGroupAndStation,
    required TResult Function(_GetLiveStreamSetting value) getLiveStreamSetting,
    required TResult Function(_GetListVehicle value) getListVehicle,
    required TResult Function(_StopGetVehicleList value) stopGetVehicleList,
    required TResult Function(_UpdateTimerGetVehicleList value)
        updateTimerGetVehicleList,
    required TResult Function(_UpdateSelectedVehicle value) selectedVehicle,
    required TResult Function(_ResetBloc value) resetBloc,
  }) {
    return updateTimerGetVehicleList(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult? Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult? Function(_GetListVehicle value)? getListVehicle,
    TResult? Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult? Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult? Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult? Function(_ResetBloc value)? resetBloc,
  }) {
    return updateTimerGetVehicleList?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult Function(_GetListVehicle value)? getListVehicle,
    TResult Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult Function(_ResetBloc value)? resetBloc,
    required TResult orElse(),
  }) {
    if (updateTimerGetVehicleList != null) {
      return updateTimerGetVehicleList(this);
    }
    return orElse();
  }
}

abstract class _UpdateTimerGetVehicleList implements MonitorEvent {
  const factory _UpdateTimerGetVehicleList(
          {required final int countDownGetVehicleList,
          required final bool isOverSpeedByRoad,
          required final List<String> listFilterVehicleId}) =
      _$UpdateTimerGetVehicleListImpl;

  int get countDownGetVehicleList;
  bool get isOverSpeedByRoad;
  List<String> get listFilterVehicleId;
  @JsonKey(ignore: true)
  _$$UpdateTimerGetVehicleListImplCopyWith<_$UpdateTimerGetVehicleListImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateSelectedVehicleImplCopyWith<$Res> {
  factory _$$UpdateSelectedVehicleImplCopyWith(
          _$UpdateSelectedVehicleImpl value,
          $Res Function(_$UpdateSelectedVehicleImpl) then) =
      __$$UpdateSelectedVehicleImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Vehicle? selectedVehicle});

  $VehicleCopyWith<$Res>? get selectedVehicle;
}

/// @nodoc
class __$$UpdateSelectedVehicleImplCopyWithImpl<$Res>
    extends _$MonitorEventCopyWithImpl<$Res, _$UpdateSelectedVehicleImpl>
    implements _$$UpdateSelectedVehicleImplCopyWith<$Res> {
  __$$UpdateSelectedVehicleImplCopyWithImpl(_$UpdateSelectedVehicleImpl _value,
      $Res Function(_$UpdateSelectedVehicleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedVehicle = freezed,
  }) {
    return _then(_$UpdateSelectedVehicleImpl(
      selectedVehicle: freezed == selectedVehicle
          ? _value.selectedVehicle
          : selectedVehicle // ignore: cast_nullable_to_non_nullable
              as Vehicle?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleCopyWith<$Res>? get selectedVehicle {
    if (_value.selectedVehicle == null) {
      return null;
    }

    return $VehicleCopyWith<$Res>(_value.selectedVehicle!, (value) {
      return _then(_value.copyWith(selectedVehicle: value));
    });
  }
}

/// @nodoc

class _$UpdateSelectedVehicleImpl implements _UpdateSelectedVehicle {
  const _$UpdateSelectedVehicleImpl({this.selectedVehicle});

  @override
  final Vehicle? selectedVehicle;

  @override
  String toString() {
    return 'MonitorEvent.selectedVehicle(selectedVehicle: $selectedVehicle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateSelectedVehicleImpl &&
            (identical(other.selectedVehicle, selectedVehicle) ||
                other.selectedVehicle == selectedVehicle));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedVehicle);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateSelectedVehicleImplCopyWith<_$UpdateSelectedVehicleImpl>
      get copyWith => __$$UpdateSelectedVehicleImplCopyWithImpl<
          _$UpdateSelectedVehicleImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListGroupAndStation,
    required TResult Function() getLiveStreamSetting,
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListVehicle,
    required TResult Function() stopGetVehicleList,
    required TResult Function(int countDownGetVehicleList,
            bool isOverSpeedByRoad, List<String> listFilterVehicleId)
        updateTimerGetVehicleList,
    required TResult Function(Vehicle? selectedVehicle) selectedVehicle,
    required TResult Function() resetBloc,
  }) {
    return selectedVehicle(this.selectedVehicle);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult? Function()? getLiveStreamSetting,
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult? Function()? stopGetVehicleList,
    TResult? Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult? Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult? Function()? resetBloc,
  }) {
    return selectedVehicle?.call(this.selectedVehicle);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult Function()? getLiveStreamSetting,
    TResult Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult Function()? stopGetVehicleList,
    TResult Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult Function()? resetBloc,
    required TResult orElse(),
  }) {
    if (selectedVehicle != null) {
      return selectedVehicle(this.selectedVehicle);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetShortVehicleResponse value)
        getListGroupAndStation,
    required TResult Function(_GetLiveStreamSetting value) getLiveStreamSetting,
    required TResult Function(_GetListVehicle value) getListVehicle,
    required TResult Function(_StopGetVehicleList value) stopGetVehicleList,
    required TResult Function(_UpdateTimerGetVehicleList value)
        updateTimerGetVehicleList,
    required TResult Function(_UpdateSelectedVehicle value) selectedVehicle,
    required TResult Function(_ResetBloc value) resetBloc,
  }) {
    return selectedVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult? Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult? Function(_GetListVehicle value)? getListVehicle,
    TResult? Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult? Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult? Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult? Function(_ResetBloc value)? resetBloc,
  }) {
    return selectedVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult Function(_GetListVehicle value)? getListVehicle,
    TResult Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult Function(_ResetBloc value)? resetBloc,
    required TResult orElse(),
  }) {
    if (selectedVehicle != null) {
      return selectedVehicle(this);
    }
    return orElse();
  }
}

abstract class _UpdateSelectedVehicle implements MonitorEvent {
  const factory _UpdateSelectedVehicle({final Vehicle? selectedVehicle}) =
      _$UpdateSelectedVehicleImpl;

  Vehicle? get selectedVehicle;
  @JsonKey(ignore: true)
  _$$UpdateSelectedVehicleImplCopyWith<_$UpdateSelectedVehicleImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetBlocImplCopyWith<$Res> {
  factory _$$ResetBlocImplCopyWith(
          _$ResetBlocImpl value, $Res Function(_$ResetBlocImpl) then) =
      __$$ResetBlocImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetBlocImplCopyWithImpl<$Res>
    extends _$MonitorEventCopyWithImpl<$Res, _$ResetBlocImpl>
    implements _$$ResetBlocImplCopyWith<$Res> {
  __$$ResetBlocImplCopyWithImpl(
      _$ResetBlocImpl _value, $Res Function(_$ResetBlocImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ResetBlocImpl implements _ResetBloc {
  const _$ResetBlocImpl();

  @override
  String toString() {
    return 'MonitorEvent.resetBloc()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetBlocImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListGroupAndStation,
    required TResult Function() getLiveStreamSetting,
    required TResult Function(List<String> ids, bool isOverSpeedByRoad)
        getListVehicle,
    required TResult Function() stopGetVehicleList,
    required TResult Function(int countDownGetVehicleList,
            bool isOverSpeedByRoad, List<String> listFilterVehicleId)
        updateTimerGetVehicleList,
    required TResult Function(Vehicle? selectedVehicle) selectedVehicle,
    required TResult Function() resetBloc,
  }) {
    return resetBloc();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult? Function()? getLiveStreamSetting,
    TResult? Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult? Function()? stopGetVehicleList,
    TResult? Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult? Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult? Function()? resetBloc,
  }) {
    return resetBloc?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> ids, bool isOverSpeedByRoad)?
        getListGroupAndStation,
    TResult Function()? getLiveStreamSetting,
    TResult Function(List<String> ids, bool isOverSpeedByRoad)? getListVehicle,
    TResult Function()? stopGetVehicleList,
    TResult Function(int countDownGetVehicleList, bool isOverSpeedByRoad,
            List<String> listFilterVehicleId)?
        updateTimerGetVehicleList,
    TResult Function(Vehicle? selectedVehicle)? selectedVehicle,
    TResult Function()? resetBloc,
    required TResult orElse(),
  }) {
    if (resetBloc != null) {
      return resetBloc();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetShortVehicleResponse value)
        getListGroupAndStation,
    required TResult Function(_GetLiveStreamSetting value) getLiveStreamSetting,
    required TResult Function(_GetListVehicle value) getListVehicle,
    required TResult Function(_StopGetVehicleList value) stopGetVehicleList,
    required TResult Function(_UpdateTimerGetVehicleList value)
        updateTimerGetVehicleList,
    required TResult Function(_UpdateSelectedVehicle value) selectedVehicle,
    required TResult Function(_ResetBloc value) resetBloc,
  }) {
    return resetBloc(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult? Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult? Function(_GetListVehicle value)? getListVehicle,
    TResult? Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult? Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult? Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult? Function(_ResetBloc value)? resetBloc,
  }) {
    return resetBloc?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetShortVehicleResponse value)? getListGroupAndStation,
    TResult Function(_GetLiveStreamSetting value)? getLiveStreamSetting,
    TResult Function(_GetListVehicle value)? getListVehicle,
    TResult Function(_StopGetVehicleList value)? stopGetVehicleList,
    TResult Function(_UpdateTimerGetVehicleList value)?
        updateTimerGetVehicleList,
    TResult Function(_UpdateSelectedVehicle value)? selectedVehicle,
    TResult Function(_ResetBloc value)? resetBloc,
    required TResult orElse(),
  }) {
    if (resetBloc != null) {
      return resetBloc(this);
    }
    return orElse();
  }
}

abstract class _ResetBloc implements MonitorEvent {
  const factory _ResetBloc() = _$ResetBlocImpl;
}

/// @nodoc
mixin _$MonitorState {
  MonitorVehicleResponse? get vehicleResponse =>
      throw _privateConstructorUsedError; // required Set<Marker> markers,
  List<Vehicle> get listVehicle => throw _privateConstructorUsedError;
  List<VehicleGroupDTO> get listVehicleGroupDTO =>
      throw _privateConstructorUsedError;
  List<VehicleWithSetting> get listVehicleSetting =>
      throw _privateConstructorUsedError; // Livestream
  List<Station> get listStation => throw _privateConstructorUsedError;
  Vehicle? get selectedVehicle => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MonitorStateCopyWith<MonitorState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MonitorStateCopyWith<$Res> {
  factory $MonitorStateCopyWith(
          MonitorState value, $Res Function(MonitorState) then) =
      _$MonitorStateCopyWithImpl<$Res, MonitorState>;
  @useResult
  $Res call(
      {MonitorVehicleResponse? vehicleResponse,
      List<Vehicle> listVehicle,
      List<VehicleGroupDTO> listVehicleGroupDTO,
      List<VehicleWithSetting> listVehicleSetting,
      List<Station> listStation,
      Vehicle? selectedVehicle});

  $MonitorVehicleResponseCopyWith<$Res>? get vehicleResponse;
  $VehicleCopyWith<$Res>? get selectedVehicle;
}

/// @nodoc
class _$MonitorStateCopyWithImpl<$Res, $Val extends MonitorState>
    implements $MonitorStateCopyWith<$Res> {
  _$MonitorStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleResponse = freezed,
    Object? listVehicle = null,
    Object? listVehicleGroupDTO = null,
    Object? listVehicleSetting = null,
    Object? listStation = null,
    Object? selectedVehicle = freezed,
  }) {
    return _then(_value.copyWith(
      vehicleResponse: freezed == vehicleResponse
          ? _value.vehicleResponse
          : vehicleResponse // ignore: cast_nullable_to_non_nullable
              as MonitorVehicleResponse?,
      listVehicle: null == listVehicle
          ? _value.listVehicle
          : listVehicle // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      listVehicleGroupDTO: null == listVehicleGroupDTO
          ? _value.listVehicleGroupDTO
          : listVehicleGroupDTO // ignore: cast_nullable_to_non_nullable
              as List<VehicleGroupDTO>,
      listVehicleSetting: null == listVehicleSetting
          ? _value.listVehicleSetting
          : listVehicleSetting // ignore: cast_nullable_to_non_nullable
              as List<VehicleWithSetting>,
      listStation: null == listStation
          ? _value.listStation
          : listStation // ignore: cast_nullable_to_non_nullable
              as List<Station>,
      selectedVehicle: freezed == selectedVehicle
          ? _value.selectedVehicle
          : selectedVehicle // ignore: cast_nullable_to_non_nullable
              as Vehicle?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MonitorVehicleResponseCopyWith<$Res>? get vehicleResponse {
    if (_value.vehicleResponse == null) {
      return null;
    }

    return $MonitorVehicleResponseCopyWith<$Res>(_value.vehicleResponse!,
        (value) {
      return _then(_value.copyWith(vehicleResponse: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleCopyWith<$Res>? get selectedVehicle {
    if (_value.selectedVehicle == null) {
      return null;
    }

    return $VehicleCopyWith<$Res>(_value.selectedVehicle!, (value) {
      return _then(_value.copyWith(selectedVehicle: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MonitorStateImplCopyWith<$Res>
    implements $MonitorStateCopyWith<$Res> {
  factory _$$MonitorStateImplCopyWith(
          _$MonitorStateImpl value, $Res Function(_$MonitorStateImpl) then) =
      __$$MonitorStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MonitorVehicleResponse? vehicleResponse,
      List<Vehicle> listVehicle,
      List<VehicleGroupDTO> listVehicleGroupDTO,
      List<VehicleWithSetting> listVehicleSetting,
      List<Station> listStation,
      Vehicle? selectedVehicle});

  @override
  $MonitorVehicleResponseCopyWith<$Res>? get vehicleResponse;
  @override
  $VehicleCopyWith<$Res>? get selectedVehicle;
}

/// @nodoc
class __$$MonitorStateImplCopyWithImpl<$Res>
    extends _$MonitorStateCopyWithImpl<$Res, _$MonitorStateImpl>
    implements _$$MonitorStateImplCopyWith<$Res> {
  __$$MonitorStateImplCopyWithImpl(
      _$MonitorStateImpl _value, $Res Function(_$MonitorStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleResponse = freezed,
    Object? listVehicle = null,
    Object? listVehicleGroupDTO = null,
    Object? listVehicleSetting = null,
    Object? listStation = null,
    Object? selectedVehicle = freezed,
  }) {
    return _then(_$MonitorStateImpl(
      vehicleResponse: freezed == vehicleResponse
          ? _value.vehicleResponse
          : vehicleResponse // ignore: cast_nullable_to_non_nullable
              as MonitorVehicleResponse?,
      listVehicle: null == listVehicle
          ? _value._listVehicle
          : listVehicle // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      listVehicleGroupDTO: null == listVehicleGroupDTO
          ? _value._listVehicleGroupDTO
          : listVehicleGroupDTO // ignore: cast_nullable_to_non_nullable
              as List<VehicleGroupDTO>,
      listVehicleSetting: null == listVehicleSetting
          ? _value._listVehicleSetting
          : listVehicleSetting // ignore: cast_nullable_to_non_nullable
              as List<VehicleWithSetting>,
      listStation: null == listStation
          ? _value._listStation
          : listStation // ignore: cast_nullable_to_non_nullable
              as List<Station>,
      selectedVehicle: freezed == selectedVehicle
          ? _value.selectedVehicle
          : selectedVehicle // ignore: cast_nullable_to_non_nullable
              as Vehicle?,
    ));
  }
}

/// @nodoc

class _$MonitorStateImpl implements _MonitorState {
  const _$MonitorStateImpl(
      {required this.vehicleResponse,
      required final List<Vehicle> listVehicle,
      required final List<VehicleGroupDTO> listVehicleGroupDTO,
      required final List<VehicleWithSetting> listVehicleSetting,
      required final List<Station> listStation,
      this.selectedVehicle})
      : _listVehicle = listVehicle,
        _listVehicleGroupDTO = listVehicleGroupDTO,
        _listVehicleSetting = listVehicleSetting,
        _listStation = listStation;

  @override
  final MonitorVehicleResponse? vehicleResponse;
// required Set<Marker> markers,
  final List<Vehicle> _listVehicle;
// required Set<Marker> markers,
  @override
  List<Vehicle> get listVehicle {
    if (_listVehicle is EqualUnmodifiableListView) return _listVehicle;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicle);
  }

  final List<VehicleGroupDTO> _listVehicleGroupDTO;
  @override
  List<VehicleGroupDTO> get listVehicleGroupDTO {
    if (_listVehicleGroupDTO is EqualUnmodifiableListView)
      return _listVehicleGroupDTO;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicleGroupDTO);
  }

  final List<VehicleWithSetting> _listVehicleSetting;
  @override
  List<VehicleWithSetting> get listVehicleSetting {
    if (_listVehicleSetting is EqualUnmodifiableListView)
      return _listVehicleSetting;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicleSetting);
  }

// Livestream
  final List<Station> _listStation;
// Livestream
  @override
  List<Station> get listStation {
    if (_listStation is EqualUnmodifiableListView) return _listStation;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listStation);
  }

  @override
  final Vehicle? selectedVehicle;

  @override
  String toString() {
    return 'MonitorState(vehicleResponse: $vehicleResponse, listVehicle: $listVehicle, listVehicleGroupDTO: $listVehicleGroupDTO, listVehicleSetting: $listVehicleSetting, listStation: $listStation, selectedVehicle: $selectedVehicle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonitorStateImpl &&
            (identical(other.vehicleResponse, vehicleResponse) ||
                other.vehicleResponse == vehicleResponse) &&
            const DeepCollectionEquality()
                .equals(other._listVehicle, _listVehicle) &&
            const DeepCollectionEquality()
                .equals(other._listVehicleGroupDTO, _listVehicleGroupDTO) &&
            const DeepCollectionEquality()
                .equals(other._listVehicleSetting, _listVehicleSetting) &&
            const DeepCollectionEquality()
                .equals(other._listStation, _listStation) &&
            (identical(other.selectedVehicle, selectedVehicle) ||
                other.selectedVehicle == selectedVehicle));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      vehicleResponse,
      const DeepCollectionEquality().hash(_listVehicle),
      const DeepCollectionEquality().hash(_listVehicleGroupDTO),
      const DeepCollectionEquality().hash(_listVehicleSetting),
      const DeepCollectionEquality().hash(_listStation),
      selectedVehicle);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MonitorStateImplCopyWith<_$MonitorStateImpl> get copyWith =>
      __$$MonitorStateImplCopyWithImpl<_$MonitorStateImpl>(this, _$identity);
}

abstract class _MonitorState implements MonitorState {
  const factory _MonitorState(
      {required final MonitorVehicleResponse? vehicleResponse,
      required final List<Vehicle> listVehicle,
      required final List<VehicleGroupDTO> listVehicleGroupDTO,
      required final List<VehicleWithSetting> listVehicleSetting,
      required final List<Station> listStation,
      final Vehicle? selectedVehicle}) = _$MonitorStateImpl;

  @override
  MonitorVehicleResponse? get vehicleResponse;
  @override // required Set<Marker> markers,
  List<Vehicle> get listVehicle;
  @override
  List<VehicleGroupDTO> get listVehicleGroupDTO;
  @override
  List<VehicleWithSetting> get listVehicleSetting;
  @override // Livestream
  List<Station> get listStation;
  @override
  Vehicle? get selectedVehicle;
  @override
  @JsonKey(ignore: true)
  _$$MonitorStateImplCopyWith<_$MonitorStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
