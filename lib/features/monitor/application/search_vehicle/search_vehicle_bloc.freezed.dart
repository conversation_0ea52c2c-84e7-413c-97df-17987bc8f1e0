// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_vehicle_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SearchVehicleEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(VehicleGroupColor? vehicleGroupColor)
        filterByVehicleColor,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function() resetFilters,
    required TResult Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)
        initialVehicle,
    required TResult Function(String searchText,
            VehicleGroupColor? vehicleGroupColor, List<Vehicle> allVehicles)
        updateListVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function()? resetFilters,
    TResult? Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult? Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function()? resetFilters,
    TResult Function(List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FilterByVehicleGroup value) filterByVehicleColor,
    required TResult Function(_SearchTextChanged value) searchLicensePlate,
    required TResult Function(_ResetFilters value) resetFilters,
    required TResult Function(_InitialVehicle value) initialVehicle,
    required TResult Function(_UpdateListVehicle value) updateListVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult? Function(_SearchTextChanged value)? searchLicensePlate,
    TResult? Function(_ResetFilters value)? resetFilters,
    TResult? Function(_InitialVehicle value)? initialVehicle,
    TResult? Function(_UpdateListVehicle value)? updateListVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult Function(_SearchTextChanged value)? searchLicensePlate,
    TResult Function(_ResetFilters value)? resetFilters,
    TResult Function(_InitialVehicle value)? initialVehicle,
    TResult Function(_UpdateListVehicle value)? updateListVehicle,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchVehicleEventCopyWith<$Res> {
  factory $SearchVehicleEventCopyWith(
          SearchVehicleEvent value, $Res Function(SearchVehicleEvent) then) =
      _$SearchVehicleEventCopyWithImpl<$Res, SearchVehicleEvent>;
}

/// @nodoc
class _$SearchVehicleEventCopyWithImpl<$Res, $Val extends SearchVehicleEvent>
    implements $SearchVehicleEventCopyWith<$Res> {
  _$SearchVehicleEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$FilterByVehicleGroupImplCopyWith<$Res> {
  factory _$$FilterByVehicleGroupImplCopyWith(_$FilterByVehicleGroupImpl value,
          $Res Function(_$FilterByVehicleGroupImpl) then) =
      __$$FilterByVehicleGroupImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VehicleGroupColor? vehicleGroupColor});
}

/// @nodoc
class __$$FilterByVehicleGroupImplCopyWithImpl<$Res>
    extends _$SearchVehicleEventCopyWithImpl<$Res, _$FilterByVehicleGroupImpl>
    implements _$$FilterByVehicleGroupImplCopyWith<$Res> {
  __$$FilterByVehicleGroupImplCopyWithImpl(_$FilterByVehicleGroupImpl _value,
      $Res Function(_$FilterByVehicleGroupImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleGroupColor = freezed,
  }) {
    return _then(_$FilterByVehicleGroupImpl(
      vehicleGroupColor: freezed == vehicleGroupColor
          ? _value.vehicleGroupColor
          : vehicleGroupColor // ignore: cast_nullable_to_non_nullable
              as VehicleGroupColor?,
    ));
  }
}

/// @nodoc

class _$FilterByVehicleGroupImpl implements _FilterByVehicleGroup {
  const _$FilterByVehicleGroupImpl({this.vehicleGroupColor});

  @override
  final VehicleGroupColor? vehicleGroupColor;

  @override
  String toString() {
    return 'SearchVehicleEvent.filterByVehicleColor(vehicleGroupColor: $vehicleGroupColor)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterByVehicleGroupImpl &&
            (identical(other.vehicleGroupColor, vehicleGroupColor) ||
                other.vehicleGroupColor == vehicleGroupColor));
  }

  @override
  int get hashCode => Object.hash(runtimeType, vehicleGroupColor);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterByVehicleGroupImplCopyWith<_$FilterByVehicleGroupImpl>
      get copyWith =>
          __$$FilterByVehicleGroupImplCopyWithImpl<_$FilterByVehicleGroupImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(VehicleGroupColor? vehicleGroupColor)
        filterByVehicleColor,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function() resetFilters,
    required TResult Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)
        initialVehicle,
    required TResult Function(String searchText,
            VehicleGroupColor? vehicleGroupColor, List<Vehicle> allVehicles)
        updateListVehicle,
  }) {
    return filterByVehicleColor(vehicleGroupColor);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function()? resetFilters,
    TResult? Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult? Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
  }) {
    return filterByVehicleColor?.call(vehicleGroupColor);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function()? resetFilters,
    TResult Function(List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
    required TResult orElse(),
  }) {
    if (filterByVehicleColor != null) {
      return filterByVehicleColor(vehicleGroupColor);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FilterByVehicleGroup value) filterByVehicleColor,
    required TResult Function(_SearchTextChanged value) searchLicensePlate,
    required TResult Function(_ResetFilters value) resetFilters,
    required TResult Function(_InitialVehicle value) initialVehicle,
    required TResult Function(_UpdateListVehicle value) updateListVehicle,
  }) {
    return filterByVehicleColor(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult? Function(_SearchTextChanged value)? searchLicensePlate,
    TResult? Function(_ResetFilters value)? resetFilters,
    TResult? Function(_InitialVehicle value)? initialVehicle,
    TResult? Function(_UpdateListVehicle value)? updateListVehicle,
  }) {
    return filterByVehicleColor?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult Function(_SearchTextChanged value)? searchLicensePlate,
    TResult Function(_ResetFilters value)? resetFilters,
    TResult Function(_InitialVehicle value)? initialVehicle,
    TResult Function(_UpdateListVehicle value)? updateListVehicle,
    required TResult orElse(),
  }) {
    if (filterByVehicleColor != null) {
      return filterByVehicleColor(this);
    }
    return orElse();
  }
}

abstract class _FilterByVehicleGroup implements SearchVehicleEvent {
  const factory _FilterByVehicleGroup(
          {final VehicleGroupColor? vehicleGroupColor}) =
      _$FilterByVehicleGroupImpl;

  VehicleGroupColor? get vehicleGroupColor;
  @JsonKey(ignore: true)
  _$$FilterByVehicleGroupImplCopyWith<_$FilterByVehicleGroupImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchTextChangedImplCopyWith<$Res> {
  factory _$$SearchTextChangedImplCopyWith(_$SearchTextChangedImpl value,
          $Res Function(_$SearchTextChangedImpl) then) =
      __$$SearchTextChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String searchText});
}

/// @nodoc
class __$$SearchTextChangedImplCopyWithImpl<$Res>
    extends _$SearchVehicleEventCopyWithImpl<$Res, _$SearchTextChangedImpl>
    implements _$$SearchTextChangedImplCopyWith<$Res> {
  __$$SearchTextChangedImplCopyWithImpl(_$SearchTextChangedImpl _value,
      $Res Function(_$SearchTextChangedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchText = null,
  }) {
    return _then(_$SearchTextChangedImpl(
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchTextChangedImpl implements _SearchTextChanged {
  const _$SearchTextChangedImpl({required this.searchText});

  @override
  final String searchText;

  @override
  String toString() {
    return 'SearchVehicleEvent.searchLicensePlate(searchText: $searchText)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchTextChangedImpl &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText));
  }

  @override
  int get hashCode => Object.hash(runtimeType, searchText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchTextChangedImplCopyWith<_$SearchTextChangedImpl> get copyWith =>
      __$$SearchTextChangedImplCopyWithImpl<_$SearchTextChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(VehicleGroupColor? vehicleGroupColor)
        filterByVehicleColor,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function() resetFilters,
    required TResult Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)
        initialVehicle,
    required TResult Function(String searchText,
            VehicleGroupColor? vehicleGroupColor, List<Vehicle> allVehicles)
        updateListVehicle,
  }) {
    return searchLicensePlate(searchText);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function()? resetFilters,
    TResult? Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult? Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
  }) {
    return searchLicensePlate?.call(searchText);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function()? resetFilters,
    TResult Function(List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
    required TResult orElse(),
  }) {
    if (searchLicensePlate != null) {
      return searchLicensePlate(searchText);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FilterByVehicleGroup value) filterByVehicleColor,
    required TResult Function(_SearchTextChanged value) searchLicensePlate,
    required TResult Function(_ResetFilters value) resetFilters,
    required TResult Function(_InitialVehicle value) initialVehicle,
    required TResult Function(_UpdateListVehicle value) updateListVehicle,
  }) {
    return searchLicensePlate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult? Function(_SearchTextChanged value)? searchLicensePlate,
    TResult? Function(_ResetFilters value)? resetFilters,
    TResult? Function(_InitialVehicle value)? initialVehicle,
    TResult? Function(_UpdateListVehicle value)? updateListVehicle,
  }) {
    return searchLicensePlate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult Function(_SearchTextChanged value)? searchLicensePlate,
    TResult Function(_ResetFilters value)? resetFilters,
    TResult Function(_InitialVehicle value)? initialVehicle,
    TResult Function(_UpdateListVehicle value)? updateListVehicle,
    required TResult orElse(),
  }) {
    if (searchLicensePlate != null) {
      return searchLicensePlate(this);
    }
    return orElse();
  }
}

abstract class _SearchTextChanged implements SearchVehicleEvent {
  const factory _SearchTextChanged({required final String searchText}) =
      _$SearchTextChangedImpl;

  String get searchText;
  @JsonKey(ignore: true)
  _$$SearchTextChangedImplCopyWith<_$SearchTextChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetFiltersImplCopyWith<$Res> {
  factory _$$ResetFiltersImplCopyWith(
          _$ResetFiltersImpl value, $Res Function(_$ResetFiltersImpl) then) =
      __$$ResetFiltersImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetFiltersImplCopyWithImpl<$Res>
    extends _$SearchVehicleEventCopyWithImpl<$Res, _$ResetFiltersImpl>
    implements _$$ResetFiltersImplCopyWith<$Res> {
  __$$ResetFiltersImplCopyWithImpl(
      _$ResetFiltersImpl _value, $Res Function(_$ResetFiltersImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ResetFiltersImpl implements _ResetFilters {
  const _$ResetFiltersImpl();

  @override
  String toString() {
    return 'SearchVehicleEvent.resetFilters()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetFiltersImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(VehicleGroupColor? vehicleGroupColor)
        filterByVehicleColor,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function() resetFilters,
    required TResult Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)
        initialVehicle,
    required TResult Function(String searchText,
            VehicleGroupColor? vehicleGroupColor, List<Vehicle> allVehicles)
        updateListVehicle,
  }) {
    return resetFilters();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function()? resetFilters,
    TResult? Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult? Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
  }) {
    return resetFilters?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function()? resetFilters,
    TResult Function(List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
    required TResult orElse(),
  }) {
    if (resetFilters != null) {
      return resetFilters();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FilterByVehicleGroup value) filterByVehicleColor,
    required TResult Function(_SearchTextChanged value) searchLicensePlate,
    required TResult Function(_ResetFilters value) resetFilters,
    required TResult Function(_InitialVehicle value) initialVehicle,
    required TResult Function(_UpdateListVehicle value) updateListVehicle,
  }) {
    return resetFilters(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult? Function(_SearchTextChanged value)? searchLicensePlate,
    TResult? Function(_ResetFilters value)? resetFilters,
    TResult? Function(_InitialVehicle value)? initialVehicle,
    TResult? Function(_UpdateListVehicle value)? updateListVehicle,
  }) {
    return resetFilters?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult Function(_SearchTextChanged value)? searchLicensePlate,
    TResult Function(_ResetFilters value)? resetFilters,
    TResult Function(_InitialVehicle value)? initialVehicle,
    TResult Function(_UpdateListVehicle value)? updateListVehicle,
    required TResult orElse(),
  }) {
    if (resetFilters != null) {
      return resetFilters(this);
    }
    return orElse();
  }
}

abstract class _ResetFilters implements SearchVehicleEvent {
  const factory _ResetFilters() = _$ResetFiltersImpl;
}

/// @nodoc
abstract class _$$InitialVehicleImplCopyWith<$Res> {
  factory _$$InitialVehicleImplCopyWith(_$InitialVehicleImpl value,
          $Res Function(_$InitialVehicleImpl) then) =
      __$$InitialVehicleImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Vehicle> filteredVehicles, List<Vehicle> allVehicles});
}

/// @nodoc
class __$$InitialVehicleImplCopyWithImpl<$Res>
    extends _$SearchVehicleEventCopyWithImpl<$Res, _$InitialVehicleImpl>
    implements _$$InitialVehicleImplCopyWith<$Res> {
  __$$InitialVehicleImplCopyWithImpl(
      _$InitialVehicleImpl _value, $Res Function(_$InitialVehicleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filteredVehicles = null,
    Object? allVehicles = null,
  }) {
    return _then(_$InitialVehicleImpl(
      filteredVehicles: null == filteredVehicles
          ? _value._filteredVehicles
          : filteredVehicles // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      allVehicles: null == allVehicles
          ? _value._allVehicles
          : allVehicles // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
    ));
  }
}

/// @nodoc

class _$InitialVehicleImpl implements _InitialVehicle {
  const _$InitialVehicleImpl(
      {required final List<Vehicle> filteredVehicles,
      required final List<Vehicle> allVehicles})
      : _filteredVehicles = filteredVehicles,
        _allVehicles = allVehicles;

  final List<Vehicle> _filteredVehicles;
  @override
  List<Vehicle> get filteredVehicles {
    if (_filteredVehicles is EqualUnmodifiableListView)
      return _filteredVehicles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredVehicles);
  }

  final List<Vehicle> _allVehicles;
  @override
  List<Vehicle> get allVehicles {
    if (_allVehicles is EqualUnmodifiableListView) return _allVehicles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allVehicles);
  }

  @override
  String toString() {
    return 'SearchVehicleEvent.initialVehicle(filteredVehicles: $filteredVehicles, allVehicles: $allVehicles)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InitialVehicleImpl &&
            const DeepCollectionEquality()
                .equals(other._filteredVehicles, _filteredVehicles) &&
            const DeepCollectionEquality()
                .equals(other._allVehicles, _allVehicles));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_filteredVehicles),
      const DeepCollectionEquality().hash(_allVehicles));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InitialVehicleImplCopyWith<_$InitialVehicleImpl> get copyWith =>
      __$$InitialVehicleImplCopyWithImpl<_$InitialVehicleImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(VehicleGroupColor? vehicleGroupColor)
        filterByVehicleColor,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function() resetFilters,
    required TResult Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)
        initialVehicle,
    required TResult Function(String searchText,
            VehicleGroupColor? vehicleGroupColor, List<Vehicle> allVehicles)
        updateListVehicle,
  }) {
    return initialVehicle(filteredVehicles, allVehicles);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function()? resetFilters,
    TResult? Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult? Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
  }) {
    return initialVehicle?.call(filteredVehicles, allVehicles);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function()? resetFilters,
    TResult Function(List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
    required TResult orElse(),
  }) {
    if (initialVehicle != null) {
      return initialVehicle(filteredVehicles, allVehicles);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FilterByVehicleGroup value) filterByVehicleColor,
    required TResult Function(_SearchTextChanged value) searchLicensePlate,
    required TResult Function(_ResetFilters value) resetFilters,
    required TResult Function(_InitialVehicle value) initialVehicle,
    required TResult Function(_UpdateListVehicle value) updateListVehicle,
  }) {
    return initialVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult? Function(_SearchTextChanged value)? searchLicensePlate,
    TResult? Function(_ResetFilters value)? resetFilters,
    TResult? Function(_InitialVehicle value)? initialVehicle,
    TResult? Function(_UpdateListVehicle value)? updateListVehicle,
  }) {
    return initialVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult Function(_SearchTextChanged value)? searchLicensePlate,
    TResult Function(_ResetFilters value)? resetFilters,
    TResult Function(_InitialVehicle value)? initialVehicle,
    TResult Function(_UpdateListVehicle value)? updateListVehicle,
    required TResult orElse(),
  }) {
    if (initialVehicle != null) {
      return initialVehicle(this);
    }
    return orElse();
  }
}

abstract class _InitialVehicle implements SearchVehicleEvent {
  const factory _InitialVehicle(
      {required final List<Vehicle> filteredVehicles,
      required final List<Vehicle> allVehicles}) = _$InitialVehicleImpl;

  List<Vehicle> get filteredVehicles;
  List<Vehicle> get allVehicles;
  @JsonKey(ignore: true)
  _$$InitialVehicleImplCopyWith<_$InitialVehicleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateListVehicleImplCopyWith<$Res> {
  factory _$$UpdateListVehicleImplCopyWith(_$UpdateListVehicleImpl value,
          $Res Function(_$UpdateListVehicleImpl) then) =
      __$$UpdateListVehicleImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String searchText,
      VehicleGroupColor? vehicleGroupColor,
      List<Vehicle> allVehicles});
}

/// @nodoc
class __$$UpdateListVehicleImplCopyWithImpl<$Res>
    extends _$SearchVehicleEventCopyWithImpl<$Res, _$UpdateListVehicleImpl>
    implements _$$UpdateListVehicleImplCopyWith<$Res> {
  __$$UpdateListVehicleImplCopyWithImpl(_$UpdateListVehicleImpl _value,
      $Res Function(_$UpdateListVehicleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchText = null,
    Object? vehicleGroupColor = freezed,
    Object? allVehicles = null,
  }) {
    return _then(_$UpdateListVehicleImpl(
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleGroupColor: freezed == vehicleGroupColor
          ? _value.vehicleGroupColor
          : vehicleGroupColor // ignore: cast_nullable_to_non_nullable
              as VehicleGroupColor?,
      allVehicles: null == allVehicles
          ? _value._allVehicles
          : allVehicles // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
    ));
  }
}

/// @nodoc

class _$UpdateListVehicleImpl implements _UpdateListVehicle {
  const _$UpdateListVehicleImpl(
      {required this.searchText,
      this.vehicleGroupColor,
      required final List<Vehicle> allVehicles})
      : _allVehicles = allVehicles;

  @override
  final String searchText;
  @override
  final VehicleGroupColor? vehicleGroupColor;
  final List<Vehicle> _allVehicles;
  @override
  List<Vehicle> get allVehicles {
    if (_allVehicles is EqualUnmodifiableListView) return _allVehicles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allVehicles);
  }

  @override
  String toString() {
    return 'SearchVehicleEvent.updateListVehicle(searchText: $searchText, vehicleGroupColor: $vehicleGroupColor, allVehicles: $allVehicles)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateListVehicleImpl &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText) &&
            (identical(other.vehicleGroupColor, vehicleGroupColor) ||
                other.vehicleGroupColor == vehicleGroupColor) &&
            const DeepCollectionEquality()
                .equals(other._allVehicles, _allVehicles));
  }

  @override
  int get hashCode => Object.hash(runtimeType, searchText, vehicleGroupColor,
      const DeepCollectionEquality().hash(_allVehicles));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateListVehicleImplCopyWith<_$UpdateListVehicleImpl> get copyWith =>
      __$$UpdateListVehicleImplCopyWithImpl<_$UpdateListVehicleImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(VehicleGroupColor? vehicleGroupColor)
        filterByVehicleColor,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function() resetFilters,
    required TResult Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)
        initialVehicle,
    required TResult Function(String searchText,
            VehicleGroupColor? vehicleGroupColor, List<Vehicle> allVehicles)
        updateListVehicle,
  }) {
    return updateListVehicle(searchText, vehicleGroupColor, allVehicles);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function()? resetFilters,
    TResult? Function(
            List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult? Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
  }) {
    return updateListVehicle?.call(searchText, vehicleGroupColor, allVehicles);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(VehicleGroupColor? vehicleGroupColor)?
        filterByVehicleColor,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function()? resetFilters,
    TResult Function(List<Vehicle> filteredVehicles, List<Vehicle> allVehicles)?
        initialVehicle,
    TResult Function(String searchText, VehicleGroupColor? vehicleGroupColor,
            List<Vehicle> allVehicles)?
        updateListVehicle,
    required TResult orElse(),
  }) {
    if (updateListVehicle != null) {
      return updateListVehicle(searchText, vehicleGroupColor, allVehicles);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FilterByVehicleGroup value) filterByVehicleColor,
    required TResult Function(_SearchTextChanged value) searchLicensePlate,
    required TResult Function(_ResetFilters value) resetFilters,
    required TResult Function(_InitialVehicle value) initialVehicle,
    required TResult Function(_UpdateListVehicle value) updateListVehicle,
  }) {
    return updateListVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult? Function(_SearchTextChanged value)? searchLicensePlate,
    TResult? Function(_ResetFilters value)? resetFilters,
    TResult? Function(_InitialVehicle value)? initialVehicle,
    TResult? Function(_UpdateListVehicle value)? updateListVehicle,
  }) {
    return updateListVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FilterByVehicleGroup value)? filterByVehicleColor,
    TResult Function(_SearchTextChanged value)? searchLicensePlate,
    TResult Function(_ResetFilters value)? resetFilters,
    TResult Function(_InitialVehicle value)? initialVehicle,
    TResult Function(_UpdateListVehicle value)? updateListVehicle,
    required TResult orElse(),
  }) {
    if (updateListVehicle != null) {
      return updateListVehicle(this);
    }
    return orElse();
  }
}

abstract class _UpdateListVehicle implements SearchVehicleEvent {
  const factory _UpdateListVehicle(
      {required final String searchText,
      final VehicleGroupColor? vehicleGroupColor,
      required final List<Vehicle> allVehicles}) = _$UpdateListVehicleImpl;

  String get searchText;
  VehicleGroupColor? get vehicleGroupColor;
  List<Vehicle> get allVehicles;
  @JsonKey(ignore: true)
  _$$UpdateListVehicleImplCopyWith<_$UpdateListVehicleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$SearchVehicleState {
  List<Vehicle> get filteredVehicles => throw _privateConstructorUsedError;
  VehicleGroupColor? get selectedColor => throw _privateConstructorUsedError;
  String get searchText => throw _privateConstructorUsedError;
  List<Vehicle> get allVehicles => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SearchVehicleStateCopyWith<SearchVehicleState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchVehicleStateCopyWith<$Res> {
  factory $SearchVehicleStateCopyWith(
          SearchVehicleState value, $Res Function(SearchVehicleState) then) =
      _$SearchVehicleStateCopyWithImpl<$Res, SearchVehicleState>;
  @useResult
  $Res call(
      {List<Vehicle> filteredVehicles,
      VehicleGroupColor? selectedColor,
      String searchText,
      List<Vehicle> allVehicles});
}

/// @nodoc
class _$SearchVehicleStateCopyWithImpl<$Res, $Val extends SearchVehicleState>
    implements $SearchVehicleStateCopyWith<$Res> {
  _$SearchVehicleStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filteredVehicles = null,
    Object? selectedColor = freezed,
    Object? searchText = null,
    Object? allVehicles = null,
  }) {
    return _then(_value.copyWith(
      filteredVehicles: null == filteredVehicles
          ? _value.filteredVehicles
          : filteredVehicles // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      selectedColor: freezed == selectedColor
          ? _value.selectedColor
          : selectedColor // ignore: cast_nullable_to_non_nullable
              as VehicleGroupColor?,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      allVehicles: null == allVehicles
          ? _value.allVehicles
          : allVehicles // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SearchVehicleStateImplCopyWith<$Res>
    implements $SearchVehicleStateCopyWith<$Res> {
  factory _$$SearchVehicleStateImplCopyWith(_$SearchVehicleStateImpl value,
          $Res Function(_$SearchVehicleStateImpl) then) =
      __$$SearchVehicleStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Vehicle> filteredVehicles,
      VehicleGroupColor? selectedColor,
      String searchText,
      List<Vehicle> allVehicles});
}

/// @nodoc
class __$$SearchVehicleStateImplCopyWithImpl<$Res>
    extends _$SearchVehicleStateCopyWithImpl<$Res, _$SearchVehicleStateImpl>
    implements _$$SearchVehicleStateImplCopyWith<$Res> {
  __$$SearchVehicleStateImplCopyWithImpl(_$SearchVehicleStateImpl _value,
      $Res Function(_$SearchVehicleStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filteredVehicles = null,
    Object? selectedColor = freezed,
    Object? searchText = null,
    Object? allVehicles = null,
  }) {
    return _then(_$SearchVehicleStateImpl(
      filteredVehicles: null == filteredVehicles
          ? _value._filteredVehicles
          : filteredVehicles // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      selectedColor: freezed == selectedColor
          ? _value.selectedColor
          : selectedColor // ignore: cast_nullable_to_non_nullable
              as VehicleGroupColor?,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      allVehicles: null == allVehicles
          ? _value._allVehicles
          : allVehicles // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
    ));
  }
}

/// @nodoc

class _$SearchVehicleStateImpl implements _SearchVehicleState {
  const _$SearchVehicleStateImpl(
      {required final List<Vehicle> filteredVehicles,
      this.selectedColor,
      required this.searchText,
      required final List<Vehicle> allVehicles})
      : _filteredVehicles = filteredVehicles,
        _allVehicles = allVehicles;

  final List<Vehicle> _filteredVehicles;
  @override
  List<Vehicle> get filteredVehicles {
    if (_filteredVehicles is EqualUnmodifiableListView)
      return _filteredVehicles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredVehicles);
  }

  @override
  final VehicleGroupColor? selectedColor;
  @override
  final String searchText;
  final List<Vehicle> _allVehicles;
  @override
  List<Vehicle> get allVehicles {
    if (_allVehicles is EqualUnmodifiableListView) return _allVehicles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allVehicles);
  }

  @override
  String toString() {
    return 'SearchVehicleState(filteredVehicles: $filteredVehicles, selectedColor: $selectedColor, searchText: $searchText, allVehicles: $allVehicles)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchVehicleStateImpl &&
            const DeepCollectionEquality()
                .equals(other._filteredVehicles, _filteredVehicles) &&
            (identical(other.selectedColor, selectedColor) ||
                other.selectedColor == selectedColor) &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText) &&
            const DeepCollectionEquality()
                .equals(other._allVehicles, _allVehicles));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_filteredVehicles),
      selectedColor,
      searchText,
      const DeepCollectionEquality().hash(_allVehicles));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchVehicleStateImplCopyWith<_$SearchVehicleStateImpl> get copyWith =>
      __$$SearchVehicleStateImplCopyWithImpl<_$SearchVehicleStateImpl>(
          this, _$identity);
}

abstract class _SearchVehicleState implements SearchVehicleState {
  const factory _SearchVehicleState(
      {required final List<Vehicle> filteredVehicles,
      final VehicleGroupColor? selectedColor,
      required final String searchText,
      required final List<Vehicle> allVehicles}) = _$SearchVehicleStateImpl;

  @override
  List<Vehicle> get filteredVehicles;
  @override
  VehicleGroupColor? get selectedColor;
  @override
  String get searchText;
  @override
  List<Vehicle> get allVehicles;
  @override
  @JsonKey(ignore: true)
  _$$SearchVehicleStateImplCopyWith<_$SearchVehicleStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
