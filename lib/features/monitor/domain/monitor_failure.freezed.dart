// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'monitor_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MonitorFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonitorFailureUnexpected value) unexpected,
    required TResult Function(_MonitorFailureUnauthorized value) unauthorized,
    required TResult Function(_MonitorFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MonitorFailureServerError value) serverError,
    required TResult Function(_MonitorFailureNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonitorFailureUnexpected value)? unexpected,
    TResult? Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult? Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MonitorFailureServerError value)? serverError,
    TResult? Function(_MonitorFailureNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonitorFailureUnexpected value)? unexpected,
    TResult Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MonitorFailureServerError value)? serverError,
    TResult Function(_MonitorFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MonitorFailureCopyWith<$Res> {
  factory $MonitorFailureCopyWith(
          MonitorFailure value, $Res Function(MonitorFailure) then) =
      _$MonitorFailureCopyWithImpl<$Res, MonitorFailure>;
}

/// @nodoc
class _$MonitorFailureCopyWithImpl<$Res, $Val extends MonitorFailure>
    implements $MonitorFailureCopyWith<$Res> {
  _$MonitorFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$MonitorFailureUnexpectedImplCopyWith<$Res> {
  factory _$$MonitorFailureUnexpectedImplCopyWith(
          _$MonitorFailureUnexpectedImpl value,
          $Res Function(_$MonitorFailureUnexpectedImpl) then) =
      __$$MonitorFailureUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$MonitorFailureUnexpectedImplCopyWithImpl<$Res>
    extends _$MonitorFailureCopyWithImpl<$Res, _$MonitorFailureUnexpectedImpl>
    implements _$$MonitorFailureUnexpectedImplCopyWith<$Res> {
  __$$MonitorFailureUnexpectedImplCopyWithImpl(
      _$MonitorFailureUnexpectedImpl _value,
      $Res Function(_$MonitorFailureUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$MonitorFailureUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$MonitorFailureUnexpectedImpl implements _MonitorFailureUnexpected {
  const _$MonitorFailureUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'MonitorFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonitorFailureUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MonitorFailureUnexpectedImplCopyWith<_$MonitorFailureUnexpectedImpl>
      get copyWith => __$$MonitorFailureUnexpectedImplCopyWithImpl<
          _$MonitorFailureUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonitorFailureUnexpected value) unexpected,
    required TResult Function(_MonitorFailureUnauthorized value) unauthorized,
    required TResult Function(_MonitorFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MonitorFailureServerError value) serverError,
    required TResult Function(_MonitorFailureNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonitorFailureUnexpected value)? unexpected,
    TResult? Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult? Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MonitorFailureServerError value)? serverError,
    TResult? Function(_MonitorFailureNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonitorFailureUnexpected value)? unexpected,
    TResult Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MonitorFailureServerError value)? serverError,
    TResult Function(_MonitorFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _MonitorFailureUnexpected implements MonitorFailure {
  const factory _MonitorFailureUnexpected({required final String error}) =
      _$MonitorFailureUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$MonitorFailureUnexpectedImplCopyWith<_$MonitorFailureUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MonitorFailureUnauthorizedImplCopyWith<$Res> {
  factory _$$MonitorFailureUnauthorizedImplCopyWith(
          _$MonitorFailureUnauthorizedImpl value,
          $Res Function(_$MonitorFailureUnauthorizedImpl) then) =
      __$$MonitorFailureUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MonitorFailureUnauthorizedImplCopyWithImpl<$Res>
    extends _$MonitorFailureCopyWithImpl<$Res, _$MonitorFailureUnauthorizedImpl>
    implements _$$MonitorFailureUnauthorizedImplCopyWith<$Res> {
  __$$MonitorFailureUnauthorizedImplCopyWithImpl(
      _$MonitorFailureUnauthorizedImpl _value,
      $Res Function(_$MonitorFailureUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$MonitorFailureUnauthorizedImpl implements _MonitorFailureUnauthorized {
  const _$MonitorFailureUnauthorizedImpl();

  @override
  String toString() {
    return 'MonitorFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonitorFailureUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonitorFailureUnexpected value) unexpected,
    required TResult Function(_MonitorFailureUnauthorized value) unauthorized,
    required TResult Function(_MonitorFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MonitorFailureServerError value) serverError,
    required TResult Function(_MonitorFailureNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonitorFailureUnexpected value)? unexpected,
    TResult? Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult? Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MonitorFailureServerError value)? serverError,
    TResult? Function(_MonitorFailureNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonitorFailureUnexpected value)? unexpected,
    TResult Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MonitorFailureServerError value)? serverError,
    TResult Function(_MonitorFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _MonitorFailureUnauthorized implements MonitorFailure {
  const factory _MonitorFailureUnauthorized() =
      _$MonitorFailureUnauthorizedImpl;
}

/// @nodoc
abstract class _$$MonitorFailureUnauthenticatedImplCopyWith<$Res> {
  factory _$$MonitorFailureUnauthenticatedImplCopyWith(
          _$MonitorFailureUnauthenticatedImpl value,
          $Res Function(_$MonitorFailureUnauthenticatedImpl) then) =
      __$$MonitorFailureUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MonitorFailureUnauthenticatedImplCopyWithImpl<$Res>
    extends _$MonitorFailureCopyWithImpl<$Res,
        _$MonitorFailureUnauthenticatedImpl>
    implements _$$MonitorFailureUnauthenticatedImplCopyWith<$Res> {
  __$$MonitorFailureUnauthenticatedImplCopyWithImpl(
      _$MonitorFailureUnauthenticatedImpl _value,
      $Res Function(_$MonitorFailureUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$MonitorFailureUnauthenticatedImpl
    implements _MonitorFailureUnauthenticated {
  const _$MonitorFailureUnauthenticatedImpl();

  @override
  String toString() {
    return 'MonitorFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonitorFailureUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonitorFailureUnexpected value) unexpected,
    required TResult Function(_MonitorFailureUnauthorized value) unauthorized,
    required TResult Function(_MonitorFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MonitorFailureServerError value) serverError,
    required TResult Function(_MonitorFailureNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonitorFailureUnexpected value)? unexpected,
    TResult? Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult? Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MonitorFailureServerError value)? serverError,
    TResult? Function(_MonitorFailureNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonitorFailureUnexpected value)? unexpected,
    TResult Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MonitorFailureServerError value)? serverError,
    TResult Function(_MonitorFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _MonitorFailureUnauthenticated implements MonitorFailure {
  const factory _MonitorFailureUnauthenticated() =
      _$MonitorFailureUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$MonitorFailureServerErrorImplCopyWith<$Res> {
  factory _$$MonitorFailureServerErrorImplCopyWith(
          _$MonitorFailureServerErrorImpl value,
          $Res Function(_$MonitorFailureServerErrorImpl) then) =
      __$$MonitorFailureServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MonitorFailureServerErrorImplCopyWithImpl<$Res>
    extends _$MonitorFailureCopyWithImpl<$Res, _$MonitorFailureServerErrorImpl>
    implements _$$MonitorFailureServerErrorImplCopyWith<$Res> {
  __$$MonitorFailureServerErrorImplCopyWithImpl(
      _$MonitorFailureServerErrorImpl _value,
      $Res Function(_$MonitorFailureServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$MonitorFailureServerErrorImpl implements _MonitorFailureServerError {
  const _$MonitorFailureServerErrorImpl();

  @override
  String toString() {
    return 'MonitorFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonitorFailureServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonitorFailureUnexpected value) unexpected,
    required TResult Function(_MonitorFailureUnauthorized value) unauthorized,
    required TResult Function(_MonitorFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MonitorFailureServerError value) serverError,
    required TResult Function(_MonitorFailureNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonitorFailureUnexpected value)? unexpected,
    TResult? Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult? Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MonitorFailureServerError value)? serverError,
    TResult? Function(_MonitorFailureNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonitorFailureUnexpected value)? unexpected,
    TResult Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MonitorFailureServerError value)? serverError,
    TResult Function(_MonitorFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _MonitorFailureServerError implements MonitorFailure {
  const factory _MonitorFailureServerError() = _$MonitorFailureServerErrorImpl;
}

/// @nodoc
abstract class _$$MonitorFailureNoInternetImplCopyWith<$Res> {
  factory _$$MonitorFailureNoInternetImplCopyWith(
          _$MonitorFailureNoInternetImpl value,
          $Res Function(_$MonitorFailureNoInternetImpl) then) =
      __$$MonitorFailureNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MonitorFailureNoInternetImplCopyWithImpl<$Res>
    extends _$MonitorFailureCopyWithImpl<$Res, _$MonitorFailureNoInternetImpl>
    implements _$$MonitorFailureNoInternetImplCopyWith<$Res> {
  __$$MonitorFailureNoInternetImplCopyWithImpl(
      _$MonitorFailureNoInternetImpl _value,
      $Res Function(_$MonitorFailureNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$MonitorFailureNoInternetImpl implements _MonitorFailureNoInternet {
  const _$MonitorFailureNoInternetImpl();

  @override
  String toString() {
    return 'MonitorFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonitorFailureNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonitorFailureUnexpected value) unexpected,
    required TResult Function(_MonitorFailureUnauthorized value) unauthorized,
    required TResult Function(_MonitorFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MonitorFailureServerError value) serverError,
    required TResult Function(_MonitorFailureNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonitorFailureUnexpected value)? unexpected,
    TResult? Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult? Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MonitorFailureServerError value)? serverError,
    TResult? Function(_MonitorFailureNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonitorFailureUnexpected value)? unexpected,
    TResult Function(_MonitorFailureUnauthorized value)? unauthorized,
    TResult Function(_MonitorFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MonitorFailureServerError value)? serverError,
    TResult Function(_MonitorFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _MonitorFailureNoInternet implements MonitorFailure {
  const factory _MonitorFailureNoInternet() = _$MonitorFailureNoInternetImpl;
}
