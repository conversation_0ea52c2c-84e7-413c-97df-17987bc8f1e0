// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() wrongUsernameAndPassword,
    required TResult Function() serverError,
    required TResult Function() needForUpdateAppVersion,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? wrongUsernameAndPassword,
    TResult? Function()? serverError,
    TResult? Function()? needForUpdateAppVersion,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? wrongUsernameAndPassword,
    TResult Function()? serverError,
    TResult Function()? needForUpdateAppVersion,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthFailureUnexpected value) unexpected,
    required TResult Function(_AuthFailureUnauthorized value) unauthorized,
    required TResult Function(_AuthFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_AuthFailureWrongPassword value)
        wrongUsernameAndPassword,
    required TResult Function(_AuthFailureServerError value) serverError,
    required TResult Function(_AuthFailureNeedForUpdateAppVersion value)
        needForUpdateAppVersion,
    required TResult Function(_AuthFailureNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthFailureUnexpected value)? unexpected,
    TResult? Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult? Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_AuthFailureWrongPassword value)?
        wrongUsernameAndPassword,
    TResult? Function(_AuthFailureServerError value)? serverError,
    TResult? Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult? Function(_AuthFailureNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthFailureUnexpected value)? unexpected,
    TResult Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult Function(_AuthFailureWrongPassword value)? wrongUsernameAndPassword,
    TResult Function(_AuthFailureServerError value)? serverError,
    TResult Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult Function(_AuthFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthFailureCopyWith<$Res> {
  factory $AuthFailureCopyWith(
          AuthFailure value, $Res Function(AuthFailure) then) =
      _$AuthFailureCopyWithImpl<$Res, AuthFailure>;
}

/// @nodoc
class _$AuthFailureCopyWithImpl<$Res, $Val extends AuthFailure>
    implements $AuthFailureCopyWith<$Res> {
  _$AuthFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$AuthFailureUnexpectedImplCopyWith<$Res> {
  factory _$$AuthFailureUnexpectedImplCopyWith(
          _$AuthFailureUnexpectedImpl value,
          $Res Function(_$AuthFailureUnexpectedImpl) then) =
      __$$AuthFailureUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$AuthFailureUnexpectedImplCopyWithImpl<$Res>
    extends _$AuthFailureCopyWithImpl<$Res, _$AuthFailureUnexpectedImpl>
    implements _$$AuthFailureUnexpectedImplCopyWith<$Res> {
  __$$AuthFailureUnexpectedImplCopyWithImpl(_$AuthFailureUnexpectedImpl _value,
      $Res Function(_$AuthFailureUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$AuthFailureUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AuthFailureUnexpectedImpl implements _AuthFailureUnexpected {
  const _$AuthFailureUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'AuthFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthFailureUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthFailureUnexpectedImplCopyWith<_$AuthFailureUnexpectedImpl>
      get copyWith => __$$AuthFailureUnexpectedImplCopyWithImpl<
          _$AuthFailureUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() wrongUsernameAndPassword,
    required TResult Function() serverError,
    required TResult Function() needForUpdateAppVersion,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? wrongUsernameAndPassword,
    TResult? Function()? serverError,
    TResult? Function()? needForUpdateAppVersion,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? wrongUsernameAndPassword,
    TResult Function()? serverError,
    TResult Function()? needForUpdateAppVersion,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthFailureUnexpected value) unexpected,
    required TResult Function(_AuthFailureUnauthorized value) unauthorized,
    required TResult Function(_AuthFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_AuthFailureWrongPassword value)
        wrongUsernameAndPassword,
    required TResult Function(_AuthFailureServerError value) serverError,
    required TResult Function(_AuthFailureNeedForUpdateAppVersion value)
        needForUpdateAppVersion,
    required TResult Function(_AuthFailureNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthFailureUnexpected value)? unexpected,
    TResult? Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult? Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_AuthFailureWrongPassword value)?
        wrongUsernameAndPassword,
    TResult? Function(_AuthFailureServerError value)? serverError,
    TResult? Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult? Function(_AuthFailureNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthFailureUnexpected value)? unexpected,
    TResult Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult Function(_AuthFailureWrongPassword value)? wrongUsernameAndPassword,
    TResult Function(_AuthFailureServerError value)? serverError,
    TResult Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult Function(_AuthFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _AuthFailureUnexpected implements AuthFailure {
  const factory _AuthFailureUnexpected({required final String error}) =
      _$AuthFailureUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$AuthFailureUnexpectedImplCopyWith<_$AuthFailureUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AuthFailureUnauthorizedImplCopyWith<$Res> {
  factory _$$AuthFailureUnauthorizedImplCopyWith(
          _$AuthFailureUnauthorizedImpl value,
          $Res Function(_$AuthFailureUnauthorizedImpl) then) =
      __$$AuthFailureUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AuthFailureUnauthorizedImplCopyWithImpl<$Res>
    extends _$AuthFailureCopyWithImpl<$Res, _$AuthFailureUnauthorizedImpl>
    implements _$$AuthFailureUnauthorizedImplCopyWith<$Res> {
  __$$AuthFailureUnauthorizedImplCopyWithImpl(
      _$AuthFailureUnauthorizedImpl _value,
      $Res Function(_$AuthFailureUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$AuthFailureUnauthorizedImpl implements _AuthFailureUnauthorized {
  const _$AuthFailureUnauthorizedImpl();

  @override
  String toString() {
    return 'AuthFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthFailureUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() wrongUsernameAndPassword,
    required TResult Function() serverError,
    required TResult Function() needForUpdateAppVersion,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? wrongUsernameAndPassword,
    TResult? Function()? serverError,
    TResult? Function()? needForUpdateAppVersion,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? wrongUsernameAndPassword,
    TResult Function()? serverError,
    TResult Function()? needForUpdateAppVersion,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthFailureUnexpected value) unexpected,
    required TResult Function(_AuthFailureUnauthorized value) unauthorized,
    required TResult Function(_AuthFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_AuthFailureWrongPassword value)
        wrongUsernameAndPassword,
    required TResult Function(_AuthFailureServerError value) serverError,
    required TResult Function(_AuthFailureNeedForUpdateAppVersion value)
        needForUpdateAppVersion,
    required TResult Function(_AuthFailureNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthFailureUnexpected value)? unexpected,
    TResult? Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult? Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_AuthFailureWrongPassword value)?
        wrongUsernameAndPassword,
    TResult? Function(_AuthFailureServerError value)? serverError,
    TResult? Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult? Function(_AuthFailureNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthFailureUnexpected value)? unexpected,
    TResult Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult Function(_AuthFailureWrongPassword value)? wrongUsernameAndPassword,
    TResult Function(_AuthFailureServerError value)? serverError,
    TResult Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult Function(_AuthFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _AuthFailureUnauthorized implements AuthFailure {
  const factory _AuthFailureUnauthorized() = _$AuthFailureUnauthorizedImpl;
}

/// @nodoc
abstract class _$$AuthFailureUnauthenticatedImplCopyWith<$Res> {
  factory _$$AuthFailureUnauthenticatedImplCopyWith(
          _$AuthFailureUnauthenticatedImpl value,
          $Res Function(_$AuthFailureUnauthenticatedImpl) then) =
      __$$AuthFailureUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AuthFailureUnauthenticatedImplCopyWithImpl<$Res>
    extends _$AuthFailureCopyWithImpl<$Res, _$AuthFailureUnauthenticatedImpl>
    implements _$$AuthFailureUnauthenticatedImplCopyWith<$Res> {
  __$$AuthFailureUnauthenticatedImplCopyWithImpl(
      _$AuthFailureUnauthenticatedImpl _value,
      $Res Function(_$AuthFailureUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$AuthFailureUnauthenticatedImpl implements _AuthFailureUnauthenticated {
  const _$AuthFailureUnauthenticatedImpl();

  @override
  String toString() {
    return 'AuthFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthFailureUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() wrongUsernameAndPassword,
    required TResult Function() serverError,
    required TResult Function() needForUpdateAppVersion,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? wrongUsernameAndPassword,
    TResult? Function()? serverError,
    TResult? Function()? needForUpdateAppVersion,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? wrongUsernameAndPassword,
    TResult Function()? serverError,
    TResult Function()? needForUpdateAppVersion,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthFailureUnexpected value) unexpected,
    required TResult Function(_AuthFailureUnauthorized value) unauthorized,
    required TResult Function(_AuthFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_AuthFailureWrongPassword value)
        wrongUsernameAndPassword,
    required TResult Function(_AuthFailureServerError value) serverError,
    required TResult Function(_AuthFailureNeedForUpdateAppVersion value)
        needForUpdateAppVersion,
    required TResult Function(_AuthFailureNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthFailureUnexpected value)? unexpected,
    TResult? Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult? Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_AuthFailureWrongPassword value)?
        wrongUsernameAndPassword,
    TResult? Function(_AuthFailureServerError value)? serverError,
    TResult? Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult? Function(_AuthFailureNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthFailureUnexpected value)? unexpected,
    TResult Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult Function(_AuthFailureWrongPassword value)? wrongUsernameAndPassword,
    TResult Function(_AuthFailureServerError value)? serverError,
    TResult Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult Function(_AuthFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _AuthFailureUnauthenticated implements AuthFailure {
  const factory _AuthFailureUnauthenticated() =
      _$AuthFailureUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$AuthFailureWrongPasswordImplCopyWith<$Res> {
  factory _$$AuthFailureWrongPasswordImplCopyWith(
          _$AuthFailureWrongPasswordImpl value,
          $Res Function(_$AuthFailureWrongPasswordImpl) then) =
      __$$AuthFailureWrongPasswordImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AuthFailureWrongPasswordImplCopyWithImpl<$Res>
    extends _$AuthFailureCopyWithImpl<$Res, _$AuthFailureWrongPasswordImpl>
    implements _$$AuthFailureWrongPasswordImplCopyWith<$Res> {
  __$$AuthFailureWrongPasswordImplCopyWithImpl(
      _$AuthFailureWrongPasswordImpl _value,
      $Res Function(_$AuthFailureWrongPasswordImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$AuthFailureWrongPasswordImpl implements _AuthFailureWrongPassword {
  const _$AuthFailureWrongPasswordImpl();

  @override
  String toString() {
    return 'AuthFailure.wrongUsernameAndPassword()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthFailureWrongPasswordImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() wrongUsernameAndPassword,
    required TResult Function() serverError,
    required TResult Function() needForUpdateAppVersion,
    required TResult Function() noInternet,
  }) {
    return wrongUsernameAndPassword();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? wrongUsernameAndPassword,
    TResult? Function()? serverError,
    TResult? Function()? needForUpdateAppVersion,
    TResult? Function()? noInternet,
  }) {
    return wrongUsernameAndPassword?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? wrongUsernameAndPassword,
    TResult Function()? serverError,
    TResult Function()? needForUpdateAppVersion,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (wrongUsernameAndPassword != null) {
      return wrongUsernameAndPassword();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthFailureUnexpected value) unexpected,
    required TResult Function(_AuthFailureUnauthorized value) unauthorized,
    required TResult Function(_AuthFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_AuthFailureWrongPassword value)
        wrongUsernameAndPassword,
    required TResult Function(_AuthFailureServerError value) serverError,
    required TResult Function(_AuthFailureNeedForUpdateAppVersion value)
        needForUpdateAppVersion,
    required TResult Function(_AuthFailureNoInternet value) noInternet,
  }) {
    return wrongUsernameAndPassword(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthFailureUnexpected value)? unexpected,
    TResult? Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult? Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_AuthFailureWrongPassword value)?
        wrongUsernameAndPassword,
    TResult? Function(_AuthFailureServerError value)? serverError,
    TResult? Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult? Function(_AuthFailureNoInternet value)? noInternet,
  }) {
    return wrongUsernameAndPassword?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthFailureUnexpected value)? unexpected,
    TResult Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult Function(_AuthFailureWrongPassword value)? wrongUsernameAndPassword,
    TResult Function(_AuthFailureServerError value)? serverError,
    TResult Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult Function(_AuthFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (wrongUsernameAndPassword != null) {
      return wrongUsernameAndPassword(this);
    }
    return orElse();
  }
}

abstract class _AuthFailureWrongPassword implements AuthFailure {
  const factory _AuthFailureWrongPassword() = _$AuthFailureWrongPasswordImpl;
}

/// @nodoc
abstract class _$$AuthFailureServerErrorImplCopyWith<$Res> {
  factory _$$AuthFailureServerErrorImplCopyWith(
          _$AuthFailureServerErrorImpl value,
          $Res Function(_$AuthFailureServerErrorImpl) then) =
      __$$AuthFailureServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AuthFailureServerErrorImplCopyWithImpl<$Res>
    extends _$AuthFailureCopyWithImpl<$Res, _$AuthFailureServerErrorImpl>
    implements _$$AuthFailureServerErrorImplCopyWith<$Res> {
  __$$AuthFailureServerErrorImplCopyWithImpl(
      _$AuthFailureServerErrorImpl _value,
      $Res Function(_$AuthFailureServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$AuthFailureServerErrorImpl implements _AuthFailureServerError {
  const _$AuthFailureServerErrorImpl();

  @override
  String toString() {
    return 'AuthFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthFailureServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() wrongUsernameAndPassword,
    required TResult Function() serverError,
    required TResult Function() needForUpdateAppVersion,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? wrongUsernameAndPassword,
    TResult? Function()? serverError,
    TResult? Function()? needForUpdateAppVersion,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? wrongUsernameAndPassword,
    TResult Function()? serverError,
    TResult Function()? needForUpdateAppVersion,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthFailureUnexpected value) unexpected,
    required TResult Function(_AuthFailureUnauthorized value) unauthorized,
    required TResult Function(_AuthFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_AuthFailureWrongPassword value)
        wrongUsernameAndPassword,
    required TResult Function(_AuthFailureServerError value) serverError,
    required TResult Function(_AuthFailureNeedForUpdateAppVersion value)
        needForUpdateAppVersion,
    required TResult Function(_AuthFailureNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthFailureUnexpected value)? unexpected,
    TResult? Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult? Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_AuthFailureWrongPassword value)?
        wrongUsernameAndPassword,
    TResult? Function(_AuthFailureServerError value)? serverError,
    TResult? Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult? Function(_AuthFailureNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthFailureUnexpected value)? unexpected,
    TResult Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult Function(_AuthFailureWrongPassword value)? wrongUsernameAndPassword,
    TResult Function(_AuthFailureServerError value)? serverError,
    TResult Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult Function(_AuthFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _AuthFailureServerError implements AuthFailure {
  const factory _AuthFailureServerError() = _$AuthFailureServerErrorImpl;
}

/// @nodoc
abstract class _$$AuthFailureNeedForUpdateAppVersionImplCopyWith<$Res> {
  factory _$$AuthFailureNeedForUpdateAppVersionImplCopyWith(
          _$AuthFailureNeedForUpdateAppVersionImpl value,
          $Res Function(_$AuthFailureNeedForUpdateAppVersionImpl) then) =
      __$$AuthFailureNeedForUpdateAppVersionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AuthFailureNeedForUpdateAppVersionImplCopyWithImpl<$Res>
    extends _$AuthFailureCopyWithImpl<$Res,
        _$AuthFailureNeedForUpdateAppVersionImpl>
    implements _$$AuthFailureNeedForUpdateAppVersionImplCopyWith<$Res> {
  __$$AuthFailureNeedForUpdateAppVersionImplCopyWithImpl(
      _$AuthFailureNeedForUpdateAppVersionImpl _value,
      $Res Function(_$AuthFailureNeedForUpdateAppVersionImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$AuthFailureNeedForUpdateAppVersionImpl
    implements _AuthFailureNeedForUpdateAppVersion {
  const _$AuthFailureNeedForUpdateAppVersionImpl();

  @override
  String toString() {
    return 'AuthFailure.needForUpdateAppVersion()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthFailureNeedForUpdateAppVersionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() wrongUsernameAndPassword,
    required TResult Function() serverError,
    required TResult Function() needForUpdateAppVersion,
    required TResult Function() noInternet,
  }) {
    return needForUpdateAppVersion();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? wrongUsernameAndPassword,
    TResult? Function()? serverError,
    TResult? Function()? needForUpdateAppVersion,
    TResult? Function()? noInternet,
  }) {
    return needForUpdateAppVersion?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? wrongUsernameAndPassword,
    TResult Function()? serverError,
    TResult Function()? needForUpdateAppVersion,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (needForUpdateAppVersion != null) {
      return needForUpdateAppVersion();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthFailureUnexpected value) unexpected,
    required TResult Function(_AuthFailureUnauthorized value) unauthorized,
    required TResult Function(_AuthFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_AuthFailureWrongPassword value)
        wrongUsernameAndPassword,
    required TResult Function(_AuthFailureServerError value) serverError,
    required TResult Function(_AuthFailureNeedForUpdateAppVersion value)
        needForUpdateAppVersion,
    required TResult Function(_AuthFailureNoInternet value) noInternet,
  }) {
    return needForUpdateAppVersion(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthFailureUnexpected value)? unexpected,
    TResult? Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult? Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_AuthFailureWrongPassword value)?
        wrongUsernameAndPassword,
    TResult? Function(_AuthFailureServerError value)? serverError,
    TResult? Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult? Function(_AuthFailureNoInternet value)? noInternet,
  }) {
    return needForUpdateAppVersion?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthFailureUnexpected value)? unexpected,
    TResult Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult Function(_AuthFailureWrongPassword value)? wrongUsernameAndPassword,
    TResult Function(_AuthFailureServerError value)? serverError,
    TResult Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult Function(_AuthFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (needForUpdateAppVersion != null) {
      return needForUpdateAppVersion(this);
    }
    return orElse();
  }
}

abstract class _AuthFailureNeedForUpdateAppVersion implements AuthFailure {
  const factory _AuthFailureNeedForUpdateAppVersion() =
      _$AuthFailureNeedForUpdateAppVersionImpl;
}

/// @nodoc
abstract class _$$AuthFailureNoInternetImplCopyWith<$Res> {
  factory _$$AuthFailureNoInternetImplCopyWith(
          _$AuthFailureNoInternetImpl value,
          $Res Function(_$AuthFailureNoInternetImpl) then) =
      __$$AuthFailureNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AuthFailureNoInternetImplCopyWithImpl<$Res>
    extends _$AuthFailureCopyWithImpl<$Res, _$AuthFailureNoInternetImpl>
    implements _$$AuthFailureNoInternetImplCopyWith<$Res> {
  __$$AuthFailureNoInternetImplCopyWithImpl(_$AuthFailureNoInternetImpl _value,
      $Res Function(_$AuthFailureNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$AuthFailureNoInternetImpl implements _AuthFailureNoInternet {
  const _$AuthFailureNoInternetImpl();

  @override
  String toString() {
    return 'AuthFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthFailureNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() wrongUsernameAndPassword,
    required TResult Function() serverError,
    required TResult Function() needForUpdateAppVersion,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? wrongUsernameAndPassword,
    TResult? Function()? serverError,
    TResult? Function()? needForUpdateAppVersion,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? wrongUsernameAndPassword,
    TResult Function()? serverError,
    TResult Function()? needForUpdateAppVersion,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthFailureUnexpected value) unexpected,
    required TResult Function(_AuthFailureUnauthorized value) unauthorized,
    required TResult Function(_AuthFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_AuthFailureWrongPassword value)
        wrongUsernameAndPassword,
    required TResult Function(_AuthFailureServerError value) serverError,
    required TResult Function(_AuthFailureNeedForUpdateAppVersion value)
        needForUpdateAppVersion,
    required TResult Function(_AuthFailureNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthFailureUnexpected value)? unexpected,
    TResult? Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult? Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_AuthFailureWrongPassword value)?
        wrongUsernameAndPassword,
    TResult? Function(_AuthFailureServerError value)? serverError,
    TResult? Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult? Function(_AuthFailureNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthFailureUnexpected value)? unexpected,
    TResult Function(_AuthFailureUnauthorized value)? unauthorized,
    TResult Function(_AuthFailureUnauthenticated value)? unauthenticated,
    TResult Function(_AuthFailureWrongPassword value)? wrongUsernameAndPassword,
    TResult Function(_AuthFailureServerError value)? serverError,
    TResult Function(_AuthFailureNeedForUpdateAppVersion value)?
        needForUpdateAppVersion,
    TResult Function(_AuthFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _AuthFailureNoInternet implements AuthFailure {
  const factory _AuthFailureNoInternet() = _$AuthFailureNoInternetImpl;
}
