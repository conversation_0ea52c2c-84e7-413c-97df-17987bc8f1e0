// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String username, String password,
            String? firebaseToken, bool savePassword)
        login,
    required TResult Function(AvemaUser avemaUser, String? firebaseToken)
        autoAuthentication,
    required TResult Function(AvemaUser avemaUser, String? firebaseToken)
        logout,
    required TResult Function(AvemaUser user, String phone, String email)
        changeInfo,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String username, String password, String? firebaseToken,
            bool savePassword)?
        login,
    TResult? Function(AvemaUser avemaUser, String? firebaseToken)?
        autoAuthentication,
    TResult? Function(AvemaUser avemaUser, String? firebaseToken)? logout,
    TResult? Function(AvemaUser user, String phone, String email)? changeInfo,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String username, String password, String? firebaseToken,
            bool savePassword)?
        login,
    TResult Function(AvemaUser avemaUser, String? firebaseToken)?
        autoAuthentication,
    TResult Function(AvemaUser avemaUser, String? firebaseToken)? logout,
    TResult Function(AvemaUser user, String phone, String email)? changeInfo,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Login value) login,
    required TResult Function(_AutoAuthentication value) autoAuthentication,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeInfo value) changeInfo,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Login value)? login,
    TResult? Function(_AutoAuthentication value)? autoAuthentication,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeInfo value)? changeInfo,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Login value)? login,
    TResult Function(_AutoAuthentication value)? autoAuthentication,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeInfo value)? changeInfo,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthEventCopyWith<$Res> {
  factory $AuthEventCopyWith(AuthEvent value, $Res Function(AuthEvent) then) =
      _$AuthEventCopyWithImpl<$Res, AuthEvent>;
}

/// @nodoc
class _$AuthEventCopyWithImpl<$Res, $Val extends AuthEvent>
    implements $AuthEventCopyWith<$Res> {
  _$AuthEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$LoginImplCopyWith<$Res> {
  factory _$$LoginImplCopyWith(
          _$LoginImpl value, $Res Function(_$LoginImpl) then) =
      __$$LoginImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String username,
      String password,
      String? firebaseToken,
      bool savePassword});
}

/// @nodoc
class __$$LoginImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$LoginImpl>
    implements _$$LoginImplCopyWith<$Res> {
  __$$LoginImplCopyWithImpl(
      _$LoginImpl _value, $Res Function(_$LoginImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? password = null,
    Object? firebaseToken = freezed,
    Object? savePassword = null,
  }) {
    return _then(_$LoginImpl(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      firebaseToken: freezed == firebaseToken
          ? _value.firebaseToken
          : firebaseToken // ignore: cast_nullable_to_non_nullable
              as String?,
      savePassword: null == savePassword
          ? _value.savePassword
          : savePassword // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$LoginImpl implements _Login {
  const _$LoginImpl(
      {required this.username,
      required this.password,
      required this.firebaseToken,
      required this.savePassword});

  @override
  final String username;
  @override
  final String password;
  @override
  final String? firebaseToken;
  @override
  final bool savePassword;

  @override
  String toString() {
    return 'AuthEvent.login(username: $username, password: $password, firebaseToken: $firebaseToken, savePassword: $savePassword)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginImpl &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.firebaseToken, firebaseToken) ||
                other.firebaseToken == firebaseToken) &&
            (identical(other.savePassword, savePassword) ||
                other.savePassword == savePassword));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, username, password, firebaseToken, savePassword);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginImplCopyWith<_$LoginImpl> get copyWith =>
      __$$LoginImplCopyWithImpl<_$LoginImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String username, String password,
            String? firebaseToken, bool savePassword)
        login,
    required TResult Function(AvemaUser avemaUser, String? firebaseToken)
        autoAuthentication,
    required TResult Function(AvemaUser avemaUser, String? firebaseToken)
        logout,
    required TResult Function(AvemaUser user, String phone, String email)
        changeInfo,
  }) {
    return login(username, password, firebaseToken, savePassword);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String username, String password, String? firebaseToken,
            bool savePassword)?
        login,
    TResult? Function(AvemaUser avemaUser, String? firebaseToken)?
        autoAuthentication,
    TResult? Function(AvemaUser avemaUser, String? firebaseToken)? logout,
    TResult? Function(AvemaUser user, String phone, String email)? changeInfo,
  }) {
    return login?.call(username, password, firebaseToken, savePassword);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String username, String password, String? firebaseToken,
            bool savePassword)?
        login,
    TResult Function(AvemaUser avemaUser, String? firebaseToken)?
        autoAuthentication,
    TResult Function(AvemaUser avemaUser, String? firebaseToken)? logout,
    TResult Function(AvemaUser user, String phone, String email)? changeInfo,
    required TResult orElse(),
  }) {
    if (login != null) {
      return login(username, password, firebaseToken, savePassword);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Login value) login,
    required TResult Function(_AutoAuthentication value) autoAuthentication,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeInfo value) changeInfo,
  }) {
    return login(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Login value)? login,
    TResult? Function(_AutoAuthentication value)? autoAuthentication,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeInfo value)? changeInfo,
  }) {
    return login?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Login value)? login,
    TResult Function(_AutoAuthentication value)? autoAuthentication,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeInfo value)? changeInfo,
    required TResult orElse(),
  }) {
    if (login != null) {
      return login(this);
    }
    return orElse();
  }
}

abstract class _Login implements AuthEvent {
  const factory _Login(
      {required final String username,
      required final String password,
      required final String? firebaseToken,
      required final bool savePassword}) = _$LoginImpl;

  String get username;
  String get password;
  String? get firebaseToken;
  bool get savePassword;
  @JsonKey(ignore: true)
  _$$LoginImplCopyWith<_$LoginImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AutoAuthenticationImplCopyWith<$Res> {
  factory _$$AutoAuthenticationImplCopyWith(_$AutoAuthenticationImpl value,
          $Res Function(_$AutoAuthenticationImpl) then) =
      __$$AutoAuthenticationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AvemaUser avemaUser, String? firebaseToken});

  $AvemaUserCopyWith<$Res> get avemaUser;
}

/// @nodoc
class __$$AutoAuthenticationImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$AutoAuthenticationImpl>
    implements _$$AutoAuthenticationImplCopyWith<$Res> {
  __$$AutoAuthenticationImplCopyWithImpl(_$AutoAuthenticationImpl _value,
      $Res Function(_$AutoAuthenticationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? avemaUser = null,
    Object? firebaseToken = freezed,
  }) {
    return _then(_$AutoAuthenticationImpl(
      avemaUser: null == avemaUser
          ? _value.avemaUser
          : avemaUser // ignore: cast_nullable_to_non_nullable
              as AvemaUser,
      firebaseToken: freezed == firebaseToken
          ? _value.firebaseToken
          : firebaseToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $AvemaUserCopyWith<$Res> get avemaUser {
    return $AvemaUserCopyWith<$Res>(_value.avemaUser, (value) {
      return _then(_value.copyWith(avemaUser: value));
    });
  }
}

/// @nodoc

class _$AutoAuthenticationImpl implements _AutoAuthentication {
  const _$AutoAuthenticationImpl(
      {required this.avemaUser, required this.firebaseToken});

  @override
  final AvemaUser avemaUser;
  @override
  final String? firebaseToken;

  @override
  String toString() {
    return 'AuthEvent.autoAuthentication(avemaUser: $avemaUser, firebaseToken: $firebaseToken)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AutoAuthenticationImpl &&
            (identical(other.avemaUser, avemaUser) ||
                other.avemaUser == avemaUser) &&
            (identical(other.firebaseToken, firebaseToken) ||
                other.firebaseToken == firebaseToken));
  }

  @override
  int get hashCode => Object.hash(runtimeType, avemaUser, firebaseToken);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AutoAuthenticationImplCopyWith<_$AutoAuthenticationImpl> get copyWith =>
      __$$AutoAuthenticationImplCopyWithImpl<_$AutoAuthenticationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String username, String password,
            String? firebaseToken, bool savePassword)
        login,
    required TResult Function(AvemaUser avemaUser, String? firebaseToken)
        autoAuthentication,
    required TResult Function(AvemaUser avemaUser, String? firebaseToken)
        logout,
    required TResult Function(AvemaUser user, String phone, String email)
        changeInfo,
  }) {
    return autoAuthentication(avemaUser, firebaseToken);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String username, String password, String? firebaseToken,
            bool savePassword)?
        login,
    TResult? Function(AvemaUser avemaUser, String? firebaseToken)?
        autoAuthentication,
    TResult? Function(AvemaUser avemaUser, String? firebaseToken)? logout,
    TResult? Function(AvemaUser user, String phone, String email)? changeInfo,
  }) {
    return autoAuthentication?.call(avemaUser, firebaseToken);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String username, String password, String? firebaseToken,
            bool savePassword)?
        login,
    TResult Function(AvemaUser avemaUser, String? firebaseToken)?
        autoAuthentication,
    TResult Function(AvemaUser avemaUser, String? firebaseToken)? logout,
    TResult Function(AvemaUser user, String phone, String email)? changeInfo,
    required TResult orElse(),
  }) {
    if (autoAuthentication != null) {
      return autoAuthentication(avemaUser, firebaseToken);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Login value) login,
    required TResult Function(_AutoAuthentication value) autoAuthentication,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeInfo value) changeInfo,
  }) {
    return autoAuthentication(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Login value)? login,
    TResult? Function(_AutoAuthentication value)? autoAuthentication,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeInfo value)? changeInfo,
  }) {
    return autoAuthentication?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Login value)? login,
    TResult Function(_AutoAuthentication value)? autoAuthentication,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeInfo value)? changeInfo,
    required TResult orElse(),
  }) {
    if (autoAuthentication != null) {
      return autoAuthentication(this);
    }
    return orElse();
  }
}

abstract class _AutoAuthentication implements AuthEvent {
  const factory _AutoAuthentication(
      {required final AvemaUser avemaUser,
      required final String? firebaseToken}) = _$AutoAuthenticationImpl;

  AvemaUser get avemaUser;
  String? get firebaseToken;
  @JsonKey(ignore: true)
  _$$AutoAuthenticationImplCopyWith<_$AutoAuthenticationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LogoutImplCopyWith<$Res> {
  factory _$$LogoutImplCopyWith(
          _$LogoutImpl value, $Res Function(_$LogoutImpl) then) =
      __$$LogoutImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AvemaUser avemaUser, String? firebaseToken});

  $AvemaUserCopyWith<$Res> get avemaUser;
}

/// @nodoc
class __$$LogoutImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$LogoutImpl>
    implements _$$LogoutImplCopyWith<$Res> {
  __$$LogoutImplCopyWithImpl(
      _$LogoutImpl _value, $Res Function(_$LogoutImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? avemaUser = null,
    Object? firebaseToken = freezed,
  }) {
    return _then(_$LogoutImpl(
      avemaUser: null == avemaUser
          ? _value.avemaUser
          : avemaUser // ignore: cast_nullable_to_non_nullable
              as AvemaUser,
      firebaseToken: freezed == firebaseToken
          ? _value.firebaseToken
          : firebaseToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $AvemaUserCopyWith<$Res> get avemaUser {
    return $AvemaUserCopyWith<$Res>(_value.avemaUser, (value) {
      return _then(_value.copyWith(avemaUser: value));
    });
  }
}

/// @nodoc

class _$LogoutImpl implements _Logout {
  const _$LogoutImpl({required this.avemaUser, required this.firebaseToken});

  @override
  final AvemaUser avemaUser;
  @override
  final String? firebaseToken;

  @override
  String toString() {
    return 'AuthEvent.logout(avemaUser: $avemaUser, firebaseToken: $firebaseToken)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LogoutImpl &&
            (identical(other.avemaUser, avemaUser) ||
                other.avemaUser == avemaUser) &&
            (identical(other.firebaseToken, firebaseToken) ||
                other.firebaseToken == firebaseToken));
  }

  @override
  int get hashCode => Object.hash(runtimeType, avemaUser, firebaseToken);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LogoutImplCopyWith<_$LogoutImpl> get copyWith =>
      __$$LogoutImplCopyWithImpl<_$LogoutImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String username, String password,
            String? firebaseToken, bool savePassword)
        login,
    required TResult Function(AvemaUser avemaUser, String? firebaseToken)
        autoAuthentication,
    required TResult Function(AvemaUser avemaUser, String? firebaseToken)
        logout,
    required TResult Function(AvemaUser user, String phone, String email)
        changeInfo,
  }) {
    return logout(avemaUser, firebaseToken);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String username, String password, String? firebaseToken,
            bool savePassword)?
        login,
    TResult? Function(AvemaUser avemaUser, String? firebaseToken)?
        autoAuthentication,
    TResult? Function(AvemaUser avemaUser, String? firebaseToken)? logout,
    TResult? Function(AvemaUser user, String phone, String email)? changeInfo,
  }) {
    return logout?.call(avemaUser, firebaseToken);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String username, String password, String? firebaseToken,
            bool savePassword)?
        login,
    TResult Function(AvemaUser avemaUser, String? firebaseToken)?
        autoAuthentication,
    TResult Function(AvemaUser avemaUser, String? firebaseToken)? logout,
    TResult Function(AvemaUser user, String phone, String email)? changeInfo,
    required TResult orElse(),
  }) {
    if (logout != null) {
      return logout(avemaUser, firebaseToken);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Login value) login,
    required TResult Function(_AutoAuthentication value) autoAuthentication,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeInfo value) changeInfo,
  }) {
    return logout(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Login value)? login,
    TResult? Function(_AutoAuthentication value)? autoAuthentication,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeInfo value)? changeInfo,
  }) {
    return logout?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Login value)? login,
    TResult Function(_AutoAuthentication value)? autoAuthentication,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeInfo value)? changeInfo,
    required TResult orElse(),
  }) {
    if (logout != null) {
      return logout(this);
    }
    return orElse();
  }
}

abstract class _Logout implements AuthEvent {
  const factory _Logout(
      {required final AvemaUser avemaUser,
      required final String? firebaseToken}) = _$LogoutImpl;

  AvemaUser get avemaUser;
  String? get firebaseToken;
  @JsonKey(ignore: true)
  _$$LogoutImplCopyWith<_$LogoutImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeInfoImplCopyWith<$Res> {
  factory _$$ChangeInfoImplCopyWith(
          _$ChangeInfoImpl value, $Res Function(_$ChangeInfoImpl) then) =
      __$$ChangeInfoImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AvemaUser user, String phone, String email});

  $AvemaUserCopyWith<$Res> get user;
}

/// @nodoc
class __$$ChangeInfoImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$ChangeInfoImpl>
    implements _$$ChangeInfoImplCopyWith<$Res> {
  __$$ChangeInfoImplCopyWithImpl(
      _$ChangeInfoImpl _value, $Res Function(_$ChangeInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
    Object? phone = null,
    Object? email = null,
  }) {
    return _then(_$ChangeInfoImpl(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as AvemaUser,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $AvemaUserCopyWith<$Res> get user {
    return $AvemaUserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$ChangeInfoImpl implements _ChangeInfo {
  const _$ChangeInfoImpl(
      {required this.user, required this.phone, required this.email});

  @override
  final AvemaUser user;
  @override
  final String phone;
  @override
  final String email;

  @override
  String toString() {
    return 'AuthEvent.changeInfo(user: $user, phone: $phone, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeInfoImpl &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.email, email) || other.email == email));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user, phone, email);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeInfoImplCopyWith<_$ChangeInfoImpl> get copyWith =>
      __$$ChangeInfoImplCopyWithImpl<_$ChangeInfoImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String username, String password,
            String? firebaseToken, bool savePassword)
        login,
    required TResult Function(AvemaUser avemaUser, String? firebaseToken)
        autoAuthentication,
    required TResult Function(AvemaUser avemaUser, String? firebaseToken)
        logout,
    required TResult Function(AvemaUser user, String phone, String email)
        changeInfo,
  }) {
    return changeInfo(user, phone, email);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String username, String password, String? firebaseToken,
            bool savePassword)?
        login,
    TResult? Function(AvemaUser avemaUser, String? firebaseToken)?
        autoAuthentication,
    TResult? Function(AvemaUser avemaUser, String? firebaseToken)? logout,
    TResult? Function(AvemaUser user, String phone, String email)? changeInfo,
  }) {
    return changeInfo?.call(user, phone, email);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String username, String password, String? firebaseToken,
            bool savePassword)?
        login,
    TResult Function(AvemaUser avemaUser, String? firebaseToken)?
        autoAuthentication,
    TResult Function(AvemaUser avemaUser, String? firebaseToken)? logout,
    TResult Function(AvemaUser user, String phone, String email)? changeInfo,
    required TResult orElse(),
  }) {
    if (changeInfo != null) {
      return changeInfo(user, phone, email);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Login value) login,
    required TResult Function(_AutoAuthentication value) autoAuthentication,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeInfo value) changeInfo,
  }) {
    return changeInfo(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Login value)? login,
    TResult? Function(_AutoAuthentication value)? autoAuthentication,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeInfo value)? changeInfo,
  }) {
    return changeInfo?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Login value)? login,
    TResult Function(_AutoAuthentication value)? autoAuthentication,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeInfo value)? changeInfo,
    required TResult orElse(),
  }) {
    if (changeInfo != null) {
      return changeInfo(this);
    }
    return orElse();
  }
}

abstract class _ChangeInfo implements AuthEvent {
  const factory _ChangeInfo(
      {required final AvemaUser user,
      required final String phone,
      required final String email}) = _$ChangeInfoImpl;

  AvemaUser get user;
  String get phone;
  String get email;
  @JsonKey(ignore: true)
  _$$ChangeInfoImplCopyWith<_$ChangeInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$AuthState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AuthFailure? error, AvemaUser? previousUser)
        unauthenticated,
    required TResult Function(
            AvemaUser user, bool isAutoAuthen, bool authenByLogin)
        authenticated,
    required TResult Function() loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AuthFailure? error, AvemaUser? previousUser)?
        unauthenticated,
    TResult? Function(AvemaUser user, bool isAutoAuthen, bool authenByLogin)?
        authenticated,
    TResult? Function()? loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AuthFailure? error, AvemaUser? previousUser)?
        unauthenticated,
    TResult Function(AvemaUser user, bool isAutoAuthen, bool authenByLogin)?
        authenticated,
    TResult Function()? loading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Loading value) loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Loading value)? loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Loading value)? loading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthStateCopyWith<$Res> {
  factory $AuthStateCopyWith(AuthState value, $Res Function(AuthState) then) =
      _$AuthStateCopyWithImpl<$Res, AuthState>;
}

/// @nodoc
class _$AuthStateCopyWithImpl<$Res, $Val extends AuthState>
    implements $AuthStateCopyWith<$Res> {
  _$AuthStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$UnauthenticatedImplCopyWith<$Res> {
  factory _$$UnauthenticatedImplCopyWith(_$UnauthenticatedImpl value,
          $Res Function(_$UnauthenticatedImpl) then) =
      __$$UnauthenticatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AuthFailure? error, AvemaUser? previousUser});

  $AuthFailureCopyWith<$Res>? get error;
  $AvemaUserCopyWith<$Res>? get previousUser;
}

/// @nodoc
class __$$UnauthenticatedImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$UnauthenticatedImpl>
    implements _$$UnauthenticatedImplCopyWith<$Res> {
  __$$UnauthenticatedImplCopyWithImpl(
      _$UnauthenticatedImpl _value, $Res Function(_$UnauthenticatedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
    Object? previousUser = freezed,
  }) {
    return _then(_$UnauthenticatedImpl(
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AuthFailure?,
      previousUser: freezed == previousUser
          ? _value.previousUser
          : previousUser // ignore: cast_nullable_to_non_nullable
              as AvemaUser?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $AuthFailureCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AuthFailureCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value));
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AvemaUserCopyWith<$Res>? get previousUser {
    if (_value.previousUser == null) {
      return null;
    }

    return $AvemaUserCopyWith<$Res>(_value.previousUser!, (value) {
      return _then(_value.copyWith(previousUser: value));
    });
  }
}

/// @nodoc

class _$UnauthenticatedImpl implements _Unauthenticated {
  const _$UnauthenticatedImpl(
      {required this.error, required this.previousUser});

  @override
  final AuthFailure? error;
  @override
  final AvemaUser? previousUser;

  @override
  String toString() {
    return 'AuthState.unauthenticated(error: $error, previousUser: $previousUser)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnauthenticatedImpl &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.previousUser, previousUser) ||
                other.previousUser == previousUser));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error, previousUser);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UnauthenticatedImplCopyWith<_$UnauthenticatedImpl> get copyWith =>
      __$$UnauthenticatedImplCopyWithImpl<_$UnauthenticatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AuthFailure? error, AvemaUser? previousUser)
        unauthenticated,
    required TResult Function(
            AvemaUser user, bool isAutoAuthen, bool authenByLogin)
        authenticated,
    required TResult Function() loading,
  }) {
    return unauthenticated(error, previousUser);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AuthFailure? error, AvemaUser? previousUser)?
        unauthenticated,
    TResult? Function(AvemaUser user, bool isAutoAuthen, bool authenByLogin)?
        authenticated,
    TResult? Function()? loading,
  }) {
    return unauthenticated?.call(error, previousUser);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AuthFailure? error, AvemaUser? previousUser)?
        unauthenticated,
    TResult Function(AvemaUser user, bool isAutoAuthen, bool authenByLogin)?
        authenticated,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(error, previousUser);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Loading value) loading,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Loading value)? loading,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Loading value)? loading,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _Unauthenticated implements AuthState {
  const factory _Unauthenticated(
      {required final AuthFailure? error,
      required final AvemaUser? previousUser}) = _$UnauthenticatedImpl;

  AuthFailure? get error;
  AvemaUser? get previousUser;
  @JsonKey(ignore: true)
  _$$UnauthenticatedImplCopyWith<_$UnauthenticatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AuthenticatedImplCopyWith<$Res> {
  factory _$$AuthenticatedImplCopyWith(
          _$AuthenticatedImpl value, $Res Function(_$AuthenticatedImpl) then) =
      __$$AuthenticatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AvemaUser user, bool isAutoAuthen, bool authenByLogin});

  $AvemaUserCopyWith<$Res> get user;
}

/// @nodoc
class __$$AuthenticatedImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$AuthenticatedImpl>
    implements _$$AuthenticatedImplCopyWith<$Res> {
  __$$AuthenticatedImplCopyWithImpl(
      _$AuthenticatedImpl _value, $Res Function(_$AuthenticatedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
    Object? isAutoAuthen = null,
    Object? authenByLogin = null,
  }) {
    return _then(_$AuthenticatedImpl(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as AvemaUser,
      isAutoAuthen: null == isAutoAuthen
          ? _value.isAutoAuthen
          : isAutoAuthen // ignore: cast_nullable_to_non_nullable
              as bool,
      authenByLogin: null == authenByLogin
          ? _value.authenByLogin
          : authenByLogin // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $AvemaUserCopyWith<$Res> get user {
    return $AvemaUserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$AuthenticatedImpl implements _Authenticated {
  const _$AuthenticatedImpl(
      {required this.user,
      required this.isAutoAuthen,
      required this.authenByLogin});

  @override
  final AvemaUser user;
  @override
  final bool isAutoAuthen;
  @override
  final bool authenByLogin;

  @override
  String toString() {
    return 'AuthState.authenticated(user: $user, isAutoAuthen: $isAutoAuthen, authenByLogin: $authenByLogin)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthenticatedImpl &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.isAutoAuthen, isAutoAuthen) ||
                other.isAutoAuthen == isAutoAuthen) &&
            (identical(other.authenByLogin, authenByLogin) ||
                other.authenByLogin == authenByLogin));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, user, isAutoAuthen, authenByLogin);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthenticatedImplCopyWith<_$AuthenticatedImpl> get copyWith =>
      __$$AuthenticatedImplCopyWithImpl<_$AuthenticatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AuthFailure? error, AvemaUser? previousUser)
        unauthenticated,
    required TResult Function(
            AvemaUser user, bool isAutoAuthen, bool authenByLogin)
        authenticated,
    required TResult Function() loading,
  }) {
    return authenticated(user, isAutoAuthen, authenByLogin);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AuthFailure? error, AvemaUser? previousUser)?
        unauthenticated,
    TResult? Function(AvemaUser user, bool isAutoAuthen, bool authenByLogin)?
        authenticated,
    TResult? Function()? loading,
  }) {
    return authenticated?.call(user, isAutoAuthen, authenByLogin);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AuthFailure? error, AvemaUser? previousUser)?
        unauthenticated,
    TResult Function(AvemaUser user, bool isAutoAuthen, bool authenByLogin)?
        authenticated,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (authenticated != null) {
      return authenticated(user, isAutoAuthen, authenByLogin);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Loading value) loading,
  }) {
    return authenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Loading value)? loading,
  }) {
    return authenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Loading value)? loading,
    required TResult orElse(),
  }) {
    if (authenticated != null) {
      return authenticated(this);
    }
    return orElse();
  }
}

abstract class _Authenticated implements AuthState {
  const factory _Authenticated(
      {required final AvemaUser user,
      required final bool isAutoAuthen,
      required final bool authenByLogin}) = _$AuthenticatedImpl;

  AvemaUser get user;
  bool get isAutoAuthen;
  bool get authenByLogin;
  @JsonKey(ignore: true)
  _$$AuthenticatedImplCopyWith<_$AuthenticatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'AuthState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AuthFailure? error, AvemaUser? previousUser)
        unauthenticated,
    required TResult Function(
            AvemaUser user, bool isAutoAuthen, bool authenByLogin)
        authenticated,
    required TResult Function() loading,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AuthFailure? error, AvemaUser? previousUser)?
        unauthenticated,
    TResult? Function(AvemaUser user, bool isAutoAuthen, bool authenByLogin)?
        authenticated,
    TResult? Function()? loading,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AuthFailure? error, AvemaUser? previousUser)?
        unauthenticated,
    TResult Function(AvemaUser user, bool isAutoAuthen, bool authenByLogin)?
        authenticated,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Loading value) loading,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Loading value)? loading,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Loading value)? loading,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements AuthState {
  const factory _Loading() = _$LoadingImpl;
}
