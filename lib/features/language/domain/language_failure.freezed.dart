// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'language_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LanguageFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LanguageFailureUnexpected value) unexpected,
    required TResult Function(_LanguageFailureUnauthorized value) unauthorized,
    required TResult Function(_LanguageFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_LanguageFailureServerError value) serverError,
    required TResult Function(_LanguageFailureNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LanguageFailureUnexpected value)? unexpected,
    TResult? Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult? Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_LanguageFailureServerError value)? serverError,
    TResult? Function(_LanguageFailureNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LanguageFailureUnexpected value)? unexpected,
    TResult Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult Function(_LanguageFailureServerError value)? serverError,
    TResult Function(_LanguageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LanguageFailureCopyWith<$Res> {
  factory $LanguageFailureCopyWith(
          LanguageFailure value, $Res Function(LanguageFailure) then) =
      _$LanguageFailureCopyWithImpl<$Res, LanguageFailure>;
}

/// @nodoc
class _$LanguageFailureCopyWithImpl<$Res, $Val extends LanguageFailure>
    implements $LanguageFailureCopyWith<$Res> {
  _$LanguageFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$LanguageFailureUnexpectedImplCopyWith<$Res> {
  factory _$$LanguageFailureUnexpectedImplCopyWith(
          _$LanguageFailureUnexpectedImpl value,
          $Res Function(_$LanguageFailureUnexpectedImpl) then) =
      __$$LanguageFailureUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$LanguageFailureUnexpectedImplCopyWithImpl<$Res>
    extends _$LanguageFailureCopyWithImpl<$Res, _$LanguageFailureUnexpectedImpl>
    implements _$$LanguageFailureUnexpectedImplCopyWith<$Res> {
  __$$LanguageFailureUnexpectedImplCopyWithImpl(
      _$LanguageFailureUnexpectedImpl _value,
      $Res Function(_$LanguageFailureUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$LanguageFailureUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$LanguageFailureUnexpectedImpl implements _LanguageFailureUnexpected {
  const _$LanguageFailureUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'LanguageFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LanguageFailureUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LanguageFailureUnexpectedImplCopyWith<_$LanguageFailureUnexpectedImpl>
      get copyWith => __$$LanguageFailureUnexpectedImplCopyWithImpl<
          _$LanguageFailureUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LanguageFailureUnexpected value) unexpected,
    required TResult Function(_LanguageFailureUnauthorized value) unauthorized,
    required TResult Function(_LanguageFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_LanguageFailureServerError value) serverError,
    required TResult Function(_LanguageFailureNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LanguageFailureUnexpected value)? unexpected,
    TResult? Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult? Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_LanguageFailureServerError value)? serverError,
    TResult? Function(_LanguageFailureNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LanguageFailureUnexpected value)? unexpected,
    TResult Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult Function(_LanguageFailureServerError value)? serverError,
    TResult Function(_LanguageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _LanguageFailureUnexpected implements LanguageFailure {
  const factory _LanguageFailureUnexpected({required final String error}) =
      _$LanguageFailureUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$LanguageFailureUnexpectedImplCopyWith<_$LanguageFailureUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LanguageFailureUnauthorizedImplCopyWith<$Res> {
  factory _$$LanguageFailureUnauthorizedImplCopyWith(
          _$LanguageFailureUnauthorizedImpl value,
          $Res Function(_$LanguageFailureUnauthorizedImpl) then) =
      __$$LanguageFailureUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LanguageFailureUnauthorizedImplCopyWithImpl<$Res>
    extends _$LanguageFailureCopyWithImpl<$Res,
        _$LanguageFailureUnauthorizedImpl>
    implements _$$LanguageFailureUnauthorizedImplCopyWith<$Res> {
  __$$LanguageFailureUnauthorizedImplCopyWithImpl(
      _$LanguageFailureUnauthorizedImpl _value,
      $Res Function(_$LanguageFailureUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LanguageFailureUnauthorizedImpl
    implements _LanguageFailureUnauthorized {
  const _$LanguageFailureUnauthorizedImpl();

  @override
  String toString() {
    return 'LanguageFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LanguageFailureUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LanguageFailureUnexpected value) unexpected,
    required TResult Function(_LanguageFailureUnauthorized value) unauthorized,
    required TResult Function(_LanguageFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_LanguageFailureServerError value) serverError,
    required TResult Function(_LanguageFailureNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LanguageFailureUnexpected value)? unexpected,
    TResult? Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult? Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_LanguageFailureServerError value)? serverError,
    TResult? Function(_LanguageFailureNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LanguageFailureUnexpected value)? unexpected,
    TResult Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult Function(_LanguageFailureServerError value)? serverError,
    TResult Function(_LanguageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _LanguageFailureUnauthorized implements LanguageFailure {
  const factory _LanguageFailureUnauthorized() =
      _$LanguageFailureUnauthorizedImpl;
}

/// @nodoc
abstract class _$$LanguageFailureUnauthenticatedImplCopyWith<$Res> {
  factory _$$LanguageFailureUnauthenticatedImplCopyWith(
          _$LanguageFailureUnauthenticatedImpl value,
          $Res Function(_$LanguageFailureUnauthenticatedImpl) then) =
      __$$LanguageFailureUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LanguageFailureUnauthenticatedImplCopyWithImpl<$Res>
    extends _$LanguageFailureCopyWithImpl<$Res,
        _$LanguageFailureUnauthenticatedImpl>
    implements _$$LanguageFailureUnauthenticatedImplCopyWith<$Res> {
  __$$LanguageFailureUnauthenticatedImplCopyWithImpl(
      _$LanguageFailureUnauthenticatedImpl _value,
      $Res Function(_$LanguageFailureUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LanguageFailureUnauthenticatedImpl
    implements _LanguageFailureUnauthenticated {
  const _$LanguageFailureUnauthenticatedImpl();

  @override
  String toString() {
    return 'LanguageFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LanguageFailureUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LanguageFailureUnexpected value) unexpected,
    required TResult Function(_LanguageFailureUnauthorized value) unauthorized,
    required TResult Function(_LanguageFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_LanguageFailureServerError value) serverError,
    required TResult Function(_LanguageFailureNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LanguageFailureUnexpected value)? unexpected,
    TResult? Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult? Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_LanguageFailureServerError value)? serverError,
    TResult? Function(_LanguageFailureNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LanguageFailureUnexpected value)? unexpected,
    TResult Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult Function(_LanguageFailureServerError value)? serverError,
    TResult Function(_LanguageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _LanguageFailureUnauthenticated implements LanguageFailure {
  const factory _LanguageFailureUnauthenticated() =
      _$LanguageFailureUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$LanguageFailureServerErrorImplCopyWith<$Res> {
  factory _$$LanguageFailureServerErrorImplCopyWith(
          _$LanguageFailureServerErrorImpl value,
          $Res Function(_$LanguageFailureServerErrorImpl) then) =
      __$$LanguageFailureServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LanguageFailureServerErrorImplCopyWithImpl<$Res>
    extends _$LanguageFailureCopyWithImpl<$Res,
        _$LanguageFailureServerErrorImpl>
    implements _$$LanguageFailureServerErrorImplCopyWith<$Res> {
  __$$LanguageFailureServerErrorImplCopyWithImpl(
      _$LanguageFailureServerErrorImpl _value,
      $Res Function(_$LanguageFailureServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LanguageFailureServerErrorImpl implements _LanguageFailureServerError {
  const _$LanguageFailureServerErrorImpl();

  @override
  String toString() {
    return 'LanguageFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LanguageFailureServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LanguageFailureUnexpected value) unexpected,
    required TResult Function(_LanguageFailureUnauthorized value) unauthorized,
    required TResult Function(_LanguageFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_LanguageFailureServerError value) serverError,
    required TResult Function(_LanguageFailureNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LanguageFailureUnexpected value)? unexpected,
    TResult? Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult? Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_LanguageFailureServerError value)? serverError,
    TResult? Function(_LanguageFailureNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LanguageFailureUnexpected value)? unexpected,
    TResult Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult Function(_LanguageFailureServerError value)? serverError,
    TResult Function(_LanguageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _LanguageFailureServerError implements LanguageFailure {
  const factory _LanguageFailureServerError() =
      _$LanguageFailureServerErrorImpl;
}

/// @nodoc
abstract class _$$LanguageFailureNoInternetImplCopyWith<$Res> {
  factory _$$LanguageFailureNoInternetImplCopyWith(
          _$LanguageFailureNoInternetImpl value,
          $Res Function(_$LanguageFailureNoInternetImpl) then) =
      __$$LanguageFailureNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LanguageFailureNoInternetImplCopyWithImpl<$Res>
    extends _$LanguageFailureCopyWithImpl<$Res, _$LanguageFailureNoInternetImpl>
    implements _$$LanguageFailureNoInternetImplCopyWith<$Res> {
  __$$LanguageFailureNoInternetImplCopyWithImpl(
      _$LanguageFailureNoInternetImpl _value,
      $Res Function(_$LanguageFailureNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LanguageFailureNoInternetImpl implements _LanguageFailureNoInternet {
  const _$LanguageFailureNoInternetImpl();

  @override
  String toString() {
    return 'LanguageFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LanguageFailureNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LanguageFailureUnexpected value) unexpected,
    required TResult Function(_LanguageFailureUnauthorized value) unauthorized,
    required TResult Function(_LanguageFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_LanguageFailureServerError value) serverError,
    required TResult Function(_LanguageFailureNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LanguageFailureUnexpected value)? unexpected,
    TResult? Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult? Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_LanguageFailureServerError value)? serverError,
    TResult? Function(_LanguageFailureNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LanguageFailureUnexpected value)? unexpected,
    TResult Function(_LanguageFailureUnauthorized value)? unauthorized,
    TResult Function(_LanguageFailureUnauthenticated value)? unauthenticated,
    TResult Function(_LanguageFailureServerError value)? serverError,
    TResult Function(_LanguageFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _LanguageFailureNoInternet implements LanguageFailure {
  const factory _LanguageFailureNoInternet() = _$LanguageFailureNoInternetImpl;
}
