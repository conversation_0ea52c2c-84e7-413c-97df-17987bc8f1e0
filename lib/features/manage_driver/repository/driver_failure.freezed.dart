// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'driver_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DriverFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DriverFailureUnexpected value) unexpected,
    required TResult Function(_DriverFailureUnauthorized value) unauthorized,
    required TResult Function(_DriverFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DriverFailureServerError value) serverError,
    required TResult Function(_DriverFailureNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DriverFailureUnexpected value)? unexpected,
    TResult? Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult? Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_DriverFailureServerError value)? serverError,
    TResult? Function(_DriverFailureNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DriverFailureUnexpected value)? unexpected,
    TResult Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult Function(_DriverFailureServerError value)? serverError,
    TResult Function(_DriverFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverFailureCopyWith<$Res> {
  factory $DriverFailureCopyWith(
          DriverFailure value, $Res Function(DriverFailure) then) =
      _$DriverFailureCopyWithImpl<$Res, DriverFailure>;
}

/// @nodoc
class _$DriverFailureCopyWithImpl<$Res, $Val extends DriverFailure>
    implements $DriverFailureCopyWith<$Res> {
  _$DriverFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$DriverFailureUnexpectedImplCopyWith<$Res> {
  factory _$$DriverFailureUnexpectedImplCopyWith(
          _$DriverFailureUnexpectedImpl value,
          $Res Function(_$DriverFailureUnexpectedImpl) then) =
      __$$DriverFailureUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$DriverFailureUnexpectedImplCopyWithImpl<$Res>
    extends _$DriverFailureCopyWithImpl<$Res, _$DriverFailureUnexpectedImpl>
    implements _$$DriverFailureUnexpectedImplCopyWith<$Res> {
  __$$DriverFailureUnexpectedImplCopyWithImpl(
      _$DriverFailureUnexpectedImpl _value,
      $Res Function(_$DriverFailureUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$DriverFailureUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DriverFailureUnexpectedImpl implements _DriverFailureUnexpected {
  const _$DriverFailureUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'DriverFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverFailureUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverFailureUnexpectedImplCopyWith<_$DriverFailureUnexpectedImpl>
      get copyWith => __$$DriverFailureUnexpectedImplCopyWithImpl<
          _$DriverFailureUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DriverFailureUnexpected value) unexpected,
    required TResult Function(_DriverFailureUnauthorized value) unauthorized,
    required TResult Function(_DriverFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DriverFailureServerError value) serverError,
    required TResult Function(_DriverFailureNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DriverFailureUnexpected value)? unexpected,
    TResult? Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult? Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_DriverFailureServerError value)? serverError,
    TResult? Function(_DriverFailureNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DriverFailureUnexpected value)? unexpected,
    TResult Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult Function(_DriverFailureServerError value)? serverError,
    TResult Function(_DriverFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _DriverFailureUnexpected implements DriverFailure {
  const factory _DriverFailureUnexpected({required final String error}) =
      _$DriverFailureUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$DriverFailureUnexpectedImplCopyWith<_$DriverFailureUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DriverFailureUnauthorizedImplCopyWith<$Res> {
  factory _$$DriverFailureUnauthorizedImplCopyWith(
          _$DriverFailureUnauthorizedImpl value,
          $Res Function(_$DriverFailureUnauthorizedImpl) then) =
      __$$DriverFailureUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DriverFailureUnauthorizedImplCopyWithImpl<$Res>
    extends _$DriverFailureCopyWithImpl<$Res, _$DriverFailureUnauthorizedImpl>
    implements _$$DriverFailureUnauthorizedImplCopyWith<$Res> {
  __$$DriverFailureUnauthorizedImplCopyWithImpl(
      _$DriverFailureUnauthorizedImpl _value,
      $Res Function(_$DriverFailureUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DriverFailureUnauthorizedImpl implements _DriverFailureUnauthorized {
  const _$DriverFailureUnauthorizedImpl();

  @override
  String toString() {
    return 'DriverFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverFailureUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DriverFailureUnexpected value) unexpected,
    required TResult Function(_DriverFailureUnauthorized value) unauthorized,
    required TResult Function(_DriverFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DriverFailureServerError value) serverError,
    required TResult Function(_DriverFailureNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DriverFailureUnexpected value)? unexpected,
    TResult? Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult? Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_DriverFailureServerError value)? serverError,
    TResult? Function(_DriverFailureNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DriverFailureUnexpected value)? unexpected,
    TResult Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult Function(_DriverFailureServerError value)? serverError,
    TResult Function(_DriverFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _DriverFailureUnauthorized implements DriverFailure {
  const factory _DriverFailureUnauthorized() = _$DriverFailureUnauthorizedImpl;
}

/// @nodoc
abstract class _$$DriverFailureUnauthenticatedImplCopyWith<$Res> {
  factory _$$DriverFailureUnauthenticatedImplCopyWith(
          _$DriverFailureUnauthenticatedImpl value,
          $Res Function(_$DriverFailureUnauthenticatedImpl) then) =
      __$$DriverFailureUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DriverFailureUnauthenticatedImplCopyWithImpl<$Res>
    extends _$DriverFailureCopyWithImpl<$Res,
        _$DriverFailureUnauthenticatedImpl>
    implements _$$DriverFailureUnauthenticatedImplCopyWith<$Res> {
  __$$DriverFailureUnauthenticatedImplCopyWithImpl(
      _$DriverFailureUnauthenticatedImpl _value,
      $Res Function(_$DriverFailureUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DriverFailureUnauthenticatedImpl
    implements _DriverFailureUnauthenticated {
  const _$DriverFailureUnauthenticatedImpl();

  @override
  String toString() {
    return 'DriverFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverFailureUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DriverFailureUnexpected value) unexpected,
    required TResult Function(_DriverFailureUnauthorized value) unauthorized,
    required TResult Function(_DriverFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DriverFailureServerError value) serverError,
    required TResult Function(_DriverFailureNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DriverFailureUnexpected value)? unexpected,
    TResult? Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult? Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_DriverFailureServerError value)? serverError,
    TResult? Function(_DriverFailureNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DriverFailureUnexpected value)? unexpected,
    TResult Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult Function(_DriverFailureServerError value)? serverError,
    TResult Function(_DriverFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _DriverFailureUnauthenticated implements DriverFailure {
  const factory _DriverFailureUnauthenticated() =
      _$DriverFailureUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$DriverFailureServerErrorImplCopyWith<$Res> {
  factory _$$DriverFailureServerErrorImplCopyWith(
          _$DriverFailureServerErrorImpl value,
          $Res Function(_$DriverFailureServerErrorImpl) then) =
      __$$DriverFailureServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DriverFailureServerErrorImplCopyWithImpl<$Res>
    extends _$DriverFailureCopyWithImpl<$Res, _$DriverFailureServerErrorImpl>
    implements _$$DriverFailureServerErrorImplCopyWith<$Res> {
  __$$DriverFailureServerErrorImplCopyWithImpl(
      _$DriverFailureServerErrorImpl _value,
      $Res Function(_$DriverFailureServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DriverFailureServerErrorImpl implements _DriverFailureServerError {
  const _$DriverFailureServerErrorImpl();

  @override
  String toString() {
    return 'DriverFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverFailureServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DriverFailureUnexpected value) unexpected,
    required TResult Function(_DriverFailureUnauthorized value) unauthorized,
    required TResult Function(_DriverFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DriverFailureServerError value) serverError,
    required TResult Function(_DriverFailureNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DriverFailureUnexpected value)? unexpected,
    TResult? Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult? Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_DriverFailureServerError value)? serverError,
    TResult? Function(_DriverFailureNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DriverFailureUnexpected value)? unexpected,
    TResult Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult Function(_DriverFailureServerError value)? serverError,
    TResult Function(_DriverFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _DriverFailureServerError implements DriverFailure {
  const factory _DriverFailureServerError() = _$DriverFailureServerErrorImpl;
}

/// @nodoc
abstract class _$$DriverFailureNoInternetImplCopyWith<$Res> {
  factory _$$DriverFailureNoInternetImplCopyWith(
          _$DriverFailureNoInternetImpl value,
          $Res Function(_$DriverFailureNoInternetImpl) then) =
      __$$DriverFailureNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DriverFailureNoInternetImplCopyWithImpl<$Res>
    extends _$DriverFailureCopyWithImpl<$Res, _$DriverFailureNoInternetImpl>
    implements _$$DriverFailureNoInternetImplCopyWith<$Res> {
  __$$DriverFailureNoInternetImplCopyWithImpl(
      _$DriverFailureNoInternetImpl _value,
      $Res Function(_$DriverFailureNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DriverFailureNoInternetImpl implements _DriverFailureNoInternet {
  const _$DriverFailureNoInternetImpl();

  @override
  String toString() {
    return 'DriverFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverFailureNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DriverFailureUnexpected value) unexpected,
    required TResult Function(_DriverFailureUnauthorized value) unauthorized,
    required TResult Function(_DriverFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_DriverFailureServerError value) serverError,
    required TResult Function(_DriverFailureNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DriverFailureUnexpected value)? unexpected,
    TResult? Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult? Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_DriverFailureServerError value)? serverError,
    TResult? Function(_DriverFailureNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DriverFailureUnexpected value)? unexpected,
    TResult Function(_DriverFailureUnauthorized value)? unauthorized,
    TResult Function(_DriverFailureUnauthenticated value)? unauthenticated,
    TResult Function(_DriverFailureServerError value)? serverError,
    TResult Function(_DriverFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _DriverFailureNoInternet implements DriverFailure {
  const factory _DriverFailureNoInternet() = _$DriverFailureNoInternetImpl;
}
