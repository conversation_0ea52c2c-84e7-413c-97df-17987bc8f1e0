import 'package:avema_v2/application/enums/avema_enums.dart';
import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:avema_v2/features/language/languages_uti.dart';
import 'package:avema_v2/features/theme/avema_text_style.dart';
import 'package:flutter/material.dart';

class BuildChipWidget extends StatelessWidget {
  final DriverSortType label;
  final void Function(DriverSortType)? onTap;
  final bool isSelected;
  final int? count;
  final SortDirection sortDirection;
  final bool isLoading;
  final void Function()? onCancel;

  const BuildChipWidget({
    super.key,
    required this.label,
    this.onTap,
    this.isSelected = false,
    this.count,
    this.sortDirection = SortDirection.none,
    this.isLoading = false,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    final String labelTranslated = getSortTypeString(label);

    return Stack(
      clipBehavior: Clip.none,
      children: [
        ActionChip(
          onPressed: () {
            if (isLoading) return;
            onTap?.call(label);
          },
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          label: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                labelTranslated,
                style: AvemaTextStyle.bodyMedium.copyWith(
                  color: isSelected ? theme.onPrimary : theme.onSurface,
                  fontWeight: isSelected ? FontWeight.w500 : null,
                ),
              ),
              context.horizontalSpaceS,
              _buildSortIcon(theme),
            ],
          ),
          backgroundColor: isSelected ? theme.primary : theme.surface,
          side: BorderSide(
            color: theme.primary,
            width: isSelected ? 2 : 1,
          ),
        ),
        if (isSelected)
          Positioned(
            top: 3,
            right: 0,
            child: GestureDetector(
              onTap: onCancel,
              child: Icon(
                Icons.cancel,
                color: theme.surface,
                size: 16,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSortIcon(ColorScheme theme) {
    final color = isSelected ? theme.onPrimary : theme.onSurface;

    switch (sortDirection) {
      case SortDirection.none:
        return Icon(
          Icons.sort,
          size: 16,
          color: color,
        );
      case SortDirection.ascending:
        return Icon(
          Icons.arrow_upward,
          size: 16,
          color: color,
        );
      case SortDirection.descending:
        return Icon(
          Icons.arrow_downward,
          size: 16,
          color: color,
        );
    }
  }

  String getSortTypeString(DriverSortType sortType) {
    final language = AVELanguages.instance;

    switch (sortType) {
      case DriverSortType.city:
        return language.city;
      case DriverSortType.rfid:
        return language.rfid;
      case DriverSortType.driverNo:
        return language.driverNo;
      case DriverSortType.name:
        return language.name;
      case DriverSortType.licenseNo:
        return language.licenseNo;
      case DriverSortType.phone:
        return language.phone;
      case DriverSortType.phone2:
        return '${language.phone2} ';
      default:
        return '';
    }
  }
}
