import 'package:avema_v2/application/enums/avema_enums.dart';
import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:flutter/material.dart';

import 'build_chip_widget.dart';

class DriverSortChipList extends StatelessWidget {
  final GlobalKey Function(int index) getChipKey;
  final ScrollController chipController;
  final void Function(int index) scrollToSelectedChip;
  final void Function(DriverSortType sortType) onSortTypeSelected;
  final List<DriverSortType> listSortType;
  final bool isLoading;
  final SortDirection sortDirection;
  final DriverSortType? sortTypeSelected;
  final void Function()? onCancel;

  const DriverSortChipList({
    super.key,
    required this.chipController,
    required this.scrollToSelectedChip,
    required this.getChipKey,
    required this.onSortTypeSelected,
    required this.listSortType,
    required this.isLoading,
    required this.sortDirection,
    this.sortTypeSelected,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      padding: context.pdSymHorizontalL,
      child: ListView.separated(
        controller: chipController,
        padding: EdgeInsets.zero,
        scrollDirection: Axis.horizontal,
        separatorBuilder: (_, __) => context.horizontalSpaceM,
        itemCount: listSortType.length,
        itemBuilder: (context, index) {
          final item = listSortType[index];
          final isSelected = item == sortTypeSelected;

          return BuildChipWidget(
            isLoading: isLoading,
            key: getChipKey(index),
            label: item,
            isSelected: isSelected,
            sortDirection: isSelected ? sortDirection : SortDirection.none,
            onTap: (type) {
              onSortTypeSelected(type);
              scrollToSelectedChip(index);
            },
            onCancel: onCancel,
          );
        },
      ),
    );
  }
}
