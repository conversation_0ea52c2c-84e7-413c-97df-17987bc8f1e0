import 'dart:async';

import 'package:avema_v2/application/enums/avema_enums.dart';
import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:avema_v2/application/mixins/chip_scroll_mixin.dart';
import 'package:avema_v2/application/mixins/scroll_mixin.dart';
import 'package:avema_v2/application/utils/avema_dialog_util.dart';
import 'package:avema_v2/application/utils/avema_loading.dart';
import 'package:avema_v2/application/utils/loading_overlay_util.dart';
import 'package:avema_v2/features/language/languages_uti.dart';
import 'package:avema_v2/features/manage_driver/application/manage_driver_bloc.dart';
import 'package:avema_v2/features/manage_driver/data/get_driver_request.dart';
import 'package:avema_v2/features/manage_driver/repository/driver_repository.dart';
import 'package:avema_v2/features/theme/avema_text_style.dart';
import 'package:avema_v2/uis/animated_button.dart';
import 'package:avema_v2/uis/avema_empty_view.dart';
import 'package:avema_v2/uis/avema_scaffold.dart';
import 'package:avema_v2/uis/avema_snack_bar.dart';
import 'package:avema_v2/uis/avema_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'add_update_driver_screen.dart';
import 'widgets/chip_list_shimmer.dart';
import 'widgets/driver_list.dart';
import 'widgets/driver_sort_chip_list.dart';

class ManageDriverScreen extends StatefulWidget {
  const ManageDriverScreen({super.key});

  @override
  State<ManageDriverScreen> createState() => _ManageDriverScreenState();
}

class _ManageDriverScreenState extends State<ManageDriverScreen>
    with ChipScrollMixin, ScrollMixin {
  late ManageDriverBloc _bloc;
  late DriverRepository driverRepository;
  late TextEditingController _searchController;
  Timer? _debounce;
  bool _isFirstLoad = true;

  @override
  void initState() {
    super.initState();
    initChipScroll();
    initScroll(() => _bloc.add(LoadMoreDriverEvent()));
    _searchController = TextEditingController();
    _getData();
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _searchController.dispose();
    disposeScroll();
    disposeChipScroll();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final language = AVELanguages.instance;
    final theme = Theme.of(context).colorScheme;
    return BlocConsumer<ManageDriverBloc, ManageDriverState>(
      bloc: _bloc,
      listenWhen: (previous, current) {
        return previous.addDriverStatus != current.addDriverStatus ||
            previous.deleteDriverStatus != current.deleteDriverStatus ||
            previous.updateDriverStatus != current.updateDriverStatus;
      },
      listener: (context, state) {
        final addDriverStatus = state.addDriverStatus;
        final deleteDriverStatus = state.deleteDriverStatus;
        final updateDriverStatus = state.updateDriverStatus;
        if (addDriverStatus == AddDriverStatus.success) {
          _getDrivers();
        }
        if (deleteDriverStatus == DeleteDriverStatus.loading) {
          LoadingOverlayUtil.show(context);
        } else if (deleteDriverStatus == DeleteDriverStatus.success ||
            updateDriverStatus == UpdateDriverStatus.success) {
          if (deleteDriverStatus == DeleteDriverStatus.success) {
            _bloc.add(ClearImageEvent());
          }
          LoadingOverlayUtil.hide();
          AvemaOverlay.showSnackbar(
            context: context,
            type: SnackbarType.success,
            content: state.message ?? "",
          );
          _getDrivers();
        } else if (deleteDriverStatus == DeleteDriverStatus.failure) {
          LoadingOverlayUtil.hide();
          AvemaOverlay.showSnackbar(
            context: context,
            type: SnackbarType.information,
            content: state.message ?? "",
          );
        }
        _bloc.add(ResetStateEvent());
      },
      builder: (context, state) {
        final itemCount = state.listDriverSorted?.length ?? 0;
        final length = itemCount > 0 ? "($itemCount)" : "";
        final titleAppbar = '${language.driver} $length';
        final isLoading = state.status == ManageDriverStatus.loading;
        final drivers = state.listDriverSorted ?? [];
        final driversEmpty = drivers.isEmpty == true;

        if (state.status == ManageDriverStatus.success && _isFirstLoad) {
          _isFirstLoad = false;
        }

        return AvemaScaffold(
          title: titleAppbar,
          action: const SizedBox.shrink(),
          floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
          floatingActionButton: AvemaButton(
            onTap: isLoading
                ? null
                : () => context.push(
                      AddUpdateDriverScreen(bloc: _bloc),
                    ),
            width: 50,
            height: 50,
            borderRadius: const BorderRadius.all(Radius.circular(50)),
            color: theme.primary,
            child: const Icon(
              Icons.add,
              color: Colors.white,
              size: 24,
            ),
          ),
          onBackTap: () => _bloc.add(ResetStateEvent()),
          body: Column(
            children: [
              context.verticalSpaceL,
              Padding(
                padding: context.pdSymHorizontalL,
                child: AvemaTextFormField(
                  enabled: !isLoading,
                  contentPadding: context.pdSymHorizontalL,
                  labelText: language.search,
                  hintText: language.searchDriver,
                  controller: _searchController,
                  hintStyle: AvemaTextStyle.bodySmall.copyWith(
                    color: theme.onSurface.withOpacity(0.4),
                  ),
                  prefixIcon: const Icon(
                    Icons.search,
                    color: Colors.grey,
                  ),
                  suffixIcon: _searchController.text.isEmpty
                      ? const SizedBox()
                      : AvemaButton(
                          color: Colors.transparent,
                          onTap: onClearSearch,
                          child: const Icon(
                            Icons.clear,
                            color: Colors.grey,
                          ),
                        ),
                  onChanged: (text) {
                    _onSearchTriggered(text.trim());
                  },
                ),
              ),
              context.verticalSpaceL,
              isLoading && _isFirstLoad
                  ? const ChipListShimmer()
                  : DriverSortChipList(
                      chipController: chipController,
                      getChipKey: getChipKey,
                      listSortType: state.listSortType,
                      isLoading: isLoading,
                      sortDirection: state.sortDirection,
                      sortTypeSelected: state.sortTypeSelected,
                      scrollToSelectedChip: (index) {
                        scrollToSelectedChip(index);
                        scrollController.jumpTo(0);
                      },
                      onSortTypeSelected: (type) {
                        final listDriver =
                            state.listDriverSearched ?? state.listDriver!;
                        _bloc.add(
                          SortDriversByType(
                            listDriver: listDriver,
                            sortType: type,
                            sortDirection: state.sortDirection,
                          ),
                        );
                      },
                      onCancel: () {
                        _bloc.add(ClearSortEvent());
                      },
                    ),
              if (!(isLoading && _isFirstLoad))
                Divider(
                  color: theme.onSurface.withOpacity(0.1),
                  thickness: 1,
                  endIndent: 16,
                  indent: 16,
                ),
              switch (state.status) {
                ManageDriverStatus.loading => _isFirstLoad
                    ? const AvemaLoading()
                    : driversEmpty
                        ? const AvemaEmptyView()
                        : DriverList(
                            scrollController: scrollController,
                            state: state,
                            imageUrl: state.driverPhoto,
                            onShowConfirmDelete: (driverId) =>
                                _showConfirmDelete(driverId),
                            onNavigateToEdit: (driverId) => context.push(
                              AddUpdateDriverScreen(
                                bloc: _bloc,
                                driverId: driverId,
                                isPhoto: state.driverDetail?.photo,
                              ),
                            ),
                          ),
                ManageDriverStatus.success => driversEmpty
                    ? const AvemaEmptyView()
                    : DriverList(
                        scrollController: scrollController,
                        state: state,
                        imageUrl: state.driverPhoto,
                        onShowConfirmDelete: (driverId) =>
                            _showConfirmDelete(driverId),
                        onNavigateToEdit: (driverId) => context.push(
                          AddUpdateDriverScreen(
                            bloc: _bloc,
                            driverId: driverId,
                            isPhoto: state.driverDetail?.photo,
                          ),
                        ),
                      ),
                ManageDriverStatus.failure => const AvemaEmptyView(),
                _ => const AvemaEmptyView(),
              }
            ],
          ),
        );
      },
    );
  }

  void onClearSearch() {
    _searchController.clear();
    _bloc.add(ClearSearchDriverEvent());
    FocusManager.instance.primaryFocus?.unfocus();
  }

  void _onSearchTriggered(String text) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      _bloc.add(SearchDriverEvent(searchKey: text));
      if (scrollController.hasClients) {
        scrollController.jumpTo(0);
      }
    });
  }

  void _showConfirmDelete(String? driverId) {
    final language = AVELanguages.instance;
    AvemaDialogUtil.show(
      context,
      content: language.contentDeleteDriver,
      onYes: () {
        if (driverId == null) return;
        _bloc.add(
          DeleteDriverEvent(
            id: driverId,
          ),
        );
      },
    );
  }

  void _getData() {
    driverRepository = DriverRepository();
    _bloc = ManageDriverBloc(driverRepository);
    _getDrivers();
    _bloc.add(GetCityEvent());
    _bloc.add(GetPlateEvent());
  }

  void _getDrivers() {
    _bloc.add(
      GetListDriverEvent(
        request: const GetDriverRequest(),
      ),
    );
  }
}
