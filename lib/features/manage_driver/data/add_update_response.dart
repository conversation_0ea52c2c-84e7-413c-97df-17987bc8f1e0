// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_update_response.freezed.dart';
part 'add_update_response.g.dart';

@freezed
class AddUpdateResponse with _$AddUpdateResponse {
  const factory AddUpdateResponse({
    int? state,
    String? keyLanguage,
    String? uiKeyLanguage,
    List<dynamic>? keyLanguages,
    dynamic value,
    String? forms,
  }) = _AddUpdateResponse;

  factory AddUpdateResponse.fromJson(Map<String, dynamic> json) =>
      _$AddUpdateResponseFromJson(json);

  const AddUpdateResponse._();
}
