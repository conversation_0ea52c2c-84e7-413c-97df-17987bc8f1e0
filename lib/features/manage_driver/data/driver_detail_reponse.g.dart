// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'driver_detail_reponse.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DriverDetailResponseImpl _$$DriverDetailResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$DriverDetailResponseImpl(
      id: json['id'] as String? ?? "",
      cityId: json['cityId'] as String,
      rfid: json['rfid'] as String? ?? "",
      name: json['name'] as String,
      phone: json['phone'] as String? ?? "",
      phone2: json['phone2'] as String? ?? "",
      address: json['address'] as String? ?? "",
      driverNo: json['driverNo'] as String? ?? "",
      email: json['email'] as String? ?? "",
      licenseNo: json['licenseNo'] as String? ?? "",
      registerPlace: json['registerPlace'] as String? ?? "",
      username: json['username'] as String? ?? "",
      isActivated: json['isActivated'] as bool? ?? false,
      registerDate: json['registerDate'] as String? ?? "",
      expiredDate: json['expiredDate'] as String? ?? "",
      photo: json['photo'] as bool? ?? false,
      fields: json['fields'] as Map<String, dynamic>? ?? const {},
      vehicleId: json['vehicleId'] as String? ?? "",
    );

Map<String, dynamic> _$$DriverDetailResponseImplToJson(
        _$DriverDetailResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'cityId': instance.cityId,
      'rfid': instance.rfid,
      'name': instance.name,
      'phone': instance.phone,
      'phone2': instance.phone2,
      'address': instance.address,
      'driverNo': instance.driverNo,
      'email': instance.email,
      'licenseNo': instance.licenseNo,
      'registerPlace': instance.registerPlace,
      'username': instance.username,
      'isActivated': instance.isActivated,
      'registerDate': instance.registerDate,
      'expiredDate': instance.expiredDate,
      'photo': instance.photo,
      'fields': instance.fields,
      'vehicleId': instance.vehicleId,
    };
