import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_driver_request.freezed.dart';
part 'get_driver_request.g.dart';

@freezed
class GetDriverRequest with _$GetDriverRequest {
  const factory GetDriverRequest({
    required String page,
    required String pageSize,
    @Default('') String? sort,
    @Default('') String? search,
  }) = _GetDriverRequest;

  factory GetDriverRequest.fromJson(Map<String, dynamic> json) =>
      _$GetDriverRequestFromJson(json);

  const GetDriverRequest._();
}
