// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'driver_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DriverResponseImpl _$$DriverResponseImplFromJson(Map<String, dynamic> json) =>
    _$DriverResponseImpl(
      total: (json['total'] as num).toInt(),
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => DriverData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$DriverResponseImplToJson(
        _$DriverResponseImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'data': instance.data,
    };

_$DriverDataImpl _$$DriverDataImplFromJson(Map<String, dynamic> json) =>
    _$DriverDataImpl(
      id: json['Id'] as String,
      rfid: json['RFID'] as String? ?? "",
      name: json['Name'] as String? ?? "",
      phone: json['Phone'] as String? ?? "",
      phone2: json['Phone2'] as String? ?? "",
      address: json['Address'] as String? ?? "",
      company: json['Company'] as String? ?? "",
      city: json['City'] as String? ?? "",
      driverNo: json['DriverNo'] as String? ?? "",
      licenseNo: json['LicenseNo'] as String? ?? "",
      registerPlace: json['RegisterPlace'] as String? ?? "",
      registerDate: json['RegisterDate'] as String? ?? "",
      expiredDate: json['ExpiredDate'] as String? ?? "",
      photo: json['Photo'] as bool? ?? false,
      isActivated: json['isActivated'] as bool? ?? false,
    );

Map<String, dynamic> _$$DriverDataImplToJson(_$DriverDataImpl instance) =>
    <String, dynamic>{
      'Id': instance.id,
      'RFID': instance.rfid,
      'Name': instance.name,
      'Phone': instance.phone,
      'Phone2': instance.phone2,
      'Address': instance.address,
      'Company': instance.company,
      'City': instance.city,
      'DriverNo': instance.driverNo,
      'LicenseNo': instance.licenseNo,
      'RegisterPlace': instance.registerPlace,
      'RegisterDate': instance.registerDate,
      'ExpiredDate': instance.expiredDate,
      'Photo': instance.photo,
      'isActivated': instance.isActivated,
    };
