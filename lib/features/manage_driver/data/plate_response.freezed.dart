// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'plate_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PlateResponse _$PlateResponseFromJson(Map<String, dynamic> json) {
  return _PlateResponse.fromJson(json);
}

/// @nodoc
mixin _$PlateResponse {
  String get value => throw _privateConstructorUsedError;
  String get label => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlateResponseCopyWith<PlateResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlateResponseCopyWith<$Res> {
  factory $PlateResponseCopyWith(
          PlateResponse value, $Res Function(PlateResponse) then) =
      _$PlateResponseCopyWithImpl<$Res, PlateResponse>;
  @useResult
  $Res call({String value, String label});
}

/// @nodoc
class _$PlateResponseCopyWithImpl<$Res, $Val extends PlateResponse>
    implements $PlateResponseCopyWith<$Res> {
  _$PlateResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
    Object? label = null,
  }) {
    return _then(_value.copyWith(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PlateResponseImplCopyWith<$Res>
    implements $PlateResponseCopyWith<$Res> {
  factory _$$PlateResponseImplCopyWith(
          _$PlateResponseImpl value, $Res Function(_$PlateResponseImpl) then) =
      __$$PlateResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String value, String label});
}

/// @nodoc
class __$$PlateResponseImplCopyWithImpl<$Res>
    extends _$PlateResponseCopyWithImpl<$Res, _$PlateResponseImpl>
    implements _$$PlateResponseImplCopyWith<$Res> {
  __$$PlateResponseImplCopyWithImpl(
      _$PlateResponseImpl _value, $Res Function(_$PlateResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
    Object? label = null,
  }) {
    return _then(_$PlateResponseImpl(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PlateResponseImpl extends _PlateResponse {
  const _$PlateResponseImpl({this.value = "", this.label = ""}) : super._();

  factory _$PlateResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlateResponseImplFromJson(json);

  @override
  @JsonKey()
  final String value;
  @override
  @JsonKey()
  final String label;

  @override
  String toString() {
    return 'PlateResponse(value: $value, label: $label)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlateResponseImpl &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.label, label) || other.label == label));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, value, label);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PlateResponseImplCopyWith<_$PlateResponseImpl> get copyWith =>
      __$$PlateResponseImplCopyWithImpl<_$PlateResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlateResponseImplToJson(
      this,
    );
  }
}

abstract class _PlateResponse extends PlateResponse {
  const factory _PlateResponse({final String value, final String label}) =
      _$PlateResponseImpl;
  const _PlateResponse._() : super._();

  factory _PlateResponse.fromJson(Map<String, dynamic> json) =
      _$PlateResponseImpl.fromJson;

  @override
  String get value;
  @override
  String get label;
  @override
  @JsonKey(ignore: true)
  _$$PlateResponseImplCopyWith<_$PlateResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
