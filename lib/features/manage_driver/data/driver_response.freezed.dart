// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'driver_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DriverResponse _$DriverResponseFromJson(Map<String, dynamic> json) {
  return _DriverResponse.fromJson(json);
}

/// @nodoc
mixin _$DriverResponse {
  int get total => throw _privateConstructorUsedError;
  List<DriverData>? get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DriverResponseCopyWith<DriverResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverResponseCopyWith<$Res> {
  factory $DriverResponseCopyWith(
          DriverResponse value, $Res Function(DriverResponse) then) =
      _$DriverResponseCopyWithImpl<$Res, DriverResponse>;
  @useResult
  $Res call({int total, List<DriverData>? data});
}

/// @nodoc
class _$DriverResponseCopyWithImpl<$Res, $Val extends DriverResponse>
    implements $DriverResponseCopyWith<$Res> {
  _$DriverResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = freezed,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<DriverData>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DriverResponseImplCopyWith<$Res>
    implements $DriverResponseCopyWith<$Res> {
  factory _$$DriverResponseImplCopyWith(_$DriverResponseImpl value,
          $Res Function(_$DriverResponseImpl) then) =
      __$$DriverResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, List<DriverData>? data});
}

/// @nodoc
class __$$DriverResponseImplCopyWithImpl<$Res>
    extends _$DriverResponseCopyWithImpl<$Res, _$DriverResponseImpl>
    implements _$$DriverResponseImplCopyWith<$Res> {
  __$$DriverResponseImplCopyWithImpl(
      _$DriverResponseImpl _value, $Res Function(_$DriverResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = freezed,
  }) {
    return _then(_$DriverResponseImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: freezed == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<DriverData>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverResponseImpl extends _DriverResponse {
  const _$DriverResponseImpl(
      {required this.total, final List<DriverData>? data})
      : _data = data,
        super._();

  factory _$DriverResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverResponseImplFromJson(json);

  @override
  final int total;
  final List<DriverData>? _data;
  @override
  List<DriverData>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'DriverResponse(total: $total, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverResponseImpl &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, total, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverResponseImplCopyWith<_$DriverResponseImpl> get copyWith =>
      __$$DriverResponseImplCopyWithImpl<_$DriverResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverResponseImplToJson(
      this,
    );
  }
}

abstract class _DriverResponse extends DriverResponse {
  const factory _DriverResponse(
      {required final int total,
      final List<DriverData>? data}) = _$DriverResponseImpl;
  const _DriverResponse._() : super._();

  factory _DriverResponse.fromJson(Map<String, dynamic> json) =
      _$DriverResponseImpl.fromJson;

  @override
  int get total;
  @override
  List<DriverData>? get data;
  @override
  @JsonKey(ignore: true)
  _$$DriverResponseImplCopyWith<_$DriverResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DriverData _$DriverDataFromJson(Map<String, dynamic> json) {
  return _DriverData.fromJson(json);
}

/// @nodoc
mixin _$DriverData {
  @JsonKey(name: 'Id')
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'RFID')
  String? get rfid => throw _privateConstructorUsedError;
  @JsonKey(name: 'Name')
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'Phone')
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: 'Phone2')
  String? get phone2 => throw _privateConstructorUsedError;
  @JsonKey(name: 'Address')
  String? get address => throw _privateConstructorUsedError;
  @JsonKey(name: 'Company')
  String? get company => throw _privateConstructorUsedError;
  @JsonKey(name: 'City')
  String? get city => throw _privateConstructorUsedError;
  @JsonKey(name: 'DriverNo')
  String? get driverNo => throw _privateConstructorUsedError;
  @JsonKey(name: 'LicenseNo')
  String? get licenseNo => throw _privateConstructorUsedError;
  @JsonKey(name: 'RegisterPlace')
  String? get registerPlace => throw _privateConstructorUsedError;
  @JsonKey(name: 'RegisterDate')
  String? get registerDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'ExpiredDate')
  String? get expiredDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'Photo')
  bool get photo => throw _privateConstructorUsedError;
  bool? get isActivated => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DriverDataCopyWith<DriverData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverDataCopyWith<$Res> {
  factory $DriverDataCopyWith(
          DriverData value, $Res Function(DriverData) then) =
      _$DriverDataCopyWithImpl<$Res, DriverData>;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String id,
      @JsonKey(name: 'RFID') String? rfid,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'Phone2') String? phone2,
      @JsonKey(name: 'Address') String? address,
      @JsonKey(name: 'Company') String? company,
      @JsonKey(name: 'City') String? city,
      @JsonKey(name: 'DriverNo') String? driverNo,
      @JsonKey(name: 'LicenseNo') String? licenseNo,
      @JsonKey(name: 'RegisterPlace') String? registerPlace,
      @JsonKey(name: 'RegisterDate') String? registerDate,
      @JsonKey(name: 'ExpiredDate') String? expiredDate,
      @JsonKey(name: 'Photo') bool photo,
      bool? isActivated});
}

/// @nodoc
class _$DriverDataCopyWithImpl<$Res, $Val extends DriverData>
    implements $DriverDataCopyWith<$Res> {
  _$DriverDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? rfid = freezed,
    Object? name = freezed,
    Object? phone = freezed,
    Object? phone2 = freezed,
    Object? address = freezed,
    Object? company = freezed,
    Object? city = freezed,
    Object? driverNo = freezed,
    Object? licenseNo = freezed,
    Object? registerPlace = freezed,
    Object? registerDate = freezed,
    Object? expiredDate = freezed,
    Object? photo = null,
    Object? isActivated = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      rfid: freezed == rfid
          ? _value.rfid
          : rfid // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      phone2: freezed == phone2
          ? _value.phone2
          : phone2 // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      company: freezed == company
          ? _value.company
          : company // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      driverNo: freezed == driverNo
          ? _value.driverNo
          : driverNo // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseNo: freezed == licenseNo
          ? _value.licenseNo
          : licenseNo // ignore: cast_nullable_to_non_nullable
              as String?,
      registerPlace: freezed == registerPlace
          ? _value.registerPlace
          : registerPlace // ignore: cast_nullable_to_non_nullable
              as String?,
      registerDate: freezed == registerDate
          ? _value.registerDate
          : registerDate // ignore: cast_nullable_to_non_nullable
              as String?,
      expiredDate: freezed == expiredDate
          ? _value.expiredDate
          : expiredDate // ignore: cast_nullable_to_non_nullable
              as String?,
      photo: null == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as bool,
      isActivated: freezed == isActivated
          ? _value.isActivated
          : isActivated // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DriverDataImplCopyWith<$Res>
    implements $DriverDataCopyWith<$Res> {
  factory _$$DriverDataImplCopyWith(
          _$DriverDataImpl value, $Res Function(_$DriverDataImpl) then) =
      __$$DriverDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String id,
      @JsonKey(name: 'RFID') String? rfid,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'Phone2') String? phone2,
      @JsonKey(name: 'Address') String? address,
      @JsonKey(name: 'Company') String? company,
      @JsonKey(name: 'City') String? city,
      @JsonKey(name: 'DriverNo') String? driverNo,
      @JsonKey(name: 'LicenseNo') String? licenseNo,
      @JsonKey(name: 'RegisterPlace') String? registerPlace,
      @JsonKey(name: 'RegisterDate') String? registerDate,
      @JsonKey(name: 'ExpiredDate') String? expiredDate,
      @JsonKey(name: 'Photo') bool photo,
      bool? isActivated});
}

/// @nodoc
class __$$DriverDataImplCopyWithImpl<$Res>
    extends _$DriverDataCopyWithImpl<$Res, _$DriverDataImpl>
    implements _$$DriverDataImplCopyWith<$Res> {
  __$$DriverDataImplCopyWithImpl(
      _$DriverDataImpl _value, $Res Function(_$DriverDataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? rfid = freezed,
    Object? name = freezed,
    Object? phone = freezed,
    Object? phone2 = freezed,
    Object? address = freezed,
    Object? company = freezed,
    Object? city = freezed,
    Object? driverNo = freezed,
    Object? licenseNo = freezed,
    Object? registerPlace = freezed,
    Object? registerDate = freezed,
    Object? expiredDate = freezed,
    Object? photo = null,
    Object? isActivated = freezed,
  }) {
    return _then(_$DriverDataImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      rfid: freezed == rfid
          ? _value.rfid
          : rfid // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      phone2: freezed == phone2
          ? _value.phone2
          : phone2 // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      company: freezed == company
          ? _value.company
          : company // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      driverNo: freezed == driverNo
          ? _value.driverNo
          : driverNo // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseNo: freezed == licenseNo
          ? _value.licenseNo
          : licenseNo // ignore: cast_nullable_to_non_nullable
              as String?,
      registerPlace: freezed == registerPlace
          ? _value.registerPlace
          : registerPlace // ignore: cast_nullable_to_non_nullable
              as String?,
      registerDate: freezed == registerDate
          ? _value.registerDate
          : registerDate // ignore: cast_nullable_to_non_nullable
              as String?,
      expiredDate: freezed == expiredDate
          ? _value.expiredDate
          : expiredDate // ignore: cast_nullable_to_non_nullable
              as String?,
      photo: null == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as bool,
      isActivated: freezed == isActivated
          ? _value.isActivated
          : isActivated // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverDataImpl extends _DriverData {
  const _$DriverDataImpl(
      {@JsonKey(name: 'Id') required this.id,
      @JsonKey(name: 'RFID') this.rfid = "",
      @JsonKey(name: 'Name') this.name = "",
      @JsonKey(name: 'Phone') this.phone = "",
      @JsonKey(name: 'Phone2') this.phone2 = "",
      @JsonKey(name: 'Address') this.address = "",
      @JsonKey(name: 'Company') this.company = "",
      @JsonKey(name: 'City') this.city = "",
      @JsonKey(name: 'DriverNo') this.driverNo = "",
      @JsonKey(name: 'LicenseNo') this.licenseNo = "",
      @JsonKey(name: 'RegisterPlace') this.registerPlace = "",
      @JsonKey(name: 'RegisterDate') this.registerDate = "",
      @JsonKey(name: 'ExpiredDate') this.expiredDate = "",
      @JsonKey(name: 'Photo') this.photo = false,
      this.isActivated = false})
      : super._();

  factory _$DriverDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverDataImplFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final String id;
  @override
  @JsonKey(name: 'RFID')
  final String? rfid;
  @override
  @JsonKey(name: 'Name')
  final String? name;
  @override
  @JsonKey(name: 'Phone')
  final String? phone;
  @override
  @JsonKey(name: 'Phone2')
  final String? phone2;
  @override
  @JsonKey(name: 'Address')
  final String? address;
  @override
  @JsonKey(name: 'Company')
  final String? company;
  @override
  @JsonKey(name: 'City')
  final String? city;
  @override
  @JsonKey(name: 'DriverNo')
  final String? driverNo;
  @override
  @JsonKey(name: 'LicenseNo')
  final String? licenseNo;
  @override
  @JsonKey(name: 'RegisterPlace')
  final String? registerPlace;
  @override
  @JsonKey(name: 'RegisterDate')
  final String? registerDate;
  @override
  @JsonKey(name: 'ExpiredDate')
  final String? expiredDate;
  @override
  @JsonKey(name: 'Photo')
  final bool photo;
  @override
  @JsonKey()
  final bool? isActivated;

  @override
  String toString() {
    return 'DriverData(id: $id, rfid: $rfid, name: $name, phone: $phone, phone2: $phone2, address: $address, company: $company, city: $city, driverNo: $driverNo, licenseNo: $licenseNo, registerPlace: $registerPlace, registerDate: $registerDate, expiredDate: $expiredDate, photo: $photo, isActivated: $isActivated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverDataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.rfid, rfid) || other.rfid == rfid) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.phone2, phone2) || other.phone2 == phone2) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.driverNo, driverNo) ||
                other.driverNo == driverNo) &&
            (identical(other.licenseNo, licenseNo) ||
                other.licenseNo == licenseNo) &&
            (identical(other.registerPlace, registerPlace) ||
                other.registerPlace == registerPlace) &&
            (identical(other.registerDate, registerDate) ||
                other.registerDate == registerDate) &&
            (identical(other.expiredDate, expiredDate) ||
                other.expiredDate == expiredDate) &&
            (identical(other.photo, photo) || other.photo == photo) &&
            (identical(other.isActivated, isActivated) ||
                other.isActivated == isActivated));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      rfid,
      name,
      phone,
      phone2,
      address,
      company,
      city,
      driverNo,
      licenseNo,
      registerPlace,
      registerDate,
      expiredDate,
      photo,
      isActivated);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverDataImplCopyWith<_$DriverDataImpl> get copyWith =>
      __$$DriverDataImplCopyWithImpl<_$DriverDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverDataImplToJson(
      this,
    );
  }
}

abstract class _DriverData extends DriverData {
  const factory _DriverData(
      {@JsonKey(name: 'Id') required final String id,
      @JsonKey(name: 'RFID') final String? rfid,
      @JsonKey(name: 'Name') final String? name,
      @JsonKey(name: 'Phone') final String? phone,
      @JsonKey(name: 'Phone2') final String? phone2,
      @JsonKey(name: 'Address') final String? address,
      @JsonKey(name: 'Company') final String? company,
      @JsonKey(name: 'City') final String? city,
      @JsonKey(name: 'DriverNo') final String? driverNo,
      @JsonKey(name: 'LicenseNo') final String? licenseNo,
      @JsonKey(name: 'RegisterPlace') final String? registerPlace,
      @JsonKey(name: 'RegisterDate') final String? registerDate,
      @JsonKey(name: 'ExpiredDate') final String? expiredDate,
      @JsonKey(name: 'Photo') final bool photo,
      final bool? isActivated}) = _$DriverDataImpl;
  const _DriverData._() : super._();

  factory _DriverData.fromJson(Map<String, dynamic> json) =
      _$DriverDataImpl.fromJson;

  @override
  @JsonKey(name: 'Id')
  String get id;
  @override
  @JsonKey(name: 'RFID')
  String? get rfid;
  @override
  @JsonKey(name: 'Name')
  String? get name;
  @override
  @JsonKey(name: 'Phone')
  String? get phone;
  @override
  @JsonKey(name: 'Phone2')
  String? get phone2;
  @override
  @JsonKey(name: 'Address')
  String? get address;
  @override
  @JsonKey(name: 'Company')
  String? get company;
  @override
  @JsonKey(name: 'City')
  String? get city;
  @override
  @JsonKey(name: 'DriverNo')
  String? get driverNo;
  @override
  @JsonKey(name: 'LicenseNo')
  String? get licenseNo;
  @override
  @JsonKey(name: 'RegisterPlace')
  String? get registerPlace;
  @override
  @JsonKey(name: 'RegisterDate')
  String? get registerDate;
  @override
  @JsonKey(name: 'ExpiredDate')
  String? get expiredDate;
  @override
  @JsonKey(name: 'Photo')
  bool get photo;
  @override
  bool? get isActivated;
  @override
  @JsonKey(ignore: true)
  _$$DriverDataImplCopyWith<_$DriverDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
