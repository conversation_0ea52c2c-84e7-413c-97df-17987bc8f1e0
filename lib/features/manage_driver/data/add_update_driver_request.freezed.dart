// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_update_driver_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AddUpdateDriverRequest _$AddUpdateDriverRequestFromJson(
    Map<String, dynamic> json) {
  return _AddUpdateDriverRequest.fromJson(json);
}

/// @nodoc
mixin _$AddUpdateDriverRequest {
  @JsonKey(name: 'Id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'Name')
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'Email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'Phone')
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: 'Phone2')
  String? get phone2 => throw _privateConstructorUsedError;
  @JsonKey(name: 'Address')
  String? get address => throw _privateConstructorUsedError;
  @JsonKey(name: 'CityId')
  String get cityId => throw _privateConstructorUsedError;
  @JsonKey(name: 'DriverNo')
  String? get driverNo => throw _privateConstructorUsedError;
  @JsonKey(name: 'RFID')
  String? get rfid => throw _privateConstructorUsedError;
  @JsonKey(name: 'LicenseNo')
  String? get licenseNo => throw _privateConstructorUsedError;
  @JsonKey(name: 'RegisterPlace')
  String? get registerPlace => throw _privateConstructorUsedError;
  @JsonKey(name: 'RegisterDate')
  int? get registerDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'ExpiredDate')
  int? get expiredDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'IsActivated')
  bool? get isActivated => throw _privateConstructorUsedError;
  @JsonKey(name: 'VehicleId')
  String? get vehicleId => throw _privateConstructorUsedError;
  @JsonKey(name: 'forms')
  Map<String, dynamic>? get forms => throw _privateConstructorUsedError;
  String? get file => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AddUpdateDriverRequestCopyWith<AddUpdateDriverRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddUpdateDriverRequestCopyWith<$Res> {
  factory $AddUpdateDriverRequestCopyWith(AddUpdateDriverRequest value,
          $Res Function(AddUpdateDriverRequest) then) =
      _$AddUpdateDriverRequestCopyWithImpl<$Res, AddUpdateDriverRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Name') String name,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'Phone2') String? phone2,
      @JsonKey(name: 'Address') String? address,
      @JsonKey(name: 'CityId') String cityId,
      @JsonKey(name: 'DriverNo') String? driverNo,
      @JsonKey(name: 'RFID') String? rfid,
      @JsonKey(name: 'LicenseNo') String? licenseNo,
      @JsonKey(name: 'RegisterPlace') String? registerPlace,
      @JsonKey(name: 'RegisterDate') int? registerDate,
      @JsonKey(name: 'ExpiredDate') int? expiredDate,
      @JsonKey(name: 'IsActivated') bool? isActivated,
      @JsonKey(name: 'VehicleId') String? vehicleId,
      @JsonKey(name: 'forms') Map<String, dynamic>? forms,
      String? file});
}

/// @nodoc
class _$AddUpdateDriverRequestCopyWithImpl<$Res,
        $Val extends AddUpdateDriverRequest>
    implements $AddUpdateDriverRequestCopyWith<$Res> {
  _$AddUpdateDriverRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = null,
    Object? email = freezed,
    Object? phone = freezed,
    Object? phone2 = freezed,
    Object? address = freezed,
    Object? cityId = null,
    Object? driverNo = freezed,
    Object? rfid = freezed,
    Object? licenseNo = freezed,
    Object? registerPlace = freezed,
    Object? registerDate = freezed,
    Object? expiredDate = freezed,
    Object? isActivated = freezed,
    Object? vehicleId = freezed,
    Object? forms = freezed,
    Object? file = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      phone2: freezed == phone2
          ? _value.phone2
          : phone2 // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      cityId: null == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as String,
      driverNo: freezed == driverNo
          ? _value.driverNo
          : driverNo // ignore: cast_nullable_to_non_nullable
              as String?,
      rfid: freezed == rfid
          ? _value.rfid
          : rfid // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseNo: freezed == licenseNo
          ? _value.licenseNo
          : licenseNo // ignore: cast_nullable_to_non_nullable
              as String?,
      registerPlace: freezed == registerPlace
          ? _value.registerPlace
          : registerPlace // ignore: cast_nullable_to_non_nullable
              as String?,
      registerDate: freezed == registerDate
          ? _value.registerDate
          : registerDate // ignore: cast_nullable_to_non_nullable
              as int?,
      expiredDate: freezed == expiredDate
          ? _value.expiredDate
          : expiredDate // ignore: cast_nullable_to_non_nullable
              as int?,
      isActivated: freezed == isActivated
          ? _value.isActivated
          : isActivated // ignore: cast_nullable_to_non_nullable
              as bool?,
      vehicleId: freezed == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
      forms: freezed == forms
          ? _value.forms
          : forms // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      file: freezed == file
          ? _value.file
          : file // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddUpdateDriverRequestImplCopyWith<$Res>
    implements $AddUpdateDriverRequestCopyWith<$Res> {
  factory _$$AddUpdateDriverRequestImplCopyWith(
          _$AddUpdateDriverRequestImpl value,
          $Res Function(_$AddUpdateDriverRequestImpl) then) =
      __$$AddUpdateDriverRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Name') String name,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'Phone2') String? phone2,
      @JsonKey(name: 'Address') String? address,
      @JsonKey(name: 'CityId') String cityId,
      @JsonKey(name: 'DriverNo') String? driverNo,
      @JsonKey(name: 'RFID') String? rfid,
      @JsonKey(name: 'LicenseNo') String? licenseNo,
      @JsonKey(name: 'RegisterPlace') String? registerPlace,
      @JsonKey(name: 'RegisterDate') int? registerDate,
      @JsonKey(name: 'ExpiredDate') int? expiredDate,
      @JsonKey(name: 'IsActivated') bool? isActivated,
      @JsonKey(name: 'VehicleId') String? vehicleId,
      @JsonKey(name: 'forms') Map<String, dynamic>? forms,
      String? file});
}

/// @nodoc
class __$$AddUpdateDriverRequestImplCopyWithImpl<$Res>
    extends _$AddUpdateDriverRequestCopyWithImpl<$Res,
        _$AddUpdateDriverRequestImpl>
    implements _$$AddUpdateDriverRequestImplCopyWith<$Res> {
  __$$AddUpdateDriverRequestImplCopyWithImpl(
      _$AddUpdateDriverRequestImpl _value,
      $Res Function(_$AddUpdateDriverRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = null,
    Object? email = freezed,
    Object? phone = freezed,
    Object? phone2 = freezed,
    Object? address = freezed,
    Object? cityId = null,
    Object? driverNo = freezed,
    Object? rfid = freezed,
    Object? licenseNo = freezed,
    Object? registerPlace = freezed,
    Object? registerDate = freezed,
    Object? expiredDate = freezed,
    Object? isActivated = freezed,
    Object? vehicleId = freezed,
    Object? forms = freezed,
    Object? file = freezed,
  }) {
    return _then(_$AddUpdateDriverRequestImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      phone2: freezed == phone2
          ? _value.phone2
          : phone2 // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      cityId: null == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as String,
      driverNo: freezed == driverNo
          ? _value.driverNo
          : driverNo // ignore: cast_nullable_to_non_nullable
              as String?,
      rfid: freezed == rfid
          ? _value.rfid
          : rfid // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseNo: freezed == licenseNo
          ? _value.licenseNo
          : licenseNo // ignore: cast_nullable_to_non_nullable
              as String?,
      registerPlace: freezed == registerPlace
          ? _value.registerPlace
          : registerPlace // ignore: cast_nullable_to_non_nullable
              as String?,
      registerDate: freezed == registerDate
          ? _value.registerDate
          : registerDate // ignore: cast_nullable_to_non_nullable
              as int?,
      expiredDate: freezed == expiredDate
          ? _value.expiredDate
          : expiredDate // ignore: cast_nullable_to_non_nullable
              as int?,
      isActivated: freezed == isActivated
          ? _value.isActivated
          : isActivated // ignore: cast_nullable_to_non_nullable
              as bool?,
      vehicleId: freezed == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
      forms: freezed == forms
          ? _value._forms
          : forms // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      file: freezed == file
          ? _value.file
          : file // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AddUpdateDriverRequestImpl implements _AddUpdateDriverRequest {
  const _$AddUpdateDriverRequestImpl(
      {@JsonKey(name: 'Id') this.id = "",
      @JsonKey(name: 'Name') required this.name,
      @JsonKey(name: 'Email') this.email = "",
      @JsonKey(name: 'Phone') this.phone = "",
      @JsonKey(name: 'Phone2') this.phone2 = "",
      @JsonKey(name: 'Address') this.address = "",
      @JsonKey(name: 'CityId') required this.cityId,
      @JsonKey(name: 'DriverNo') this.driverNo = "",
      @JsonKey(name: 'RFID') this.rfid = "",
      @JsonKey(name: 'LicenseNo') this.licenseNo = "",
      @JsonKey(name: 'RegisterPlace') this.registerPlace = "",
      @JsonKey(name: 'RegisterDate') this.registerDate = 0,
      @JsonKey(name: 'ExpiredDate') this.expiredDate = 0,
      @JsonKey(name: 'IsActivated') this.isActivated,
      @JsonKey(name: 'VehicleId') this.vehicleId = "",
      @JsonKey(name: 'forms')
      final Map<String, dynamic>? forms = const <String, dynamic>{},
      this.file})
      : _forms = forms;

  factory _$AddUpdateDriverRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$AddUpdateDriverRequestImplFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'Name')
  final String name;
  @override
  @JsonKey(name: 'Email')
  final String? email;
  @override
  @JsonKey(name: 'Phone')
  final String? phone;
  @override
  @JsonKey(name: 'Phone2')
  final String? phone2;
  @override
  @JsonKey(name: 'Address')
  final String? address;
  @override
  @JsonKey(name: 'CityId')
  final String cityId;
  @override
  @JsonKey(name: 'DriverNo')
  final String? driverNo;
  @override
  @JsonKey(name: 'RFID')
  final String? rfid;
  @override
  @JsonKey(name: 'LicenseNo')
  final String? licenseNo;
  @override
  @JsonKey(name: 'RegisterPlace')
  final String? registerPlace;
  @override
  @JsonKey(name: 'RegisterDate')
  final int? registerDate;
  @override
  @JsonKey(name: 'ExpiredDate')
  final int? expiredDate;
  @override
  @JsonKey(name: 'IsActivated')
  final bool? isActivated;
  @override
  @JsonKey(name: 'VehicleId')
  final String? vehicleId;
  final Map<String, dynamic>? _forms;
  @override
  @JsonKey(name: 'forms')
  Map<String, dynamic>? get forms {
    final value = _forms;
    if (value == null) return null;
    if (_forms is EqualUnmodifiableMapView) return _forms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final String? file;

  @override
  String toString() {
    return 'AddUpdateDriverRequest(id: $id, name: $name, email: $email, phone: $phone, phone2: $phone2, address: $address, cityId: $cityId, driverNo: $driverNo, rfid: $rfid, licenseNo: $licenseNo, registerPlace: $registerPlace, registerDate: $registerDate, expiredDate: $expiredDate, isActivated: $isActivated, vehicleId: $vehicleId, forms: $forms, file: $file)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddUpdateDriverRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.phone2, phone2) || other.phone2 == phone2) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.cityId, cityId) || other.cityId == cityId) &&
            (identical(other.driverNo, driverNo) ||
                other.driverNo == driverNo) &&
            (identical(other.rfid, rfid) || other.rfid == rfid) &&
            (identical(other.licenseNo, licenseNo) ||
                other.licenseNo == licenseNo) &&
            (identical(other.registerPlace, registerPlace) ||
                other.registerPlace == registerPlace) &&
            (identical(other.registerDate, registerDate) ||
                other.registerDate == registerDate) &&
            (identical(other.expiredDate, expiredDate) ||
                other.expiredDate == expiredDate) &&
            (identical(other.isActivated, isActivated) ||
                other.isActivated == isActivated) &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            const DeepCollectionEquality().equals(other._forms, _forms) &&
            (identical(other.file, file) || other.file == file));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      email,
      phone,
      phone2,
      address,
      cityId,
      driverNo,
      rfid,
      licenseNo,
      registerPlace,
      registerDate,
      expiredDate,
      isActivated,
      vehicleId,
      const DeepCollectionEquality().hash(_forms),
      file);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AddUpdateDriverRequestImplCopyWith<_$AddUpdateDriverRequestImpl>
      get copyWith => __$$AddUpdateDriverRequestImplCopyWithImpl<
          _$AddUpdateDriverRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AddUpdateDriverRequestImplToJson(
      this,
    );
  }
}

abstract class _AddUpdateDriverRequest implements AddUpdateDriverRequest {
  const factory _AddUpdateDriverRequest(
      {@JsonKey(name: 'Id') final String? id,
      @JsonKey(name: 'Name') required final String name,
      @JsonKey(name: 'Email') final String? email,
      @JsonKey(name: 'Phone') final String? phone,
      @JsonKey(name: 'Phone2') final String? phone2,
      @JsonKey(name: 'Address') final String? address,
      @JsonKey(name: 'CityId') required final String cityId,
      @JsonKey(name: 'DriverNo') final String? driverNo,
      @JsonKey(name: 'RFID') final String? rfid,
      @JsonKey(name: 'LicenseNo') final String? licenseNo,
      @JsonKey(name: 'RegisterPlace') final String? registerPlace,
      @JsonKey(name: 'RegisterDate') final int? registerDate,
      @JsonKey(name: 'ExpiredDate') final int? expiredDate,
      @JsonKey(name: 'IsActivated') final bool? isActivated,
      @JsonKey(name: 'VehicleId') final String? vehicleId,
      @JsonKey(name: 'forms') final Map<String, dynamic>? forms,
      final String? file}) = _$AddUpdateDriverRequestImpl;

  factory _AddUpdateDriverRequest.fromJson(Map<String, dynamic> json) =
      _$AddUpdateDriverRequestImpl.fromJson;

  @override
  @JsonKey(name: 'Id')
  String? get id;
  @override
  @JsonKey(name: 'Name')
  String get name;
  @override
  @JsonKey(name: 'Email')
  String? get email;
  @override
  @JsonKey(name: 'Phone')
  String? get phone;
  @override
  @JsonKey(name: 'Phone2')
  String? get phone2;
  @override
  @JsonKey(name: 'Address')
  String? get address;
  @override
  @JsonKey(name: 'CityId')
  String get cityId;
  @override
  @JsonKey(name: 'DriverNo')
  String? get driverNo;
  @override
  @JsonKey(name: 'RFID')
  String? get rfid;
  @override
  @JsonKey(name: 'LicenseNo')
  String? get licenseNo;
  @override
  @JsonKey(name: 'RegisterPlace')
  String? get registerPlace;
  @override
  @JsonKey(name: 'RegisterDate')
  int? get registerDate;
  @override
  @JsonKey(name: 'ExpiredDate')
  int? get expiredDate;
  @override
  @JsonKey(name: 'IsActivated')
  bool? get isActivated;
  @override
  @JsonKey(name: 'VehicleId')
  String? get vehicleId;
  @override
  @JsonKey(name: 'forms')
  Map<String, dynamic>? get forms;
  @override
  String? get file;
  @override
  @JsonKey(ignore: true)
  _$$AddUpdateDriverRequestImplCopyWith<_$AddUpdateDriverRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
