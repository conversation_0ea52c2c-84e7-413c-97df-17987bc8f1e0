// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'driver_response.freezed.dart';
part 'driver_response.g.dart';


@freezed
class DriverResponse with _$DriverResponse {
  const factory DriverResponse({
    required int total,
    List<DriverData>? data,
  }) = _DriverResponse;

  factory DriverResponse.fromJson(Map<String, dynamic> json) =>
      _$DriverResponseFromJson(json);

  const DriverResponse._();
}

@freezed
class DriverData with _$DriverData {
  const factory DriverData({
    @JsonKey(name: 'Id') required String id,
    @J<PERSON><PERSON><PERSON>(name: 'RFID') @Default("") String? rfid,
    @J<PERSON><PERSON><PERSON>(name: 'Name') @Default("") String? name,
    @JsonKey(name: 'Phone') @Default("") String? phone,
    @JsonKey(name: 'Phone2') @Default("") String? phone2,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Address') @Default("") String? address,
    @Json<PERSON>ey(name: 'Company') @Default("") String? company,
    @JsonKey(name: 'City') @Default("") String? city,
    @JsonKey(name: 'DriverNo') @Default("") String? driverNo,
    @JsonKey(name: 'LicenseNo') @Default("") String? licenseNo,
    @JsonKey(name: 'RegisterPlace') @Default("") String? registerPlace,
    @JsonKey(name: 'RegisterDate') @Default("") String? registerDate,
    @JsonKey(name: 'ExpiredDate') @Default("") String? expiredDate,
    @JsonKey(name: 'Photo') @Default(false) bool photo,
    @Default(false) bool? isActivated,
  }) = _DriverData;

  factory DriverData.fromJson(Map<String, dynamic> json) => _$DriverDataFromJson(json);

  const DriverData._();
}
