// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_update_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AddUpdateResponse _$AddUpdateResponseFromJson(Map<String, dynamic> json) {
  return _AddUpdateResponse.fromJson(json);
}

/// @nodoc
mixin _$AddUpdateResponse {
  int? get state => throw _privateConstructorUsedError;
  String? get keyLanguage => throw _privateConstructorUsedError;
  String? get uiKeyLanguage => throw _privateConstructorUsedError;
  List<dynamic>? get keyLanguages => throw _privateConstructorUsedError;
  dynamic get value => throw _privateConstructorUsedError;
  String? get forms => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AddUpdateResponseCopyWith<AddUpdateResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddUpdateResponseCopyWith<$Res> {
  factory $AddUpdateResponseCopyWith(
          AddUpdateResponse value, $Res Function(AddUpdateResponse) then) =
      _$AddUpdateResponseCopyWithImpl<$Res, AddUpdateResponse>;
  @useResult
  $Res call(
      {int? state,
      String? keyLanguage,
      String? uiKeyLanguage,
      List<dynamic>? keyLanguages,
      dynamic value,
      String? forms});
}

/// @nodoc
class _$AddUpdateResponseCopyWithImpl<$Res, $Val extends AddUpdateResponse>
    implements $AddUpdateResponseCopyWith<$Res> {
  _$AddUpdateResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = freezed,
    Object? keyLanguage = freezed,
    Object? uiKeyLanguage = freezed,
    Object? keyLanguages = freezed,
    Object? value = freezed,
    Object? forms = freezed,
  }) {
    return _then(_value.copyWith(
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as int?,
      keyLanguage: freezed == keyLanguage
          ? _value.keyLanguage
          : keyLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      uiKeyLanguage: freezed == uiKeyLanguage
          ? _value.uiKeyLanguage
          : uiKeyLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      keyLanguages: freezed == keyLanguages
          ? _value.keyLanguages
          : keyLanguages // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as dynamic,
      forms: freezed == forms
          ? _value.forms
          : forms // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddUpdateResponseImplCopyWith<$Res>
    implements $AddUpdateResponseCopyWith<$Res> {
  factory _$$AddUpdateResponseImplCopyWith(_$AddUpdateResponseImpl value,
          $Res Function(_$AddUpdateResponseImpl) then) =
      __$$AddUpdateResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? state,
      String? keyLanguage,
      String? uiKeyLanguage,
      List<dynamic>? keyLanguages,
      dynamic value,
      String? forms});
}

/// @nodoc
class __$$AddUpdateResponseImplCopyWithImpl<$Res>
    extends _$AddUpdateResponseCopyWithImpl<$Res, _$AddUpdateResponseImpl>
    implements _$$AddUpdateResponseImplCopyWith<$Res> {
  __$$AddUpdateResponseImplCopyWithImpl(_$AddUpdateResponseImpl _value,
      $Res Function(_$AddUpdateResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = freezed,
    Object? keyLanguage = freezed,
    Object? uiKeyLanguage = freezed,
    Object? keyLanguages = freezed,
    Object? value = freezed,
    Object? forms = freezed,
  }) {
    return _then(_$AddUpdateResponseImpl(
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as int?,
      keyLanguage: freezed == keyLanguage
          ? _value.keyLanguage
          : keyLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      uiKeyLanguage: freezed == uiKeyLanguage
          ? _value.uiKeyLanguage
          : uiKeyLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      keyLanguages: freezed == keyLanguages
          ? _value._keyLanguages
          : keyLanguages // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as dynamic,
      forms: freezed == forms
          ? _value.forms
          : forms // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AddUpdateResponseImpl extends _AddUpdateResponse {
  const _$AddUpdateResponseImpl(
      {this.state,
      this.keyLanguage,
      this.uiKeyLanguage,
      final List<dynamic>? keyLanguages,
      this.value,
      this.forms})
      : _keyLanguages = keyLanguages,
        super._();

  factory _$AddUpdateResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AddUpdateResponseImplFromJson(json);

  @override
  final int? state;
  @override
  final String? keyLanguage;
  @override
  final String? uiKeyLanguage;
  final List<dynamic>? _keyLanguages;
  @override
  List<dynamic>? get keyLanguages {
    final value = _keyLanguages;
    if (value == null) return null;
    if (_keyLanguages is EqualUnmodifiableListView) return _keyLanguages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final dynamic value;
  @override
  final String? forms;

  @override
  String toString() {
    return 'AddUpdateResponse(state: $state, keyLanguage: $keyLanguage, uiKeyLanguage: $uiKeyLanguage, keyLanguages: $keyLanguages, value: $value, forms: $forms)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddUpdateResponseImpl &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.keyLanguage, keyLanguage) ||
                other.keyLanguage == keyLanguage) &&
            (identical(other.uiKeyLanguage, uiKeyLanguage) ||
                other.uiKeyLanguage == uiKeyLanguage) &&
            const DeepCollectionEquality()
                .equals(other._keyLanguages, _keyLanguages) &&
            const DeepCollectionEquality().equals(other.value, value) &&
            (identical(other.forms, forms) || other.forms == forms));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      state,
      keyLanguage,
      uiKeyLanguage,
      const DeepCollectionEquality().hash(_keyLanguages),
      const DeepCollectionEquality().hash(value),
      forms);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AddUpdateResponseImplCopyWith<_$AddUpdateResponseImpl> get copyWith =>
      __$$AddUpdateResponseImplCopyWithImpl<_$AddUpdateResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AddUpdateResponseImplToJson(
      this,
    );
  }
}

abstract class _AddUpdateResponse extends AddUpdateResponse {
  const factory _AddUpdateResponse(
      {final int? state,
      final String? keyLanguage,
      final String? uiKeyLanguage,
      final List<dynamic>? keyLanguages,
      final dynamic value,
      final String? forms}) = _$AddUpdateResponseImpl;
  const _AddUpdateResponse._() : super._();

  factory _AddUpdateResponse.fromJson(Map<String, dynamic> json) =
      _$AddUpdateResponseImpl.fromJson;

  @override
  int? get state;
  @override
  String? get keyLanguage;
  @override
  String? get uiKeyLanguage;
  @override
  List<dynamic>? get keyLanguages;
  @override
  dynamic get value;
  @override
  String? get forms;
  @override
  @JsonKey(ignore: true)
  _$$AddUpdateResponseImplCopyWith<_$AddUpdateResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
