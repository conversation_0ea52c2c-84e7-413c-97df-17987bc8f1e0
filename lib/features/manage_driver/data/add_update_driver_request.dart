// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_update_driver_request.freezed.dart';
part 'add_update_driver_request.g.dart';

@freezed
class AddUpdateDriverRequest with _$AddUpdateDriverRequest {
  const factory AddUpdateDriverRequest({
    @Json<PERSON>ey(name: 'Id') @Default("") String? id,
    @Json<PERSON>ey(name: 'Name') required String name,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Email') @Default("") String? email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Phone') @Default("") String? phone,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Phone2') @Default("") String? phone2,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Address') @Default("") String? address,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'CityId') required String cityId,
    @<PERSON><PERSON>Key(name: 'DriverNo') @Default("") String? driverNo,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'RFID') @Default("") String? rfid,
    @J<PERSON><PERSON>ey(name: 'LicenseNo') @Default("") String? licenseNo,
    @Json<PERSON><PERSON>(name: 'RegisterPlace') @Default("") String? registerPlace,
    @Json<PERSON>ey(name: 'RegisterDate') @Default(0) int? registerDate,
    @JsonKey(name: 'ExpiredDate') @Default(0) int? expiredDate,
    @JsonKey(name: 'IsActivated') bool? isActivated,
    @JsonKey(name: 'VehicleId') @Default("") String? vehicleId,
    @JsonKey(name: 'forms')
    @Default(<String, dynamic>{})
    Map<String, dynamic>? forms,
    String? file,
  }) = _AddUpdateDriverRequest;

  factory AddUpdateDriverRequest.fromJson(Map<String, dynamic> json) =>
      _$AddUpdateDriverRequestFromJson(json);
}
