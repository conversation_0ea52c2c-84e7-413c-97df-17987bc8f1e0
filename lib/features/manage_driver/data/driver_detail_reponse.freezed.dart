// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'driver_detail_reponse.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DriverDetailResponse _$DriverDetailResponseFromJson(Map<String, dynamic> json) {
  return _DriverDetailResponse.fromJson(json);
}

/// @nodoc
mixin _$DriverDetailResponse {
  String? get id => throw _privateConstructorUsedError;
  String get cityId => throw _privateConstructorUsedError;
  String? get rfid => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get phone => throw _privateConstructorUsedError;
  String? get phone2 => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get driverNo => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get licenseNo => throw _privateConstructorUsedError;
  String? get registerPlace => throw _privateConstructorUsedError;
  String? get username => throw _privateConstructorUsedError;
  bool get isActivated => throw _privateConstructorUsedError;
  String? get registerDate => throw _privateConstructorUsedError;
  String? get expiredDate => throw _privateConstructorUsedError;
  bool get photo => throw _privateConstructorUsedError;
  Map<String, dynamic> get fields => throw _privateConstructorUsedError;
  String? get vehicleId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DriverDetailResponseCopyWith<DriverDetailResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverDetailResponseCopyWith<$Res> {
  factory $DriverDetailResponseCopyWith(DriverDetailResponse value,
          $Res Function(DriverDetailResponse) then) =
      _$DriverDetailResponseCopyWithImpl<$Res, DriverDetailResponse>;
  @useResult
  $Res call(
      {String? id,
      String cityId,
      String? rfid,
      String name,
      String? phone,
      String? phone2,
      String? address,
      String? driverNo,
      String? email,
      String? licenseNo,
      String? registerPlace,
      String? username,
      bool isActivated,
      String? registerDate,
      String? expiredDate,
      bool photo,
      Map<String, dynamic> fields,
      String? vehicleId});
}

/// @nodoc
class _$DriverDetailResponseCopyWithImpl<$Res,
        $Val extends DriverDetailResponse>
    implements $DriverDetailResponseCopyWith<$Res> {
  _$DriverDetailResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cityId = null,
    Object? rfid = freezed,
    Object? name = null,
    Object? phone = freezed,
    Object? phone2 = freezed,
    Object? address = freezed,
    Object? driverNo = freezed,
    Object? email = freezed,
    Object? licenseNo = freezed,
    Object? registerPlace = freezed,
    Object? username = freezed,
    Object? isActivated = null,
    Object? registerDate = freezed,
    Object? expiredDate = freezed,
    Object? photo = null,
    Object? fields = null,
    Object? vehicleId = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      cityId: null == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as String,
      rfid: freezed == rfid
          ? _value.rfid
          : rfid // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      phone2: freezed == phone2
          ? _value.phone2
          : phone2 // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      driverNo: freezed == driverNo
          ? _value.driverNo
          : driverNo // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseNo: freezed == licenseNo
          ? _value.licenseNo
          : licenseNo // ignore: cast_nullable_to_non_nullable
              as String?,
      registerPlace: freezed == registerPlace
          ? _value.registerPlace
          : registerPlace // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      isActivated: null == isActivated
          ? _value.isActivated
          : isActivated // ignore: cast_nullable_to_non_nullable
              as bool,
      registerDate: freezed == registerDate
          ? _value.registerDate
          : registerDate // ignore: cast_nullable_to_non_nullable
              as String?,
      expiredDate: freezed == expiredDate
          ? _value.expiredDate
          : expiredDate // ignore: cast_nullable_to_non_nullable
              as String?,
      photo: null == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as bool,
      fields: null == fields
          ? _value.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      vehicleId: freezed == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DriverDetailResponseImplCopyWith<$Res>
    implements $DriverDetailResponseCopyWith<$Res> {
  factory _$$DriverDetailResponseImplCopyWith(_$DriverDetailResponseImpl value,
          $Res Function(_$DriverDetailResponseImpl) then) =
      __$$DriverDetailResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String cityId,
      String? rfid,
      String name,
      String? phone,
      String? phone2,
      String? address,
      String? driverNo,
      String? email,
      String? licenseNo,
      String? registerPlace,
      String? username,
      bool isActivated,
      String? registerDate,
      String? expiredDate,
      bool photo,
      Map<String, dynamic> fields,
      String? vehicleId});
}

/// @nodoc
class __$$DriverDetailResponseImplCopyWithImpl<$Res>
    extends _$DriverDetailResponseCopyWithImpl<$Res, _$DriverDetailResponseImpl>
    implements _$$DriverDetailResponseImplCopyWith<$Res> {
  __$$DriverDetailResponseImplCopyWithImpl(_$DriverDetailResponseImpl _value,
      $Res Function(_$DriverDetailResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cityId = null,
    Object? rfid = freezed,
    Object? name = null,
    Object? phone = freezed,
    Object? phone2 = freezed,
    Object? address = freezed,
    Object? driverNo = freezed,
    Object? email = freezed,
    Object? licenseNo = freezed,
    Object? registerPlace = freezed,
    Object? username = freezed,
    Object? isActivated = null,
    Object? registerDate = freezed,
    Object? expiredDate = freezed,
    Object? photo = null,
    Object? fields = null,
    Object? vehicleId = freezed,
  }) {
    return _then(_$DriverDetailResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      cityId: null == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as String,
      rfid: freezed == rfid
          ? _value.rfid
          : rfid // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      phone2: freezed == phone2
          ? _value.phone2
          : phone2 // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      driverNo: freezed == driverNo
          ? _value.driverNo
          : driverNo // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseNo: freezed == licenseNo
          ? _value.licenseNo
          : licenseNo // ignore: cast_nullable_to_non_nullable
              as String?,
      registerPlace: freezed == registerPlace
          ? _value.registerPlace
          : registerPlace // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      isActivated: null == isActivated
          ? _value.isActivated
          : isActivated // ignore: cast_nullable_to_non_nullable
              as bool,
      registerDate: freezed == registerDate
          ? _value.registerDate
          : registerDate // ignore: cast_nullable_to_non_nullable
              as String?,
      expiredDate: freezed == expiredDate
          ? _value.expiredDate
          : expiredDate // ignore: cast_nullable_to_non_nullable
              as String?,
      photo: null == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as bool,
      fields: null == fields
          ? _value._fields
          : fields // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      vehicleId: freezed == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverDetailResponseImpl extends _DriverDetailResponse {
  const _$DriverDetailResponseImpl(
      {this.id = "",
      required this.cityId,
      this.rfid = "",
      required this.name,
      this.phone = "",
      this.phone2 = "",
      this.address = "",
      this.driverNo = "",
      this.email = "",
      this.licenseNo = "",
      this.registerPlace = "",
      this.username = "",
      this.isActivated = false,
      this.registerDate = "",
      this.expiredDate = "",
      this.photo = false,
      final Map<String, dynamic> fields = const {},
      this.vehicleId = ""})
      : _fields = fields,
        super._();

  factory _$DriverDetailResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverDetailResponseImplFromJson(json);

  @override
  @JsonKey()
  final String? id;
  @override
  final String cityId;
  @override
  @JsonKey()
  final String? rfid;
  @override
  final String name;
  @override
  @JsonKey()
  final String? phone;
  @override
  @JsonKey()
  final String? phone2;
  @override
  @JsonKey()
  final String? address;
  @override
  @JsonKey()
  final String? driverNo;
  @override
  @JsonKey()
  final String? email;
  @override
  @JsonKey()
  final String? licenseNo;
  @override
  @JsonKey()
  final String? registerPlace;
  @override
  @JsonKey()
  final String? username;
  @override
  @JsonKey()
  final bool isActivated;
  @override
  @JsonKey()
  final String? registerDate;
  @override
  @JsonKey()
  final String? expiredDate;
  @override
  @JsonKey()
  final bool photo;
  final Map<String, dynamic> _fields;
  @override
  @JsonKey()
  Map<String, dynamic> get fields {
    if (_fields is EqualUnmodifiableMapView) return _fields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fields);
  }

  @override
  @JsonKey()
  final String? vehicleId;

  @override
  String toString() {
    return 'DriverDetailResponse(id: $id, cityId: $cityId, rfid: $rfid, name: $name, phone: $phone, phone2: $phone2, address: $address, driverNo: $driverNo, email: $email, licenseNo: $licenseNo, registerPlace: $registerPlace, username: $username, isActivated: $isActivated, registerDate: $registerDate, expiredDate: $expiredDate, photo: $photo, fields: $fields, vehicleId: $vehicleId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverDetailResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cityId, cityId) || other.cityId == cityId) &&
            (identical(other.rfid, rfid) || other.rfid == rfid) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.phone2, phone2) || other.phone2 == phone2) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.driverNo, driverNo) ||
                other.driverNo == driverNo) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.licenseNo, licenseNo) ||
                other.licenseNo == licenseNo) &&
            (identical(other.registerPlace, registerPlace) ||
                other.registerPlace == registerPlace) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.isActivated, isActivated) ||
                other.isActivated == isActivated) &&
            (identical(other.registerDate, registerDate) ||
                other.registerDate == registerDate) &&
            (identical(other.expiredDate, expiredDate) ||
                other.expiredDate == expiredDate) &&
            (identical(other.photo, photo) || other.photo == photo) &&
            const DeepCollectionEquality().equals(other._fields, _fields) &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      cityId,
      rfid,
      name,
      phone,
      phone2,
      address,
      driverNo,
      email,
      licenseNo,
      registerPlace,
      username,
      isActivated,
      registerDate,
      expiredDate,
      photo,
      const DeepCollectionEquality().hash(_fields),
      vehicleId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverDetailResponseImplCopyWith<_$DriverDetailResponseImpl>
      get copyWith =>
          __$$DriverDetailResponseImplCopyWithImpl<_$DriverDetailResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverDetailResponseImplToJson(
      this,
    );
  }
}

abstract class _DriverDetailResponse extends DriverDetailResponse {
  const factory _DriverDetailResponse(
      {final String? id,
      required final String cityId,
      final String? rfid,
      required final String name,
      final String? phone,
      final String? phone2,
      final String? address,
      final String? driverNo,
      final String? email,
      final String? licenseNo,
      final String? registerPlace,
      final String? username,
      final bool isActivated,
      final String? registerDate,
      final String? expiredDate,
      final bool photo,
      final Map<String, dynamic> fields,
      final String? vehicleId}) = _$DriverDetailResponseImpl;
  const _DriverDetailResponse._() : super._();

  factory _DriverDetailResponse.fromJson(Map<String, dynamic> json) =
      _$DriverDetailResponseImpl.fromJson;

  @override
  String? get id;
  @override
  String get cityId;
  @override
  String? get rfid;
  @override
  String get name;
  @override
  String? get phone;
  @override
  String? get phone2;
  @override
  String? get address;
  @override
  String? get driverNo;
  @override
  String? get email;
  @override
  String? get licenseNo;
  @override
  String? get registerPlace;
  @override
  String? get username;
  @override
  bool get isActivated;
  @override
  String? get registerDate;
  @override
  String? get expiredDate;
  @override
  bool get photo;
  @override
  Map<String, dynamic> get fields;
  @override
  String? get vehicleId;
  @override
  @JsonKey(ignore: true)
  _$$DriverDetailResponseImplCopyWith<_$DriverDetailResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
