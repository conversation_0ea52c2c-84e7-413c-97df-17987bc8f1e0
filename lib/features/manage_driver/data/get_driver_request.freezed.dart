// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_driver_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GetDriverRequest _$GetDriverRequestFromJson(Map<String, dynamic> json) {
  return _GetDriverRequest.fromJson(json);
}

/// @nodoc
mixin _$GetDriverRequest {
  String get page => throw _privateConstructorUsedError;
  String get pageSize => throw _privateConstructorUsedError;
  String? get sort => throw _privateConstructorUsedError;
  String? get search => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GetDriverRequestCopyWith<GetDriverRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetDriverRequestCopyWith<$Res> {
  factory $GetDriverRequestCopyWith(
          GetDriverRequest value, $Res Function(GetDriverRequest) then) =
      _$GetDriverRequestCopyWithImpl<$Res, GetDriverRequest>;
  @useResult
  $Res call({String page, String pageSize, String? sort, String? search});
}

/// @nodoc
class _$GetDriverRequestCopyWithImpl<$Res, $Val extends GetDriverRequest>
    implements $GetDriverRequestCopyWith<$Res> {
  _$GetDriverRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? pageSize = null,
    Object? sort = freezed,
    Object? search = freezed,
  }) {
    return _then(_value.copyWith(
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as String,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as String,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as String?,
      search: freezed == search
          ? _value.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetDriverRequestImplCopyWith<$Res>
    implements $GetDriverRequestCopyWith<$Res> {
  factory _$$GetDriverRequestImplCopyWith(_$GetDriverRequestImpl value,
          $Res Function(_$GetDriverRequestImpl) then) =
      __$$GetDriverRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String page, String pageSize, String? sort, String? search});
}

/// @nodoc
class __$$GetDriverRequestImplCopyWithImpl<$Res>
    extends _$GetDriverRequestCopyWithImpl<$Res, _$GetDriverRequestImpl>
    implements _$$GetDriverRequestImplCopyWith<$Res> {
  __$$GetDriverRequestImplCopyWithImpl(_$GetDriverRequestImpl _value,
      $Res Function(_$GetDriverRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? pageSize = null,
    Object? sort = freezed,
    Object? search = freezed,
  }) {
    return _then(_$GetDriverRequestImpl(
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as String,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as String,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as String?,
      search: freezed == search
          ? _value.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GetDriverRequestImpl extends _GetDriverRequest {
  const _$GetDriverRequestImpl(
      {this.page = '0', this.pageSize = '10', this.sort = '', this.search = ''})
      : super._();

  factory _$GetDriverRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetDriverRequestImplFromJson(json);

  @override
  @JsonKey()
  final String page;
  @override
  @JsonKey()
  final String pageSize;
  @override
  @JsonKey()
  final String? sort;
  @override
  @JsonKey()
  final String? search;

  @override
  String toString() {
    return 'GetDriverRequest(page: $page, pageSize: $pageSize, sort: $sort, search: $search)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetDriverRequestImpl &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            (identical(other.search, search) || other.search == search));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, page, pageSize, sort, search);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetDriverRequestImplCopyWith<_$GetDriverRequestImpl> get copyWith =>
      __$$GetDriverRequestImplCopyWithImpl<_$GetDriverRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetDriverRequestImplToJson(
      this,
    );
  }
}

abstract class _GetDriverRequest extends GetDriverRequest {
  const factory _GetDriverRequest(
      {final String page,
      final String pageSize,
      final String? sort,
      final String? search}) = _$GetDriverRequestImpl;
  const _GetDriverRequest._() : super._();

  factory _GetDriverRequest.fromJson(Map<String, dynamic> json) =
      _$GetDriverRequestImpl.fromJson;

  @override
  String get page;
  @override
  String get pageSize;
  @override
  String? get sort;
  @override
  String? get search;
  @override
  @JsonKey(ignore: true)
  _$$GetDriverRequestImplCopyWith<_$GetDriverRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
