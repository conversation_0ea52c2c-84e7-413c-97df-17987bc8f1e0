// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'plate_response.freezed.dart';
part 'plate_response.g.dart';

@freezed
class PlateResponse with _$PlateResponse {
  const factory PlateResponse({
    @Default("") String value,
    @Default("") String label,
  }) = _PlateResponse;

  factory PlateResponse.fromJson(Map<String, dynamic> json) =>
      _$PlateResponseFromJson(json);

  const PlateResponse._();
}

extension PlateResponseEx on List<PlateResponse>? {
  List<String>? get listLabel {
    if (this?.isEmpty == true) return [];
    return this!.map((e) => e.label).toList();
  }

  PlateResponse? getPlateByValue(String? value) {
    if (this?.isEmpty == true) return null;
    return this!.firstWhere(
      (element) => element.value == value,
      orElse: () => const PlateResponse(value: "", label: ""),
    );
  }
}
