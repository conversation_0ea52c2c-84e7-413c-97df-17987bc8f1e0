// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'driver_detail_reponse.freezed.dart';
part 'driver_detail_reponse.g.dart';

@freezed
class DriverDetailResponse with _$DriverDetailResponse {
  const factory DriverDetailResponse({
    @Default("") String? id,
    required String cityId,
    @Default("") String? rfid,
    required String name,
    @Default("") String? phone,
    @Default("") String? phone2,
    @Default("") String? address,
    @Default("") String? driverNo,
    @Default("") String? email,
    @Default("") String? licenseNo,
    @Default("") String? registerPlace,
    @Default("") String? username,
    @Default(false) bool isActivated,
    @Default("") String? registerDate,
    @Default("") String? expiredDate,
    @Default(false) bool photo,
    @Default({}) Map<String, dynamic> fields,
    @Default("") String? vehicleId,
  }) = _DriverDetailResponse;

  factory DriverDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$DriverDetailResponseFromJson(json);

  const DriverDetailResponse._();
}
