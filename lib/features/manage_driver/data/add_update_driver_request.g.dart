// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_update_driver_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AddUpdateDriverRequestImpl _$$AddUpdateDriverRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$AddUpdateDriverRequestImpl(
      id: json['Id'] as String? ?? "",
      name: json['Name'] as String,
      email: json['Email'] as String? ?? "",
      phone: json['Phone'] as String? ?? "",
      phone2: json['Phone2'] as String? ?? "",
      address: json['Address'] as String? ?? "",
      cityId: json['CityId'] as String,
      driverNo: json['DriverNo'] as String? ?? "",
      rfid: json['RFID'] as String? ?? "",
      licenseNo: json['LicenseNo'] as String? ?? "",
      registerPlace: json['RegisterPlace'] as String? ?? "",
      registerDate: (json['RegisterDate'] as num?)?.toInt() ?? 0,
      expiredDate: (json['ExpiredDate'] as num?)?.toInt() ?? 0,
      isActivated: json['IsActivated'] as bool?,
      vehicleId: json['VehicleId'] as String? ?? "",
      forms:
          json['forms'] as Map<String, dynamic>? ?? const <String, dynamic>{},
      file: json['file'] as String?,
    );

Map<String, dynamic> _$$AddUpdateDriverRequestImplToJson(
        _$AddUpdateDriverRequestImpl instance) =>
    <String, dynamic>{
      'Id': instance.id,
      'Name': instance.name,
      'Email': instance.email,
      'Phone': instance.phone,
      'Phone2': instance.phone2,
      'Address': instance.address,
      'CityId': instance.cityId,
      'DriverNo': instance.driverNo,
      'RFID': instance.rfid,
      'LicenseNo': instance.licenseNo,
      'RegisterPlace': instance.registerPlace,
      'RegisterDate': instance.registerDate,
      'ExpiredDate': instance.expiredDate,
      'IsActivated': instance.isActivated,
      'VehicleId': instance.vehicleId,
      'forms': instance.forms,
      'file': instance.file,
    };
