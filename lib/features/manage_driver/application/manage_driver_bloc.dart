import 'package:avema_v2/application/base_bloc.dart';
import 'package:avema_v2/application/enums/avema_enums.dart';
import 'package:avema_v2/application/utils/avema_helper.dart';
import 'package:avema_v2/features/manage_driver/data/add_update_driver_request.dart';
import 'package:avema_v2/features/manage_driver/data/city_response.dart';
import 'package:avema_v2/features/manage_driver/data/driver_detail_reponse.dart';
import 'package:avema_v2/features/manage_driver/data/driver_response.dart';
import 'package:avema_v2/features/manage_driver/data/get_driver_request.dart';
import 'package:avema_v2/features/manage_driver/data/plate_response.dart';
import 'package:avema_v2/features/manage_driver/repository/driver_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:stream_transform/stream_transform.dart';

part 'manage_driver_event.dart';
part 'manage_driver_state.dart';

class ManageDriverBloc extends BaseBloc<ManageDriverEvent, ManageDriverState> {
  final DriverRepository driverRepository;
  ManageDriverBloc(this.driverRepository) : super(ManageDriverState.initial()) {
    on<GetListDriverEvent>(_onGetListDriver);
    on<LoadMoreDriverEvent>(_onLoadMoreDriver);
    on<SearchDriverEvent>(_onSearchDriver);
    on<ClearSearchDriverEvent>(_onClearSearchDriver);
    on<ClearSortEvent>(_onClearSort);
    on<UpdateActivateDriverEvent>(_onUpdateActivateDriver);
    on<SortTypeSelectedEvent>(_onSortTypeSelected);
    on<SortDriversByType>(
      _onSortDriversByType,
      transformer: (events, mapper) => events
          .throttle(const Duration(milliseconds: 600))
          .asyncExpand(mapper),
    );
    on<PickImageEvent>(_onPickImage);
    on<ClearImageEvent>(_onClearImage);
    on<UpdateActivated>(_onUpdateActivated);
    on<InitActivated>(_onInitActivated);
    on<GetCityEvent>(_onGetCity);
    on<GetPlateEvent>(_onGetPlate);
    on<GetDriverDetailEvent>(_onGetDriverDetail);
    on<ClearDetailDriverEvent>(_onClearDetailDriver);
    on<ResetStateEvent>(_onResetState);
    on<DeleteDriverEvent>(_onDeleteDriver);
    on<AddDriverEvent>(_onAddDriver);
    on<UpdateDriverEvent>(_onUpdateDriver);
  }

  Future<void> _onGetListDriver(
    GetListDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    if (event.isRefresh) {
      emit(state.copyWith(
        status: ManageDriverStatus.loading,
        currentPage: 1,
        hasMore: true,
      ));
    } else {
      emit(state.copyWith(status: ManageDriverStatus.loading));
    }

    final response = await driverRepository.getAllDriver(
      request: event.request,
    );

    response.fold(
      (failure) {
        emit(state.copyWith(status: ManageDriverStatus.failure));
      },
      (right) {
        final result = right.data ?? [];
        final hasMore = result.length >= 10;

        emit(
          state.copyWith(
            status: ManageDriverStatus.success,
            listDriver: result,
            listDriverSearched: result,
            listDriverSorted: result,
            currentPage: 1,
            hasMore: hasMore,
          ),
        );
      },
    );
  }

  Future<void> _onLoadMoreDriver(
    LoadMoreDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    if (!state.hasMore ||
        state.isLoadingMore ||
        state.status == ManageDriverStatus.loading) {
      return;
    }

    emit(state.copyWith(isLoadingMore: true));

    final nextPage = state.currentPage + 1;
    final request = GetDriverRequest(
      page: nextPage.toString(),
      search: state.searchQuery,
      sort: state.sortParameter,
    );

    final response = await driverRepository.getAllDriver(request: request);

    response.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingMore: false,
        ));
      },
      (right) {
        final newDrivers = right.data ?? [];

        final hasMore = newDrivers.length >= 10;

        final existingIds = state.listDriver?.map((e) => e.id).toSet() ?? {};
        final uniqueNewDrivers = newDrivers
            .where((driver) => !existingIds.contains(driver.id))
            .toList();

        final List<DriverData> updatedList = [
          ...state.listDriver ?? [],
          ...uniqueNewDrivers
        ];

        emit(
          state.copyWith(
            listDriver: updatedList,
            listDriverSearched: updatedList,
            listDriverSorted: updatedList,
            currentPage: nextPage,
            hasMore: hasMore,
            isLoadingMore: false,
          ),
        );
      },
    );
  }

  Future<void> _onSearchDriver(
    SearchDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    final searchKey = event.searchKey.trim();

    emit(state.copyWith(
      searchQuery: searchKey.isEmpty ? null : searchKey,
      status: ManageDriverStatus.loading,
    ));

    final request = GetDriverRequest(
      search: searchKey.isEmpty ? null : searchKey,
      sort: state.sortParameter,
    );

    final response = await driverRepository.getAllDriver(request: request);

    response.fold(
      (failure) {
        emit(state.copyWith(status: ManageDriverStatus.failure));
      },
      (right) {
        final result = right.data ?? [];
        final hasMore = result.length >= 10;

        emit(
          state.copyWith(
            status: ManageDriverStatus.success,
            listDriver: result,
            listDriverSearched: result,
            listDriverSorted: result,
            currentPage: 1,
            hasMore: hasMore,
          ),
        );
      },
    );
  }

  Future<void> _onClearSearchDriver(
    ClearSearchDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(
      searchQuery: "",
      status: ManageDriverStatus.loading,
    ));

    final request = GetDriverRequest(
      sort: state.sortParameter,
    );

    final response = await driverRepository.getAllDriver(request: request);

    response.fold(
      (failure) {
        emit(state.copyWith(status: ManageDriverStatus.failure));
      },
      (right) {
        final result = right.data ?? [];
        final hasMore = result.length >= 10;

        emit(
          state.copyWith(
            status: ManageDriverStatus.success,
            listDriver: result,
            listDriverSearched: result,
            listDriverSorted: result,
            currentPage: 1,
            hasMore: hasMore,
          ),
        );
      },
    );
  }

  Future<void> _onClearSort(
    ClearSortEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(
      sortTypeSelected: const NullableValue(null),
      sortDirection: SortDirection.none,
      sortParameter: null,
      status: ManageDriverStatus.loading,
    ));

    final request = GetDriverRequest(
      search: state.searchQuery,
    );

    final response = await driverRepository.getAllDriver(request: request);

    response.fold(
      (failure) {
        emit(state.copyWith(status: ManageDriverStatus.failure));
      },
      (right) {
        final result = right.data ?? [];
        final hasMore = result.length >= 10;

        emit(
          state.copyWith(
            status: ManageDriverStatus.success,
            listDriver: result,
            listDriverSearched: result,
            listDriverSorted: result,
            currentPage: 1,
            hasMore: hasMore,
          ),
        );
      },
    );
  }

  Future<void> _onUpdateActivateDriver(
    UpdateActivateDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    final listDriver = state.listDriver?.map((driver) {
      if (driver.id == event.id) {
        return driver.copyWith(
          isActivated: event.isActivated,
        );
      }
      return driver;
    }).toList();
    emit(state.copyWith(listDriver: listDriver));
  }

  Future<void> _onSortTypeSelected(
    SortTypeSelectedEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(sortTypeSelected: NullableValue(event.sortType)));
  }

  Future<void> _onSortDriversByType(
    SortDriversByType event,
    Emitter<ManageDriverState> emit,
  ) async {
    SortDirection newSortDirection;
    DriverSortType? newSortType = event.sortType;

    if (state.sortTypeSelected == event.sortType) {
      if (event.sortType == null) {
        switch (state.sortDirection) {
          case SortDirection.none:
            newSortDirection = SortDirection.ascending;
            break;
          case SortDirection.ascending:
            newSortDirection = SortDirection.descending;
            break;
          case SortDirection.descending:
            newSortDirection = SortDirection.none;
            newSortType = null;
            break;
        }
      } else {
        switch (state.sortDirection) {
          case SortDirection.none:
          case SortDirection.ascending:
            newSortDirection = SortDirection.descending;
            break;
          case SortDirection.descending:
            newSortDirection = SortDirection.ascending;
            break;
        }
      }
    } else {
      newSortDirection = SortDirection.ascending;
    }

    if (newSortDirection == SortDirection.none) {
      emit(state.copyWith(
        sortTypeSelected: NullableValue(newSortType),
        sortDirection: newSortDirection,
        sortParameter: null,
      ));
      return;
    }

    final type = event.sortType?.value ?? '';
    final sort = newSortDirection.value;
    final listSort = '[["$type","$sort"]]';

    // Nếu listDriver rỗng, chỉ cập nhật state mà không gọi API
    if (event.listDriver.isEmpty) {
      emit(state.copyWith(
        sortTypeSelected: NullableValue(newSortType),
        sortParameter: listSort,
        sortDirection: newSortDirection,
      ));
      return;
    }

    emit(state.copyWith(
      sortTypeSelected: NullableValue(newSortType),
      sortParameter: listSort,
      sortDirection: newSortDirection,
      status: ManageDriverStatus.loading,
    ));

    final request = GetDriverRequest(
      page: '0',
      pageSize: '10',
      search: state.searchQuery,
      sort: listSort,
    );

    final response = await driverRepository.getAllDriver(request: request);

    response.fold(
      (failure) {
        emit(state.copyWith(status: ManageDriverStatus.failure));
      },
      (right) {
        final result = right.data ?? [];
        final hasMore = result.length >= 10;

        emit(
          state.copyWith(
            status: ManageDriverStatus.success,
            listDriver: result,
            listDriverSearched: result,
            listDriverSorted: result,
            currentPage: 1,
            hasMore: hasMore,
          ),
        );
      },
    );
  }

  List<DriverData> _filterDriversByType(
    List<DriverData> listDriver,
    DriverSortType? sortType,
  ) {
    if (sortType == null) {
      return listDriver;
    }

    final fieldGetter = AvemaHelper.getFieldExtractor(sortType);
    return listDriver.where((driver) {
      final value = fieldGetter(driver);
      return value != null && value.isNotEmpty;
    }).toList();
  }

  int getDriverCountByType(DriverSortType sortType) {
    if (state.listDriver == null) return 0;
    return _filterDriversByType(state.listDriver!, sortType).length;
  }

  Future<void> _onPickImage(
    PickImageEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(imagePicked: NullableValue(event.image)));
  }

  Future<void> _onClearImage(
    ClearImageEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(imagePicked: const NullableValue(null)));
  }

  Future<void> _onUpdateActivated(
    UpdateActivated event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(isActivated: event.isActivated));
  }

  Future<void> _onInitActivated(
    InitActivated event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(isActivated: event.isActivated));
  }

  Future<void> _onGetCity(
    GetCityEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    final response = await driverRepository.getCityList();
    response.fold(
      (failure) {
        emit(state.copyWith(listCity: null));
      },
      (result) {
        emit(state.copyWith(listCity: result));
      },
    );
  }

  Future<void> _onGetPlate(
    GetPlateEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    final response = await driverRepository.getPlateList();
    response.fold(
      (failure) {
        emit(state.copyWith(listPlate: null));
      },
      (result) {
        emit(state.copyWith(listPlate: result));
      },
    );
  }

  Future<void> _onGetDriverDetail(
    GetDriverDetailEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(detailDriverStatus: DetailDriverStatus.loading));
    final response = await driverRepository.getDriverDetail(id: event.id);
    response.fold(
      (failure) {
        debugPrint('[ManageDriverBloc] failure: $failure');
        emit(state.copyWith(detailDriverStatus: DetailDriverStatus.failure));
      },
      (right) {
        emit(
          state.copyWith(
            detailDriverStatus: DetailDriverStatus.success,
            driverDetail: right,
          ),
        );
      },
    );
  }

  Future<void> _onClearDetailDriver(
    ClearDetailDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(detailDriverStatus: DetailDriverStatus.initial));
  }

  Future<void> _onResetState(
    ResetStateEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(
      state.copyWith(
        addDriverStatus: AddDriverStatus.initial,
        deleteDriverStatus: DeleteDriverStatus.initial,
        updateDriverStatus: UpdateDriverStatus.initial,
        message: null,
      ),
    );
  }

  Future<void> _onDeleteDriver(
    DeleteDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(deleteDriverStatus: DeleteDriverStatus.loading));
    final response = await driverRepository.deleteDriver(id: event.id);
    response.fold(
      (failure) {
        emit(state.copyWith(deleteDriverStatus: DeleteDriverStatus.failure));
      },
      (right) {
        emit(
          state.copyWith(
            deleteDriverStatus: DeleteDriverStatus.success,
            message: right.keyLanguage,
          ),
        );
      },
    );
  }

  Future<void> _onAddDriver(
    AddDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(addDriverStatus: AddDriverStatus.loading));
    final response = await driverRepository.addDriver(
      request: event.request,
      image: event.image,
    );
    response.fold(
      (failure) {
        emit(state.copyWith(addDriverStatus: AddDriverStatus.failure));
      },
      (right) {
        emit(
          state.copyWith(
            addDriverStatus: AddDriverStatus.success,
            message: right.keyLanguage,
          ),
        );
      },
    );
  }

  Future<void> _onUpdateDriver(
    UpdateDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(updateDriverStatus: UpdateDriverStatus.loading));
    final response = await driverRepository.updateDriver(
      request: event.request,
      image: event.image,
    );
    response.fold(
      (failure) {
        emit(
          state.copyWith(
            updateDriverStatus: UpdateDriverStatus.failure,
            message: failure.mapOrNull(
              unexpected: (e) => e.error,
            ),
          ),
        );
      },
      (right) {
        debugPrint('[ManageDriverBloc] right: $right');
        emit(
          state.copyWith(
            updateDriverStatus: UpdateDriverStatus.success,
            message: right.keyLanguage,
          ),
        );
      },
    );
  }
}
