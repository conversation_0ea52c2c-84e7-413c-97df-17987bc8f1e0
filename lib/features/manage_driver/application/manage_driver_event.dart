part of 'manage_driver_bloc.dart';

abstract class ManageDriverEvent {}

class GetListDriverEvent extends ManageDriverEvent {
  final bool isRefresh;
  final GetDriverRequest request;
  GetListDriverEvent({
    this.isRefresh = false,
    required this.request,
  });
}

class LoadMoreDriverEvent extends ManageDriverEvent {}

class SearchDriverEvent extends ManageDriverEvent {
  final String searchKey;
  SearchDriverEvent({required this.searchKey});
}

class ClearSearchDriverEvent extends ManageDriverEvent {}

class UpdateActivateDriverEvent extends ManageDriverEvent {
  final String id;
  final bool isActivated;
  UpdateActivateDriverEvent({
    required this.id,
    this.isActivated = false,
  });
}

class SortTypeSelectedEvent extends ManageDriverEvent {
  final DriverSortType? sortType;
  SortTypeSelectedEvent({
    this.sortType,
  });
}

class SortDriversByType extends ManageDriverEvent {
  final List<DriverData> listDriver;
  final DriverSortType? sortType;
  final SortDirection sortDirection;
  SortDriversByType({
    required this.listDriver,
    this.sortType,
    required this.sortDirection,
  });
}

class PickImageEvent extends ManageDriverEvent {
  final XFile image;
  PickImageEvent({required this.image});
}

class ClearImageEvent extends ManageDriverEvent {}

class UpdateActivated extends ManageDriverEvent {
  final String? id;
  final bool? isActivated;
  UpdateActivated({
    this.id,
    this.isActivated = false,
  });
}

class InitActivated extends ManageDriverEvent {
  final bool isActivated;
  InitActivated({required this.isActivated});
}

class GetCityEvent extends ManageDriverEvent {}

class GetPlateEvent extends ManageDriverEvent {}

class GetDriverDetailEvent extends ManageDriverEvent {
  final String id;
  GetDriverDetailEvent({required this.id});
}

class ClearDetailDriverEvent extends ManageDriverEvent {}

class ResetStateEvent extends ManageDriverEvent {}

class DeleteDriverEvent extends ManageDriverEvent {
  final String id;
  DeleteDriverEvent({required this.id});
}

class GetDriverPhoto extends ManageDriverEvent {
  final String id;
  GetDriverPhoto({required this.id});
}

class AddDriverEvent extends ManageDriverEvent {
  final AddUpdateDriverRequest request;
  final XFile? image;
  AddDriverEvent({
    required this.request,
    this.image,
  });
}

class UpdateDriverEvent extends ManageDriverEvent {
  final AddUpdateDriverRequest request;
  final XFile? image;
  UpdateDriverEvent({
    required this.request,
    this.image,
  });
}
