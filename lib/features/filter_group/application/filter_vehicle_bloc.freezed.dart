// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'filter_vehicle_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FilterVehicleEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) addVehicle,
    required TResult Function(String id) removeVehicle,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        addAllVehicleOfGroup,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        removeAllVehicleOfGroup,
    required TResult Function() removeAll,
    required TResult Function() addAll,
    required TResult Function(
            List<String> selectedIds, List<VehicleGroupDTO> listVehicleGroupDTO)
        syncDataWhenInitial,
    required TResult Function(String searchText) search,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? addVehicle,
    TResult? Function(String id)? removeVehicle,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult? Function()? removeAll,
    TResult? Function()? addAll,
    TResult? Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult? Function(String searchText)? search,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? addVehicle,
    TResult Function(String id)? removeVehicle,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult Function()? removeAll,
    TResult Function()? addAll,
    TResult Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult Function(String searchText)? search,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddVehicleEvent value) addVehicle,
    required TResult Function(_RemoveVehicleEvent value) removeVehicle,
    required TResult Function(_AddAllVehicleOfGroup value) addAllVehicleOfGroup,
    required TResult Function(_RemoveAllVehicleOfGroupEvent value)
        removeAllVehicleOfGroup,
    required TResult Function(_RemoveAllEvent value) removeAll,
    required TResult Function(_AddAllEvent value) addAll,
    required TResult Function(_SyncDataWhenInitialEvent value)
        syncDataWhenInitial,
    required TResult Function(_Search value) search,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddVehicleEvent value)? addVehicle,
    TResult? Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult? Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult? Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult? Function(_RemoveAllEvent value)? removeAll,
    TResult? Function(_AddAllEvent value)? addAll,
    TResult? Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult? Function(_Search value)? search,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddVehicleEvent value)? addVehicle,
    TResult Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult Function(_RemoveAllEvent value)? removeAll,
    TResult Function(_AddAllEvent value)? addAll,
    TResult Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult Function(_Search value)? search,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FilterVehicleEventCopyWith<$Res> {
  factory $FilterVehicleEventCopyWith(
          FilterVehicleEvent value, $Res Function(FilterVehicleEvent) then) =
      _$FilterVehicleEventCopyWithImpl<$Res, FilterVehicleEvent>;
}

/// @nodoc
class _$FilterVehicleEventCopyWithImpl<$Res, $Val extends FilterVehicleEvent>
    implements $FilterVehicleEventCopyWith<$Res> {
  _$FilterVehicleEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$AddVehicleEventImplCopyWith<$Res> {
  factory _$$AddVehicleEventImplCopyWith(_$AddVehicleEventImpl value,
          $Res Function(_$AddVehicleEventImpl) then) =
      __$$AddVehicleEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$AddVehicleEventImplCopyWithImpl<$Res>
    extends _$FilterVehicleEventCopyWithImpl<$Res, _$AddVehicleEventImpl>
    implements _$$AddVehicleEventImplCopyWith<$Res> {
  __$$AddVehicleEventImplCopyWithImpl(
      _$AddVehicleEventImpl _value, $Res Function(_$AddVehicleEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$AddVehicleEventImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AddVehicleEventImpl implements _AddVehicleEvent {
  const _$AddVehicleEventImpl({required this.id});

  @override
  final String id;

  @override
  String toString() {
    return 'FilterVehicleEvent.addVehicle(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddVehicleEventImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AddVehicleEventImplCopyWith<_$AddVehicleEventImpl> get copyWith =>
      __$$AddVehicleEventImplCopyWithImpl<_$AddVehicleEventImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) addVehicle,
    required TResult Function(String id) removeVehicle,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        addAllVehicleOfGroup,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        removeAllVehicleOfGroup,
    required TResult Function() removeAll,
    required TResult Function() addAll,
    required TResult Function(
            List<String> selectedIds, List<VehicleGroupDTO> listVehicleGroupDTO)
        syncDataWhenInitial,
    required TResult Function(String searchText) search,
  }) {
    return addVehicle(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? addVehicle,
    TResult? Function(String id)? removeVehicle,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult? Function()? removeAll,
    TResult? Function()? addAll,
    TResult? Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult? Function(String searchText)? search,
  }) {
    return addVehicle?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? addVehicle,
    TResult Function(String id)? removeVehicle,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult Function()? removeAll,
    TResult Function()? addAll,
    TResult Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult Function(String searchText)? search,
    required TResult orElse(),
  }) {
    if (addVehicle != null) {
      return addVehicle(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddVehicleEvent value) addVehicle,
    required TResult Function(_RemoveVehicleEvent value) removeVehicle,
    required TResult Function(_AddAllVehicleOfGroup value) addAllVehicleOfGroup,
    required TResult Function(_RemoveAllVehicleOfGroupEvent value)
        removeAllVehicleOfGroup,
    required TResult Function(_RemoveAllEvent value) removeAll,
    required TResult Function(_AddAllEvent value) addAll,
    required TResult Function(_SyncDataWhenInitialEvent value)
        syncDataWhenInitial,
    required TResult Function(_Search value) search,
  }) {
    return addVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddVehicleEvent value)? addVehicle,
    TResult? Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult? Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult? Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult? Function(_RemoveAllEvent value)? removeAll,
    TResult? Function(_AddAllEvent value)? addAll,
    TResult? Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult? Function(_Search value)? search,
  }) {
    return addVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddVehicleEvent value)? addVehicle,
    TResult Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult Function(_RemoveAllEvent value)? removeAll,
    TResult Function(_AddAllEvent value)? addAll,
    TResult Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult Function(_Search value)? search,
    required TResult orElse(),
  }) {
    if (addVehicle != null) {
      return addVehicle(this);
    }
    return orElse();
  }
}

abstract class _AddVehicleEvent implements FilterVehicleEvent {
  const factory _AddVehicleEvent({required final String id}) =
      _$AddVehicleEventImpl;

  String get id;
  @JsonKey(ignore: true)
  _$$AddVehicleEventImplCopyWith<_$AddVehicleEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RemoveVehicleEventImplCopyWith<$Res> {
  factory _$$RemoveVehicleEventImplCopyWith(_$RemoveVehicleEventImpl value,
          $Res Function(_$RemoveVehicleEventImpl) then) =
      __$$RemoveVehicleEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$RemoveVehicleEventImplCopyWithImpl<$Res>
    extends _$FilterVehicleEventCopyWithImpl<$Res, _$RemoveVehicleEventImpl>
    implements _$$RemoveVehicleEventImplCopyWith<$Res> {
  __$$RemoveVehicleEventImplCopyWithImpl(_$RemoveVehicleEventImpl _value,
      $Res Function(_$RemoveVehicleEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$RemoveVehicleEventImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RemoveVehicleEventImpl implements _RemoveVehicleEvent {
  const _$RemoveVehicleEventImpl({required this.id});

  @override
  final String id;

  @override
  String toString() {
    return 'FilterVehicleEvent.removeVehicle(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemoveVehicleEventImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RemoveVehicleEventImplCopyWith<_$RemoveVehicleEventImpl> get copyWith =>
      __$$RemoveVehicleEventImplCopyWithImpl<_$RemoveVehicleEventImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) addVehicle,
    required TResult Function(String id) removeVehicle,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        addAllVehicleOfGroup,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        removeAllVehicleOfGroup,
    required TResult Function() removeAll,
    required TResult Function() addAll,
    required TResult Function(
            List<String> selectedIds, List<VehicleGroupDTO> listVehicleGroupDTO)
        syncDataWhenInitial,
    required TResult Function(String searchText) search,
  }) {
    return removeVehicle(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? addVehicle,
    TResult? Function(String id)? removeVehicle,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult? Function()? removeAll,
    TResult? Function()? addAll,
    TResult? Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult? Function(String searchText)? search,
  }) {
    return removeVehicle?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? addVehicle,
    TResult Function(String id)? removeVehicle,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult Function()? removeAll,
    TResult Function()? addAll,
    TResult Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult Function(String searchText)? search,
    required TResult orElse(),
  }) {
    if (removeVehicle != null) {
      return removeVehicle(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddVehicleEvent value) addVehicle,
    required TResult Function(_RemoveVehicleEvent value) removeVehicle,
    required TResult Function(_AddAllVehicleOfGroup value) addAllVehicleOfGroup,
    required TResult Function(_RemoveAllVehicleOfGroupEvent value)
        removeAllVehicleOfGroup,
    required TResult Function(_RemoveAllEvent value) removeAll,
    required TResult Function(_AddAllEvent value) addAll,
    required TResult Function(_SyncDataWhenInitialEvent value)
        syncDataWhenInitial,
    required TResult Function(_Search value) search,
  }) {
    return removeVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddVehicleEvent value)? addVehicle,
    TResult? Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult? Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult? Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult? Function(_RemoveAllEvent value)? removeAll,
    TResult? Function(_AddAllEvent value)? addAll,
    TResult? Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult? Function(_Search value)? search,
  }) {
    return removeVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddVehicleEvent value)? addVehicle,
    TResult Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult Function(_RemoveAllEvent value)? removeAll,
    TResult Function(_AddAllEvent value)? addAll,
    TResult Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult Function(_Search value)? search,
    required TResult orElse(),
  }) {
    if (removeVehicle != null) {
      return removeVehicle(this);
    }
    return orElse();
  }
}

abstract class _RemoveVehicleEvent implements FilterVehicleEvent {
  const factory _RemoveVehicleEvent({required final String id}) =
      _$RemoveVehicleEventImpl;

  String get id;
  @JsonKey(ignore: true)
  _$$RemoveVehicleEventImplCopyWith<_$RemoveVehicleEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddAllVehicleOfGroupImplCopyWith<$Res> {
  factory _$$AddAllVehicleOfGroupImplCopyWith(_$AddAllVehicleOfGroupImpl value,
          $Res Function(_$AddAllVehicleOfGroupImpl) then) =
      __$$AddAllVehicleOfGroupImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VehicleGroupDTO vehicleGroupDTO});

  $VehicleGroupDTOCopyWith<$Res> get vehicleGroupDTO;
}

/// @nodoc
class __$$AddAllVehicleOfGroupImplCopyWithImpl<$Res>
    extends _$FilterVehicleEventCopyWithImpl<$Res, _$AddAllVehicleOfGroupImpl>
    implements _$$AddAllVehicleOfGroupImplCopyWith<$Res> {
  __$$AddAllVehicleOfGroupImplCopyWithImpl(_$AddAllVehicleOfGroupImpl _value,
      $Res Function(_$AddAllVehicleOfGroupImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleGroupDTO = null,
  }) {
    return _then(_$AddAllVehicleOfGroupImpl(
      vehicleGroupDTO: null == vehicleGroupDTO
          ? _value.vehicleGroupDTO
          : vehicleGroupDTO // ignore: cast_nullable_to_non_nullable
              as VehicleGroupDTO,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleGroupDTOCopyWith<$Res> get vehicleGroupDTO {
    return $VehicleGroupDTOCopyWith<$Res>(_value.vehicleGroupDTO, (value) {
      return _then(_value.copyWith(vehicleGroupDTO: value));
    });
  }
}

/// @nodoc

class _$AddAllVehicleOfGroupImpl implements _AddAllVehicleOfGroup {
  const _$AddAllVehicleOfGroupImpl({required this.vehicleGroupDTO});

  @override
  final VehicleGroupDTO vehicleGroupDTO;

  @override
  String toString() {
    return 'FilterVehicleEvent.addAllVehicleOfGroup(vehicleGroupDTO: $vehicleGroupDTO)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddAllVehicleOfGroupImpl &&
            (identical(other.vehicleGroupDTO, vehicleGroupDTO) ||
                other.vehicleGroupDTO == vehicleGroupDTO));
  }

  @override
  int get hashCode => Object.hash(runtimeType, vehicleGroupDTO);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AddAllVehicleOfGroupImplCopyWith<_$AddAllVehicleOfGroupImpl>
      get copyWith =>
          __$$AddAllVehicleOfGroupImplCopyWithImpl<_$AddAllVehicleOfGroupImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) addVehicle,
    required TResult Function(String id) removeVehicle,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        addAllVehicleOfGroup,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        removeAllVehicleOfGroup,
    required TResult Function() removeAll,
    required TResult Function() addAll,
    required TResult Function(
            List<String> selectedIds, List<VehicleGroupDTO> listVehicleGroupDTO)
        syncDataWhenInitial,
    required TResult Function(String searchText) search,
  }) {
    return addAllVehicleOfGroup(vehicleGroupDTO);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? addVehicle,
    TResult? Function(String id)? removeVehicle,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult? Function()? removeAll,
    TResult? Function()? addAll,
    TResult? Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult? Function(String searchText)? search,
  }) {
    return addAllVehicleOfGroup?.call(vehicleGroupDTO);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? addVehicle,
    TResult Function(String id)? removeVehicle,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult Function()? removeAll,
    TResult Function()? addAll,
    TResult Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult Function(String searchText)? search,
    required TResult orElse(),
  }) {
    if (addAllVehicleOfGroup != null) {
      return addAllVehicleOfGroup(vehicleGroupDTO);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddVehicleEvent value) addVehicle,
    required TResult Function(_RemoveVehicleEvent value) removeVehicle,
    required TResult Function(_AddAllVehicleOfGroup value) addAllVehicleOfGroup,
    required TResult Function(_RemoveAllVehicleOfGroupEvent value)
        removeAllVehicleOfGroup,
    required TResult Function(_RemoveAllEvent value) removeAll,
    required TResult Function(_AddAllEvent value) addAll,
    required TResult Function(_SyncDataWhenInitialEvent value)
        syncDataWhenInitial,
    required TResult Function(_Search value) search,
  }) {
    return addAllVehicleOfGroup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddVehicleEvent value)? addVehicle,
    TResult? Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult? Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult? Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult? Function(_RemoveAllEvent value)? removeAll,
    TResult? Function(_AddAllEvent value)? addAll,
    TResult? Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult? Function(_Search value)? search,
  }) {
    return addAllVehicleOfGroup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddVehicleEvent value)? addVehicle,
    TResult Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult Function(_RemoveAllEvent value)? removeAll,
    TResult Function(_AddAllEvent value)? addAll,
    TResult Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult Function(_Search value)? search,
    required TResult orElse(),
  }) {
    if (addAllVehicleOfGroup != null) {
      return addAllVehicleOfGroup(this);
    }
    return orElse();
  }
}

abstract class _AddAllVehicleOfGroup implements FilterVehicleEvent {
  const factory _AddAllVehicleOfGroup(
          {required final VehicleGroupDTO vehicleGroupDTO}) =
      _$AddAllVehicleOfGroupImpl;

  VehicleGroupDTO get vehicleGroupDTO;
  @JsonKey(ignore: true)
  _$$AddAllVehicleOfGroupImplCopyWith<_$AddAllVehicleOfGroupImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RemoveAllVehicleOfGroupEventImplCopyWith<$Res> {
  factory _$$RemoveAllVehicleOfGroupEventImplCopyWith(
          _$RemoveAllVehicleOfGroupEventImpl value,
          $Res Function(_$RemoveAllVehicleOfGroupEventImpl) then) =
      __$$RemoveAllVehicleOfGroupEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VehicleGroupDTO vehicleGroupDTO});

  $VehicleGroupDTOCopyWith<$Res> get vehicleGroupDTO;
}

/// @nodoc
class __$$RemoveAllVehicleOfGroupEventImplCopyWithImpl<$Res>
    extends _$FilterVehicleEventCopyWithImpl<$Res,
        _$RemoveAllVehicleOfGroupEventImpl>
    implements _$$RemoveAllVehicleOfGroupEventImplCopyWith<$Res> {
  __$$RemoveAllVehicleOfGroupEventImplCopyWithImpl(
      _$RemoveAllVehicleOfGroupEventImpl _value,
      $Res Function(_$RemoveAllVehicleOfGroupEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleGroupDTO = null,
  }) {
    return _then(_$RemoveAllVehicleOfGroupEventImpl(
      vehicleGroupDTO: null == vehicleGroupDTO
          ? _value.vehicleGroupDTO
          : vehicleGroupDTO // ignore: cast_nullable_to_non_nullable
              as VehicleGroupDTO,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleGroupDTOCopyWith<$Res> get vehicleGroupDTO {
    return $VehicleGroupDTOCopyWith<$Res>(_value.vehicleGroupDTO, (value) {
      return _then(_value.copyWith(vehicleGroupDTO: value));
    });
  }
}

/// @nodoc

class _$RemoveAllVehicleOfGroupEventImpl
    implements _RemoveAllVehicleOfGroupEvent {
  const _$RemoveAllVehicleOfGroupEventImpl({required this.vehicleGroupDTO});

  @override
  final VehicleGroupDTO vehicleGroupDTO;

  @override
  String toString() {
    return 'FilterVehicleEvent.removeAllVehicleOfGroup(vehicleGroupDTO: $vehicleGroupDTO)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemoveAllVehicleOfGroupEventImpl &&
            (identical(other.vehicleGroupDTO, vehicleGroupDTO) ||
                other.vehicleGroupDTO == vehicleGroupDTO));
  }

  @override
  int get hashCode => Object.hash(runtimeType, vehicleGroupDTO);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RemoveAllVehicleOfGroupEventImplCopyWith<
          _$RemoveAllVehicleOfGroupEventImpl>
      get copyWith => __$$RemoveAllVehicleOfGroupEventImplCopyWithImpl<
          _$RemoveAllVehicleOfGroupEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) addVehicle,
    required TResult Function(String id) removeVehicle,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        addAllVehicleOfGroup,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        removeAllVehicleOfGroup,
    required TResult Function() removeAll,
    required TResult Function() addAll,
    required TResult Function(
            List<String> selectedIds, List<VehicleGroupDTO> listVehicleGroupDTO)
        syncDataWhenInitial,
    required TResult Function(String searchText) search,
  }) {
    return removeAllVehicleOfGroup(vehicleGroupDTO);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? addVehicle,
    TResult? Function(String id)? removeVehicle,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult? Function()? removeAll,
    TResult? Function()? addAll,
    TResult? Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult? Function(String searchText)? search,
  }) {
    return removeAllVehicleOfGroup?.call(vehicleGroupDTO);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? addVehicle,
    TResult Function(String id)? removeVehicle,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult Function()? removeAll,
    TResult Function()? addAll,
    TResult Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult Function(String searchText)? search,
    required TResult orElse(),
  }) {
    if (removeAllVehicleOfGroup != null) {
      return removeAllVehicleOfGroup(vehicleGroupDTO);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddVehicleEvent value) addVehicle,
    required TResult Function(_RemoveVehicleEvent value) removeVehicle,
    required TResult Function(_AddAllVehicleOfGroup value) addAllVehicleOfGroup,
    required TResult Function(_RemoveAllVehicleOfGroupEvent value)
        removeAllVehicleOfGroup,
    required TResult Function(_RemoveAllEvent value) removeAll,
    required TResult Function(_AddAllEvent value) addAll,
    required TResult Function(_SyncDataWhenInitialEvent value)
        syncDataWhenInitial,
    required TResult Function(_Search value) search,
  }) {
    return removeAllVehicleOfGroup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddVehicleEvent value)? addVehicle,
    TResult? Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult? Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult? Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult? Function(_RemoveAllEvent value)? removeAll,
    TResult? Function(_AddAllEvent value)? addAll,
    TResult? Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult? Function(_Search value)? search,
  }) {
    return removeAllVehicleOfGroup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddVehicleEvent value)? addVehicle,
    TResult Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult Function(_RemoveAllEvent value)? removeAll,
    TResult Function(_AddAllEvent value)? addAll,
    TResult Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult Function(_Search value)? search,
    required TResult orElse(),
  }) {
    if (removeAllVehicleOfGroup != null) {
      return removeAllVehicleOfGroup(this);
    }
    return orElse();
  }
}

abstract class _RemoveAllVehicleOfGroupEvent implements FilterVehicleEvent {
  const factory _RemoveAllVehicleOfGroupEvent(
          {required final VehicleGroupDTO vehicleGroupDTO}) =
      _$RemoveAllVehicleOfGroupEventImpl;

  VehicleGroupDTO get vehicleGroupDTO;
  @JsonKey(ignore: true)
  _$$RemoveAllVehicleOfGroupEventImplCopyWith<
          _$RemoveAllVehicleOfGroupEventImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RemoveAllEventImplCopyWith<$Res> {
  factory _$$RemoveAllEventImplCopyWith(_$RemoveAllEventImpl value,
          $Res Function(_$RemoveAllEventImpl) then) =
      __$$RemoveAllEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RemoveAllEventImplCopyWithImpl<$Res>
    extends _$FilterVehicleEventCopyWithImpl<$Res, _$RemoveAllEventImpl>
    implements _$$RemoveAllEventImplCopyWith<$Res> {
  __$$RemoveAllEventImplCopyWithImpl(
      _$RemoveAllEventImpl _value, $Res Function(_$RemoveAllEventImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$RemoveAllEventImpl implements _RemoveAllEvent {
  const _$RemoveAllEventImpl();

  @override
  String toString() {
    return 'FilterVehicleEvent.removeAll()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RemoveAllEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) addVehicle,
    required TResult Function(String id) removeVehicle,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        addAllVehicleOfGroup,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        removeAllVehicleOfGroup,
    required TResult Function() removeAll,
    required TResult Function() addAll,
    required TResult Function(
            List<String> selectedIds, List<VehicleGroupDTO> listVehicleGroupDTO)
        syncDataWhenInitial,
    required TResult Function(String searchText) search,
  }) {
    return removeAll();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? addVehicle,
    TResult? Function(String id)? removeVehicle,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult? Function()? removeAll,
    TResult? Function()? addAll,
    TResult? Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult? Function(String searchText)? search,
  }) {
    return removeAll?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? addVehicle,
    TResult Function(String id)? removeVehicle,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult Function()? removeAll,
    TResult Function()? addAll,
    TResult Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult Function(String searchText)? search,
    required TResult orElse(),
  }) {
    if (removeAll != null) {
      return removeAll();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddVehicleEvent value) addVehicle,
    required TResult Function(_RemoveVehicleEvent value) removeVehicle,
    required TResult Function(_AddAllVehicleOfGroup value) addAllVehicleOfGroup,
    required TResult Function(_RemoveAllVehicleOfGroupEvent value)
        removeAllVehicleOfGroup,
    required TResult Function(_RemoveAllEvent value) removeAll,
    required TResult Function(_AddAllEvent value) addAll,
    required TResult Function(_SyncDataWhenInitialEvent value)
        syncDataWhenInitial,
    required TResult Function(_Search value) search,
  }) {
    return removeAll(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddVehicleEvent value)? addVehicle,
    TResult? Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult? Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult? Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult? Function(_RemoveAllEvent value)? removeAll,
    TResult? Function(_AddAllEvent value)? addAll,
    TResult? Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult? Function(_Search value)? search,
  }) {
    return removeAll?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddVehicleEvent value)? addVehicle,
    TResult Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult Function(_RemoveAllEvent value)? removeAll,
    TResult Function(_AddAllEvent value)? addAll,
    TResult Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult Function(_Search value)? search,
    required TResult orElse(),
  }) {
    if (removeAll != null) {
      return removeAll(this);
    }
    return orElse();
  }
}

abstract class _RemoveAllEvent implements FilterVehicleEvent {
  const factory _RemoveAllEvent() = _$RemoveAllEventImpl;
}

/// @nodoc
abstract class _$$AddAllEventImplCopyWith<$Res> {
  factory _$$AddAllEventImplCopyWith(
          _$AddAllEventImpl value, $Res Function(_$AddAllEventImpl) then) =
      __$$AddAllEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AddAllEventImplCopyWithImpl<$Res>
    extends _$FilterVehicleEventCopyWithImpl<$Res, _$AddAllEventImpl>
    implements _$$AddAllEventImplCopyWith<$Res> {
  __$$AddAllEventImplCopyWithImpl(
      _$AddAllEventImpl _value, $Res Function(_$AddAllEventImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$AddAllEventImpl implements _AddAllEvent {
  const _$AddAllEventImpl();

  @override
  String toString() {
    return 'FilterVehicleEvent.addAll()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$AddAllEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) addVehicle,
    required TResult Function(String id) removeVehicle,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        addAllVehicleOfGroup,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        removeAllVehicleOfGroup,
    required TResult Function() removeAll,
    required TResult Function() addAll,
    required TResult Function(
            List<String> selectedIds, List<VehicleGroupDTO> listVehicleGroupDTO)
        syncDataWhenInitial,
    required TResult Function(String searchText) search,
  }) {
    return addAll();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? addVehicle,
    TResult? Function(String id)? removeVehicle,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult? Function()? removeAll,
    TResult? Function()? addAll,
    TResult? Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult? Function(String searchText)? search,
  }) {
    return addAll?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? addVehicle,
    TResult Function(String id)? removeVehicle,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult Function()? removeAll,
    TResult Function()? addAll,
    TResult Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult Function(String searchText)? search,
    required TResult orElse(),
  }) {
    if (addAll != null) {
      return addAll();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddVehicleEvent value) addVehicle,
    required TResult Function(_RemoveVehicleEvent value) removeVehicle,
    required TResult Function(_AddAllVehicleOfGroup value) addAllVehicleOfGroup,
    required TResult Function(_RemoveAllVehicleOfGroupEvent value)
        removeAllVehicleOfGroup,
    required TResult Function(_RemoveAllEvent value) removeAll,
    required TResult Function(_AddAllEvent value) addAll,
    required TResult Function(_SyncDataWhenInitialEvent value)
        syncDataWhenInitial,
    required TResult Function(_Search value) search,
  }) {
    return addAll(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddVehicleEvent value)? addVehicle,
    TResult? Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult? Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult? Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult? Function(_RemoveAllEvent value)? removeAll,
    TResult? Function(_AddAllEvent value)? addAll,
    TResult? Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult? Function(_Search value)? search,
  }) {
    return addAll?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddVehicleEvent value)? addVehicle,
    TResult Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult Function(_RemoveAllEvent value)? removeAll,
    TResult Function(_AddAllEvent value)? addAll,
    TResult Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult Function(_Search value)? search,
    required TResult orElse(),
  }) {
    if (addAll != null) {
      return addAll(this);
    }
    return orElse();
  }
}

abstract class _AddAllEvent implements FilterVehicleEvent {
  const factory _AddAllEvent() = _$AddAllEventImpl;
}

/// @nodoc
abstract class _$$SyncDataWhenInitialEventImplCopyWith<$Res> {
  factory _$$SyncDataWhenInitialEventImplCopyWith(
          _$SyncDataWhenInitialEventImpl value,
          $Res Function(_$SyncDataWhenInitialEventImpl) then) =
      __$$SyncDataWhenInitialEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<String> selectedIds, List<VehicleGroupDTO> listVehicleGroupDTO});
}

/// @nodoc
class __$$SyncDataWhenInitialEventImplCopyWithImpl<$Res>
    extends _$FilterVehicleEventCopyWithImpl<$Res,
        _$SyncDataWhenInitialEventImpl>
    implements _$$SyncDataWhenInitialEventImplCopyWith<$Res> {
  __$$SyncDataWhenInitialEventImplCopyWithImpl(
      _$SyncDataWhenInitialEventImpl _value,
      $Res Function(_$SyncDataWhenInitialEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedIds = null,
    Object? listVehicleGroupDTO = null,
  }) {
    return _then(_$SyncDataWhenInitialEventImpl(
      selectedIds: null == selectedIds
          ? _value._selectedIds
          : selectedIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listVehicleGroupDTO: null == listVehicleGroupDTO
          ? _value._listVehicleGroupDTO
          : listVehicleGroupDTO // ignore: cast_nullable_to_non_nullable
              as List<VehicleGroupDTO>,
    ));
  }
}

/// @nodoc

class _$SyncDataWhenInitialEventImpl implements _SyncDataWhenInitialEvent {
  const _$SyncDataWhenInitialEventImpl(
      {required final List<String> selectedIds,
      required final List<VehicleGroupDTO> listVehicleGroupDTO})
      : _selectedIds = selectedIds,
        _listVehicleGroupDTO = listVehicleGroupDTO;

  final List<String> _selectedIds;
  @override
  List<String> get selectedIds {
    if (_selectedIds is EqualUnmodifiableListView) return _selectedIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedIds);
  }

  final List<VehicleGroupDTO> _listVehicleGroupDTO;
  @override
  List<VehicleGroupDTO> get listVehicleGroupDTO {
    if (_listVehicleGroupDTO is EqualUnmodifiableListView)
      return _listVehicleGroupDTO;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicleGroupDTO);
  }

  @override
  String toString() {
    return 'FilterVehicleEvent.syncDataWhenInitial(selectedIds: $selectedIds, listVehicleGroupDTO: $listVehicleGroupDTO)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SyncDataWhenInitialEventImpl &&
            const DeepCollectionEquality()
                .equals(other._selectedIds, _selectedIds) &&
            const DeepCollectionEquality()
                .equals(other._listVehicleGroupDTO, _listVehicleGroupDTO));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_selectedIds),
      const DeepCollectionEquality().hash(_listVehicleGroupDTO));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SyncDataWhenInitialEventImplCopyWith<_$SyncDataWhenInitialEventImpl>
      get copyWith => __$$SyncDataWhenInitialEventImplCopyWithImpl<
          _$SyncDataWhenInitialEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) addVehicle,
    required TResult Function(String id) removeVehicle,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        addAllVehicleOfGroup,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        removeAllVehicleOfGroup,
    required TResult Function() removeAll,
    required TResult Function() addAll,
    required TResult Function(
            List<String> selectedIds, List<VehicleGroupDTO> listVehicleGroupDTO)
        syncDataWhenInitial,
    required TResult Function(String searchText) search,
  }) {
    return syncDataWhenInitial(selectedIds, listVehicleGroupDTO);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? addVehicle,
    TResult? Function(String id)? removeVehicle,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult? Function()? removeAll,
    TResult? Function()? addAll,
    TResult? Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult? Function(String searchText)? search,
  }) {
    return syncDataWhenInitial?.call(selectedIds, listVehicleGroupDTO);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? addVehicle,
    TResult Function(String id)? removeVehicle,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult Function()? removeAll,
    TResult Function()? addAll,
    TResult Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult Function(String searchText)? search,
    required TResult orElse(),
  }) {
    if (syncDataWhenInitial != null) {
      return syncDataWhenInitial(selectedIds, listVehicleGroupDTO);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddVehicleEvent value) addVehicle,
    required TResult Function(_RemoveVehicleEvent value) removeVehicle,
    required TResult Function(_AddAllVehicleOfGroup value) addAllVehicleOfGroup,
    required TResult Function(_RemoveAllVehicleOfGroupEvent value)
        removeAllVehicleOfGroup,
    required TResult Function(_RemoveAllEvent value) removeAll,
    required TResult Function(_AddAllEvent value) addAll,
    required TResult Function(_SyncDataWhenInitialEvent value)
        syncDataWhenInitial,
    required TResult Function(_Search value) search,
  }) {
    return syncDataWhenInitial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddVehicleEvent value)? addVehicle,
    TResult? Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult? Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult? Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult? Function(_RemoveAllEvent value)? removeAll,
    TResult? Function(_AddAllEvent value)? addAll,
    TResult? Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult? Function(_Search value)? search,
  }) {
    return syncDataWhenInitial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddVehicleEvent value)? addVehicle,
    TResult Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult Function(_RemoveAllEvent value)? removeAll,
    TResult Function(_AddAllEvent value)? addAll,
    TResult Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult Function(_Search value)? search,
    required TResult orElse(),
  }) {
    if (syncDataWhenInitial != null) {
      return syncDataWhenInitial(this);
    }
    return orElse();
  }
}

abstract class _SyncDataWhenInitialEvent implements FilterVehicleEvent {
  const factory _SyncDataWhenInitialEvent(
          {required final List<String> selectedIds,
          required final List<VehicleGroupDTO> listVehicleGroupDTO}) =
      _$SyncDataWhenInitialEventImpl;

  List<String> get selectedIds;
  List<VehicleGroupDTO> get listVehicleGroupDTO;
  @JsonKey(ignore: true)
  _$$SyncDataWhenInitialEventImplCopyWith<_$SyncDataWhenInitialEventImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchImplCopyWith<$Res> {
  factory _$$SearchImplCopyWith(
          _$SearchImpl value, $Res Function(_$SearchImpl) then) =
      __$$SearchImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String searchText});
}

/// @nodoc
class __$$SearchImplCopyWithImpl<$Res>
    extends _$FilterVehicleEventCopyWithImpl<$Res, _$SearchImpl>
    implements _$$SearchImplCopyWith<$Res> {
  __$$SearchImplCopyWithImpl(
      _$SearchImpl _value, $Res Function(_$SearchImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchText = null,
  }) {
    return _then(_$SearchImpl(
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchImpl implements _Search {
  const _$SearchImpl({required this.searchText});

  @override
  final String searchText;

  @override
  String toString() {
    return 'FilterVehicleEvent.search(searchText: $searchText)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchImpl &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText));
  }

  @override
  int get hashCode => Object.hash(runtimeType, searchText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchImplCopyWith<_$SearchImpl> get copyWith =>
      __$$SearchImplCopyWithImpl<_$SearchImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) addVehicle,
    required TResult Function(String id) removeVehicle,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        addAllVehicleOfGroup,
    required TResult Function(VehicleGroupDTO vehicleGroupDTO)
        removeAllVehicleOfGroup,
    required TResult Function() removeAll,
    required TResult Function() addAll,
    required TResult Function(
            List<String> selectedIds, List<VehicleGroupDTO> listVehicleGroupDTO)
        syncDataWhenInitial,
    required TResult Function(String searchText) search,
  }) {
    return search(searchText);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? addVehicle,
    TResult? Function(String id)? removeVehicle,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult? Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult? Function()? removeAll,
    TResult? Function()? addAll,
    TResult? Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult? Function(String searchText)? search,
  }) {
    return search?.call(searchText);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? addVehicle,
    TResult Function(String id)? removeVehicle,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? addAllVehicleOfGroup,
    TResult Function(VehicleGroupDTO vehicleGroupDTO)? removeAllVehicleOfGroup,
    TResult Function()? removeAll,
    TResult Function()? addAll,
    TResult Function(List<String> selectedIds,
            List<VehicleGroupDTO> listVehicleGroupDTO)?
        syncDataWhenInitial,
    TResult Function(String searchText)? search,
    required TResult orElse(),
  }) {
    if (search != null) {
      return search(searchText);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddVehicleEvent value) addVehicle,
    required TResult Function(_RemoveVehicleEvent value) removeVehicle,
    required TResult Function(_AddAllVehicleOfGroup value) addAllVehicleOfGroup,
    required TResult Function(_RemoveAllVehicleOfGroupEvent value)
        removeAllVehicleOfGroup,
    required TResult Function(_RemoveAllEvent value) removeAll,
    required TResult Function(_AddAllEvent value) addAll,
    required TResult Function(_SyncDataWhenInitialEvent value)
        syncDataWhenInitial,
    required TResult Function(_Search value) search,
  }) {
    return search(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddVehicleEvent value)? addVehicle,
    TResult? Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult? Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult? Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult? Function(_RemoveAllEvent value)? removeAll,
    TResult? Function(_AddAllEvent value)? addAll,
    TResult? Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult? Function(_Search value)? search,
  }) {
    return search?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddVehicleEvent value)? addVehicle,
    TResult Function(_RemoveVehicleEvent value)? removeVehicle,
    TResult Function(_AddAllVehicleOfGroup value)? addAllVehicleOfGroup,
    TResult Function(_RemoveAllVehicleOfGroupEvent value)?
        removeAllVehicleOfGroup,
    TResult Function(_RemoveAllEvent value)? removeAll,
    TResult Function(_AddAllEvent value)? addAll,
    TResult Function(_SyncDataWhenInitialEvent value)? syncDataWhenInitial,
    TResult Function(_Search value)? search,
    required TResult orElse(),
  }) {
    if (search != null) {
      return search(this);
    }
    return orElse();
  }
}

abstract class _Search implements FilterVehicleEvent {
  const factory _Search({required final String searchText}) = _$SearchImpl;

  String get searchText;
  @JsonKey(ignore: true)
  _$$SearchImplCopyWith<_$SearchImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$FilterVehicleState {
  List<String> get selectedIds => throw _privateConstructorUsedError;
  List<VehicleGroupDTO> get listVehicleGroupDTO =>
      throw _privateConstructorUsedError;
  List<VehicleGroupDTO>? get listVehicelGroupSearched =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $FilterVehicleStateCopyWith<FilterVehicleState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FilterVehicleStateCopyWith<$Res> {
  factory $FilterVehicleStateCopyWith(
          FilterVehicleState value, $Res Function(FilterVehicleState) then) =
      _$FilterVehicleStateCopyWithImpl<$Res, FilterVehicleState>;
  @useResult
  $Res call(
      {List<String> selectedIds,
      List<VehicleGroupDTO> listVehicleGroupDTO,
      List<VehicleGroupDTO>? listVehicelGroupSearched});
}

/// @nodoc
class _$FilterVehicleStateCopyWithImpl<$Res, $Val extends FilterVehicleState>
    implements $FilterVehicleStateCopyWith<$Res> {
  _$FilterVehicleStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedIds = null,
    Object? listVehicleGroupDTO = null,
    Object? listVehicelGroupSearched = freezed,
  }) {
    return _then(_value.copyWith(
      selectedIds: null == selectedIds
          ? _value.selectedIds
          : selectedIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listVehicleGroupDTO: null == listVehicleGroupDTO
          ? _value.listVehicleGroupDTO
          : listVehicleGroupDTO // ignore: cast_nullable_to_non_nullable
              as List<VehicleGroupDTO>,
      listVehicelGroupSearched: freezed == listVehicelGroupSearched
          ? _value.listVehicelGroupSearched
          : listVehicelGroupSearched // ignore: cast_nullable_to_non_nullable
              as List<VehicleGroupDTO>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FilterVehicleStateImplCopyWith<$Res>
    implements $FilterVehicleStateCopyWith<$Res> {
  factory _$$FilterVehicleStateImplCopyWith(_$FilterVehicleStateImpl value,
          $Res Function(_$FilterVehicleStateImpl) then) =
      __$$FilterVehicleStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<String> selectedIds,
      List<VehicleGroupDTO> listVehicleGroupDTO,
      List<VehicleGroupDTO>? listVehicelGroupSearched});
}

/// @nodoc
class __$$FilterVehicleStateImplCopyWithImpl<$Res>
    extends _$FilterVehicleStateCopyWithImpl<$Res, _$FilterVehicleStateImpl>
    implements _$$FilterVehicleStateImplCopyWith<$Res> {
  __$$FilterVehicleStateImplCopyWithImpl(_$FilterVehicleStateImpl _value,
      $Res Function(_$FilterVehicleStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedIds = null,
    Object? listVehicleGroupDTO = null,
    Object? listVehicelGroupSearched = freezed,
  }) {
    return _then(_$FilterVehicleStateImpl(
      selectedIds: null == selectedIds
          ? _value._selectedIds
          : selectedIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      listVehicleGroupDTO: null == listVehicleGroupDTO
          ? _value._listVehicleGroupDTO
          : listVehicleGroupDTO // ignore: cast_nullable_to_non_nullable
              as List<VehicleGroupDTO>,
      listVehicelGroupSearched: freezed == listVehicelGroupSearched
          ? _value._listVehicelGroupSearched
          : listVehicelGroupSearched // ignore: cast_nullable_to_non_nullable
              as List<VehicleGroupDTO>?,
    ));
  }
}

/// @nodoc

class _$FilterVehicleStateImpl implements _FilterVehicleState {
  const _$FilterVehicleStateImpl(
      {required final List<String> selectedIds,
      required final List<VehicleGroupDTO> listVehicleGroupDTO,
      required final List<VehicleGroupDTO>? listVehicelGroupSearched})
      : _selectedIds = selectedIds,
        _listVehicleGroupDTO = listVehicleGroupDTO,
        _listVehicelGroupSearched = listVehicelGroupSearched;

  final List<String> _selectedIds;
  @override
  List<String> get selectedIds {
    if (_selectedIds is EqualUnmodifiableListView) return _selectedIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedIds);
  }

  final List<VehicleGroupDTO> _listVehicleGroupDTO;
  @override
  List<VehicleGroupDTO> get listVehicleGroupDTO {
    if (_listVehicleGroupDTO is EqualUnmodifiableListView)
      return _listVehicleGroupDTO;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicleGroupDTO);
  }

  final List<VehicleGroupDTO>? _listVehicelGroupSearched;
  @override
  List<VehicleGroupDTO>? get listVehicelGroupSearched {
    final value = _listVehicelGroupSearched;
    if (value == null) return null;
    if (_listVehicelGroupSearched is EqualUnmodifiableListView)
      return _listVehicelGroupSearched;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'FilterVehicleState(selectedIds: $selectedIds, listVehicleGroupDTO: $listVehicleGroupDTO, listVehicelGroupSearched: $listVehicelGroupSearched)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterVehicleStateImpl &&
            const DeepCollectionEquality()
                .equals(other._selectedIds, _selectedIds) &&
            const DeepCollectionEquality()
                .equals(other._listVehicleGroupDTO, _listVehicleGroupDTO) &&
            const DeepCollectionEquality().equals(
                other._listVehicelGroupSearched, _listVehicelGroupSearched));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_selectedIds),
      const DeepCollectionEquality().hash(_listVehicleGroupDTO),
      const DeepCollectionEquality().hash(_listVehicelGroupSearched));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterVehicleStateImplCopyWith<_$FilterVehicleStateImpl> get copyWith =>
      __$$FilterVehicleStateImplCopyWithImpl<_$FilterVehicleStateImpl>(
          this, _$identity);
}

abstract class _FilterVehicleState implements FilterVehicleState {
  const factory _FilterVehicleState(
          {required final List<String> selectedIds,
          required final List<VehicleGroupDTO> listVehicleGroupDTO,
          required final List<VehicleGroupDTO>? listVehicelGroupSearched}) =
      _$FilterVehicleStateImpl;

  @override
  List<String> get selectedIds;
  @override
  List<VehicleGroupDTO> get listVehicleGroupDTO;
  @override
  List<VehicleGroupDTO>? get listVehicelGroupSearched;
  @override
  @JsonKey(ignore: true)
  _$$FilterVehicleStateImplCopyWith<_$FilterVehicleStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
