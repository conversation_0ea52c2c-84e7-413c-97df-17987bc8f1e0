// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'fuel_pump_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FuelPumpEvent {
  String get command => throw _privateConstructorUsedError;
  String get vehicleId => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String command, String vehicleId) sendCommand,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String command, String vehicleId)? sendCommand,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String command, String vehicleId)? sendCommand,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_sendCommandEvents value) sendCommand,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_sendCommandEvents value)? sendCommand,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_sendCommandEvents value)? sendCommand,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $FuelPumpEventCopyWith<FuelPumpEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FuelPumpEventCopyWith<$Res> {
  factory $FuelPumpEventCopyWith(
          FuelPumpEvent value, $Res Function(FuelPumpEvent) then) =
      _$FuelPumpEventCopyWithImpl<$Res, FuelPumpEvent>;
  @useResult
  $Res call({String command, String vehicleId});
}

/// @nodoc
class _$FuelPumpEventCopyWithImpl<$Res, $Val extends FuelPumpEvent>
    implements $FuelPumpEventCopyWith<$Res> {
  _$FuelPumpEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? command = null,
    Object? vehicleId = null,
  }) {
    return _then(_value.copyWith(
      command: null == command
          ? _value.command
          : command // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$sendCommandEventsImplCopyWith<$Res>
    implements $FuelPumpEventCopyWith<$Res> {
  factory _$$sendCommandEventsImplCopyWith(_$sendCommandEventsImpl value,
          $Res Function(_$sendCommandEventsImpl) then) =
      __$$sendCommandEventsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String command, String vehicleId});
}

/// @nodoc
class __$$sendCommandEventsImplCopyWithImpl<$Res>
    extends _$FuelPumpEventCopyWithImpl<$Res, _$sendCommandEventsImpl>
    implements _$$sendCommandEventsImplCopyWith<$Res> {
  __$$sendCommandEventsImplCopyWithImpl(_$sendCommandEventsImpl _value,
      $Res Function(_$sendCommandEventsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? command = null,
    Object? vehicleId = null,
  }) {
    return _then(_$sendCommandEventsImpl(
      command: null == command
          ? _value.command
          : command // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$sendCommandEventsImpl implements _sendCommandEvents {
  const _$sendCommandEventsImpl(
      {required this.command, required this.vehicleId});

  @override
  final String command;
  @override
  final String vehicleId;

  @override
  String toString() {
    return 'FuelPumpEvent.sendCommand(command: $command, vehicleId: $vehicleId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$sendCommandEventsImpl &&
            (identical(other.command, command) || other.command == command) &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, command, vehicleId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$sendCommandEventsImplCopyWith<_$sendCommandEventsImpl> get copyWith =>
      __$$sendCommandEventsImplCopyWithImpl<_$sendCommandEventsImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String command, String vehicleId) sendCommand,
  }) {
    return sendCommand(command, vehicleId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String command, String vehicleId)? sendCommand,
  }) {
    return sendCommand?.call(command, vehicleId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String command, String vehicleId)? sendCommand,
    required TResult orElse(),
  }) {
    if (sendCommand != null) {
      return sendCommand(command, vehicleId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_sendCommandEvents value) sendCommand,
  }) {
    return sendCommand(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_sendCommandEvents value)? sendCommand,
  }) {
    return sendCommand?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_sendCommandEvents value)? sendCommand,
    required TResult orElse(),
  }) {
    if (sendCommand != null) {
      return sendCommand(this);
    }
    return orElse();
  }
}

abstract class _sendCommandEvents implements FuelPumpEvent {
  const factory _sendCommandEvents(
      {required final String command,
      required final String vehicleId}) = _$sendCommandEventsImpl;

  @override
  String get command;
  @override
  String get vehicleId;
  @override
  @JsonKey(ignore: true)
  _$$sendCommandEventsImplCopyWith<_$sendCommandEventsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$FuelPumpState {
  bool? get isSentSuccess => throw _privateConstructorUsedError;
  bool get isSendingCommand => throw _privateConstructorUsedError;
  FuelPumpFailure? get fuelPumpFailure => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $FuelPumpStateCopyWith<FuelPumpState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FuelPumpStateCopyWith<$Res> {
  factory $FuelPumpStateCopyWith(
          FuelPumpState value, $Res Function(FuelPumpState) then) =
      _$FuelPumpStateCopyWithImpl<$Res, FuelPumpState>;
  @useResult
  $Res call(
      {bool? isSentSuccess,
      bool isSendingCommand,
      FuelPumpFailure? fuelPumpFailure});

  $FuelPumpFailureCopyWith<$Res>? get fuelPumpFailure;
}

/// @nodoc
class _$FuelPumpStateCopyWithImpl<$Res, $Val extends FuelPumpState>
    implements $FuelPumpStateCopyWith<$Res> {
  _$FuelPumpStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isSentSuccess = freezed,
    Object? isSendingCommand = null,
    Object? fuelPumpFailure = freezed,
  }) {
    return _then(_value.copyWith(
      isSentSuccess: freezed == isSentSuccess
          ? _value.isSentSuccess
          : isSentSuccess // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSendingCommand: null == isSendingCommand
          ? _value.isSendingCommand
          : isSendingCommand // ignore: cast_nullable_to_non_nullable
              as bool,
      fuelPumpFailure: freezed == fuelPumpFailure
          ? _value.fuelPumpFailure
          : fuelPumpFailure // ignore: cast_nullable_to_non_nullable
              as FuelPumpFailure?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $FuelPumpFailureCopyWith<$Res>? get fuelPumpFailure {
    if (_value.fuelPumpFailure == null) {
      return null;
    }

    return $FuelPumpFailureCopyWith<$Res>(_value.fuelPumpFailure!, (value) {
      return _then(_value.copyWith(fuelPumpFailure: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FuelPumpStateImplCopyWith<$Res>
    implements $FuelPumpStateCopyWith<$Res> {
  factory _$$FuelPumpStateImplCopyWith(
          _$FuelPumpStateImpl value, $Res Function(_$FuelPumpStateImpl) then) =
      __$$FuelPumpStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? isSentSuccess,
      bool isSendingCommand,
      FuelPumpFailure? fuelPumpFailure});

  @override
  $FuelPumpFailureCopyWith<$Res>? get fuelPumpFailure;
}

/// @nodoc
class __$$FuelPumpStateImplCopyWithImpl<$Res>
    extends _$FuelPumpStateCopyWithImpl<$Res, _$FuelPumpStateImpl>
    implements _$$FuelPumpStateImplCopyWith<$Res> {
  __$$FuelPumpStateImplCopyWithImpl(
      _$FuelPumpStateImpl _value, $Res Function(_$FuelPumpStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isSentSuccess = freezed,
    Object? isSendingCommand = null,
    Object? fuelPumpFailure = freezed,
  }) {
    return _then(_$FuelPumpStateImpl(
      isSentSuccess: freezed == isSentSuccess
          ? _value.isSentSuccess
          : isSentSuccess // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSendingCommand: null == isSendingCommand
          ? _value.isSendingCommand
          : isSendingCommand // ignore: cast_nullable_to_non_nullable
              as bool,
      fuelPumpFailure: freezed == fuelPumpFailure
          ? _value.fuelPumpFailure
          : fuelPumpFailure // ignore: cast_nullable_to_non_nullable
              as FuelPumpFailure?,
    ));
  }
}

/// @nodoc

class _$FuelPumpStateImpl implements _FuelPumpState {
  const _$FuelPumpStateImpl(
      {required this.isSentSuccess,
      required this.isSendingCommand,
      required this.fuelPumpFailure});

  @override
  final bool? isSentSuccess;
  @override
  final bool isSendingCommand;
  @override
  final FuelPumpFailure? fuelPumpFailure;

  @override
  String toString() {
    return 'FuelPumpState(isSentSuccess: $isSentSuccess, isSendingCommand: $isSendingCommand, fuelPumpFailure: $fuelPumpFailure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FuelPumpStateImpl &&
            (identical(other.isSentSuccess, isSentSuccess) ||
                other.isSentSuccess == isSentSuccess) &&
            (identical(other.isSendingCommand, isSendingCommand) ||
                other.isSendingCommand == isSendingCommand) &&
            (identical(other.fuelPumpFailure, fuelPumpFailure) ||
                other.fuelPumpFailure == fuelPumpFailure));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, isSentSuccess, isSendingCommand, fuelPumpFailure);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FuelPumpStateImplCopyWith<_$FuelPumpStateImpl> get copyWith =>
      __$$FuelPumpStateImplCopyWithImpl<_$FuelPumpStateImpl>(this, _$identity);
}

abstract class _FuelPumpState implements FuelPumpState {
  const factory _FuelPumpState(
      {required final bool? isSentSuccess,
      required final bool isSendingCommand,
      required final FuelPumpFailure? fuelPumpFailure}) = _$FuelPumpStateImpl;

  @override
  bool? get isSentSuccess;
  @override
  bool get isSendingCommand;
  @override
  FuelPumpFailure? get fuelPumpFailure;
  @override
  @JsonKey(ignore: true)
  _$$FuelPumpStateImplCopyWith<_$FuelPumpStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
