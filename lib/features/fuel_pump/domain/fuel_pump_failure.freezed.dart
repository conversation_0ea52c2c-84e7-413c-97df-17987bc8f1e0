// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'fuel_pump_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FuelPumpFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FuelPumpUnexpected value) unexpected,
    required TResult Function(_FuelPumpUnauthorized value) unauthorized,
    required TResult Function(_FuelPumpUnauthenticated value) unauthenticated,
    required TResult Function(_FuelPumpServerError value) serverError,
    required TResult Function(_FuelPumpNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FuelPumpUnexpected value)? unexpected,
    TResult? Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult? Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult? Function(_FuelPumpServerError value)? serverError,
    TResult? Function(_FuelPumpNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FuelPumpUnexpected value)? unexpected,
    TResult Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult Function(_FuelPumpServerError value)? serverError,
    TResult Function(_FuelPumpNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FuelPumpFailureCopyWith<$Res> {
  factory $FuelPumpFailureCopyWith(
          FuelPumpFailure value, $Res Function(FuelPumpFailure) then) =
      _$FuelPumpFailureCopyWithImpl<$Res, FuelPumpFailure>;
}

/// @nodoc
class _$FuelPumpFailureCopyWithImpl<$Res, $Val extends FuelPumpFailure>
    implements $FuelPumpFailureCopyWith<$Res> {
  _$FuelPumpFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$FuelPumpUnexpectedImplCopyWith<$Res> {
  factory _$$FuelPumpUnexpectedImplCopyWith(_$FuelPumpUnexpectedImpl value,
          $Res Function(_$FuelPumpUnexpectedImpl) then) =
      __$$FuelPumpUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$FuelPumpUnexpectedImplCopyWithImpl<$Res>
    extends _$FuelPumpFailureCopyWithImpl<$Res, _$FuelPumpUnexpectedImpl>
    implements _$$FuelPumpUnexpectedImplCopyWith<$Res> {
  __$$FuelPumpUnexpectedImplCopyWithImpl(_$FuelPumpUnexpectedImpl _value,
      $Res Function(_$FuelPumpUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$FuelPumpUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FuelPumpUnexpectedImpl implements _FuelPumpUnexpected {
  const _$FuelPumpUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'FuelPumpFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FuelPumpUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FuelPumpUnexpectedImplCopyWith<_$FuelPumpUnexpectedImpl> get copyWith =>
      __$$FuelPumpUnexpectedImplCopyWithImpl<_$FuelPumpUnexpectedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FuelPumpUnexpected value) unexpected,
    required TResult Function(_FuelPumpUnauthorized value) unauthorized,
    required TResult Function(_FuelPumpUnauthenticated value) unauthenticated,
    required TResult Function(_FuelPumpServerError value) serverError,
    required TResult Function(_FuelPumpNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FuelPumpUnexpected value)? unexpected,
    TResult? Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult? Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult? Function(_FuelPumpServerError value)? serverError,
    TResult? Function(_FuelPumpNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FuelPumpUnexpected value)? unexpected,
    TResult Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult Function(_FuelPumpServerError value)? serverError,
    TResult Function(_FuelPumpNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _FuelPumpUnexpected implements FuelPumpFailure {
  const factory _FuelPumpUnexpected({required final String error}) =
      _$FuelPumpUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$FuelPumpUnexpectedImplCopyWith<_$FuelPumpUnexpectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FuelPumpUnauthorizedImplCopyWith<$Res> {
  factory _$$FuelPumpUnauthorizedImplCopyWith(_$FuelPumpUnauthorizedImpl value,
          $Res Function(_$FuelPumpUnauthorizedImpl) then) =
      __$$FuelPumpUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FuelPumpUnauthorizedImplCopyWithImpl<$Res>
    extends _$FuelPumpFailureCopyWithImpl<$Res, _$FuelPumpUnauthorizedImpl>
    implements _$$FuelPumpUnauthorizedImplCopyWith<$Res> {
  __$$FuelPumpUnauthorizedImplCopyWithImpl(_$FuelPumpUnauthorizedImpl _value,
      $Res Function(_$FuelPumpUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$FuelPumpUnauthorizedImpl implements _FuelPumpUnauthorized {
  const _$FuelPumpUnauthorizedImpl();

  @override
  String toString() {
    return 'FuelPumpFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FuelPumpUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FuelPumpUnexpected value) unexpected,
    required TResult Function(_FuelPumpUnauthorized value) unauthorized,
    required TResult Function(_FuelPumpUnauthenticated value) unauthenticated,
    required TResult Function(_FuelPumpServerError value) serverError,
    required TResult Function(_FuelPumpNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FuelPumpUnexpected value)? unexpected,
    TResult? Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult? Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult? Function(_FuelPumpServerError value)? serverError,
    TResult? Function(_FuelPumpNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FuelPumpUnexpected value)? unexpected,
    TResult Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult Function(_FuelPumpServerError value)? serverError,
    TResult Function(_FuelPumpNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _FuelPumpUnauthorized implements FuelPumpFailure {
  const factory _FuelPumpUnauthorized() = _$FuelPumpUnauthorizedImpl;
}

/// @nodoc
abstract class _$$FuelPumpUnauthenticatedImplCopyWith<$Res> {
  factory _$$FuelPumpUnauthenticatedImplCopyWith(
          _$FuelPumpUnauthenticatedImpl value,
          $Res Function(_$FuelPumpUnauthenticatedImpl) then) =
      __$$FuelPumpUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FuelPumpUnauthenticatedImplCopyWithImpl<$Res>
    extends _$FuelPumpFailureCopyWithImpl<$Res, _$FuelPumpUnauthenticatedImpl>
    implements _$$FuelPumpUnauthenticatedImplCopyWith<$Res> {
  __$$FuelPumpUnauthenticatedImplCopyWithImpl(
      _$FuelPumpUnauthenticatedImpl _value,
      $Res Function(_$FuelPumpUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$FuelPumpUnauthenticatedImpl implements _FuelPumpUnauthenticated {
  const _$FuelPumpUnauthenticatedImpl();

  @override
  String toString() {
    return 'FuelPumpFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FuelPumpUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FuelPumpUnexpected value) unexpected,
    required TResult Function(_FuelPumpUnauthorized value) unauthorized,
    required TResult Function(_FuelPumpUnauthenticated value) unauthenticated,
    required TResult Function(_FuelPumpServerError value) serverError,
    required TResult Function(_FuelPumpNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FuelPumpUnexpected value)? unexpected,
    TResult? Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult? Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult? Function(_FuelPumpServerError value)? serverError,
    TResult? Function(_FuelPumpNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FuelPumpUnexpected value)? unexpected,
    TResult Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult Function(_FuelPumpServerError value)? serverError,
    TResult Function(_FuelPumpNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _FuelPumpUnauthenticated implements FuelPumpFailure {
  const factory _FuelPumpUnauthenticated() = _$FuelPumpUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$FuelPumpServerErrorImplCopyWith<$Res> {
  factory _$$FuelPumpServerErrorImplCopyWith(_$FuelPumpServerErrorImpl value,
          $Res Function(_$FuelPumpServerErrorImpl) then) =
      __$$FuelPumpServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FuelPumpServerErrorImplCopyWithImpl<$Res>
    extends _$FuelPumpFailureCopyWithImpl<$Res, _$FuelPumpServerErrorImpl>
    implements _$$FuelPumpServerErrorImplCopyWith<$Res> {
  __$$FuelPumpServerErrorImplCopyWithImpl(_$FuelPumpServerErrorImpl _value,
      $Res Function(_$FuelPumpServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$FuelPumpServerErrorImpl implements _FuelPumpServerError {
  const _$FuelPumpServerErrorImpl();

  @override
  String toString() {
    return 'FuelPumpFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FuelPumpServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FuelPumpUnexpected value) unexpected,
    required TResult Function(_FuelPumpUnauthorized value) unauthorized,
    required TResult Function(_FuelPumpUnauthenticated value) unauthenticated,
    required TResult Function(_FuelPumpServerError value) serverError,
    required TResult Function(_FuelPumpNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FuelPumpUnexpected value)? unexpected,
    TResult? Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult? Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult? Function(_FuelPumpServerError value)? serverError,
    TResult? Function(_FuelPumpNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FuelPumpUnexpected value)? unexpected,
    TResult Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult Function(_FuelPumpServerError value)? serverError,
    TResult Function(_FuelPumpNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _FuelPumpServerError implements FuelPumpFailure {
  const factory _FuelPumpServerError() = _$FuelPumpServerErrorImpl;
}

/// @nodoc
abstract class _$$FuelPumpNoInternetImplCopyWith<$Res> {
  factory _$$FuelPumpNoInternetImplCopyWith(_$FuelPumpNoInternetImpl value,
          $Res Function(_$FuelPumpNoInternetImpl) then) =
      __$$FuelPumpNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FuelPumpNoInternetImplCopyWithImpl<$Res>
    extends _$FuelPumpFailureCopyWithImpl<$Res, _$FuelPumpNoInternetImpl>
    implements _$$FuelPumpNoInternetImplCopyWith<$Res> {
  __$$FuelPumpNoInternetImplCopyWithImpl(_$FuelPumpNoInternetImpl _value,
      $Res Function(_$FuelPumpNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$FuelPumpNoInternetImpl implements _FuelPumpNoInternet {
  const _$FuelPumpNoInternetImpl();

  @override
  String toString() {
    return 'FuelPumpFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FuelPumpNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FuelPumpUnexpected value) unexpected,
    required TResult Function(_FuelPumpUnauthorized value) unauthorized,
    required TResult Function(_FuelPumpUnauthenticated value) unauthenticated,
    required TResult Function(_FuelPumpServerError value) serverError,
    required TResult Function(_FuelPumpNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FuelPumpUnexpected value)? unexpected,
    TResult? Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult? Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult? Function(_FuelPumpServerError value)? serverError,
    TResult? Function(_FuelPumpNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FuelPumpUnexpected value)? unexpected,
    TResult Function(_FuelPumpUnauthorized value)? unauthorized,
    TResult Function(_FuelPumpUnauthenticated value)? unauthenticated,
    TResult Function(_FuelPumpServerError value)? serverError,
    TResult Function(_FuelPumpNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _FuelPumpNoInternet implements FuelPumpFailure {
  const factory _FuelPumpNoInternet() = _$FuelPumpNoInternetImpl;
}
