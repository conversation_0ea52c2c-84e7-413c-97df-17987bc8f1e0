// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'query_playback_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$QueryPlaybackEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QueryPlaybackEventCopyWith<$Res> {
  factory $QueryPlaybackEventCopyWith(
          QueryPlaybackEvent value, $Res Function(QueryPlaybackEvent) then) =
      _$QueryPlaybackEventCopyWithImpl<$Res, QueryPlaybackEvent>;
}

/// @nodoc
class _$QueryPlaybackEventCopyWithImpl<$Res, $Val extends QueryPlaybackEvent>
    implements $QueryPlaybackEventCopyWith<$Res> {
  _$QueryPlaybackEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$GetListVideoEventImplCopyWith<$Res> {
  factory _$$GetListVideoEventImplCopyWith(_$GetListVideoEventImpl value,
          $Res Function(_$GetListVideoEventImpl) then) =
      __$$GetListVideoEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({TimelineRequest timeLineRequest});

  $TimelineRequestCopyWith<$Res> get timeLineRequest;
}

/// @nodoc
class __$$GetListVideoEventImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$GetListVideoEventImpl>
    implements _$$GetListVideoEventImplCopyWith<$Res> {
  __$$GetListVideoEventImplCopyWithImpl(_$GetListVideoEventImpl _value,
      $Res Function(_$GetListVideoEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timeLineRequest = null,
  }) {
    return _then(_$GetListVideoEventImpl(
      timeLineRequest: null == timeLineRequest
          ? _value.timeLineRequest
          : timeLineRequest // ignore: cast_nullable_to_non_nullable
              as TimelineRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $TimelineRequestCopyWith<$Res> get timeLineRequest {
    return $TimelineRequestCopyWith<$Res>(_value.timeLineRequest, (value) {
      return _then(_value.copyWith(timeLineRequest: value));
    });
  }
}

/// @nodoc

class _$GetListVideoEventImpl implements _GetListVideoEvent {
  const _$GetListVideoEventImpl({required this.timeLineRequest});

  @override
  final TimelineRequest timeLineRequest;

  @override
  String toString() {
    return 'QueryPlaybackEvent.getListVideo(timeLineRequest: $timeLineRequest)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetListVideoEventImpl &&
            (identical(other.timeLineRequest, timeLineRequest) ||
                other.timeLineRequest == timeLineRequest));
  }

  @override
  int get hashCode => Object.hash(runtimeType, timeLineRequest);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetListVideoEventImplCopyWith<_$GetListVideoEventImpl> get copyWith =>
      __$$GetListVideoEventImplCopyWithImpl<_$GetListVideoEventImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return getListVideo(timeLineRequest);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return getListVideo?.call(timeLineRequest);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (getListVideo != null) {
      return getListVideo(timeLineRequest);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return getListVideo(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return getListVideo?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (getListVideo != null) {
      return getListVideo(this);
    }
    return orElse();
  }
}

abstract class _GetListVideoEvent implements QueryPlaybackEvent {
  const factory _GetListVideoEvent(
          {required final TimelineRequest timeLineRequest}) =
      _$GetListVideoEventImpl;

  TimelineRequest get timeLineRequest;
  @JsonKey(ignore: true)
  _$$GetListVideoEventImplCopyWith<_$GetListVideoEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectVehicleEventImplCopyWith<$Res> {
  factory _$$SelectVehicleEventImplCopyWith(_$SelectVehicleEventImpl value,
          $Res Function(_$SelectVehicleEventImpl) then) =
      __$$SelectVehicleEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Vehicle selectedVehicle});

  $VehicleCopyWith<$Res> get selectedVehicle;
}

/// @nodoc
class __$$SelectVehicleEventImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$SelectVehicleEventImpl>
    implements _$$SelectVehicleEventImplCopyWith<$Res> {
  __$$SelectVehicleEventImplCopyWithImpl(_$SelectVehicleEventImpl _value,
      $Res Function(_$SelectVehicleEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedVehicle = null,
  }) {
    return _then(_$SelectVehicleEventImpl(
      selectedVehicle: null == selectedVehicle
          ? _value.selectedVehicle
          : selectedVehicle // ignore: cast_nullable_to_non_nullable
              as Vehicle,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleCopyWith<$Res> get selectedVehicle {
    return $VehicleCopyWith<$Res>(_value.selectedVehicle, (value) {
      return _then(_value.copyWith(selectedVehicle: value));
    });
  }
}

/// @nodoc

class _$SelectVehicleEventImpl implements _SelectVehicleEvent {
  const _$SelectVehicleEventImpl({required this.selectedVehicle});

  @override
  final Vehicle selectedVehicle;

  @override
  String toString() {
    return 'QueryPlaybackEvent.selectVehicle(selectedVehicle: $selectedVehicle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectVehicleEventImpl &&
            (identical(other.selectedVehicle, selectedVehicle) ||
                other.selectedVehicle == selectedVehicle));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedVehicle);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectVehicleEventImplCopyWith<_$SelectVehicleEventImpl> get copyWith =>
      __$$SelectVehicleEventImplCopyWithImpl<_$SelectVehicleEventImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return selectVehicle(selectedVehicle);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return selectVehicle?.call(selectedVehicle);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (selectVehicle != null) {
      return selectVehicle(selectedVehicle);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return selectVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return selectVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (selectVehicle != null) {
      return selectVehicle(this);
    }
    return orElse();
  }
}

abstract class _SelectVehicleEvent implements QueryPlaybackEvent {
  const factory _SelectVehicleEvent({required final Vehicle selectedVehicle}) =
      _$SelectVehicleEventImpl;

  Vehicle get selectedVehicle;
  @JsonKey(ignore: true)
  _$$SelectVehicleEventImplCopyWith<_$SelectVehicleEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddChannelEventImplCopyWith<$Res> {
  factory _$$AddChannelEventImplCopyWith(_$AddChannelEventImpl value,
          $Res Function(_$AddChannelEventImpl) then) =
      __$$AddChannelEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VehicleSetting channel});

  $VehicleSettingCopyWith<$Res> get channel;
}

/// @nodoc
class __$$AddChannelEventImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$AddChannelEventImpl>
    implements _$$AddChannelEventImplCopyWith<$Res> {
  __$$AddChannelEventImplCopyWithImpl(
      _$AddChannelEventImpl _value, $Res Function(_$AddChannelEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channel = null,
  }) {
    return _then(_$AddChannelEventImpl(
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as VehicleSetting,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleSettingCopyWith<$Res> get channel {
    return $VehicleSettingCopyWith<$Res>(_value.channel, (value) {
      return _then(_value.copyWith(channel: value));
    });
  }
}

/// @nodoc

class _$AddChannelEventImpl implements _AddChannelEvent {
  const _$AddChannelEventImpl({required this.channel});

  @override
  final VehicleSetting channel;

  @override
  String toString() {
    return 'QueryPlaybackEvent.addChannel(channel: $channel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddChannelEventImpl &&
            (identical(other.channel, channel) || other.channel == channel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, channel);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AddChannelEventImplCopyWith<_$AddChannelEventImpl> get copyWith =>
      __$$AddChannelEventImplCopyWithImpl<_$AddChannelEventImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return addChannel(channel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return addChannel?.call(channel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (addChannel != null) {
      return addChannel(channel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return addChannel(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return addChannel?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (addChannel != null) {
      return addChannel(this);
    }
    return orElse();
  }
}

abstract class _AddChannelEvent implements QueryPlaybackEvent {
  const factory _AddChannelEvent({required final VehicleSetting channel}) =
      _$AddChannelEventImpl;

  VehicleSetting get channel;
  @JsonKey(ignore: true)
  _$$AddChannelEventImplCopyWith<_$AddChannelEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectDateEventImplCopyWith<$Res> {
  factory _$$SelectDateEventImplCopyWith(_$SelectDateEventImpl value,
          $Res Function(_$SelectDateEventImpl) then) =
      __$$SelectDateEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime selectedDate});
}

/// @nodoc
class __$$SelectDateEventImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$SelectDateEventImpl>
    implements _$$SelectDateEventImplCopyWith<$Res> {
  __$$SelectDateEventImplCopyWithImpl(
      _$SelectDateEventImpl _value, $Res Function(_$SelectDateEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedDate = null,
  }) {
    return _then(_$SelectDateEventImpl(
      selectedDate: null == selectedDate
          ? _value.selectedDate
          : selectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$SelectDateEventImpl implements _SelectDateEvent {
  const _$SelectDateEventImpl({required this.selectedDate});

  @override
  final DateTime selectedDate;

  @override
  String toString() {
    return 'QueryPlaybackEvent.selectDate(selectedDate: $selectedDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectDateEventImpl &&
            (identical(other.selectedDate, selectedDate) ||
                other.selectedDate == selectedDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedDate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectDateEventImplCopyWith<_$SelectDateEventImpl> get copyWith =>
      __$$SelectDateEventImplCopyWithImpl<_$SelectDateEventImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return selectDate(selectedDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return selectDate?.call(selectedDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (selectDate != null) {
      return selectDate(selectedDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return selectDate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return selectDate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (selectDate != null) {
      return selectDate(this);
    }
    return orElse();
  }
}

abstract class _SelectDateEvent implements QueryPlaybackEvent {
  const factory _SelectDateEvent({required final DateTime selectedDate}) =
      _$SelectDateEventImpl;

  DateTime get selectedDate;
  @JsonKey(ignore: true)
  _$$SelectDateEventImplCopyWith<_$SelectDateEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetEventImplCopyWith<$Res> {
  factory _$$ResetEventImplCopyWith(
          _$ResetEventImpl value, $Res Function(_$ResetEventImpl) then) =
      __$$ResetEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetEventImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$ResetEventImpl>
    implements _$$ResetEventImplCopyWith<$Res> {
  __$$ResetEventImplCopyWithImpl(
      _$ResetEventImpl _value, $Res Function(_$ResetEventImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ResetEventImpl implements _ResetEvent {
  const _$ResetEventImpl();

  @override
  String toString() {
    return 'QueryPlaybackEvent.resetState()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return resetState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return resetState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return resetState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return resetState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState(this);
    }
    return orElse();
  }
}

abstract class _ResetEvent implements QueryPlaybackEvent {
  const factory _ResetEvent() = _$ResetEventImpl;
}

/// @nodoc
abstract class _$$LoadedEventImplCopyWith<$Res> {
  factory _$$LoadedEventImplCopyWith(
          _$LoadedEventImpl value, $Res Function(_$LoadedEventImpl) then) =
      __$$LoadedEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({TimelineResponse timelineResponse});

  $TimelineResponseCopyWith<$Res> get timelineResponse;
}

/// @nodoc
class __$$LoadedEventImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$LoadedEventImpl>
    implements _$$LoadedEventImplCopyWith<$Res> {
  __$$LoadedEventImplCopyWithImpl(
      _$LoadedEventImpl _value, $Res Function(_$LoadedEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timelineResponse = null,
  }) {
    return _then(_$LoadedEventImpl(
      timelineResponse: null == timelineResponse
          ? _value.timelineResponse
          : timelineResponse // ignore: cast_nullable_to_non_nullable
              as TimelineResponse,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $TimelineResponseCopyWith<$Res> get timelineResponse {
    return $TimelineResponseCopyWith<$Res>(_value.timelineResponse, (value) {
      return _then(_value.copyWith(timelineResponse: value));
    });
  }
}

/// @nodoc

class _$LoadedEventImpl implements _LoadedEvent {
  const _$LoadedEventImpl({required this.timelineResponse});

  @override
  final TimelineResponse timelineResponse;

  @override
  String toString() {
    return 'QueryPlaybackEvent.loaded(timelineResponse: $timelineResponse)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedEventImpl &&
            (identical(other.timelineResponse, timelineResponse) ||
                other.timelineResponse == timelineResponse));
  }

  @override
  int get hashCode => Object.hash(runtimeType, timelineResponse);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedEventImplCopyWith<_$LoadedEventImpl> get copyWith =>
      __$$LoadedEventImplCopyWithImpl<_$LoadedEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return loaded(timelineResponse);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return loaded?.call(timelineResponse);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(timelineResponse);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _LoadedEvent implements QueryPlaybackEvent {
  const factory _LoadedEvent(
      {required final TimelineResponse timelineResponse}) = _$LoadedEventImpl;

  TimelineResponse get timelineResponse;
  @JsonKey(ignore: true)
  _$$LoadedEventImplCopyWith<_$LoadedEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FailureEventImplCopyWith<$Res> {
  factory _$$FailureEventImplCopyWith(
          _$FailureEventImpl value, $Res Function(_$FailureEventImpl) then) =
      __$$FailureEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PlaybackFailure error});

  $PlaybackFailureCopyWith<$Res> get error;
}

/// @nodoc
class __$$FailureEventImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$FailureEventImpl>
    implements _$$FailureEventImplCopyWith<$Res> {
  __$$FailureEventImplCopyWithImpl(
      _$FailureEventImpl _value, $Res Function(_$FailureEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$FailureEventImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as PlaybackFailure,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $PlaybackFailureCopyWith<$Res> get error {
    return $PlaybackFailureCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$FailureEventImpl implements _FailureEvent {
  const _$FailureEventImpl({required this.error});

  @override
  final PlaybackFailure error;

  @override
  String toString() {
    return 'QueryPlaybackEvent.failureEvent(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureEventImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureEventImplCopyWith<_$FailureEventImpl> get copyWith =>
      __$$FailureEventImplCopyWithImpl<_$FailureEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return failureEvent(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return failureEvent?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (failureEvent != null) {
      return failureEvent(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return failureEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return failureEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (failureEvent != null) {
      return failureEvent(this);
    }
    return orElse();
  }
}

abstract class _FailureEvent implements QueryPlaybackEvent {
  const factory _FailureEvent({required final PlaybackFailure error}) =
      _$FailureEventImpl;

  PlaybackFailure get error;
  @JsonKey(ignore: true)
  _$$FailureEventImplCopyWith<_$FailureEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectPlaybackVideoImplCopyWith<$Res> {
  factory _$$SelectPlaybackVideoImplCopyWith(_$SelectPlaybackVideoImpl value,
          $Res Function(_$SelectPlaybackVideoImpl) then) =
      __$$SelectPlaybackVideoImplCopyWithImpl<$Res>;
  @useResult
  $Res call({TimelinePlayback timelinePlayback});

  $TimelinePlaybackCopyWith<$Res> get timelinePlayback;
}

/// @nodoc
class __$$SelectPlaybackVideoImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$SelectPlaybackVideoImpl>
    implements _$$SelectPlaybackVideoImplCopyWith<$Res> {
  __$$SelectPlaybackVideoImplCopyWithImpl(_$SelectPlaybackVideoImpl _value,
      $Res Function(_$SelectPlaybackVideoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timelinePlayback = null,
  }) {
    return _then(_$SelectPlaybackVideoImpl(
      timelinePlayback: null == timelinePlayback
          ? _value.timelinePlayback
          : timelinePlayback // ignore: cast_nullable_to_non_nullable
              as TimelinePlayback,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $TimelinePlaybackCopyWith<$Res> get timelinePlayback {
    return $TimelinePlaybackCopyWith<$Res>(_value.timelinePlayback, (value) {
      return _then(_value.copyWith(timelinePlayback: value));
    });
  }
}

/// @nodoc

class _$SelectPlaybackVideoImpl implements _SelectPlaybackVideo {
  const _$SelectPlaybackVideoImpl({required this.timelinePlayback});

  @override
  final TimelinePlayback timelinePlayback;

  @override
  String toString() {
    return 'QueryPlaybackEvent.selectPlaybackVideo(timelinePlayback: $timelinePlayback)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectPlaybackVideoImpl &&
            (identical(other.timelinePlayback, timelinePlayback) ||
                other.timelinePlayback == timelinePlayback));
  }

  @override
  int get hashCode => Object.hash(runtimeType, timelinePlayback);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectPlaybackVideoImplCopyWith<_$SelectPlaybackVideoImpl> get copyWith =>
      __$$SelectPlaybackVideoImplCopyWithImpl<_$SelectPlaybackVideoImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return selectPlaybackVideo(timelinePlayback);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return selectPlaybackVideo?.call(timelinePlayback);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (selectPlaybackVideo != null) {
      return selectPlaybackVideo(timelinePlayback);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return selectPlaybackVideo(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return selectPlaybackVideo?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (selectPlaybackVideo != null) {
      return selectPlaybackVideo(this);
    }
    return orElse();
  }
}

abstract class _SelectPlaybackVideo implements QueryPlaybackEvent {
  const factory _SelectPlaybackVideo(
          {required final TimelinePlayback timelinePlayback}) =
      _$SelectPlaybackVideoImpl;

  TimelinePlayback get timelinePlayback;
  @JsonKey(ignore: true)
  _$$SelectPlaybackVideoImplCopyWith<_$SelectPlaybackVideoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectDurationImplCopyWith<$Res> {
  factory _$$SelectDurationImplCopyWith(_$SelectDurationImpl value,
          $Res Function(_$SelectDurationImpl) then) =
      __$$SelectDurationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DurationEachTimeLine duration});
}

/// @nodoc
class __$$SelectDurationImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$SelectDurationImpl>
    implements _$$SelectDurationImplCopyWith<$Res> {
  __$$SelectDurationImplCopyWithImpl(
      _$SelectDurationImpl _value, $Res Function(_$SelectDurationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? duration = null,
  }) {
    return _then(_$SelectDurationImpl(
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as DurationEachTimeLine,
    ));
  }
}

/// @nodoc

class _$SelectDurationImpl implements _SelectDuration {
  const _$SelectDurationImpl({required this.duration});

  @override
  final DurationEachTimeLine duration;

  @override
  String toString() {
    return 'QueryPlaybackEvent.selectDuration(duration: $duration)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectDurationImpl &&
            (identical(other.duration, duration) ||
                other.duration == duration));
  }

  @override
  int get hashCode => Object.hash(runtimeType, duration);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectDurationImplCopyWith<_$SelectDurationImpl> get copyWith =>
      __$$SelectDurationImplCopyWithImpl<_$SelectDurationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return selectDuration(duration);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return selectDuration?.call(duration);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (selectDuration != null) {
      return selectDuration(duration);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return selectDuration(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return selectDuration?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (selectDuration != null) {
      return selectDuration(this);
    }
    return orElse();
  }
}

abstract class _SelectDuration implements QueryPlaybackEvent {
  const factory _SelectDuration(
      {required final DurationEachTimeLine duration}) = _$SelectDurationImpl;

  DurationEachTimeLine get duration;
  @JsonKey(ignore: true)
  _$$SelectDurationImplCopyWith<_$SelectDurationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DisposeEventImplCopyWith<$Res> {
  factory _$$DisposeEventImplCopyWith(
          _$DisposeEventImpl value, $Res Function(_$DisposeEventImpl) then) =
      __$$DisposeEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DisposeEventImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$DisposeEventImpl>
    implements _$$DisposeEventImplCopyWith<$Res> {
  __$$DisposeEventImplCopyWithImpl(
      _$DisposeEventImpl _value, $Res Function(_$DisposeEventImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DisposeEventImpl implements _DisposeEvent {
  const _$DisposeEventImpl();

  @override
  String toString() {
    return 'QueryPlaybackEvent.dispose()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DisposeEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return dispose();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return dispose?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (dispose != null) {
      return dispose();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return dispose(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return dispose?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (dispose != null) {
      return dispose(this);
    }
    return orElse();
  }
}

abstract class _DisposeEvent implements QueryPlaybackEvent {
  const factory _DisposeEvent() = _$DisposeEventImpl;
}

/// @nodoc
abstract class _$$SaveStateEventImplCopyWith<$Res> {
  factory _$$SaveStateEventImplCopyWith(_$SaveStateEventImpl value,
          $Res Function(_$SaveStateEventImpl) then) =
      __$$SaveStateEventImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SaveStateEventImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$SaveStateEventImpl>
    implements _$$SaveStateEventImplCopyWith<$Res> {
  __$$SaveStateEventImplCopyWithImpl(
      _$SaveStateEventImpl _value, $Res Function(_$SaveStateEventImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$SaveStateEventImpl implements _SaveStateEvent {
  const _$SaveStateEventImpl();

  @override
  String toString() {
    return 'QueryPlaybackEvent.saveState()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SaveStateEventImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return saveState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return saveState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (saveState != null) {
      return saveState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return saveState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return saveState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (saveState != null) {
      return saveState(this);
    }
    return orElse();
  }
}

abstract class _SaveStateEvent implements QueryPlaybackEvent {
  const factory _SaveStateEvent() = _$SaveStateEventImpl;
}

/// @nodoc
abstract class _$$LoadSaveStateImplCopyWith<$Res> {
  factory _$$LoadSaveStateImplCopyWith(
          _$LoadSaveStateImpl value, $Res Function(_$LoadSaveStateImpl) then) =
      __$$LoadSaveStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadSaveStateImplCopyWithImpl<$Res>
    extends _$QueryPlaybackEventCopyWithImpl<$Res, _$LoadSaveStateImpl>
    implements _$$LoadSaveStateImplCopyWith<$Res> {
  __$$LoadSaveStateImplCopyWithImpl(
      _$LoadSaveStateImpl _value, $Res Function(_$LoadSaveStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LoadSaveStateImpl implements _LoadSaveState {
  const _$LoadSaveStateImpl();

  @override
  String toString() {
    return 'QueryPlaybackEvent.loadSaveState()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadSaveStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TimelineRequest timeLineRequest) getListVideo,
    required TResult Function(Vehicle selectedVehicle) selectVehicle,
    required TResult Function(VehicleSetting channel) addChannel,
    required TResult Function(DateTime selectedDate) selectDate,
    required TResult Function() resetState,
    required TResult Function(TimelineResponse timelineResponse) loaded,
    required TResult Function(PlaybackFailure error) failureEvent,
    required TResult Function(TimelinePlayback timelinePlayback)
        selectPlaybackVideo,
    required TResult Function(DurationEachTimeLine duration) selectDuration,
    required TResult Function() dispose,
    required TResult Function() saveState,
    required TResult Function() loadSaveState,
  }) {
    return loadSaveState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult? Function(Vehicle selectedVehicle)? selectVehicle,
    TResult? Function(VehicleSetting channel)? addChannel,
    TResult? Function(DateTime selectedDate)? selectDate,
    TResult? Function()? resetState,
    TResult? Function(TimelineResponse timelineResponse)? loaded,
    TResult? Function(PlaybackFailure error)? failureEvent,
    TResult? Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult? Function(DurationEachTimeLine duration)? selectDuration,
    TResult? Function()? dispose,
    TResult? Function()? saveState,
    TResult? Function()? loadSaveState,
  }) {
    return loadSaveState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TimelineRequest timeLineRequest)? getListVideo,
    TResult Function(Vehicle selectedVehicle)? selectVehicle,
    TResult Function(VehicleSetting channel)? addChannel,
    TResult Function(DateTime selectedDate)? selectDate,
    TResult Function()? resetState,
    TResult Function(TimelineResponse timelineResponse)? loaded,
    TResult Function(PlaybackFailure error)? failureEvent,
    TResult Function(TimelinePlayback timelinePlayback)? selectPlaybackVideo,
    TResult Function(DurationEachTimeLine duration)? selectDuration,
    TResult Function()? dispose,
    TResult Function()? saveState,
    TResult Function()? loadSaveState,
    required TResult orElse(),
  }) {
    if (loadSaveState != null) {
      return loadSaveState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetListVideoEvent value) getListVideo,
    required TResult Function(_SelectVehicleEvent value) selectVehicle,
    required TResult Function(_AddChannelEvent value) addChannel,
    required TResult Function(_SelectDateEvent value) selectDate,
    required TResult Function(_ResetEvent value) resetState,
    required TResult Function(_LoadedEvent value) loaded,
    required TResult Function(_FailureEvent value) failureEvent,
    required TResult Function(_SelectPlaybackVideo value) selectPlaybackVideo,
    required TResult Function(_SelectDuration value) selectDuration,
    required TResult Function(_DisposeEvent value) dispose,
    required TResult Function(_SaveStateEvent value) saveState,
    required TResult Function(_LoadSaveState value) loadSaveState,
  }) {
    return loadSaveState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetListVideoEvent value)? getListVideo,
    TResult? Function(_SelectVehicleEvent value)? selectVehicle,
    TResult? Function(_AddChannelEvent value)? addChannel,
    TResult? Function(_SelectDateEvent value)? selectDate,
    TResult? Function(_ResetEvent value)? resetState,
    TResult? Function(_LoadedEvent value)? loaded,
    TResult? Function(_FailureEvent value)? failureEvent,
    TResult? Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult? Function(_SelectDuration value)? selectDuration,
    TResult? Function(_DisposeEvent value)? dispose,
    TResult? Function(_SaveStateEvent value)? saveState,
    TResult? Function(_LoadSaveState value)? loadSaveState,
  }) {
    return loadSaveState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetListVideoEvent value)? getListVideo,
    TResult Function(_SelectVehicleEvent value)? selectVehicle,
    TResult Function(_AddChannelEvent value)? addChannel,
    TResult Function(_SelectDateEvent value)? selectDate,
    TResult Function(_ResetEvent value)? resetState,
    TResult Function(_LoadedEvent value)? loaded,
    TResult Function(_FailureEvent value)? failureEvent,
    TResult Function(_SelectPlaybackVideo value)? selectPlaybackVideo,
    TResult Function(_SelectDuration value)? selectDuration,
    TResult Function(_DisposeEvent value)? dispose,
    TResult Function(_SaveStateEvent value)? saveState,
    TResult Function(_LoadSaveState value)? loadSaveState,
    required TResult orElse(),
  }) {
    if (loadSaveState != null) {
      return loadSaveState(this);
    }
    return orElse();
  }
}

abstract class _LoadSaveState implements QueryPlaybackEvent {
  const factory _LoadSaveState() = _$LoadSaveStateImpl;
}

/// @nodoc
mixin _$QueryPlaybackState {
  Vehicle? get vehicle => throw _privateConstructorUsedError;
  String? get token => throw _privateConstructorUsedError;
  bool get isGettingList => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  TimelineResponse? get timelineResponse => throw _privateConstructorUsedError;
  List<TimelinePlayback> get listTimeline => throw _privateConstructorUsedError;
  DateTime get dateSelected => throw _privateConstructorUsedError;
  List<VehicleSetting> get channelSelected =>
      throw _privateConstructorUsedError;
  DurationEachTimeLine get durationSelected =>
      throw _privateConstructorUsedError;
  TimelinePlayback? get timelineSelected => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $QueryPlaybackStateCopyWith<QueryPlaybackState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QueryPlaybackStateCopyWith<$Res> {
  factory $QueryPlaybackStateCopyWith(
          QueryPlaybackState value, $Res Function(QueryPlaybackState) then) =
      _$QueryPlaybackStateCopyWithImpl<$Res, QueryPlaybackState>;
  @useResult
  $Res call(
      {Vehicle? vehicle,
      String? token,
      bool isGettingList,
      String? error,
      TimelineResponse? timelineResponse,
      List<TimelinePlayback> listTimeline,
      DateTime dateSelected,
      List<VehicleSetting> channelSelected,
      DurationEachTimeLine durationSelected,
      TimelinePlayback? timelineSelected});

  $VehicleCopyWith<$Res>? get vehicle;
  $TimelineResponseCopyWith<$Res>? get timelineResponse;
  $TimelinePlaybackCopyWith<$Res>? get timelineSelected;
}

/// @nodoc
class _$QueryPlaybackStateCopyWithImpl<$Res, $Val extends QueryPlaybackState>
    implements $QueryPlaybackStateCopyWith<$Res> {
  _$QueryPlaybackStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicle = freezed,
    Object? token = freezed,
    Object? isGettingList = null,
    Object? error = freezed,
    Object? timelineResponse = freezed,
    Object? listTimeline = null,
    Object? dateSelected = null,
    Object? channelSelected = null,
    Object? durationSelected = null,
    Object? timelineSelected = freezed,
  }) {
    return _then(_value.copyWith(
      vehicle: freezed == vehicle
          ? _value.vehicle
          : vehicle // ignore: cast_nullable_to_non_nullable
              as Vehicle?,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      isGettingList: null == isGettingList
          ? _value.isGettingList
          : isGettingList // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      timelineResponse: freezed == timelineResponse
          ? _value.timelineResponse
          : timelineResponse // ignore: cast_nullable_to_non_nullable
              as TimelineResponse?,
      listTimeline: null == listTimeline
          ? _value.listTimeline
          : listTimeline // ignore: cast_nullable_to_non_nullable
              as List<TimelinePlayback>,
      dateSelected: null == dateSelected
          ? _value.dateSelected
          : dateSelected // ignore: cast_nullable_to_non_nullable
              as DateTime,
      channelSelected: null == channelSelected
          ? _value.channelSelected
          : channelSelected // ignore: cast_nullable_to_non_nullable
              as List<VehicleSetting>,
      durationSelected: null == durationSelected
          ? _value.durationSelected
          : durationSelected // ignore: cast_nullable_to_non_nullable
              as DurationEachTimeLine,
      timelineSelected: freezed == timelineSelected
          ? _value.timelineSelected
          : timelineSelected // ignore: cast_nullable_to_non_nullable
              as TimelinePlayback?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $VehicleCopyWith<$Res>? get vehicle {
    if (_value.vehicle == null) {
      return null;
    }

    return $VehicleCopyWith<$Res>(_value.vehicle!, (value) {
      return _then(_value.copyWith(vehicle: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $TimelineResponseCopyWith<$Res>? get timelineResponse {
    if (_value.timelineResponse == null) {
      return null;
    }

    return $TimelineResponseCopyWith<$Res>(_value.timelineResponse!, (value) {
      return _then(_value.copyWith(timelineResponse: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $TimelinePlaybackCopyWith<$Res>? get timelineSelected {
    if (_value.timelineSelected == null) {
      return null;
    }

    return $TimelinePlaybackCopyWith<$Res>(_value.timelineSelected!, (value) {
      return _then(_value.copyWith(timelineSelected: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$QueryPlaybackStateImplCopyWith<$Res>
    implements $QueryPlaybackStateCopyWith<$Res> {
  factory _$$QueryPlaybackStateImplCopyWith(_$QueryPlaybackStateImpl value,
          $Res Function(_$QueryPlaybackStateImpl) then) =
      __$$QueryPlaybackStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Vehicle? vehicle,
      String? token,
      bool isGettingList,
      String? error,
      TimelineResponse? timelineResponse,
      List<TimelinePlayback> listTimeline,
      DateTime dateSelected,
      List<VehicleSetting> channelSelected,
      DurationEachTimeLine durationSelected,
      TimelinePlayback? timelineSelected});

  @override
  $VehicleCopyWith<$Res>? get vehicle;
  @override
  $TimelineResponseCopyWith<$Res>? get timelineResponse;
  @override
  $TimelinePlaybackCopyWith<$Res>? get timelineSelected;
}

/// @nodoc
class __$$QueryPlaybackStateImplCopyWithImpl<$Res>
    extends _$QueryPlaybackStateCopyWithImpl<$Res, _$QueryPlaybackStateImpl>
    implements _$$QueryPlaybackStateImplCopyWith<$Res> {
  __$$QueryPlaybackStateImplCopyWithImpl(_$QueryPlaybackStateImpl _value,
      $Res Function(_$QueryPlaybackStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicle = freezed,
    Object? token = freezed,
    Object? isGettingList = null,
    Object? error = freezed,
    Object? timelineResponse = freezed,
    Object? listTimeline = null,
    Object? dateSelected = null,
    Object? channelSelected = null,
    Object? durationSelected = null,
    Object? timelineSelected = freezed,
  }) {
    return _then(_$QueryPlaybackStateImpl(
      vehicle: freezed == vehicle
          ? _value.vehicle
          : vehicle // ignore: cast_nullable_to_non_nullable
              as Vehicle?,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      isGettingList: null == isGettingList
          ? _value.isGettingList
          : isGettingList // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      timelineResponse: freezed == timelineResponse
          ? _value.timelineResponse
          : timelineResponse // ignore: cast_nullable_to_non_nullable
              as TimelineResponse?,
      listTimeline: null == listTimeline
          ? _value._listTimeline
          : listTimeline // ignore: cast_nullable_to_non_nullable
              as List<TimelinePlayback>,
      dateSelected: null == dateSelected
          ? _value.dateSelected
          : dateSelected // ignore: cast_nullable_to_non_nullable
              as DateTime,
      channelSelected: null == channelSelected
          ? _value._channelSelected
          : channelSelected // ignore: cast_nullable_to_non_nullable
              as List<VehicleSetting>,
      durationSelected: null == durationSelected
          ? _value.durationSelected
          : durationSelected // ignore: cast_nullable_to_non_nullable
              as DurationEachTimeLine,
      timelineSelected: freezed == timelineSelected
          ? _value.timelineSelected
          : timelineSelected // ignore: cast_nullable_to_non_nullable
              as TimelinePlayback?,
    ));
  }
}

/// @nodoc

class _$QueryPlaybackStateImpl implements _QueryPlaybackState {
  const _$QueryPlaybackStateImpl(
      {required this.vehicle,
      required this.token,
      required this.isGettingList,
      required this.error,
      required this.timelineResponse,
      required final List<TimelinePlayback> listTimeline,
      required this.dateSelected,
      required final List<VehicleSetting> channelSelected,
      required this.durationSelected,
      required this.timelineSelected})
      : _listTimeline = listTimeline,
        _channelSelected = channelSelected;

  @override
  final Vehicle? vehicle;
  @override
  final String? token;
  @override
  final bool isGettingList;
  @override
  final String? error;
  @override
  final TimelineResponse? timelineResponse;
  final List<TimelinePlayback> _listTimeline;
  @override
  List<TimelinePlayback> get listTimeline {
    if (_listTimeline is EqualUnmodifiableListView) return _listTimeline;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listTimeline);
  }

  @override
  final DateTime dateSelected;
  final List<VehicleSetting> _channelSelected;
  @override
  List<VehicleSetting> get channelSelected {
    if (_channelSelected is EqualUnmodifiableListView) return _channelSelected;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_channelSelected);
  }

  @override
  final DurationEachTimeLine durationSelected;
  @override
  final TimelinePlayback? timelineSelected;

  @override
  String toString() {
    return 'QueryPlaybackState(vehicle: $vehicle, token: $token, isGettingList: $isGettingList, error: $error, timelineResponse: $timelineResponse, listTimeline: $listTimeline, dateSelected: $dateSelected, channelSelected: $channelSelected, durationSelected: $durationSelected, timelineSelected: $timelineSelected)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QueryPlaybackStateImpl &&
            (identical(other.vehicle, vehicle) || other.vehicle == vehicle) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.isGettingList, isGettingList) ||
                other.isGettingList == isGettingList) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.timelineResponse, timelineResponse) ||
                other.timelineResponse == timelineResponse) &&
            const DeepCollectionEquality()
                .equals(other._listTimeline, _listTimeline) &&
            (identical(other.dateSelected, dateSelected) ||
                other.dateSelected == dateSelected) &&
            const DeepCollectionEquality()
                .equals(other._channelSelected, _channelSelected) &&
            (identical(other.durationSelected, durationSelected) ||
                other.durationSelected == durationSelected) &&
            (identical(other.timelineSelected, timelineSelected) ||
                other.timelineSelected == timelineSelected));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      vehicle,
      token,
      isGettingList,
      error,
      timelineResponse,
      const DeepCollectionEquality().hash(_listTimeline),
      dateSelected,
      const DeepCollectionEquality().hash(_channelSelected),
      durationSelected,
      timelineSelected);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QueryPlaybackStateImplCopyWith<_$QueryPlaybackStateImpl> get copyWith =>
      __$$QueryPlaybackStateImplCopyWithImpl<_$QueryPlaybackStateImpl>(
          this, _$identity);
}

abstract class _QueryPlaybackState implements QueryPlaybackState {
  const factory _QueryPlaybackState(
          {required final Vehicle? vehicle,
          required final String? token,
          required final bool isGettingList,
          required final String? error,
          required final TimelineResponse? timelineResponse,
          required final List<TimelinePlayback> listTimeline,
          required final DateTime dateSelected,
          required final List<VehicleSetting> channelSelected,
          required final DurationEachTimeLine durationSelected,
          required final TimelinePlayback? timelineSelected}) =
      _$QueryPlaybackStateImpl;

  @override
  Vehicle? get vehicle;
  @override
  String? get token;
  @override
  bool get isGettingList;
  @override
  String? get error;
  @override
  TimelineResponse? get timelineResponse;
  @override
  List<TimelinePlayback> get listTimeline;
  @override
  DateTime get dateSelected;
  @override
  List<VehicleSetting> get channelSelected;
  @override
  DurationEachTimeLine get durationSelected;
  @override
  TimelinePlayback? get timelineSelected;
  @override
  @JsonKey(ignore: true)
  _$$QueryPlaybackStateImplCopyWith<_$QueryPlaybackStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
