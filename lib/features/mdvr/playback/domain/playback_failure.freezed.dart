// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'playback_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PlaybackFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function(String? token) registerWithToken,
    required TResult Function() pending,
    required TResult Function() noListVideoData,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function(String? token)? registerWithToken,
    TResult? Function()? pending,
    TResult? Function()? noListVideoData,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function(String? token)? registerWithToken,
    TResult Function()? pending,
    TResult Function()? noListVideoData,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PlaybackFailureUnexpected value) unexpected,
    required TResult Function(_PlaybackFailureUnauthorized value) unauthorized,
    required TResult Function(_PlaybackFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_PlaybackFailureServerError value) serverError,
    required TResult Function(_PlaybackFailureRegisterWithToken value)
        registerWithToken,
    required TResult Function(_PlaybackFailurePending value) pending,
    required TResult Function(_PlaybackFailureNoListVideoData value)
        noListVideoData,
    required TResult Function(_PlaybackFailureNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult? Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult? Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_PlaybackFailureServerError value)? serverError,
    TResult? Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult? Function(_PlaybackFailurePending value)? pending,
    TResult? Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult? Function(_PlaybackFailureNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult Function(_PlaybackFailureServerError value)? serverError,
    TResult Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult Function(_PlaybackFailurePending value)? pending,
    TResult Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult Function(_PlaybackFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlaybackFailureCopyWith<$Res> {
  factory $PlaybackFailureCopyWith(
          PlaybackFailure value, $Res Function(PlaybackFailure) then) =
      _$PlaybackFailureCopyWithImpl<$Res, PlaybackFailure>;
}

/// @nodoc
class _$PlaybackFailureCopyWithImpl<$Res, $Val extends PlaybackFailure>
    implements $PlaybackFailureCopyWith<$Res> {
  _$PlaybackFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$PlaybackFailureUnexpectedImplCopyWith<$Res> {
  factory _$$PlaybackFailureUnexpectedImplCopyWith(
          _$PlaybackFailureUnexpectedImpl value,
          $Res Function(_$PlaybackFailureUnexpectedImpl) then) =
      __$$PlaybackFailureUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$PlaybackFailureUnexpectedImplCopyWithImpl<$Res>
    extends _$PlaybackFailureCopyWithImpl<$Res, _$PlaybackFailureUnexpectedImpl>
    implements _$$PlaybackFailureUnexpectedImplCopyWith<$Res> {
  __$$PlaybackFailureUnexpectedImplCopyWithImpl(
      _$PlaybackFailureUnexpectedImpl _value,
      $Res Function(_$PlaybackFailureUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$PlaybackFailureUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$PlaybackFailureUnexpectedImpl implements _PlaybackFailureUnexpected {
  const _$PlaybackFailureUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'PlaybackFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaybackFailureUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PlaybackFailureUnexpectedImplCopyWith<_$PlaybackFailureUnexpectedImpl>
      get copyWith => __$$PlaybackFailureUnexpectedImplCopyWithImpl<
          _$PlaybackFailureUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function(String? token) registerWithToken,
    required TResult Function() pending,
    required TResult Function() noListVideoData,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function(String? token)? registerWithToken,
    TResult? Function()? pending,
    TResult? Function()? noListVideoData,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function(String? token)? registerWithToken,
    TResult Function()? pending,
    TResult Function()? noListVideoData,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PlaybackFailureUnexpected value) unexpected,
    required TResult Function(_PlaybackFailureUnauthorized value) unauthorized,
    required TResult Function(_PlaybackFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_PlaybackFailureServerError value) serverError,
    required TResult Function(_PlaybackFailureRegisterWithToken value)
        registerWithToken,
    required TResult Function(_PlaybackFailurePending value) pending,
    required TResult Function(_PlaybackFailureNoListVideoData value)
        noListVideoData,
    required TResult Function(_PlaybackFailureNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult? Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult? Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_PlaybackFailureServerError value)? serverError,
    TResult? Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult? Function(_PlaybackFailurePending value)? pending,
    TResult? Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult? Function(_PlaybackFailureNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult Function(_PlaybackFailureServerError value)? serverError,
    TResult Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult Function(_PlaybackFailurePending value)? pending,
    TResult Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult Function(_PlaybackFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _PlaybackFailureUnexpected implements PlaybackFailure {
  const factory _PlaybackFailureUnexpected({required final String error}) =
      _$PlaybackFailureUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$PlaybackFailureUnexpectedImplCopyWith<_$PlaybackFailureUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PlaybackFailureUnauthorizedImplCopyWith<$Res> {
  factory _$$PlaybackFailureUnauthorizedImplCopyWith(
          _$PlaybackFailureUnauthorizedImpl value,
          $Res Function(_$PlaybackFailureUnauthorizedImpl) then) =
      __$$PlaybackFailureUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PlaybackFailureUnauthorizedImplCopyWithImpl<$Res>
    extends _$PlaybackFailureCopyWithImpl<$Res,
        _$PlaybackFailureUnauthorizedImpl>
    implements _$$PlaybackFailureUnauthorizedImplCopyWith<$Res> {
  __$$PlaybackFailureUnauthorizedImplCopyWithImpl(
      _$PlaybackFailureUnauthorizedImpl _value,
      $Res Function(_$PlaybackFailureUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$PlaybackFailureUnauthorizedImpl
    implements _PlaybackFailureUnauthorized {
  const _$PlaybackFailureUnauthorizedImpl();

  @override
  String toString() {
    return 'PlaybackFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaybackFailureUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function(String? token) registerWithToken,
    required TResult Function() pending,
    required TResult Function() noListVideoData,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function(String? token)? registerWithToken,
    TResult? Function()? pending,
    TResult? Function()? noListVideoData,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function(String? token)? registerWithToken,
    TResult Function()? pending,
    TResult Function()? noListVideoData,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PlaybackFailureUnexpected value) unexpected,
    required TResult Function(_PlaybackFailureUnauthorized value) unauthorized,
    required TResult Function(_PlaybackFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_PlaybackFailureServerError value) serverError,
    required TResult Function(_PlaybackFailureRegisterWithToken value)
        registerWithToken,
    required TResult Function(_PlaybackFailurePending value) pending,
    required TResult Function(_PlaybackFailureNoListVideoData value)
        noListVideoData,
    required TResult Function(_PlaybackFailureNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult? Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult? Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_PlaybackFailureServerError value)? serverError,
    TResult? Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult? Function(_PlaybackFailurePending value)? pending,
    TResult? Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult? Function(_PlaybackFailureNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult Function(_PlaybackFailureServerError value)? serverError,
    TResult Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult Function(_PlaybackFailurePending value)? pending,
    TResult Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult Function(_PlaybackFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _PlaybackFailureUnauthorized implements PlaybackFailure {
  const factory _PlaybackFailureUnauthorized() =
      _$PlaybackFailureUnauthorizedImpl;
}

/// @nodoc
abstract class _$$PlaybackFailureUnauthenticatedImplCopyWith<$Res> {
  factory _$$PlaybackFailureUnauthenticatedImplCopyWith(
          _$PlaybackFailureUnauthenticatedImpl value,
          $Res Function(_$PlaybackFailureUnauthenticatedImpl) then) =
      __$$PlaybackFailureUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PlaybackFailureUnauthenticatedImplCopyWithImpl<$Res>
    extends _$PlaybackFailureCopyWithImpl<$Res,
        _$PlaybackFailureUnauthenticatedImpl>
    implements _$$PlaybackFailureUnauthenticatedImplCopyWith<$Res> {
  __$$PlaybackFailureUnauthenticatedImplCopyWithImpl(
      _$PlaybackFailureUnauthenticatedImpl _value,
      $Res Function(_$PlaybackFailureUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$PlaybackFailureUnauthenticatedImpl
    implements _PlaybackFailureUnauthenticated {
  const _$PlaybackFailureUnauthenticatedImpl();

  @override
  String toString() {
    return 'PlaybackFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaybackFailureUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function(String? token) registerWithToken,
    required TResult Function() pending,
    required TResult Function() noListVideoData,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function(String? token)? registerWithToken,
    TResult? Function()? pending,
    TResult? Function()? noListVideoData,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function(String? token)? registerWithToken,
    TResult Function()? pending,
    TResult Function()? noListVideoData,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PlaybackFailureUnexpected value) unexpected,
    required TResult Function(_PlaybackFailureUnauthorized value) unauthorized,
    required TResult Function(_PlaybackFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_PlaybackFailureServerError value) serverError,
    required TResult Function(_PlaybackFailureRegisterWithToken value)
        registerWithToken,
    required TResult Function(_PlaybackFailurePending value) pending,
    required TResult Function(_PlaybackFailureNoListVideoData value)
        noListVideoData,
    required TResult Function(_PlaybackFailureNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult? Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult? Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_PlaybackFailureServerError value)? serverError,
    TResult? Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult? Function(_PlaybackFailurePending value)? pending,
    TResult? Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult? Function(_PlaybackFailureNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult Function(_PlaybackFailureServerError value)? serverError,
    TResult Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult Function(_PlaybackFailurePending value)? pending,
    TResult Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult Function(_PlaybackFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _PlaybackFailureUnauthenticated implements PlaybackFailure {
  const factory _PlaybackFailureUnauthenticated() =
      _$PlaybackFailureUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$PlaybackFailureServerErrorImplCopyWith<$Res> {
  factory _$$PlaybackFailureServerErrorImplCopyWith(
          _$PlaybackFailureServerErrorImpl value,
          $Res Function(_$PlaybackFailureServerErrorImpl) then) =
      __$$PlaybackFailureServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PlaybackFailureServerErrorImplCopyWithImpl<$Res>
    extends _$PlaybackFailureCopyWithImpl<$Res,
        _$PlaybackFailureServerErrorImpl>
    implements _$$PlaybackFailureServerErrorImplCopyWith<$Res> {
  __$$PlaybackFailureServerErrorImplCopyWithImpl(
      _$PlaybackFailureServerErrorImpl _value,
      $Res Function(_$PlaybackFailureServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$PlaybackFailureServerErrorImpl implements _PlaybackFailureServerError {
  const _$PlaybackFailureServerErrorImpl();

  @override
  String toString() {
    return 'PlaybackFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaybackFailureServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function(String? token) registerWithToken,
    required TResult Function() pending,
    required TResult Function() noListVideoData,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function(String? token)? registerWithToken,
    TResult? Function()? pending,
    TResult? Function()? noListVideoData,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function(String? token)? registerWithToken,
    TResult Function()? pending,
    TResult Function()? noListVideoData,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PlaybackFailureUnexpected value) unexpected,
    required TResult Function(_PlaybackFailureUnauthorized value) unauthorized,
    required TResult Function(_PlaybackFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_PlaybackFailureServerError value) serverError,
    required TResult Function(_PlaybackFailureRegisterWithToken value)
        registerWithToken,
    required TResult Function(_PlaybackFailurePending value) pending,
    required TResult Function(_PlaybackFailureNoListVideoData value)
        noListVideoData,
    required TResult Function(_PlaybackFailureNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult? Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult? Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_PlaybackFailureServerError value)? serverError,
    TResult? Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult? Function(_PlaybackFailurePending value)? pending,
    TResult? Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult? Function(_PlaybackFailureNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult Function(_PlaybackFailureServerError value)? serverError,
    TResult Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult Function(_PlaybackFailurePending value)? pending,
    TResult Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult Function(_PlaybackFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _PlaybackFailureServerError implements PlaybackFailure {
  const factory _PlaybackFailureServerError() =
      _$PlaybackFailureServerErrorImpl;
}

/// @nodoc
abstract class _$$PlaybackFailureRegisterWithTokenImplCopyWith<$Res> {
  factory _$$PlaybackFailureRegisterWithTokenImplCopyWith(
          _$PlaybackFailureRegisterWithTokenImpl value,
          $Res Function(_$PlaybackFailureRegisterWithTokenImpl) then) =
      __$$PlaybackFailureRegisterWithTokenImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? token});
}

/// @nodoc
class __$$PlaybackFailureRegisterWithTokenImplCopyWithImpl<$Res>
    extends _$PlaybackFailureCopyWithImpl<$Res,
        _$PlaybackFailureRegisterWithTokenImpl>
    implements _$$PlaybackFailureRegisterWithTokenImplCopyWith<$Res> {
  __$$PlaybackFailureRegisterWithTokenImplCopyWithImpl(
      _$PlaybackFailureRegisterWithTokenImpl _value,
      $Res Function(_$PlaybackFailureRegisterWithTokenImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = freezed,
  }) {
    return _then(_$PlaybackFailureRegisterWithTokenImpl(
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$PlaybackFailureRegisterWithTokenImpl
    implements _PlaybackFailureRegisterWithToken {
  const _$PlaybackFailureRegisterWithTokenImpl({required this.token});

  @override
  final String? token;

  @override
  String toString() {
    return 'PlaybackFailure.registerWithToken(token: $token)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaybackFailureRegisterWithTokenImpl &&
            (identical(other.token, token) || other.token == token));
  }

  @override
  int get hashCode => Object.hash(runtimeType, token);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PlaybackFailureRegisterWithTokenImplCopyWith<
          _$PlaybackFailureRegisterWithTokenImpl>
      get copyWith => __$$PlaybackFailureRegisterWithTokenImplCopyWithImpl<
          _$PlaybackFailureRegisterWithTokenImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function(String? token) registerWithToken,
    required TResult Function() pending,
    required TResult Function() noListVideoData,
    required TResult Function() noInternet,
  }) {
    return registerWithToken(token);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function(String? token)? registerWithToken,
    TResult? Function()? pending,
    TResult? Function()? noListVideoData,
    TResult? Function()? noInternet,
  }) {
    return registerWithToken?.call(token);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function(String? token)? registerWithToken,
    TResult Function()? pending,
    TResult Function()? noListVideoData,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (registerWithToken != null) {
      return registerWithToken(token);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PlaybackFailureUnexpected value) unexpected,
    required TResult Function(_PlaybackFailureUnauthorized value) unauthorized,
    required TResult Function(_PlaybackFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_PlaybackFailureServerError value) serverError,
    required TResult Function(_PlaybackFailureRegisterWithToken value)
        registerWithToken,
    required TResult Function(_PlaybackFailurePending value) pending,
    required TResult Function(_PlaybackFailureNoListVideoData value)
        noListVideoData,
    required TResult Function(_PlaybackFailureNoInternet value) noInternet,
  }) {
    return registerWithToken(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult? Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult? Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_PlaybackFailureServerError value)? serverError,
    TResult? Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult? Function(_PlaybackFailurePending value)? pending,
    TResult? Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult? Function(_PlaybackFailureNoInternet value)? noInternet,
  }) {
    return registerWithToken?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult Function(_PlaybackFailureServerError value)? serverError,
    TResult Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult Function(_PlaybackFailurePending value)? pending,
    TResult Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult Function(_PlaybackFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (registerWithToken != null) {
      return registerWithToken(this);
    }
    return orElse();
  }
}

abstract class _PlaybackFailureRegisterWithToken implements PlaybackFailure {
  const factory _PlaybackFailureRegisterWithToken(
      {required final String? token}) = _$PlaybackFailureRegisterWithTokenImpl;

  String? get token;
  @JsonKey(ignore: true)
  _$$PlaybackFailureRegisterWithTokenImplCopyWith<
          _$PlaybackFailureRegisterWithTokenImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PlaybackFailurePendingImplCopyWith<$Res> {
  factory _$$PlaybackFailurePendingImplCopyWith(
          _$PlaybackFailurePendingImpl value,
          $Res Function(_$PlaybackFailurePendingImpl) then) =
      __$$PlaybackFailurePendingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PlaybackFailurePendingImplCopyWithImpl<$Res>
    extends _$PlaybackFailureCopyWithImpl<$Res, _$PlaybackFailurePendingImpl>
    implements _$$PlaybackFailurePendingImplCopyWith<$Res> {
  __$$PlaybackFailurePendingImplCopyWithImpl(
      _$PlaybackFailurePendingImpl _value,
      $Res Function(_$PlaybackFailurePendingImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$PlaybackFailurePendingImpl implements _PlaybackFailurePending {
  const _$PlaybackFailurePendingImpl();

  @override
  String toString() {
    return 'PlaybackFailure.pending()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaybackFailurePendingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function(String? token) registerWithToken,
    required TResult Function() pending,
    required TResult Function() noListVideoData,
    required TResult Function() noInternet,
  }) {
    return pending();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function(String? token)? registerWithToken,
    TResult? Function()? pending,
    TResult? Function()? noListVideoData,
    TResult? Function()? noInternet,
  }) {
    return pending?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function(String? token)? registerWithToken,
    TResult Function()? pending,
    TResult Function()? noListVideoData,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (pending != null) {
      return pending();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PlaybackFailureUnexpected value) unexpected,
    required TResult Function(_PlaybackFailureUnauthorized value) unauthorized,
    required TResult Function(_PlaybackFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_PlaybackFailureServerError value) serverError,
    required TResult Function(_PlaybackFailureRegisterWithToken value)
        registerWithToken,
    required TResult Function(_PlaybackFailurePending value) pending,
    required TResult Function(_PlaybackFailureNoListVideoData value)
        noListVideoData,
    required TResult Function(_PlaybackFailureNoInternet value) noInternet,
  }) {
    return pending(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult? Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult? Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_PlaybackFailureServerError value)? serverError,
    TResult? Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult? Function(_PlaybackFailurePending value)? pending,
    TResult? Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult? Function(_PlaybackFailureNoInternet value)? noInternet,
  }) {
    return pending?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult Function(_PlaybackFailureServerError value)? serverError,
    TResult Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult Function(_PlaybackFailurePending value)? pending,
    TResult Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult Function(_PlaybackFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (pending != null) {
      return pending(this);
    }
    return orElse();
  }
}

abstract class _PlaybackFailurePending implements PlaybackFailure {
  const factory _PlaybackFailurePending() = _$PlaybackFailurePendingImpl;
}

/// @nodoc
abstract class _$$PlaybackFailureNoListVideoDataImplCopyWith<$Res> {
  factory _$$PlaybackFailureNoListVideoDataImplCopyWith(
          _$PlaybackFailureNoListVideoDataImpl value,
          $Res Function(_$PlaybackFailureNoListVideoDataImpl) then) =
      __$$PlaybackFailureNoListVideoDataImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PlaybackFailureNoListVideoDataImplCopyWithImpl<$Res>
    extends _$PlaybackFailureCopyWithImpl<$Res,
        _$PlaybackFailureNoListVideoDataImpl>
    implements _$$PlaybackFailureNoListVideoDataImplCopyWith<$Res> {
  __$$PlaybackFailureNoListVideoDataImplCopyWithImpl(
      _$PlaybackFailureNoListVideoDataImpl _value,
      $Res Function(_$PlaybackFailureNoListVideoDataImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$PlaybackFailureNoListVideoDataImpl
    implements _PlaybackFailureNoListVideoData {
  const _$PlaybackFailureNoListVideoDataImpl();

  @override
  String toString() {
    return 'PlaybackFailure.noListVideoData()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaybackFailureNoListVideoDataImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function(String? token) registerWithToken,
    required TResult Function() pending,
    required TResult Function() noListVideoData,
    required TResult Function() noInternet,
  }) {
    return noListVideoData();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function(String? token)? registerWithToken,
    TResult? Function()? pending,
    TResult? Function()? noListVideoData,
    TResult? Function()? noInternet,
  }) {
    return noListVideoData?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function(String? token)? registerWithToken,
    TResult Function()? pending,
    TResult Function()? noListVideoData,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noListVideoData != null) {
      return noListVideoData();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PlaybackFailureUnexpected value) unexpected,
    required TResult Function(_PlaybackFailureUnauthorized value) unauthorized,
    required TResult Function(_PlaybackFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_PlaybackFailureServerError value) serverError,
    required TResult Function(_PlaybackFailureRegisterWithToken value)
        registerWithToken,
    required TResult Function(_PlaybackFailurePending value) pending,
    required TResult Function(_PlaybackFailureNoListVideoData value)
        noListVideoData,
    required TResult Function(_PlaybackFailureNoInternet value) noInternet,
  }) {
    return noListVideoData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult? Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult? Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_PlaybackFailureServerError value)? serverError,
    TResult? Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult? Function(_PlaybackFailurePending value)? pending,
    TResult? Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult? Function(_PlaybackFailureNoInternet value)? noInternet,
  }) {
    return noListVideoData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult Function(_PlaybackFailureServerError value)? serverError,
    TResult Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult Function(_PlaybackFailurePending value)? pending,
    TResult Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult Function(_PlaybackFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noListVideoData != null) {
      return noListVideoData(this);
    }
    return orElse();
  }
}

abstract class _PlaybackFailureNoListVideoData implements PlaybackFailure {
  const factory _PlaybackFailureNoListVideoData() =
      _$PlaybackFailureNoListVideoDataImpl;
}

/// @nodoc
abstract class _$$PlaybackFailureNoInternetImplCopyWith<$Res> {
  factory _$$PlaybackFailureNoInternetImplCopyWith(
          _$PlaybackFailureNoInternetImpl value,
          $Res Function(_$PlaybackFailureNoInternetImpl) then) =
      __$$PlaybackFailureNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PlaybackFailureNoInternetImplCopyWithImpl<$Res>
    extends _$PlaybackFailureCopyWithImpl<$Res, _$PlaybackFailureNoInternetImpl>
    implements _$$PlaybackFailureNoInternetImplCopyWith<$Res> {
  __$$PlaybackFailureNoInternetImplCopyWithImpl(
      _$PlaybackFailureNoInternetImpl _value,
      $Res Function(_$PlaybackFailureNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$PlaybackFailureNoInternetImpl implements _PlaybackFailureNoInternet {
  const _$PlaybackFailureNoInternetImpl();

  @override
  String toString() {
    return 'PlaybackFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaybackFailureNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function(String? token) registerWithToken,
    required TResult Function() pending,
    required TResult Function() noListVideoData,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function(String? token)? registerWithToken,
    TResult? Function()? pending,
    TResult? Function()? noListVideoData,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function(String? token)? registerWithToken,
    TResult Function()? pending,
    TResult Function()? noListVideoData,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PlaybackFailureUnexpected value) unexpected,
    required TResult Function(_PlaybackFailureUnauthorized value) unauthorized,
    required TResult Function(_PlaybackFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_PlaybackFailureServerError value) serverError,
    required TResult Function(_PlaybackFailureRegisterWithToken value)
        registerWithToken,
    required TResult Function(_PlaybackFailurePending value) pending,
    required TResult Function(_PlaybackFailureNoListVideoData value)
        noListVideoData,
    required TResult Function(_PlaybackFailureNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult? Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult? Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_PlaybackFailureServerError value)? serverError,
    TResult? Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult? Function(_PlaybackFailurePending value)? pending,
    TResult? Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult? Function(_PlaybackFailureNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PlaybackFailureUnexpected value)? unexpected,
    TResult Function(_PlaybackFailureUnauthorized value)? unauthorized,
    TResult Function(_PlaybackFailureUnauthenticated value)? unauthenticated,
    TResult Function(_PlaybackFailureServerError value)? serverError,
    TResult Function(_PlaybackFailureRegisterWithToken value)?
        registerWithToken,
    TResult Function(_PlaybackFailurePending value)? pending,
    TResult Function(_PlaybackFailureNoListVideoData value)? noListVideoData,
    TResult Function(_PlaybackFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _PlaybackFailureNoInternet implements PlaybackFailure {
  const factory _PlaybackFailureNoInternet() = _$PlaybackFailureNoInternetImpl;
}
