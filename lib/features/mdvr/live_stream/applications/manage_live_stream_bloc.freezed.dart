// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_live_stream_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ManageLiveStreamEvent {
  List<LiveStreamRequest> get listRequest => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<LiveStreamRequest> listRequest)
        updateLiveList,
    required TResult Function(List<LiveStreamRequest> listRequest)
        removeListLive,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<LiveStreamRequest> listRequest)? updateLiveList,
    TResult? Function(List<LiveStreamRequest> listRequest)? removeListLive,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<LiveStreamRequest> listRequest)? updateLiveList,
    TResult Function(List<LiveStreamRequest> listRequest)? removeListLive,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_UpdateLiveList value) updateLiveList,
    required TResult Function(_RemoveListLive value) removeListLive,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_UpdateLiveList value)? updateLiveList,
    TResult? Function(_RemoveListLive value)? removeListLive,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_UpdateLiveList value)? updateLiveList,
    TResult Function(_RemoveListLive value)? removeListLive,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ManageLiveStreamEventCopyWith<ManageLiveStreamEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageLiveStreamEventCopyWith<$Res> {
  factory $ManageLiveStreamEventCopyWith(ManageLiveStreamEvent value,
          $Res Function(ManageLiveStreamEvent) then) =
      _$ManageLiveStreamEventCopyWithImpl<$Res, ManageLiveStreamEvent>;
  @useResult
  $Res call({List<LiveStreamRequest> listRequest});
}

/// @nodoc
class _$ManageLiveStreamEventCopyWithImpl<$Res,
        $Val extends ManageLiveStreamEvent>
    implements $ManageLiveStreamEventCopyWith<$Res> {
  _$ManageLiveStreamEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listRequest = null,
  }) {
    return _then(_value.copyWith(
      listRequest: null == listRequest
          ? _value.listRequest
          : listRequest // ignore: cast_nullable_to_non_nullable
              as List<LiveStreamRequest>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateLiveListImplCopyWith<$Res>
    implements $ManageLiveStreamEventCopyWith<$Res> {
  factory _$$UpdateLiveListImplCopyWith(_$UpdateLiveListImpl value,
          $Res Function(_$UpdateLiveListImpl) then) =
      __$$UpdateLiveListImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<LiveStreamRequest> listRequest});
}

/// @nodoc
class __$$UpdateLiveListImplCopyWithImpl<$Res>
    extends _$ManageLiveStreamEventCopyWithImpl<$Res, _$UpdateLiveListImpl>
    implements _$$UpdateLiveListImplCopyWith<$Res> {
  __$$UpdateLiveListImplCopyWithImpl(
      _$UpdateLiveListImpl _value, $Res Function(_$UpdateLiveListImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listRequest = null,
  }) {
    return _then(_$UpdateLiveListImpl(
      listRequest: null == listRequest
          ? _value._listRequest
          : listRequest // ignore: cast_nullable_to_non_nullable
              as List<LiveStreamRequest>,
    ));
  }
}

/// @nodoc

class _$UpdateLiveListImpl implements _UpdateLiveList {
  const _$UpdateLiveListImpl(
      {required final List<LiveStreamRequest> listRequest})
      : _listRequest = listRequest;

  final List<LiveStreamRequest> _listRequest;
  @override
  List<LiveStreamRequest> get listRequest {
    if (_listRequest is EqualUnmodifiableListView) return _listRequest;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listRequest);
  }

  @override
  String toString() {
    return 'ManageLiveStreamEvent.updateLiveList(listRequest: $listRequest)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateLiveListImpl &&
            const DeepCollectionEquality()
                .equals(other._listRequest, _listRequest));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_listRequest));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateLiveListImplCopyWith<_$UpdateLiveListImpl> get copyWith =>
      __$$UpdateLiveListImplCopyWithImpl<_$UpdateLiveListImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<LiveStreamRequest> listRequest)
        updateLiveList,
    required TResult Function(List<LiveStreamRequest> listRequest)
        removeListLive,
  }) {
    return updateLiveList(listRequest);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<LiveStreamRequest> listRequest)? updateLiveList,
    TResult? Function(List<LiveStreamRequest> listRequest)? removeListLive,
  }) {
    return updateLiveList?.call(listRequest);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<LiveStreamRequest> listRequest)? updateLiveList,
    TResult Function(List<LiveStreamRequest> listRequest)? removeListLive,
    required TResult orElse(),
  }) {
    if (updateLiveList != null) {
      return updateLiveList(listRequest);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_UpdateLiveList value) updateLiveList,
    required TResult Function(_RemoveListLive value) removeListLive,
  }) {
    return updateLiveList(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_UpdateLiveList value)? updateLiveList,
    TResult? Function(_RemoveListLive value)? removeListLive,
  }) {
    return updateLiveList?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_UpdateLiveList value)? updateLiveList,
    TResult Function(_RemoveListLive value)? removeListLive,
    required TResult orElse(),
  }) {
    if (updateLiveList != null) {
      return updateLiveList(this);
    }
    return orElse();
  }
}

abstract class _UpdateLiveList implements ManageLiveStreamEvent {
  const factory _UpdateLiveList(
          {required final List<LiveStreamRequest> listRequest}) =
      _$UpdateLiveListImpl;

  @override
  List<LiveStreamRequest> get listRequest;
  @override
  @JsonKey(ignore: true)
  _$$UpdateLiveListImplCopyWith<_$UpdateLiveListImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RemoveListLiveImplCopyWith<$Res>
    implements $ManageLiveStreamEventCopyWith<$Res> {
  factory _$$RemoveListLiveImplCopyWith(_$RemoveListLiveImpl value,
          $Res Function(_$RemoveListLiveImpl) then) =
      __$$RemoveListLiveImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<LiveStreamRequest> listRequest});
}

/// @nodoc
class __$$RemoveListLiveImplCopyWithImpl<$Res>
    extends _$ManageLiveStreamEventCopyWithImpl<$Res, _$RemoveListLiveImpl>
    implements _$$RemoveListLiveImplCopyWith<$Res> {
  __$$RemoveListLiveImplCopyWithImpl(
      _$RemoveListLiveImpl _value, $Res Function(_$RemoveListLiveImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listRequest = null,
  }) {
    return _then(_$RemoveListLiveImpl(
      listRequest: null == listRequest
          ? _value._listRequest
          : listRequest // ignore: cast_nullable_to_non_nullable
              as List<LiveStreamRequest>,
    ));
  }
}

/// @nodoc

class _$RemoveListLiveImpl implements _RemoveListLive {
  const _$RemoveListLiveImpl(
      {required final List<LiveStreamRequest> listRequest})
      : _listRequest = listRequest;

  final List<LiveStreamRequest> _listRequest;
  @override
  List<LiveStreamRequest> get listRequest {
    if (_listRequest is EqualUnmodifiableListView) return _listRequest;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listRequest);
  }

  @override
  String toString() {
    return 'ManageLiveStreamEvent.removeListLive(listRequest: $listRequest)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemoveListLiveImpl &&
            const DeepCollectionEquality()
                .equals(other._listRequest, _listRequest));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_listRequest));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RemoveListLiveImplCopyWith<_$RemoveListLiveImpl> get copyWith =>
      __$$RemoveListLiveImplCopyWithImpl<_$RemoveListLiveImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<LiveStreamRequest> listRequest)
        updateLiveList,
    required TResult Function(List<LiveStreamRequest> listRequest)
        removeListLive,
  }) {
    return removeListLive(listRequest);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<LiveStreamRequest> listRequest)? updateLiveList,
    TResult? Function(List<LiveStreamRequest> listRequest)? removeListLive,
  }) {
    return removeListLive?.call(listRequest);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<LiveStreamRequest> listRequest)? updateLiveList,
    TResult Function(List<LiveStreamRequest> listRequest)? removeListLive,
    required TResult orElse(),
  }) {
    if (removeListLive != null) {
      return removeListLive(listRequest);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_UpdateLiveList value) updateLiveList,
    required TResult Function(_RemoveListLive value) removeListLive,
  }) {
    return removeListLive(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_UpdateLiveList value)? updateLiveList,
    TResult? Function(_RemoveListLive value)? removeListLive,
  }) {
    return removeListLive?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_UpdateLiveList value)? updateLiveList,
    TResult Function(_RemoveListLive value)? removeListLive,
    required TResult orElse(),
  }) {
    if (removeListLive != null) {
      return removeListLive(this);
    }
    return orElse();
  }
}

abstract class _RemoveListLive implements ManageLiveStreamEvent {
  const factory _RemoveListLive(
          {required final List<LiveStreamRequest> listRequest}) =
      _$RemoveListLiveImpl;

  @override
  List<LiveStreamRequest> get listRequest;
  @override
  @JsonKey(ignore: true)
  _$$RemoveListLiveImplCopyWith<_$RemoveListLiveImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ManageLiveStreamState {
  List<LiveStreamRequest> get listLiveRequest =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ManageLiveStreamStateCopyWith<ManageLiveStreamState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageLiveStreamStateCopyWith<$Res> {
  factory $ManageLiveStreamStateCopyWith(ManageLiveStreamState value,
          $Res Function(ManageLiveStreamState) then) =
      _$ManageLiveStreamStateCopyWithImpl<$Res, ManageLiveStreamState>;
  @useResult
  $Res call({List<LiveStreamRequest> listLiveRequest});
}

/// @nodoc
class _$ManageLiveStreamStateCopyWithImpl<$Res,
        $Val extends ManageLiveStreamState>
    implements $ManageLiveStreamStateCopyWith<$Res> {
  _$ManageLiveStreamStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listLiveRequest = null,
  }) {
    return _then(_value.copyWith(
      listLiveRequest: null == listLiveRequest
          ? _value.listLiveRequest
          : listLiveRequest // ignore: cast_nullable_to_non_nullable
              as List<LiveStreamRequest>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ManageLiveStreamStateImplCopyWith<$Res>
    implements $ManageLiveStreamStateCopyWith<$Res> {
  factory _$$ManageLiveStreamStateImplCopyWith(
          _$ManageLiveStreamStateImpl value,
          $Res Function(_$ManageLiveStreamStateImpl) then) =
      __$$ManageLiveStreamStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<LiveStreamRequest> listLiveRequest});
}

/// @nodoc
class __$$ManageLiveStreamStateImplCopyWithImpl<$Res>
    extends _$ManageLiveStreamStateCopyWithImpl<$Res,
        _$ManageLiveStreamStateImpl>
    implements _$$ManageLiveStreamStateImplCopyWith<$Res> {
  __$$ManageLiveStreamStateImplCopyWithImpl(_$ManageLiveStreamStateImpl _value,
      $Res Function(_$ManageLiveStreamStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listLiveRequest = null,
  }) {
    return _then(_$ManageLiveStreamStateImpl(
      listLiveRequest: null == listLiveRequest
          ? _value._listLiveRequest
          : listLiveRequest // ignore: cast_nullable_to_non_nullable
              as List<LiveStreamRequest>,
    ));
  }
}

/// @nodoc

class _$ManageLiveStreamStateImpl implements _ManageLiveStreamState {
  const _$ManageLiveStreamStateImpl(
      {required final List<LiveStreamRequest> listLiveRequest})
      : _listLiveRequest = listLiveRequest;

  final List<LiveStreamRequest> _listLiveRequest;
  @override
  List<LiveStreamRequest> get listLiveRequest {
    if (_listLiveRequest is EqualUnmodifiableListView) return _listLiveRequest;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listLiveRequest);
  }

  @override
  String toString() {
    return 'ManageLiveStreamState(listLiveRequest: $listLiveRequest)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ManageLiveStreamStateImpl &&
            const DeepCollectionEquality()
                .equals(other._listLiveRequest, _listLiveRequest));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_listLiveRequest));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ManageLiveStreamStateImplCopyWith<_$ManageLiveStreamStateImpl>
      get copyWith => __$$ManageLiveStreamStateImplCopyWithImpl<
          _$ManageLiveStreamStateImpl>(this, _$identity);
}

abstract class _ManageLiveStreamState implements ManageLiveStreamState {
  const factory _ManageLiveStreamState(
          {required final List<LiveStreamRequest> listLiveRequest}) =
      _$ManageLiveStreamStateImpl;

  @override
  List<LiveStreamRequest> get listLiveRequest;
  @override
  @JsonKey(ignore: true)
  _$$ManageLiveStreamStateImplCopyWith<_$ManageLiveStreamStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
