// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'window_live_player_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WindowLivePlayerEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hiddenWindow,
    required TResult Function(LiveStreamRequest request, String plate)
        getLiveStreamUrl,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loadedLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate, bool reachMaxRetry)
        retry,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failureEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hiddenWindow,
    TResult? Function(LiveStreamRequest request, String plate)?
        getLiveStreamUrl,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hiddenWindow,
    TResult Function(LiveStreamRequest request, String plate)? getLiveStreamUrl,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HiddenWindow value) hiddenWindow,
    required TResult Function(_GettingLiveStream value) getLiveStreamUrl,
    required TResult Function(_LoadedLive value) loadedLive,
    required TResult Function(_Retry value) retry,
    required TResult Function(_FailureEvent value) failureEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HiddenWindow value)? hiddenWindow,
    TResult? Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult? Function(_LoadedLive value)? loadedLive,
    TResult? Function(_Retry value)? retry,
    TResult? Function(_FailureEvent value)? failureEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HiddenWindow value)? hiddenWindow,
    TResult Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult Function(_LoadedLive value)? loadedLive,
    TResult Function(_Retry value)? retry,
    TResult Function(_FailureEvent value)? failureEvent,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WindowLivePlayerEventCopyWith<$Res> {
  factory $WindowLivePlayerEventCopyWith(WindowLivePlayerEvent value,
          $Res Function(WindowLivePlayerEvent) then) =
      _$WindowLivePlayerEventCopyWithImpl<$Res, WindowLivePlayerEvent>;
}

/// @nodoc
class _$WindowLivePlayerEventCopyWithImpl<$Res,
        $Val extends WindowLivePlayerEvent>
    implements $WindowLivePlayerEventCopyWith<$Res> {
  _$WindowLivePlayerEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$HiddenWindowImplCopyWith<$Res> {
  factory _$$HiddenWindowImplCopyWith(
          _$HiddenWindowImpl value, $Res Function(_$HiddenWindowImpl) then) =
      __$$HiddenWindowImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$HiddenWindowImplCopyWithImpl<$Res>
    extends _$WindowLivePlayerEventCopyWithImpl<$Res, _$HiddenWindowImpl>
    implements _$$HiddenWindowImplCopyWith<$Res> {
  __$$HiddenWindowImplCopyWithImpl(
      _$HiddenWindowImpl _value, $Res Function(_$HiddenWindowImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$HiddenWindowImpl implements _HiddenWindow {
  const _$HiddenWindowImpl();

  @override
  String toString() {
    return 'WindowLivePlayerEvent.hiddenWindow()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$HiddenWindowImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hiddenWindow,
    required TResult Function(LiveStreamRequest request, String plate)
        getLiveStreamUrl,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loadedLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate, bool reachMaxRetry)
        retry,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failureEvent,
  }) {
    return hiddenWindow();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hiddenWindow,
    TResult? Function(LiveStreamRequest request, String plate)?
        getLiveStreamUrl,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
  }) {
    return hiddenWindow?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hiddenWindow,
    TResult Function(LiveStreamRequest request, String plate)? getLiveStreamUrl,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
    required TResult orElse(),
  }) {
    if (hiddenWindow != null) {
      return hiddenWindow();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HiddenWindow value) hiddenWindow,
    required TResult Function(_GettingLiveStream value) getLiveStreamUrl,
    required TResult Function(_LoadedLive value) loadedLive,
    required TResult Function(_Retry value) retry,
    required TResult Function(_FailureEvent value) failureEvent,
  }) {
    return hiddenWindow(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HiddenWindow value)? hiddenWindow,
    TResult? Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult? Function(_LoadedLive value)? loadedLive,
    TResult? Function(_Retry value)? retry,
    TResult? Function(_FailureEvent value)? failureEvent,
  }) {
    return hiddenWindow?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HiddenWindow value)? hiddenWindow,
    TResult Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult Function(_LoadedLive value)? loadedLive,
    TResult Function(_Retry value)? retry,
    TResult Function(_FailureEvent value)? failureEvent,
    required TResult orElse(),
  }) {
    if (hiddenWindow != null) {
      return hiddenWindow(this);
    }
    return orElse();
  }
}

abstract class _HiddenWindow implements WindowLivePlayerEvent {
  const factory _HiddenWindow() = _$HiddenWindowImpl;
}

/// @nodoc
abstract class _$$GettingLiveStreamImplCopyWith<$Res> {
  factory _$$GettingLiveStreamImplCopyWith(_$GettingLiveStreamImpl value,
          $Res Function(_$GettingLiveStreamImpl) then) =
      __$$GettingLiveStreamImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LiveStreamRequest request, String plate});

  $LiveStreamRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$GettingLiveStreamImplCopyWithImpl<$Res>
    extends _$WindowLivePlayerEventCopyWithImpl<$Res, _$GettingLiveStreamImpl>
    implements _$$GettingLiveStreamImplCopyWith<$Res> {
  __$$GettingLiveStreamImplCopyWithImpl(_$GettingLiveStreamImpl _value,
      $Res Function(_$GettingLiveStreamImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
    Object? plate = null,
  }) {
    return _then(_$GettingLiveStreamImpl(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as LiveStreamRequest,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LiveStreamRequestCopyWith<$Res> get request {
    return $LiveStreamRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$GettingLiveStreamImpl implements _GettingLiveStream {
  const _$GettingLiveStreamImpl({required this.request, required this.plate});

  @override
  final LiveStreamRequest request;
  @override
  final String plate;

  @override
  String toString() {
    return 'WindowLivePlayerEvent.getLiveStreamUrl(request: $request, plate: $plate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GettingLiveStreamImpl &&
            (identical(other.request, request) || other.request == request) &&
            (identical(other.plate, plate) || other.plate == plate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request, plate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GettingLiveStreamImplCopyWith<_$GettingLiveStreamImpl> get copyWith =>
      __$$GettingLiveStreamImplCopyWithImpl<_$GettingLiveStreamImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hiddenWindow,
    required TResult Function(LiveStreamRequest request, String plate)
        getLiveStreamUrl,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loadedLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate, bool reachMaxRetry)
        retry,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failureEvent,
  }) {
    return getLiveStreamUrl(request, plate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hiddenWindow,
    TResult? Function(LiveStreamRequest request, String plate)?
        getLiveStreamUrl,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
  }) {
    return getLiveStreamUrl?.call(request, plate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hiddenWindow,
    TResult Function(LiveStreamRequest request, String plate)? getLiveStreamUrl,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
    required TResult orElse(),
  }) {
    if (getLiveStreamUrl != null) {
      return getLiveStreamUrl(request, plate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HiddenWindow value) hiddenWindow,
    required TResult Function(_GettingLiveStream value) getLiveStreamUrl,
    required TResult Function(_LoadedLive value) loadedLive,
    required TResult Function(_Retry value) retry,
    required TResult Function(_FailureEvent value) failureEvent,
  }) {
    return getLiveStreamUrl(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HiddenWindow value)? hiddenWindow,
    TResult? Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult? Function(_LoadedLive value)? loadedLive,
    TResult? Function(_Retry value)? retry,
    TResult? Function(_FailureEvent value)? failureEvent,
  }) {
    return getLiveStreamUrl?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HiddenWindow value)? hiddenWindow,
    TResult Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult Function(_LoadedLive value)? loadedLive,
    TResult Function(_Retry value)? retry,
    TResult Function(_FailureEvent value)? failureEvent,
    required TResult orElse(),
  }) {
    if (getLiveStreamUrl != null) {
      return getLiveStreamUrl(this);
    }
    return orElse();
  }
}

abstract class _GettingLiveStream implements WindowLivePlayerEvent {
  const factory _GettingLiveStream(
      {required final LiveStreamRequest request,
      required final String plate}) = _$GettingLiveStreamImpl;

  LiveStreamRequest get request;
  String get plate;
  @JsonKey(ignore: true)
  _$$GettingLiveStreamImplCopyWith<_$GettingLiveStreamImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadedLiveImplCopyWith<$Res> {
  factory _$$LoadedLiveImplCopyWith(
          _$LoadedLiveImpl value, $Res Function(_$LoadedLiveImpl) then) =
      __$$LoadedLiveImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<LiveStreamData> liveData, LiveStreamRequest request, String plate});

  $LiveStreamRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$LoadedLiveImplCopyWithImpl<$Res>
    extends _$WindowLivePlayerEventCopyWithImpl<$Res, _$LoadedLiveImpl>
    implements _$$LoadedLiveImplCopyWith<$Res> {
  __$$LoadedLiveImplCopyWithImpl(
      _$LoadedLiveImpl _value, $Res Function(_$LoadedLiveImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? liveData = null,
    Object? request = null,
    Object? plate = null,
  }) {
    return _then(_$LoadedLiveImpl(
      liveData: null == liveData
          ? _value._liveData
          : liveData // ignore: cast_nullable_to_non_nullable
              as List<LiveStreamData>,
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as LiveStreamRequest,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LiveStreamRequestCopyWith<$Res> get request {
    return $LiveStreamRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$LoadedLiveImpl implements _LoadedLive {
  const _$LoadedLiveImpl(
      {required final List<LiveStreamData> liveData,
      required this.request,
      required this.plate})
      : _liveData = liveData;

  final List<LiveStreamData> _liveData;
  @override
  List<LiveStreamData> get liveData {
    if (_liveData is EqualUnmodifiableListView) return _liveData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_liveData);
  }

  @override
  final LiveStreamRequest request;
  @override
  final String plate;

  @override
  String toString() {
    return 'WindowLivePlayerEvent.loadedLive(liveData: $liveData, request: $request, plate: $plate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedLiveImpl &&
            const DeepCollectionEquality().equals(other._liveData, _liveData) &&
            (identical(other.request, request) || other.request == request) &&
            (identical(other.plate, plate) || other.plate == plate));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_liveData), request, plate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedLiveImplCopyWith<_$LoadedLiveImpl> get copyWith =>
      __$$LoadedLiveImplCopyWithImpl<_$LoadedLiveImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hiddenWindow,
    required TResult Function(LiveStreamRequest request, String plate)
        getLiveStreamUrl,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loadedLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate, bool reachMaxRetry)
        retry,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failureEvent,
  }) {
    return loadedLive(liveData, request, plate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hiddenWindow,
    TResult? Function(LiveStreamRequest request, String plate)?
        getLiveStreamUrl,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
  }) {
    return loadedLive?.call(liveData, request, plate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hiddenWindow,
    TResult Function(LiveStreamRequest request, String plate)? getLiveStreamUrl,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
    required TResult orElse(),
  }) {
    if (loadedLive != null) {
      return loadedLive(liveData, request, plate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HiddenWindow value) hiddenWindow,
    required TResult Function(_GettingLiveStream value) getLiveStreamUrl,
    required TResult Function(_LoadedLive value) loadedLive,
    required TResult Function(_Retry value) retry,
    required TResult Function(_FailureEvent value) failureEvent,
  }) {
    return loadedLive(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HiddenWindow value)? hiddenWindow,
    TResult? Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult? Function(_LoadedLive value)? loadedLive,
    TResult? Function(_Retry value)? retry,
    TResult? Function(_FailureEvent value)? failureEvent,
  }) {
    return loadedLive?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HiddenWindow value)? hiddenWindow,
    TResult Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult Function(_LoadedLive value)? loadedLive,
    TResult Function(_Retry value)? retry,
    TResult Function(_FailureEvent value)? failureEvent,
    required TResult orElse(),
  }) {
    if (loadedLive != null) {
      return loadedLive(this);
    }
    return orElse();
  }
}

abstract class _LoadedLive implements WindowLivePlayerEvent {
  const factory _LoadedLive(
      {required final List<LiveStreamData> liveData,
      required final LiveStreamRequest request,
      required final String plate}) = _$LoadedLiveImpl;

  List<LiveStreamData> get liveData;
  LiveStreamRequest get request;
  String get plate;
  @JsonKey(ignore: true)
  _$$LoadedLiveImplCopyWith<_$LoadedLiveImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RetryImplCopyWith<$Res> {
  factory _$$RetryImplCopyWith(
          _$RetryImpl value, $Res Function(_$RetryImpl) then) =
      __$$RetryImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<LiveStreamData> liveData,
      LiveStreamRequest request,
      String plate,
      bool reachMaxRetry});

  $LiveStreamRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$RetryImplCopyWithImpl<$Res>
    extends _$WindowLivePlayerEventCopyWithImpl<$Res, _$RetryImpl>
    implements _$$RetryImplCopyWith<$Res> {
  __$$RetryImplCopyWithImpl(
      _$RetryImpl _value, $Res Function(_$RetryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? liveData = null,
    Object? request = null,
    Object? plate = null,
    Object? reachMaxRetry = null,
  }) {
    return _then(_$RetryImpl(
      liveData: null == liveData
          ? _value._liveData
          : liveData // ignore: cast_nullable_to_non_nullable
              as List<LiveStreamData>,
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as LiveStreamRequest,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      reachMaxRetry: null == reachMaxRetry
          ? _value.reachMaxRetry
          : reachMaxRetry // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LiveStreamRequestCopyWith<$Res> get request {
    return $LiveStreamRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$RetryImpl implements _Retry {
  const _$RetryImpl(
      {required final List<LiveStreamData> liveData,
      required this.request,
      required this.plate,
      required this.reachMaxRetry})
      : _liveData = liveData;

  final List<LiveStreamData> _liveData;
  @override
  List<LiveStreamData> get liveData {
    if (_liveData is EqualUnmodifiableListView) return _liveData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_liveData);
  }

  @override
  final LiveStreamRequest request;
  @override
  final String plate;
  @override
  final bool reachMaxRetry;

  @override
  String toString() {
    return 'WindowLivePlayerEvent.retry(liveData: $liveData, request: $request, plate: $plate, reachMaxRetry: $reachMaxRetry)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RetryImpl &&
            const DeepCollectionEquality().equals(other._liveData, _liveData) &&
            (identical(other.request, request) || other.request == request) &&
            (identical(other.plate, plate) || other.plate == plate) &&
            (identical(other.reachMaxRetry, reachMaxRetry) ||
                other.reachMaxRetry == reachMaxRetry));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_liveData),
      request,
      plate,
      reachMaxRetry);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RetryImplCopyWith<_$RetryImpl> get copyWith =>
      __$$RetryImplCopyWithImpl<_$RetryImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hiddenWindow,
    required TResult Function(LiveStreamRequest request, String plate)
        getLiveStreamUrl,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loadedLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate, bool reachMaxRetry)
        retry,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failureEvent,
  }) {
    return retry(liveData, request, plate, reachMaxRetry);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hiddenWindow,
    TResult? Function(LiveStreamRequest request, String plate)?
        getLiveStreamUrl,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
  }) {
    return retry?.call(liveData, request, plate, reachMaxRetry);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hiddenWindow,
    TResult Function(LiveStreamRequest request, String plate)? getLiveStreamUrl,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
    required TResult orElse(),
  }) {
    if (retry != null) {
      return retry(liveData, request, plate, reachMaxRetry);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HiddenWindow value) hiddenWindow,
    required TResult Function(_GettingLiveStream value) getLiveStreamUrl,
    required TResult Function(_LoadedLive value) loadedLive,
    required TResult Function(_Retry value) retry,
    required TResult Function(_FailureEvent value) failureEvent,
  }) {
    return retry(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HiddenWindow value)? hiddenWindow,
    TResult? Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult? Function(_LoadedLive value)? loadedLive,
    TResult? Function(_Retry value)? retry,
    TResult? Function(_FailureEvent value)? failureEvent,
  }) {
    return retry?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HiddenWindow value)? hiddenWindow,
    TResult Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult Function(_LoadedLive value)? loadedLive,
    TResult Function(_Retry value)? retry,
    TResult Function(_FailureEvent value)? failureEvent,
    required TResult orElse(),
  }) {
    if (retry != null) {
      return retry(this);
    }
    return orElse();
  }
}

abstract class _Retry implements WindowLivePlayerEvent {
  const factory _Retry(
      {required final List<LiveStreamData> liveData,
      required final LiveStreamRequest request,
      required final String plate,
      required final bool reachMaxRetry}) = _$RetryImpl;

  List<LiveStreamData> get liveData;
  LiveStreamRequest get request;
  String get plate;
  bool get reachMaxRetry;
  @JsonKey(ignore: true)
  _$$RetryImplCopyWith<_$RetryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FailureEventImplCopyWith<$Res> {
  factory _$$FailureEventImplCopyWith(
          _$FailureEventImpl value, $Res Function(_$FailureEventImpl) then) =
      __$$FailureEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {LiveStreamFailure failure, LiveStreamRequest request, String plate});

  $LiveStreamFailureCopyWith<$Res> get failure;
  $LiveStreamRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$FailureEventImplCopyWithImpl<$Res>
    extends _$WindowLivePlayerEventCopyWithImpl<$Res, _$FailureEventImpl>
    implements _$$FailureEventImplCopyWith<$Res> {
  __$$FailureEventImplCopyWithImpl(
      _$FailureEventImpl _value, $Res Function(_$FailureEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
    Object? request = null,
    Object? plate = null,
  }) {
    return _then(_$FailureEventImpl(
      failure: null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as LiveStreamFailure,
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as LiveStreamRequest,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LiveStreamFailureCopyWith<$Res> get failure {
    return $LiveStreamFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LiveStreamRequestCopyWith<$Res> get request {
    return $LiveStreamRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$FailureEventImpl implements _FailureEvent {
  const _$FailureEventImpl(
      {required this.failure, required this.request, required this.plate});

  @override
  final LiveStreamFailure failure;
  @override
  final LiveStreamRequest request;
  @override
  final String plate;

  @override
  String toString() {
    return 'WindowLivePlayerEvent.failureEvent(failure: $failure, request: $request, plate: $plate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureEventImpl &&
            (identical(other.failure, failure) || other.failure == failure) &&
            (identical(other.request, request) || other.request == request) &&
            (identical(other.plate, plate) || other.plate == plate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure, request, plate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureEventImplCopyWith<_$FailureEventImpl> get copyWith =>
      __$$FailureEventImplCopyWithImpl<_$FailureEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hiddenWindow,
    required TResult Function(LiveStreamRequest request, String plate)
        getLiveStreamUrl,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loadedLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate, bool reachMaxRetry)
        retry,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failureEvent,
  }) {
    return failureEvent(failure, request, plate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hiddenWindow,
    TResult? Function(LiveStreamRequest request, String plate)?
        getLiveStreamUrl,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
  }) {
    return failureEvent?.call(failure, request, plate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hiddenWindow,
    TResult Function(LiveStreamRequest request, String plate)? getLiveStreamUrl,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loadedLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate, bool reachMaxRetry)?
        retry,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failureEvent,
    required TResult orElse(),
  }) {
    if (failureEvent != null) {
      return failureEvent(failure, request, plate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HiddenWindow value) hiddenWindow,
    required TResult Function(_GettingLiveStream value) getLiveStreamUrl,
    required TResult Function(_LoadedLive value) loadedLive,
    required TResult Function(_Retry value) retry,
    required TResult Function(_FailureEvent value) failureEvent,
  }) {
    return failureEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HiddenWindow value)? hiddenWindow,
    TResult? Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult? Function(_LoadedLive value)? loadedLive,
    TResult? Function(_Retry value)? retry,
    TResult? Function(_FailureEvent value)? failureEvent,
  }) {
    return failureEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HiddenWindow value)? hiddenWindow,
    TResult Function(_GettingLiveStream value)? getLiveStreamUrl,
    TResult Function(_LoadedLive value)? loadedLive,
    TResult Function(_Retry value)? retry,
    TResult Function(_FailureEvent value)? failureEvent,
    required TResult orElse(),
  }) {
    if (failureEvent != null) {
      return failureEvent(this);
    }
    return orElse();
  }
}

abstract class _FailureEvent implements WindowLivePlayerEvent {
  const factory _FailureEvent(
      {required final LiveStreamFailure failure,
      required final LiveStreamRequest request,
      required final String plate}) = _$FailureEventImpl;

  LiveStreamFailure get failure;
  LiveStreamRequest get request;
  String get plate;
  @JsonKey(ignore: true)
  _$$FailureEventImplCopyWith<_$FailureEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$WindowLivePlayerState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hidden,
    required TResult Function(String channel, String plate) gettingLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loaded,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        retrying,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hidden,
    TResult? Function(String channel, String plate)? gettingLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hidden,
    TResult Function(String channel, String plate)? gettingLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Hidden value) hidden,
    required TResult Function(_Loading value) gettingLive,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Retrying value) retrying,
    required TResult Function(_Failure value) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Hidden value)? hidden,
    TResult? Function(_Loading value)? gettingLive,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Retrying value)? retrying,
    TResult? Function(_Failure value)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Hidden value)? hidden,
    TResult Function(_Loading value)? gettingLive,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Retrying value)? retrying,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WindowLivePlayerStateCopyWith<$Res> {
  factory $WindowLivePlayerStateCopyWith(WindowLivePlayerState value,
          $Res Function(WindowLivePlayerState) then) =
      _$WindowLivePlayerStateCopyWithImpl<$Res, WindowLivePlayerState>;
}

/// @nodoc
class _$WindowLivePlayerStateCopyWithImpl<$Res,
        $Val extends WindowLivePlayerState>
    implements $WindowLivePlayerStateCopyWith<$Res> {
  _$WindowLivePlayerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$HiddenImplCopyWith<$Res> {
  factory _$$HiddenImplCopyWith(
          _$HiddenImpl value, $Res Function(_$HiddenImpl) then) =
      __$$HiddenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$HiddenImplCopyWithImpl<$Res>
    extends _$WindowLivePlayerStateCopyWithImpl<$Res, _$HiddenImpl>
    implements _$$HiddenImplCopyWith<$Res> {
  __$$HiddenImplCopyWithImpl(
      _$HiddenImpl _value, $Res Function(_$HiddenImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$HiddenImpl implements _Hidden {
  const _$HiddenImpl();

  @override
  String toString() {
    return 'WindowLivePlayerState.hidden()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$HiddenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hidden,
    required TResult Function(String channel, String plate) gettingLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loaded,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        retrying,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failure,
  }) {
    return hidden();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hidden,
    TResult? Function(String channel, String plate)? gettingLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
  }) {
    return hidden?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hidden,
    TResult Function(String channel, String plate)? gettingLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
    required TResult orElse(),
  }) {
    if (hidden != null) {
      return hidden();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Hidden value) hidden,
    required TResult Function(_Loading value) gettingLive,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Retrying value) retrying,
    required TResult Function(_Failure value) failure,
  }) {
    return hidden(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Hidden value)? hidden,
    TResult? Function(_Loading value)? gettingLive,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Retrying value)? retrying,
    TResult? Function(_Failure value)? failure,
  }) {
    return hidden?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Hidden value)? hidden,
    TResult Function(_Loading value)? gettingLive,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Retrying value)? retrying,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (hidden != null) {
      return hidden(this);
    }
    return orElse();
  }
}

abstract class _Hidden implements WindowLivePlayerState {
  const factory _Hidden() = _$HiddenImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String channel, String plate});
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$WindowLivePlayerStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channel = null,
    Object? plate = null,
  }) {
    return _then(_$LoadingImpl(
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl({required this.channel, required this.plate});

  @override
  final String channel;
  @override
  final String plate;

  @override
  String toString() {
    return 'WindowLivePlayerState.gettingLive(channel: $channel, plate: $plate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadingImpl &&
            (identical(other.channel, channel) || other.channel == channel) &&
            (identical(other.plate, plate) || other.plate == plate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, channel, plate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadingImplCopyWith<_$LoadingImpl> get copyWith =>
      __$$LoadingImplCopyWithImpl<_$LoadingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hidden,
    required TResult Function(String channel, String plate) gettingLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loaded,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        retrying,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failure,
  }) {
    return gettingLive(channel, plate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hidden,
    TResult? Function(String channel, String plate)? gettingLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
  }) {
    return gettingLive?.call(channel, plate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hidden,
    TResult Function(String channel, String plate)? gettingLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
    required TResult orElse(),
  }) {
    if (gettingLive != null) {
      return gettingLive(channel, plate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Hidden value) hidden,
    required TResult Function(_Loading value) gettingLive,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Retrying value) retrying,
    required TResult Function(_Failure value) failure,
  }) {
    return gettingLive(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Hidden value)? hidden,
    TResult? Function(_Loading value)? gettingLive,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Retrying value)? retrying,
    TResult? Function(_Failure value)? failure,
  }) {
    return gettingLive?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Hidden value)? hidden,
    TResult Function(_Loading value)? gettingLive,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Retrying value)? retrying,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (gettingLive != null) {
      return gettingLive(this);
    }
    return orElse();
  }
}

abstract class _Loading implements WindowLivePlayerState {
  const factory _Loading(
      {required final String channel,
      required final String plate}) = _$LoadingImpl;

  String get channel;
  String get plate;
  @JsonKey(ignore: true)
  _$$LoadingImplCopyWith<_$LoadingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<LiveStreamData> liveData, LiveStreamRequest request, String plate});

  $LiveStreamRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$WindowLivePlayerStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? liveData = null,
    Object? request = null,
    Object? plate = null,
  }) {
    return _then(_$LoadedImpl(
      liveData: null == liveData
          ? _value._liveData
          : liveData // ignore: cast_nullable_to_non_nullable
              as List<LiveStreamData>,
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as LiveStreamRequest,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LiveStreamRequestCopyWith<$Res> get request {
    return $LiveStreamRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl(
      {required final List<LiveStreamData> liveData,
      required this.request,
      required this.plate})
      : _liveData = liveData;

  final List<LiveStreamData> _liveData;
  @override
  List<LiveStreamData> get liveData {
    if (_liveData is EqualUnmodifiableListView) return _liveData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_liveData);
  }

  @override
  final LiveStreamRequest request;
  @override
  final String plate;

  @override
  String toString() {
    return 'WindowLivePlayerState.loaded(liveData: $liveData, request: $request, plate: $plate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            const DeepCollectionEquality().equals(other._liveData, _liveData) &&
            (identical(other.request, request) || other.request == request) &&
            (identical(other.plate, plate) || other.plate == plate));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_liveData), request, plate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hidden,
    required TResult Function(String channel, String plate) gettingLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loaded,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        retrying,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failure,
  }) {
    return loaded(liveData, request, plate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hidden,
    TResult? Function(String channel, String plate)? gettingLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
  }) {
    return loaded?.call(liveData, request, plate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hidden,
    TResult Function(String channel, String plate)? gettingLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(liveData, request, plate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Hidden value) hidden,
    required TResult Function(_Loading value) gettingLive,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Retrying value) retrying,
    required TResult Function(_Failure value) failure,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Hidden value)? hidden,
    TResult? Function(_Loading value)? gettingLive,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Retrying value)? retrying,
    TResult? Function(_Failure value)? failure,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Hidden value)? hidden,
    TResult Function(_Loading value)? gettingLive,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Retrying value)? retrying,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements WindowLivePlayerState {
  const factory _Loaded(
      {required final List<LiveStreamData> liveData,
      required final LiveStreamRequest request,
      required final String plate}) = _$LoadedImpl;

  List<LiveStreamData> get liveData;
  LiveStreamRequest get request;
  String get plate;
  @JsonKey(ignore: true)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RetryingImplCopyWith<$Res> {
  factory _$$RetryingImplCopyWith(
          _$RetryingImpl value, $Res Function(_$RetryingImpl) then) =
      __$$RetryingImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<LiveStreamData> liveData, LiveStreamRequest request, String plate});

  $LiveStreamRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$RetryingImplCopyWithImpl<$Res>
    extends _$WindowLivePlayerStateCopyWithImpl<$Res, _$RetryingImpl>
    implements _$$RetryingImplCopyWith<$Res> {
  __$$RetryingImplCopyWithImpl(
      _$RetryingImpl _value, $Res Function(_$RetryingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? liveData = null,
    Object? request = null,
    Object? plate = null,
  }) {
    return _then(_$RetryingImpl(
      liveData: null == liveData
          ? _value._liveData
          : liveData // ignore: cast_nullable_to_non_nullable
              as List<LiveStreamData>,
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as LiveStreamRequest,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LiveStreamRequestCopyWith<$Res> get request {
    return $LiveStreamRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$RetryingImpl implements _Retrying {
  const _$RetryingImpl(
      {required final List<LiveStreamData> liveData,
      required this.request,
      required this.plate})
      : _liveData = liveData;

  final List<LiveStreamData> _liveData;
  @override
  List<LiveStreamData> get liveData {
    if (_liveData is EqualUnmodifiableListView) return _liveData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_liveData);
  }

  @override
  final LiveStreamRequest request;
  @override
  final String plate;

  @override
  String toString() {
    return 'WindowLivePlayerState.retrying(liveData: $liveData, request: $request, plate: $plate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RetryingImpl &&
            const DeepCollectionEquality().equals(other._liveData, _liveData) &&
            (identical(other.request, request) || other.request == request) &&
            (identical(other.plate, plate) || other.plate == plate));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_liveData), request, plate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RetryingImplCopyWith<_$RetryingImpl> get copyWith =>
      __$$RetryingImplCopyWithImpl<_$RetryingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hidden,
    required TResult Function(String channel, String plate) gettingLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loaded,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        retrying,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failure,
  }) {
    return retrying(liveData, request, plate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hidden,
    TResult? Function(String channel, String plate)? gettingLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
  }) {
    return retrying?.call(liveData, request, plate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hidden,
    TResult Function(String channel, String plate)? gettingLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
    required TResult orElse(),
  }) {
    if (retrying != null) {
      return retrying(liveData, request, plate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Hidden value) hidden,
    required TResult Function(_Loading value) gettingLive,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Retrying value) retrying,
    required TResult Function(_Failure value) failure,
  }) {
    return retrying(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Hidden value)? hidden,
    TResult? Function(_Loading value)? gettingLive,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Retrying value)? retrying,
    TResult? Function(_Failure value)? failure,
  }) {
    return retrying?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Hidden value)? hidden,
    TResult Function(_Loading value)? gettingLive,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Retrying value)? retrying,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (retrying != null) {
      return retrying(this);
    }
    return orElse();
  }
}

abstract class _Retrying implements WindowLivePlayerState {
  const factory _Retrying(
      {required final List<LiveStreamData> liveData,
      required final LiveStreamRequest request,
      required final String plate}) = _$RetryingImpl;

  List<LiveStreamData> get liveData;
  LiveStreamRequest get request;
  String get plate;
  @JsonKey(ignore: true)
  _$$RetryingImplCopyWith<_$RetryingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FailureImplCopyWith<$Res> {
  factory _$$FailureImplCopyWith(
          _$FailureImpl value, $Res Function(_$FailureImpl) then) =
      __$$FailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {LiveStreamFailure failure, LiveStreamRequest request, String plate});

  $LiveStreamFailureCopyWith<$Res> get failure;
  $LiveStreamRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$FailureImplCopyWithImpl<$Res>
    extends _$WindowLivePlayerStateCopyWithImpl<$Res, _$FailureImpl>
    implements _$$FailureImplCopyWith<$Res> {
  __$$FailureImplCopyWithImpl(
      _$FailureImpl _value, $Res Function(_$FailureImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
    Object? request = null,
    Object? plate = null,
  }) {
    return _then(_$FailureImpl(
      failure: null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as LiveStreamFailure,
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as LiveStreamRequest,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LiveStreamFailureCopyWith<$Res> get failure {
    return $LiveStreamFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LiveStreamRequestCopyWith<$Res> get request {
    return $LiveStreamRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$FailureImpl implements _Failure {
  const _$FailureImpl(
      {required this.failure, required this.request, required this.plate});

  @override
  final LiveStreamFailure failure;
  @override
  final LiveStreamRequest request;
  @override
  final String plate;

  @override
  String toString() {
    return 'WindowLivePlayerState.failure(failure: $failure, request: $request, plate: $plate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureImpl &&
            (identical(other.failure, failure) || other.failure == failure) &&
            (identical(other.request, request) || other.request == request) &&
            (identical(other.plate, plate) || other.plate == plate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure, request, plate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      __$$FailureImplCopyWithImpl<_$FailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() hidden,
    required TResult Function(String channel, String plate) gettingLive,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        loaded,
    required TResult Function(List<LiveStreamData> liveData,
            LiveStreamRequest request, String plate)
        retrying,
    required TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)
        failure,
  }) {
    return failure(this.failure, request, plate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? hidden,
    TResult? Function(String channel, String plate)? gettingLive,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult? Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult? Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
  }) {
    return failure?.call(this.failure, request, plate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? hidden,
    TResult Function(String channel, String plate)? gettingLive,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        loaded,
    TResult Function(List<LiveStreamData> liveData, LiveStreamRequest request,
            String plate)?
        retrying,
    TResult Function(
            LiveStreamFailure failure, LiveStreamRequest request, String plate)?
        failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this.failure, request, plate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Hidden value) hidden,
    required TResult Function(_Loading value) gettingLive,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Retrying value) retrying,
    required TResult Function(_Failure value) failure,
  }) {
    return failure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Hidden value)? hidden,
    TResult? Function(_Loading value)? gettingLive,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Retrying value)? retrying,
    TResult? Function(_Failure value)? failure,
  }) {
    return failure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Hidden value)? hidden,
    TResult Function(_Loading value)? gettingLive,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Retrying value)? retrying,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this);
    }
    return orElse();
  }
}

abstract class _Failure implements WindowLivePlayerState {
  const factory _Failure(
      {required final LiveStreamFailure failure,
      required final LiveStreamRequest request,
      required final String plate}) = _$FailureImpl;

  LiveStreamFailure get failure;
  LiveStreamRequest get request;
  String get plate;
  @JsonKey(ignore: true)
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
