// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'live_stream_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LiveStreamFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LiveStreamFailureUnexpected value) unexpected,
    required TResult Function(_LiveStreamUnauthorized value) unauthorized,
    required TResult Function(_LiveStreamUnauthenticated value) unauthenticated,
    required TResult Function(_LiveStreamServerError value) serverError,
    required TResult Function(_LiveStreamNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult? Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult? Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult? Function(_LiveStreamServerError value)? serverError,
    TResult? Function(_LiveStreamNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult Function(_LiveStreamServerError value)? serverError,
    TResult Function(_LiveStreamNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LiveStreamFailureCopyWith<$Res> {
  factory $LiveStreamFailureCopyWith(
          LiveStreamFailure value, $Res Function(LiveStreamFailure) then) =
      _$LiveStreamFailureCopyWithImpl<$Res, LiveStreamFailure>;
}

/// @nodoc
class _$LiveStreamFailureCopyWithImpl<$Res, $Val extends LiveStreamFailure>
    implements $LiveStreamFailureCopyWith<$Res> {
  _$LiveStreamFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$LiveStreamFailureUnexpectedImplCopyWith<$Res> {
  factory _$$LiveStreamFailureUnexpectedImplCopyWith(
          _$LiveStreamFailureUnexpectedImpl value,
          $Res Function(_$LiveStreamFailureUnexpectedImpl) then) =
      __$$LiveStreamFailureUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$LiveStreamFailureUnexpectedImplCopyWithImpl<$Res>
    extends _$LiveStreamFailureCopyWithImpl<$Res,
        _$LiveStreamFailureUnexpectedImpl>
    implements _$$LiveStreamFailureUnexpectedImplCopyWith<$Res> {
  __$$LiveStreamFailureUnexpectedImplCopyWithImpl(
      _$LiveStreamFailureUnexpectedImpl _value,
      $Res Function(_$LiveStreamFailureUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$LiveStreamFailureUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$LiveStreamFailureUnexpectedImpl
    implements _LiveStreamFailureUnexpected {
  const _$LiveStreamFailureUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'LiveStreamFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LiveStreamFailureUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LiveStreamFailureUnexpectedImplCopyWith<_$LiveStreamFailureUnexpectedImpl>
      get copyWith => __$$LiveStreamFailureUnexpectedImplCopyWithImpl<
          _$LiveStreamFailureUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LiveStreamFailureUnexpected value) unexpected,
    required TResult Function(_LiveStreamUnauthorized value) unauthorized,
    required TResult Function(_LiveStreamUnauthenticated value) unauthenticated,
    required TResult Function(_LiveStreamServerError value) serverError,
    required TResult Function(_LiveStreamNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult? Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult? Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult? Function(_LiveStreamServerError value)? serverError,
    TResult? Function(_LiveStreamNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult Function(_LiveStreamServerError value)? serverError,
    TResult Function(_LiveStreamNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _LiveStreamFailureUnexpected implements LiveStreamFailure {
  const factory _LiveStreamFailureUnexpected({required final String error}) =
      _$LiveStreamFailureUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$LiveStreamFailureUnexpectedImplCopyWith<_$LiveStreamFailureUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LiveStreamUnauthorizedImplCopyWith<$Res> {
  factory _$$LiveStreamUnauthorizedImplCopyWith(
          _$LiveStreamUnauthorizedImpl value,
          $Res Function(_$LiveStreamUnauthorizedImpl) then) =
      __$$LiveStreamUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LiveStreamUnauthorizedImplCopyWithImpl<$Res>
    extends _$LiveStreamFailureCopyWithImpl<$Res, _$LiveStreamUnauthorizedImpl>
    implements _$$LiveStreamUnauthorizedImplCopyWith<$Res> {
  __$$LiveStreamUnauthorizedImplCopyWithImpl(
      _$LiveStreamUnauthorizedImpl _value,
      $Res Function(_$LiveStreamUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LiveStreamUnauthorizedImpl implements _LiveStreamUnauthorized {
  const _$LiveStreamUnauthorizedImpl();

  @override
  String toString() {
    return 'LiveStreamFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LiveStreamUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LiveStreamFailureUnexpected value) unexpected,
    required TResult Function(_LiveStreamUnauthorized value) unauthorized,
    required TResult Function(_LiveStreamUnauthenticated value) unauthenticated,
    required TResult Function(_LiveStreamServerError value) serverError,
    required TResult Function(_LiveStreamNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult? Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult? Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult? Function(_LiveStreamServerError value)? serverError,
    TResult? Function(_LiveStreamNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult Function(_LiveStreamServerError value)? serverError,
    TResult Function(_LiveStreamNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _LiveStreamUnauthorized implements LiveStreamFailure {
  const factory _LiveStreamUnauthorized() = _$LiveStreamUnauthorizedImpl;
}

/// @nodoc
abstract class _$$LiveStreamUnauthenticatedImplCopyWith<$Res> {
  factory _$$LiveStreamUnauthenticatedImplCopyWith(
          _$LiveStreamUnauthenticatedImpl value,
          $Res Function(_$LiveStreamUnauthenticatedImpl) then) =
      __$$LiveStreamUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LiveStreamUnauthenticatedImplCopyWithImpl<$Res>
    extends _$LiveStreamFailureCopyWithImpl<$Res,
        _$LiveStreamUnauthenticatedImpl>
    implements _$$LiveStreamUnauthenticatedImplCopyWith<$Res> {
  __$$LiveStreamUnauthenticatedImplCopyWithImpl(
      _$LiveStreamUnauthenticatedImpl _value,
      $Res Function(_$LiveStreamUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LiveStreamUnauthenticatedImpl implements _LiveStreamUnauthenticated {
  const _$LiveStreamUnauthenticatedImpl();

  @override
  String toString() {
    return 'LiveStreamFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LiveStreamUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LiveStreamFailureUnexpected value) unexpected,
    required TResult Function(_LiveStreamUnauthorized value) unauthorized,
    required TResult Function(_LiveStreamUnauthenticated value) unauthenticated,
    required TResult Function(_LiveStreamServerError value) serverError,
    required TResult Function(_LiveStreamNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult? Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult? Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult? Function(_LiveStreamServerError value)? serverError,
    TResult? Function(_LiveStreamNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult Function(_LiveStreamServerError value)? serverError,
    TResult Function(_LiveStreamNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _LiveStreamUnauthenticated implements LiveStreamFailure {
  const factory _LiveStreamUnauthenticated() = _$LiveStreamUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$LiveStreamServerErrorImplCopyWith<$Res> {
  factory _$$LiveStreamServerErrorImplCopyWith(
          _$LiveStreamServerErrorImpl value,
          $Res Function(_$LiveStreamServerErrorImpl) then) =
      __$$LiveStreamServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LiveStreamServerErrorImplCopyWithImpl<$Res>
    extends _$LiveStreamFailureCopyWithImpl<$Res, _$LiveStreamServerErrorImpl>
    implements _$$LiveStreamServerErrorImplCopyWith<$Res> {
  __$$LiveStreamServerErrorImplCopyWithImpl(_$LiveStreamServerErrorImpl _value,
      $Res Function(_$LiveStreamServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LiveStreamServerErrorImpl implements _LiveStreamServerError {
  const _$LiveStreamServerErrorImpl();

  @override
  String toString() {
    return 'LiveStreamFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LiveStreamServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LiveStreamFailureUnexpected value) unexpected,
    required TResult Function(_LiveStreamUnauthorized value) unauthorized,
    required TResult Function(_LiveStreamUnauthenticated value) unauthenticated,
    required TResult Function(_LiveStreamServerError value) serverError,
    required TResult Function(_LiveStreamNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult? Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult? Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult? Function(_LiveStreamServerError value)? serverError,
    TResult? Function(_LiveStreamNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult Function(_LiveStreamServerError value)? serverError,
    TResult Function(_LiveStreamNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _LiveStreamServerError implements LiveStreamFailure {
  const factory _LiveStreamServerError() = _$LiveStreamServerErrorImpl;
}

/// @nodoc
abstract class _$$LiveStreamNoInternetImplCopyWith<$Res> {
  factory _$$LiveStreamNoInternetImplCopyWith(_$LiveStreamNoInternetImpl value,
          $Res Function(_$LiveStreamNoInternetImpl) then) =
      __$$LiveStreamNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LiveStreamNoInternetImplCopyWithImpl<$Res>
    extends _$LiveStreamFailureCopyWithImpl<$Res, _$LiveStreamNoInternetImpl>
    implements _$$LiveStreamNoInternetImplCopyWith<$Res> {
  __$$LiveStreamNoInternetImplCopyWithImpl(_$LiveStreamNoInternetImpl _value,
      $Res Function(_$LiveStreamNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LiveStreamNoInternetImpl implements _LiveStreamNoInternet {
  const _$LiveStreamNoInternetImpl();

  @override
  String toString() {
    return 'LiveStreamFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LiveStreamNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LiveStreamFailureUnexpected value) unexpected,
    required TResult Function(_LiveStreamUnauthorized value) unauthorized,
    required TResult Function(_LiveStreamUnauthenticated value) unauthenticated,
    required TResult Function(_LiveStreamServerError value) serverError,
    required TResult Function(_LiveStreamNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult? Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult? Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult? Function(_LiveStreamServerError value)? serverError,
    TResult? Function(_LiveStreamNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LiveStreamFailureUnexpected value)? unexpected,
    TResult Function(_LiveStreamUnauthorized value)? unauthorized,
    TResult Function(_LiveStreamUnauthenticated value)? unauthenticated,
    TResult Function(_LiveStreamServerError value)? serverError,
    TResult Function(_LiveStreamNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _LiveStreamNoInternet implements LiveStreamFailure {
  const factory _LiveStreamNoInternet() = _$LiveStreamNoInternetImpl;
}
