// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_vehicle_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ManageVehicleFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MVehicleFailureUnexpected value) unexpected,
    required TResult Function(_MVehicleFailureUnauthorized value) unauthorized,
    required TResult Function(_MVehicleFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MVehicleFailureServerError value) serverError,
    required TResult Function(_MVehicleFailureNoInternet value) noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult? Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult? Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MVehicleFailureServerError value)? serverError,
    TResult? Function(_MVehicleFailureNoInternet value)? noInternet,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MVehicleFailureServerError value)? serverError,
    TResult Function(_MVehicleFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageVehicleFailureCopyWith<$Res> {
  factory $ManageVehicleFailureCopyWith(ManageVehicleFailure value,
          $Res Function(ManageVehicleFailure) then) =
      _$ManageVehicleFailureCopyWithImpl<$Res, ManageVehicleFailure>;
}

/// @nodoc
class _$ManageVehicleFailureCopyWithImpl<$Res,
        $Val extends ManageVehicleFailure>
    implements $ManageVehicleFailureCopyWith<$Res> {
  _$ManageVehicleFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$MVehicleFailureUnexpectedImplCopyWith<$Res> {
  factory _$$MVehicleFailureUnexpectedImplCopyWith(
          _$MVehicleFailureUnexpectedImpl value,
          $Res Function(_$MVehicleFailureUnexpectedImpl) then) =
      __$$MVehicleFailureUnexpectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$MVehicleFailureUnexpectedImplCopyWithImpl<$Res>
    extends _$ManageVehicleFailureCopyWithImpl<$Res,
        _$MVehicleFailureUnexpectedImpl>
    implements _$$MVehicleFailureUnexpectedImplCopyWith<$Res> {
  __$$MVehicleFailureUnexpectedImplCopyWithImpl(
      _$MVehicleFailureUnexpectedImpl _value,
      $Res Function(_$MVehicleFailureUnexpectedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$MVehicleFailureUnexpectedImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$MVehicleFailureUnexpectedImpl implements _MVehicleFailureUnexpected {
  const _$MVehicleFailureUnexpectedImpl({required this.error});

  @override
  final String error;

  @override
  String toString() {
    return 'ManageVehicleFailure.unexpected(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleFailureUnexpectedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MVehicleFailureUnexpectedImplCopyWith<_$MVehicleFailureUnexpectedImpl>
      get copyWith => __$$MVehicleFailureUnexpectedImplCopyWithImpl<
          _$MVehicleFailureUnexpectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unexpected(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unexpected?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MVehicleFailureUnexpected value) unexpected,
    required TResult Function(_MVehicleFailureUnauthorized value) unauthorized,
    required TResult Function(_MVehicleFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MVehicleFailureServerError value) serverError,
    required TResult Function(_MVehicleFailureNoInternet value) noInternet,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult? Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult? Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MVehicleFailureServerError value)? serverError,
    TResult? Function(_MVehicleFailureNoInternet value)? noInternet,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MVehicleFailureServerError value)? serverError,
    TResult Function(_MVehicleFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _MVehicleFailureUnexpected implements ManageVehicleFailure {
  const factory _MVehicleFailureUnexpected({required final String error}) =
      _$MVehicleFailureUnexpectedImpl;

  String get error;
  @JsonKey(ignore: true)
  _$$MVehicleFailureUnexpectedImplCopyWith<_$MVehicleFailureUnexpectedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MVehicleFailureUnauthorizedImplCopyWith<$Res> {
  factory _$$MVehicleFailureUnauthorizedImplCopyWith(
          _$MVehicleFailureUnauthorizedImpl value,
          $Res Function(_$MVehicleFailureUnauthorizedImpl) then) =
      __$$MVehicleFailureUnauthorizedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MVehicleFailureUnauthorizedImplCopyWithImpl<$Res>
    extends _$ManageVehicleFailureCopyWithImpl<$Res,
        _$MVehicleFailureUnauthorizedImpl>
    implements _$$MVehicleFailureUnauthorizedImplCopyWith<$Res> {
  __$$MVehicleFailureUnauthorizedImplCopyWithImpl(
      _$MVehicleFailureUnauthorizedImpl _value,
      $Res Function(_$MVehicleFailureUnauthorizedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$MVehicleFailureUnauthorizedImpl
    implements _MVehicleFailureUnauthorized {
  const _$MVehicleFailureUnauthorizedImpl();

  @override
  String toString() {
    return 'ManageVehicleFailure.unauthorized()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleFailureUnauthorizedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthorized();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthorized?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MVehicleFailureUnexpected value) unexpected,
    required TResult Function(_MVehicleFailureUnauthorized value) unauthorized,
    required TResult Function(_MVehicleFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MVehicleFailureServerError value) serverError,
    required TResult Function(_MVehicleFailureNoInternet value) noInternet,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult? Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult? Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MVehicleFailureServerError value)? serverError,
    TResult? Function(_MVehicleFailureNoInternet value)? noInternet,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MVehicleFailureServerError value)? serverError,
    TResult Function(_MVehicleFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class _MVehicleFailureUnauthorized implements ManageVehicleFailure {
  const factory _MVehicleFailureUnauthorized() =
      _$MVehicleFailureUnauthorizedImpl;
}

/// @nodoc
abstract class _$$MVehicleFailureUnauthenticatedImplCopyWith<$Res> {
  factory _$$MVehicleFailureUnauthenticatedImplCopyWith(
          _$MVehicleFailureUnauthenticatedImpl value,
          $Res Function(_$MVehicleFailureUnauthenticatedImpl) then) =
      __$$MVehicleFailureUnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MVehicleFailureUnauthenticatedImplCopyWithImpl<$Res>
    extends _$ManageVehicleFailureCopyWithImpl<$Res,
        _$MVehicleFailureUnauthenticatedImpl>
    implements _$$MVehicleFailureUnauthenticatedImplCopyWith<$Res> {
  __$$MVehicleFailureUnauthenticatedImplCopyWithImpl(
      _$MVehicleFailureUnauthenticatedImpl _value,
      $Res Function(_$MVehicleFailureUnauthenticatedImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$MVehicleFailureUnauthenticatedImpl
    implements _MVehicleFailureUnauthenticated {
  const _$MVehicleFailureUnauthenticatedImpl();

  @override
  String toString() {
    return 'ManageVehicleFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleFailureUnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MVehicleFailureUnexpected value) unexpected,
    required TResult Function(_MVehicleFailureUnauthorized value) unauthorized,
    required TResult Function(_MVehicleFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MVehicleFailureServerError value) serverError,
    required TResult Function(_MVehicleFailureNoInternet value) noInternet,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult? Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult? Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MVehicleFailureServerError value)? serverError,
    TResult? Function(_MVehicleFailureNoInternet value)? noInternet,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MVehicleFailureServerError value)? serverError,
    TResult Function(_MVehicleFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _MVehicleFailureUnauthenticated implements ManageVehicleFailure {
  const factory _MVehicleFailureUnauthenticated() =
      _$MVehicleFailureUnauthenticatedImpl;
}

/// @nodoc
abstract class _$$MVehicleFailureServerErrorImplCopyWith<$Res> {
  factory _$$MVehicleFailureServerErrorImplCopyWith(
          _$MVehicleFailureServerErrorImpl value,
          $Res Function(_$MVehicleFailureServerErrorImpl) then) =
      __$$MVehicleFailureServerErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MVehicleFailureServerErrorImplCopyWithImpl<$Res>
    extends _$ManageVehicleFailureCopyWithImpl<$Res,
        _$MVehicleFailureServerErrorImpl>
    implements _$$MVehicleFailureServerErrorImplCopyWith<$Res> {
  __$$MVehicleFailureServerErrorImplCopyWithImpl(
      _$MVehicleFailureServerErrorImpl _value,
      $Res Function(_$MVehicleFailureServerErrorImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$MVehicleFailureServerErrorImpl implements _MVehicleFailureServerError {
  const _$MVehicleFailureServerErrorImpl();

  @override
  String toString() {
    return 'ManageVehicleFailure.serverError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleFailureServerErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return serverError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return serverError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MVehicleFailureUnexpected value) unexpected,
    required TResult Function(_MVehicleFailureUnauthorized value) unauthorized,
    required TResult Function(_MVehicleFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MVehicleFailureServerError value) serverError,
    required TResult Function(_MVehicleFailureNoInternet value) noInternet,
  }) {
    return serverError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult? Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult? Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MVehicleFailureServerError value)? serverError,
    TResult? Function(_MVehicleFailureNoInternet value)? noInternet,
  }) {
    return serverError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MVehicleFailureServerError value)? serverError,
    TResult Function(_MVehicleFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (serverError != null) {
      return serverError(this);
    }
    return orElse();
  }
}

abstract class _MVehicleFailureServerError implements ManageVehicleFailure {
  const factory _MVehicleFailureServerError() =
      _$MVehicleFailureServerErrorImpl;
}

/// @nodoc
abstract class _$$MVehicleFailureNoInternetImplCopyWith<$Res> {
  factory _$$MVehicleFailureNoInternetImplCopyWith(
          _$MVehicleFailureNoInternetImpl value,
          $Res Function(_$MVehicleFailureNoInternetImpl) then) =
      __$$MVehicleFailureNoInternetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MVehicleFailureNoInternetImplCopyWithImpl<$Res>
    extends _$ManageVehicleFailureCopyWithImpl<$Res,
        _$MVehicleFailureNoInternetImpl>
    implements _$$MVehicleFailureNoInternetImplCopyWith<$Res> {
  __$$MVehicleFailureNoInternetImplCopyWithImpl(
      _$MVehicleFailureNoInternetImpl _value,
      $Res Function(_$MVehicleFailureNoInternetImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$MVehicleFailureNoInternetImpl implements _MVehicleFailureNoInternet {
  const _$MVehicleFailureNoInternetImpl();

  @override
  String toString() {
    return 'ManageVehicleFailure.noInternet()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleFailureNoInternetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String error) unexpected,
    required TResult Function() unauthorized,
    required TResult Function() unauthenticated,
    required TResult Function() serverError,
    required TResult Function() noInternet,
  }) {
    return noInternet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String error)? unexpected,
    TResult? Function()? unauthorized,
    TResult? Function()? unauthenticated,
    TResult? Function()? serverError,
    TResult? Function()? noInternet,
  }) {
    return noInternet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String error)? unexpected,
    TResult Function()? unauthorized,
    TResult Function()? unauthenticated,
    TResult Function()? serverError,
    TResult Function()? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MVehicleFailureUnexpected value) unexpected,
    required TResult Function(_MVehicleFailureUnauthorized value) unauthorized,
    required TResult Function(_MVehicleFailureUnauthenticated value)
        unauthenticated,
    required TResult Function(_MVehicleFailureServerError value) serverError,
    required TResult Function(_MVehicleFailureNoInternet value) noInternet,
  }) {
    return noInternet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult? Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult? Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult? Function(_MVehicleFailureServerError value)? serverError,
    TResult? Function(_MVehicleFailureNoInternet value)? noInternet,
  }) {
    return noInternet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MVehicleFailureUnexpected value)? unexpected,
    TResult Function(_MVehicleFailureUnauthorized value)? unauthorized,
    TResult Function(_MVehicleFailureUnauthenticated value)? unauthenticated,
    TResult Function(_MVehicleFailureServerError value)? serverError,
    TResult Function(_MVehicleFailureNoInternet value)? noInternet,
    required TResult orElse(),
  }) {
    if (noInternet != null) {
      return noInternet(this);
    }
    return orElse();
  }
}

abstract class _MVehicleFailureNoInternet implements ManageVehicleFailure {
  const factory _MVehicleFailureNoInternet() = _$MVehicleFailureNoInternetImpl;
}
