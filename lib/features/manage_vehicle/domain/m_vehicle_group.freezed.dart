// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_vehicle_group.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MVehicleGroupDTO _$MVehicleGroupDTOFromJson(Map<String, dynamic> json) {
  return _MVehicleGroupDTO.fromJson(json);
}

/// @nodoc
mixin _$MVehicleGroupDTO {
  String get groupName => throw _privateConstructorUsedError;
  List<MVehicle> get listVehicle => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MVehicleGroupDTOCopyWith<MVehicleGroupDTO> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MVehicleGroupDTOCopyWith<$Res> {
  factory $MVehicleGroupDTOCopyWith(
          MVehicleGroupDTO value, $Res Function(MVehicleGroupDTO) then) =
      _$MVehicleGroupDTOCopyWithImpl<$Res, MVehicleGroupDTO>;
  @useResult
  $Res call({String groupName, List<MVehicle> listVehicle});
}

/// @nodoc
class _$MVehicleGroupDTOCopyWithImpl<$Res, $Val extends MVehicleGroupDTO>
    implements $MVehicleGroupDTOCopyWith<$Res> {
  _$MVehicleGroupDTOCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupName = null,
    Object? listVehicle = null,
  }) {
    return _then(_value.copyWith(
      groupName: null == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String,
      listVehicle: null == listVehicle
          ? _value.listVehicle
          : listVehicle // ignore: cast_nullable_to_non_nullable
              as List<MVehicle>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MVehicleGroupDTOImplCopyWith<$Res>
    implements $MVehicleGroupDTOCopyWith<$Res> {
  factory _$$MVehicleGroupDTOImplCopyWith(_$MVehicleGroupDTOImpl value,
          $Res Function(_$MVehicleGroupDTOImpl) then) =
      __$$MVehicleGroupDTOImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String groupName, List<MVehicle> listVehicle});
}

/// @nodoc
class __$$MVehicleGroupDTOImplCopyWithImpl<$Res>
    extends _$MVehicleGroupDTOCopyWithImpl<$Res, _$MVehicleGroupDTOImpl>
    implements _$$MVehicleGroupDTOImplCopyWith<$Res> {
  __$$MVehicleGroupDTOImplCopyWithImpl(_$MVehicleGroupDTOImpl _value,
      $Res Function(_$MVehicleGroupDTOImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupName = null,
    Object? listVehicle = null,
  }) {
    return _then(_$MVehicleGroupDTOImpl(
      groupName: null == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String,
      listVehicle: null == listVehicle
          ? _value._listVehicle
          : listVehicle // ignore: cast_nullable_to_non_nullable
              as List<MVehicle>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MVehicleGroupDTOImpl extends _MVehicleGroupDTO {
  const _$MVehicleGroupDTOImpl(
      {required this.groupName, required final List<MVehicle> listVehicle})
      : _listVehicle = listVehicle,
        super._();

  factory _$MVehicleGroupDTOImpl.fromJson(Map<String, dynamic> json) =>
      _$$MVehicleGroupDTOImplFromJson(json);

  @override
  final String groupName;
  final List<MVehicle> _listVehicle;
  @override
  List<MVehicle> get listVehicle {
    if (_listVehicle is EqualUnmodifiableListView) return _listVehicle;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicle);
  }

  @override
  String toString() {
    return 'MVehicleGroupDTO(groupName: $groupName, listVehicle: $listVehicle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleGroupDTOImpl &&
            (identical(other.groupName, groupName) ||
                other.groupName == groupName) &&
            const DeepCollectionEquality()
                .equals(other._listVehicle, _listVehicle));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, groupName,
      const DeepCollectionEquality().hash(_listVehicle));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MVehicleGroupDTOImplCopyWith<_$MVehicleGroupDTOImpl> get copyWith =>
      __$$MVehicleGroupDTOImplCopyWithImpl<_$MVehicleGroupDTOImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MVehicleGroupDTOImplToJson(
      this,
    );
  }
}

abstract class _MVehicleGroupDTO extends MVehicleGroupDTO {
  const factory _MVehicleGroupDTO(
      {required final String groupName,
      required final List<MVehicle> listVehicle}) = _$MVehicleGroupDTOImpl;
  const _MVehicleGroupDTO._() : super._();

  factory _MVehicleGroupDTO.fromJson(Map<String, dynamic> json) =
      _$MVehicleGroupDTOImpl.fromJson;

  @override
  String get groupName;
  @override
  List<MVehicle> get listVehicle;
  @override
  @JsonKey(ignore: true)
  _$$MVehicleGroupDTOImplCopyWith<_$MVehicleGroupDTOImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
