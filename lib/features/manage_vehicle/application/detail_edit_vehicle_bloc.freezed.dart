// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'detail_edit_vehicle_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DetailEditVehicleEvent {
  Object get request => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleDetailRequest request) getVehicleDetail,
    required TResult Function(MUpdateVehicleRequest request) updateVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleDetailRequest request)? getVehicleDetail,
    TResult? Function(MUpdateVehicleRequest request)? updateVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleDetailRequest request)? getVehicleDetail,
    TResult Function(MUpdateVehicleRequest request)? updateVehicle,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleDetailEvent value) getVehicleDetail,
    required TResult Function(_UpdatVehicleEvent value) updateVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleDetailEvent value)? getVehicleDetail,
    TResult? Function(_UpdatVehicleEvent value)? updateVehicle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleDetailEvent value)? getVehicleDetail,
    TResult Function(_UpdatVehicleEvent value)? updateVehicle,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DetailEditVehicleEventCopyWith<$Res> {
  factory $DetailEditVehicleEventCopyWith(DetailEditVehicleEvent value,
          $Res Function(DetailEditVehicleEvent) then) =
      _$DetailEditVehicleEventCopyWithImpl<$Res, DetailEditVehicleEvent>;
}

/// @nodoc
class _$DetailEditVehicleEventCopyWithImpl<$Res,
        $Val extends DetailEditVehicleEvent>
    implements $DetailEditVehicleEventCopyWith<$Res> {
  _$DetailEditVehicleEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$GetVehicleDetailEventImplCopyWith<$Res> {
  factory _$$GetVehicleDetailEventImplCopyWith(
          _$GetVehicleDetailEventImpl value,
          $Res Function(_$GetVehicleDetailEventImpl) then) =
      __$$GetVehicleDetailEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({MVehicleDetailRequest request});

  $MVehicleDetailRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$GetVehicleDetailEventImplCopyWithImpl<$Res>
    extends _$DetailEditVehicleEventCopyWithImpl<$Res,
        _$GetVehicleDetailEventImpl>
    implements _$$GetVehicleDetailEventImplCopyWith<$Res> {
  __$$GetVehicleDetailEventImplCopyWithImpl(_$GetVehicleDetailEventImpl _value,
      $Res Function(_$GetVehicleDetailEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$GetVehicleDetailEventImpl(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as MVehicleDetailRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $MVehicleDetailRequestCopyWith<$Res> get request {
    return $MVehicleDetailRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$GetVehicleDetailEventImpl implements _GetVehicleDetailEvent {
  const _$GetVehicleDetailEventImpl({required this.request});

  @override
  final MVehicleDetailRequest request;

  @override
  String toString() {
    return 'DetailEditVehicleEvent.getVehicleDetail(request: $request)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetVehicleDetailEventImpl &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetVehicleDetailEventImplCopyWith<_$GetVehicleDetailEventImpl>
      get copyWith => __$$GetVehicleDetailEventImplCopyWithImpl<
          _$GetVehicleDetailEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleDetailRequest request) getVehicleDetail,
    required TResult Function(MUpdateVehicleRequest request) updateVehicle,
  }) {
    return getVehicleDetail(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleDetailRequest request)? getVehicleDetail,
    TResult? Function(MUpdateVehicleRequest request)? updateVehicle,
  }) {
    return getVehicleDetail?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleDetailRequest request)? getVehicleDetail,
    TResult Function(MUpdateVehicleRequest request)? updateVehicle,
    required TResult orElse(),
  }) {
    if (getVehicleDetail != null) {
      return getVehicleDetail(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleDetailEvent value) getVehicleDetail,
    required TResult Function(_UpdatVehicleEvent value) updateVehicle,
  }) {
    return getVehicleDetail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleDetailEvent value)? getVehicleDetail,
    TResult? Function(_UpdatVehicleEvent value)? updateVehicle,
  }) {
    return getVehicleDetail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleDetailEvent value)? getVehicleDetail,
    TResult Function(_UpdatVehicleEvent value)? updateVehicle,
    required TResult orElse(),
  }) {
    if (getVehicleDetail != null) {
      return getVehicleDetail(this);
    }
    return orElse();
  }
}

abstract class _GetVehicleDetailEvent implements DetailEditVehicleEvent {
  const factory _GetVehicleDetailEvent(
          {required final MVehicleDetailRequest request}) =
      _$GetVehicleDetailEventImpl;

  @override
  MVehicleDetailRequest get request;
  @JsonKey(ignore: true)
  _$$GetVehicleDetailEventImplCopyWith<_$GetVehicleDetailEventImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatVehicleEventImplCopyWith<$Res> {
  factory _$$UpdatVehicleEventImplCopyWith(_$UpdatVehicleEventImpl value,
          $Res Function(_$UpdatVehicleEventImpl) then) =
      __$$UpdatVehicleEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({MUpdateVehicleRequest request});

  $MUpdateVehicleRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$UpdatVehicleEventImplCopyWithImpl<$Res>
    extends _$DetailEditVehicleEventCopyWithImpl<$Res, _$UpdatVehicleEventImpl>
    implements _$$UpdatVehicleEventImplCopyWith<$Res> {
  __$$UpdatVehicleEventImplCopyWithImpl(_$UpdatVehicleEventImpl _value,
      $Res Function(_$UpdatVehicleEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$UpdatVehicleEventImpl(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as MUpdateVehicleRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $MUpdateVehicleRequestCopyWith<$Res> get request {
    return $MUpdateVehicleRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$UpdatVehicleEventImpl implements _UpdatVehicleEvent {
  const _$UpdatVehicleEventImpl({required this.request});

  @override
  final MUpdateVehicleRequest request;

  @override
  String toString() {
    return 'DetailEditVehicleEvent.updateVehicle(request: $request)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatVehicleEventImpl &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatVehicleEventImplCopyWith<_$UpdatVehicleEventImpl> get copyWith =>
      __$$UpdatVehicleEventImplCopyWithImpl<_$UpdatVehicleEventImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleDetailRequest request) getVehicleDetail,
    required TResult Function(MUpdateVehicleRequest request) updateVehicle,
  }) {
    return updateVehicle(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleDetailRequest request)? getVehicleDetail,
    TResult? Function(MUpdateVehicleRequest request)? updateVehicle,
  }) {
    return updateVehicle?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleDetailRequest request)? getVehicleDetail,
    TResult Function(MUpdateVehicleRequest request)? updateVehicle,
    required TResult orElse(),
  }) {
    if (updateVehicle != null) {
      return updateVehicle(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleDetailEvent value) getVehicleDetail,
    required TResult Function(_UpdatVehicleEvent value) updateVehicle,
  }) {
    return updateVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleDetailEvent value)? getVehicleDetail,
    TResult? Function(_UpdatVehicleEvent value)? updateVehicle,
  }) {
    return updateVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleDetailEvent value)? getVehicleDetail,
    TResult Function(_UpdatVehicleEvent value)? updateVehicle,
    required TResult orElse(),
  }) {
    if (updateVehicle != null) {
      return updateVehicle(this);
    }
    return orElse();
  }
}

abstract class _UpdatVehicleEvent implements DetailEditVehicleEvent {
  const factory _UpdatVehicleEvent(
      {required final MUpdateVehicleRequest request}) = _$UpdatVehicleEventImpl;

  @override
  MUpdateVehicleRequest get request;
  @JsonKey(ignore: true)
  _$$UpdatVehicleEventImplCopyWith<_$UpdatVehicleEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DetailEditVehicleState {
  MVehicleDetailResponse? get vehicleDetail =>
      throw _privateConstructorUsedError;
  ManageVehicleFailure? get failureVehicleDetail =>
      throw _privateConstructorUsedError;
  ManageVehicleFailure? get failureUpdate => throw _privateConstructorUsedError;
  bool get isGettingVehicleDetail => throw _privateConstructorUsedError;
  bool get isUpdatingDetail => throw _privateConstructorUsedError;
  List<MVehicleGroup> get listVehicleGroup =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DetailEditVehicleStateCopyWith<DetailEditVehicleState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DetailEditVehicleStateCopyWith<$Res> {
  factory $DetailEditVehicleStateCopyWith(DetailEditVehicleState value,
          $Res Function(DetailEditVehicleState) then) =
      _$DetailEditVehicleStateCopyWithImpl<$Res, DetailEditVehicleState>;
  @useResult
  $Res call(
      {MVehicleDetailResponse? vehicleDetail,
      ManageVehicleFailure? failureVehicleDetail,
      ManageVehicleFailure? failureUpdate,
      bool isGettingVehicleDetail,
      bool isUpdatingDetail,
      List<MVehicleGroup> listVehicleGroup});

  $MVehicleDetailResponseCopyWith<$Res>? get vehicleDetail;
  $ManageVehicleFailureCopyWith<$Res>? get failureVehicleDetail;
  $ManageVehicleFailureCopyWith<$Res>? get failureUpdate;
}

/// @nodoc
class _$DetailEditVehicleStateCopyWithImpl<$Res,
        $Val extends DetailEditVehicleState>
    implements $DetailEditVehicleStateCopyWith<$Res> {
  _$DetailEditVehicleStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleDetail = freezed,
    Object? failureVehicleDetail = freezed,
    Object? failureUpdate = freezed,
    Object? isGettingVehicleDetail = null,
    Object? isUpdatingDetail = null,
    Object? listVehicleGroup = null,
  }) {
    return _then(_value.copyWith(
      vehicleDetail: freezed == vehicleDetail
          ? _value.vehicleDetail
          : vehicleDetail // ignore: cast_nullable_to_non_nullable
              as MVehicleDetailResponse?,
      failureVehicleDetail: freezed == failureVehicleDetail
          ? _value.failureVehicleDetail
          : failureVehicleDetail // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      failureUpdate: freezed == failureUpdate
          ? _value.failureUpdate
          : failureUpdate // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      isGettingVehicleDetail: null == isGettingVehicleDetail
          ? _value.isGettingVehicleDetail
          : isGettingVehicleDetail // ignore: cast_nullable_to_non_nullable
              as bool,
      isUpdatingDetail: null == isUpdatingDetail
          ? _value.isUpdatingDetail
          : isUpdatingDetail // ignore: cast_nullable_to_non_nullable
              as bool,
      listVehicleGroup: null == listVehicleGroup
          ? _value.listVehicleGroup
          : listVehicleGroup // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroup>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MVehicleDetailResponseCopyWith<$Res>? get vehicleDetail {
    if (_value.vehicleDetail == null) {
      return null;
    }

    return $MVehicleDetailResponseCopyWith<$Res>(_value.vehicleDetail!,
        (value) {
      return _then(_value.copyWith(vehicleDetail: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ManageVehicleFailureCopyWith<$Res>? get failureVehicleDetail {
    if (_value.failureVehicleDetail == null) {
      return null;
    }

    return $ManageVehicleFailureCopyWith<$Res>(_value.failureVehicleDetail!,
        (value) {
      return _then(_value.copyWith(failureVehicleDetail: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ManageVehicleFailureCopyWith<$Res>? get failureUpdate {
    if (_value.failureUpdate == null) {
      return null;
    }

    return $ManageVehicleFailureCopyWith<$Res>(_value.failureUpdate!, (value) {
      return _then(_value.copyWith(failureUpdate: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DetailEditVehicleStateImplCopyWith<$Res>
    implements $DetailEditVehicleStateCopyWith<$Res> {
  factory _$$DetailEditVehicleStateImplCopyWith(
          _$DetailEditVehicleStateImpl value,
          $Res Function(_$DetailEditVehicleStateImpl) then) =
      __$$DetailEditVehicleStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MVehicleDetailResponse? vehicleDetail,
      ManageVehicleFailure? failureVehicleDetail,
      ManageVehicleFailure? failureUpdate,
      bool isGettingVehicleDetail,
      bool isUpdatingDetail,
      List<MVehicleGroup> listVehicleGroup});

  @override
  $MVehicleDetailResponseCopyWith<$Res>? get vehicleDetail;
  @override
  $ManageVehicleFailureCopyWith<$Res>? get failureVehicleDetail;
  @override
  $ManageVehicleFailureCopyWith<$Res>? get failureUpdate;
}

/// @nodoc
class __$$DetailEditVehicleStateImplCopyWithImpl<$Res>
    extends _$DetailEditVehicleStateCopyWithImpl<$Res,
        _$DetailEditVehicleStateImpl>
    implements _$$DetailEditVehicleStateImplCopyWith<$Res> {
  __$$DetailEditVehicleStateImplCopyWithImpl(
      _$DetailEditVehicleStateImpl _value,
      $Res Function(_$DetailEditVehicleStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleDetail = freezed,
    Object? failureVehicleDetail = freezed,
    Object? failureUpdate = freezed,
    Object? isGettingVehicleDetail = null,
    Object? isUpdatingDetail = null,
    Object? listVehicleGroup = null,
  }) {
    return _then(_$DetailEditVehicleStateImpl(
      vehicleDetail: freezed == vehicleDetail
          ? _value.vehicleDetail
          : vehicleDetail // ignore: cast_nullable_to_non_nullable
              as MVehicleDetailResponse?,
      failureVehicleDetail: freezed == failureVehicleDetail
          ? _value.failureVehicleDetail
          : failureVehicleDetail // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      failureUpdate: freezed == failureUpdate
          ? _value.failureUpdate
          : failureUpdate // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      isGettingVehicleDetail: null == isGettingVehicleDetail
          ? _value.isGettingVehicleDetail
          : isGettingVehicleDetail // ignore: cast_nullable_to_non_nullable
              as bool,
      isUpdatingDetail: null == isUpdatingDetail
          ? _value.isUpdatingDetail
          : isUpdatingDetail // ignore: cast_nullable_to_non_nullable
              as bool,
      listVehicleGroup: null == listVehicleGroup
          ? _value._listVehicleGroup
          : listVehicleGroup // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroup>,
    ));
  }
}

/// @nodoc

class _$DetailEditVehicleStateImpl implements _DetailEditVehicleState {
  const _$DetailEditVehicleStateImpl(
      {required this.vehicleDetail,
      required this.failureVehicleDetail,
      required this.failureUpdate,
      required this.isGettingVehicleDetail,
      required this.isUpdatingDetail,
      required final List<MVehicleGroup> listVehicleGroup})
      : _listVehicleGroup = listVehicleGroup;

  @override
  final MVehicleDetailResponse? vehicleDetail;
  @override
  final ManageVehicleFailure? failureVehicleDetail;
  @override
  final ManageVehicleFailure? failureUpdate;
  @override
  final bool isGettingVehicleDetail;
  @override
  final bool isUpdatingDetail;
  final List<MVehicleGroup> _listVehicleGroup;
  @override
  List<MVehicleGroup> get listVehicleGroup {
    if (_listVehicleGroup is EqualUnmodifiableListView)
      return _listVehicleGroup;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicleGroup);
  }

  @override
  String toString() {
    return 'DetailEditVehicleState(vehicleDetail: $vehicleDetail, failureVehicleDetail: $failureVehicleDetail, failureUpdate: $failureUpdate, isGettingVehicleDetail: $isGettingVehicleDetail, isUpdatingDetail: $isUpdatingDetail, listVehicleGroup: $listVehicleGroup)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DetailEditVehicleStateImpl &&
            (identical(other.vehicleDetail, vehicleDetail) ||
                other.vehicleDetail == vehicleDetail) &&
            (identical(other.failureVehicleDetail, failureVehicleDetail) ||
                other.failureVehicleDetail == failureVehicleDetail) &&
            (identical(other.failureUpdate, failureUpdate) ||
                other.failureUpdate == failureUpdate) &&
            (identical(other.isGettingVehicleDetail, isGettingVehicleDetail) ||
                other.isGettingVehicleDetail == isGettingVehicleDetail) &&
            (identical(other.isUpdatingDetail, isUpdatingDetail) ||
                other.isUpdatingDetail == isUpdatingDetail) &&
            const DeepCollectionEquality()
                .equals(other._listVehicleGroup, _listVehicleGroup));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      vehicleDetail,
      failureVehicleDetail,
      failureUpdate,
      isGettingVehicleDetail,
      isUpdatingDetail,
      const DeepCollectionEquality().hash(_listVehicleGroup));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DetailEditVehicleStateImplCopyWith<_$DetailEditVehicleStateImpl>
      get copyWith => __$$DetailEditVehicleStateImplCopyWithImpl<
          _$DetailEditVehicleStateImpl>(this, _$identity);
}

abstract class _DetailEditVehicleState implements DetailEditVehicleState {
  const factory _DetailEditVehicleState(
          {required final MVehicleDetailResponse? vehicleDetail,
          required final ManageVehicleFailure? failureVehicleDetail,
          required final ManageVehicleFailure? failureUpdate,
          required final bool isGettingVehicleDetail,
          required final bool isUpdatingDetail,
          required final List<MVehicleGroup> listVehicleGroup}) =
      _$DetailEditVehicleStateImpl;

  @override
  MVehicleDetailResponse? get vehicleDetail;
  @override
  ManageVehicleFailure? get failureVehicleDetail;
  @override
  ManageVehicleFailure? get failureUpdate;
  @override
  bool get isGettingVehicleDetail;
  @override
  bool get isUpdatingDetail;
  @override
  List<MVehicleGroup> get listVehicleGroup;
  @override
  @JsonKey(ignore: true)
  _$$DetailEditVehicleStateImplCopyWith<_$DetailEditVehicleStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
