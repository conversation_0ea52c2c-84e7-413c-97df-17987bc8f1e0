// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bus_setting_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BusSettingEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleBusSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleBusSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleBusSetting value) getVehicleBusSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleBusSetting value)
        updateVehicleBusSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BusSettingEventCopyWith<$Res> {
  factory $BusSettingEventCopyWith(
          BusSettingEvent value, $Res Function(BusSettingEvent) then) =
      _$BusSettingEventCopyWithImpl<$Res, BusSettingEvent>;
}

/// @nodoc
class _$BusSettingEventCopyWithImpl<$Res, $Val extends BusSettingEvent>
    implements $BusSettingEventCopyWith<$Res> {
  _$BusSettingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$GetVehicleBusSettingImplCopyWith<$Res> {
  factory _$$GetVehicleBusSettingImplCopyWith(_$GetVehicleBusSettingImpl value,
          $Res Function(_$GetVehicleBusSettingImpl) then) =
      __$$GetVehicleBusSettingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({MVehicleSettingRequest request});

  $MVehicleSettingRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$GetVehicleBusSettingImplCopyWithImpl<$Res>
    extends _$BusSettingEventCopyWithImpl<$Res, _$GetVehicleBusSettingImpl>
    implements _$$GetVehicleBusSettingImplCopyWith<$Res> {
  __$$GetVehicleBusSettingImplCopyWithImpl(_$GetVehicleBusSettingImpl _value,
      $Res Function(_$GetVehicleBusSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$GetVehicleBusSettingImpl(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as MVehicleSettingRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $MVehicleSettingRequestCopyWith<$Res> get request {
    return $MVehicleSettingRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$GetVehicleBusSettingImpl implements _GetVehicleBusSetting {
  const _$GetVehicleBusSettingImpl({required this.request});

  @override
  final MVehicleSettingRequest request;

  @override
  String toString() {
    return 'BusSettingEvent.getVehicleBusSetting(request: $request)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetVehicleBusSettingImpl &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetVehicleBusSettingImplCopyWith<_$GetVehicleBusSettingImpl>
      get copyWith =>
          __$$GetVehicleBusSettingImplCopyWithImpl<_$GetVehicleBusSettingImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleBusSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleBusSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return getVehicleBusSetting(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return getVehicleBusSetting?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (getVehicleBusSetting != null) {
      return getVehicleBusSetting(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleBusSetting value) getVehicleBusSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleBusSetting value)
        updateVehicleBusSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return getVehicleBusSetting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return getVehicleBusSetting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (getVehicleBusSetting != null) {
      return getVehicleBusSetting(this);
    }
    return orElse();
  }
}

abstract class _GetVehicleBusSetting implements BusSettingEvent {
  const factory _GetVehicleBusSetting(
          {required final MVehicleSettingRequest request}) =
      _$GetVehicleBusSettingImpl;

  MVehicleSettingRequest get request;
  @JsonKey(ignore: true)
  _$$GetVehicleBusSettingImplCopyWith<_$GetVehicleBusSettingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadMoreVehicleImplCopyWith<$Res> {
  factory _$$LoadMoreVehicleImplCopyWith(_$LoadMoreVehicleImpl value,
          $Res Function(_$LoadMoreVehicleImpl) then) =
      __$$LoadMoreVehicleImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int pageSize});
}

/// @nodoc
class __$$LoadMoreVehicleImplCopyWithImpl<$Res>
    extends _$BusSettingEventCopyWithImpl<$Res, _$LoadMoreVehicleImpl>
    implements _$$LoadMoreVehicleImplCopyWith<$Res> {
  __$$LoadMoreVehicleImplCopyWithImpl(
      _$LoadMoreVehicleImpl _value, $Res Function(_$LoadMoreVehicleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
  }) {
    return _then(_$LoadMoreVehicleImpl(
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$LoadMoreVehicleImpl implements _LoadMoreVehicle {
  const _$LoadMoreVehicleImpl({this.pageSize = 10});

  @override
  @JsonKey()
  final int pageSize;

  @override
  String toString() {
    return 'BusSettingEvent.loadMoreVehicle(pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadMoreVehicleImpl &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @override
  int get hashCode => Object.hash(runtimeType, pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadMoreVehicleImplCopyWith<_$LoadMoreVehicleImpl> get copyWith =>
      __$$LoadMoreVehicleImplCopyWithImpl<_$LoadMoreVehicleImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleBusSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleBusSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return loadMoreVehicle(pageSize);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return loadMoreVehicle?.call(pageSize);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (loadMoreVehicle != null) {
      return loadMoreVehicle(pageSize);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleBusSetting value) getVehicleBusSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleBusSetting value)
        updateVehicleBusSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return loadMoreVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return loadMoreVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (loadMoreVehicle != null) {
      return loadMoreVehicle(this);
    }
    return orElse();
  }
}

abstract class _LoadMoreVehicle implements BusSettingEvent {
  const factory _LoadMoreVehicle({final int pageSize}) = _$LoadMoreVehicleImpl;

  int get pageSize;
  @JsonKey(ignore: true)
  _$$LoadMoreVehicleImplCopyWith<_$LoadMoreVehicleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OnScrolledToBottomEventImplCopyWith<$Res> {
  factory _$$OnScrolledToBottomEventImplCopyWith(
          _$OnScrolledToBottomEventImpl value,
          $Res Function(_$OnScrolledToBottomEventImpl) then) =
      __$$OnScrolledToBottomEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int pageSize});
}

/// @nodoc
class __$$OnScrolledToBottomEventImplCopyWithImpl<$Res>
    extends _$BusSettingEventCopyWithImpl<$Res, _$OnScrolledToBottomEventImpl>
    implements _$$OnScrolledToBottomEventImplCopyWith<$Res> {
  __$$OnScrolledToBottomEventImplCopyWithImpl(
      _$OnScrolledToBottomEventImpl _value,
      $Res Function(_$OnScrolledToBottomEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
  }) {
    return _then(_$OnScrolledToBottomEventImpl(
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$OnScrolledToBottomEventImpl implements _OnScrolledToBottomEvent {
  const _$OnScrolledToBottomEventImpl({this.pageSize = 10});

  @override
  @JsonKey()
  final int pageSize;

  @override
  String toString() {
    return 'BusSettingEvent.onScrolledToBottom(pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnScrolledToBottomEventImpl &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @override
  int get hashCode => Object.hash(runtimeType, pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OnScrolledToBottomEventImplCopyWith<_$OnScrolledToBottomEventImpl>
      get copyWith => __$$OnScrolledToBottomEventImplCopyWithImpl<
          _$OnScrolledToBottomEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleBusSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleBusSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return onScrolledToBottom(pageSize);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return onScrolledToBottom?.call(pageSize);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (onScrolledToBottom != null) {
      return onScrolledToBottom(pageSize);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleBusSetting value) getVehicleBusSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleBusSetting value)
        updateVehicleBusSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return onScrolledToBottom(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return onScrolledToBottom?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (onScrolledToBottom != null) {
      return onScrolledToBottom(this);
    }
    return orElse();
  }
}

abstract class _OnScrolledToBottomEvent implements BusSettingEvent {
  const factory _OnScrolledToBottomEvent({final int pageSize}) =
      _$OnScrolledToBottomEventImpl;

  int get pageSize;
  @JsonKey(ignore: true)
  _$$OnScrolledToBottomEventImplCopyWith<_$OnScrolledToBottomEventImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchLicensePlateImplCopyWith<$Res> {
  factory _$$SearchLicensePlateImplCopyWith(_$SearchLicensePlateImpl value,
          $Res Function(_$SearchLicensePlateImpl) then) =
      __$$SearchLicensePlateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String searchText});
}

/// @nodoc
class __$$SearchLicensePlateImplCopyWithImpl<$Res>
    extends _$BusSettingEventCopyWithImpl<$Res, _$SearchLicensePlateImpl>
    implements _$$SearchLicensePlateImplCopyWith<$Res> {
  __$$SearchLicensePlateImplCopyWithImpl(_$SearchLicensePlateImpl _value,
      $Res Function(_$SearchLicensePlateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchText = null,
  }) {
    return _then(_$SearchLicensePlateImpl(
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchLicensePlateImpl implements _SearchLicensePlate {
  const _$SearchLicensePlateImpl({required this.searchText});

  @override
  final String searchText;

  @override
  String toString() {
    return 'BusSettingEvent.searchLicensePlate(searchText: $searchText)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchLicensePlateImpl &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText));
  }

  @override
  int get hashCode => Object.hash(runtimeType, searchText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchLicensePlateImplCopyWith<_$SearchLicensePlateImpl> get copyWith =>
      __$$SearchLicensePlateImplCopyWithImpl<_$SearchLicensePlateImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleBusSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleBusSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return searchLicensePlate(searchText);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return searchLicensePlate?.call(searchText);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (searchLicensePlate != null) {
      return searchLicensePlate(searchText);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleBusSetting value) getVehicleBusSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleBusSetting value)
        updateVehicleBusSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return searchLicensePlate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return searchLicensePlate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (searchLicensePlate != null) {
      return searchLicensePlate(this);
    }
    return orElse();
  }
}

abstract class _SearchLicensePlate implements BusSettingEvent {
  const factory _SearchLicensePlate({required final String searchText}) =
      _$SearchLicensePlateImpl;

  String get searchText;
  @JsonKey(ignore: true)
  _$$SearchLicensePlateImplCopyWith<_$SearchLicensePlateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateVehicleBusSettingImplCopyWith<$Res> {
  factory _$$UpdateVehicleBusSettingImplCopyWith(
          _$UpdateVehicleBusSettingImpl value,
          $Res Function(_$UpdateVehicleBusSettingImpl) then) =
      __$$UpdateVehicleBusSettingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({MUpdateSendDataRequest request});

  $MUpdateSendDataRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$UpdateVehicleBusSettingImplCopyWithImpl<$Res>
    extends _$BusSettingEventCopyWithImpl<$Res, _$UpdateVehicleBusSettingImpl>
    implements _$$UpdateVehicleBusSettingImplCopyWith<$Res> {
  __$$UpdateVehicleBusSettingImplCopyWithImpl(
      _$UpdateVehicleBusSettingImpl _value,
      $Res Function(_$UpdateVehicleBusSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$UpdateVehicleBusSettingImpl(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as MUpdateSendDataRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $MUpdateSendDataRequestCopyWith<$Res> get request {
    return $MUpdateSendDataRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$UpdateVehicleBusSettingImpl implements _UpdateVehicleBusSetting {
  const _$UpdateVehicleBusSettingImpl({required this.request});

  @override
  final MUpdateSendDataRequest request;

  @override
  String toString() {
    return 'BusSettingEvent.updateVehicleBusSetting(request: $request)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateVehicleBusSettingImpl &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateVehicleBusSettingImplCopyWith<_$UpdateVehicleBusSettingImpl>
      get copyWith => __$$UpdateVehicleBusSettingImplCopyWithImpl<
          _$UpdateVehicleBusSettingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleBusSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleBusSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return updateVehicleBusSetting(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return updateVehicleBusSetting?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (updateVehicleBusSetting != null) {
      return updateVehicleBusSetting(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleBusSetting value) getVehicleBusSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleBusSetting value)
        updateVehicleBusSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return updateVehicleBusSetting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return updateVehicleBusSetting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (updateVehicleBusSetting != null) {
      return updateVehicleBusSetting(this);
    }
    return orElse();
  }
}

abstract class _UpdateVehicleBusSetting implements BusSettingEvent {
  const factory _UpdateVehicleBusSetting(
          {required final MUpdateSendDataRequest request}) =
      _$UpdateVehicleBusSettingImpl;

  MUpdateSendDataRequest get request;
  @JsonKey(ignore: true)
  _$$UpdateVehicleBusSettingImplCopyWith<_$UpdateVehicleBusSettingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateVehicleSwitchImplCopyWith<$Res> {
  factory _$$UpdateVehicleSwitchImplCopyWith(_$UpdateVehicleSwitchImpl value,
          $Res Function(_$UpdateVehicleSwitchImpl) then) =
      __$$UpdateVehicleSwitchImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Map<String, Map<String, bool>> switchState});
}

/// @nodoc
class __$$UpdateVehicleSwitchImplCopyWithImpl<$Res>
    extends _$BusSettingEventCopyWithImpl<$Res, _$UpdateVehicleSwitchImpl>
    implements _$$UpdateVehicleSwitchImplCopyWith<$Res> {
  __$$UpdateVehicleSwitchImplCopyWithImpl(_$UpdateVehicleSwitchImpl _value,
      $Res Function(_$UpdateVehicleSwitchImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? switchState = null,
  }) {
    return _then(_$UpdateVehicleSwitchImpl(
      switchState: null == switchState
          ? _value._switchState
          : switchState // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, bool>>,
    ));
  }
}

/// @nodoc

class _$UpdateVehicleSwitchImpl implements _UpdateVehicleSwitch {
  const _$UpdateVehicleSwitchImpl(
      {required final Map<String, Map<String, bool>> switchState})
      : _switchState = switchState;

  final Map<String, Map<String, bool>> _switchState;
  @override
  Map<String, Map<String, bool>> get switchState {
    if (_switchState is EqualUnmodifiableMapView) return _switchState;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_switchState);
  }

  @override
  String toString() {
    return 'BusSettingEvent.updateVehicleSwitch(switchState: $switchState)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateVehicleSwitchImpl &&
            const DeepCollectionEquality()
                .equals(other._switchState, _switchState));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_switchState));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateVehicleSwitchImplCopyWith<_$UpdateVehicleSwitchImpl> get copyWith =>
      __$$UpdateVehicleSwitchImplCopyWithImpl<_$UpdateVehicleSwitchImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleBusSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleBusSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return updateVehicleSwitch(switchState);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return updateVehicleSwitch?.call(switchState);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (updateVehicleSwitch != null) {
      return updateVehicleSwitch(switchState);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleBusSetting value) getVehicleBusSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleBusSetting value)
        updateVehicleBusSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return updateVehicleSwitch(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return updateVehicleSwitch?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (updateVehicleSwitch != null) {
      return updateVehicleSwitch(this);
    }
    return orElse();
  }
}

abstract class _UpdateVehicleSwitch implements BusSettingEvent {
  const factory _UpdateVehicleSwitch(
          {required final Map<String, Map<String, bool>> switchState}) =
      _$UpdateVehicleSwitchImpl;

  Map<String, Map<String, bool>> get switchState;
  @JsonKey(ignore: true)
  _$$UpdateVehicleSwitchImplCopyWith<_$UpdateVehicleSwitchImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetStateImplCopyWith<$Res> {
  factory _$$ResetStateImplCopyWith(
          _$ResetStateImpl value, $Res Function(_$ResetStateImpl) then) =
      __$$ResetStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetStateImplCopyWithImpl<$Res>
    extends _$BusSettingEventCopyWithImpl<$Res, _$ResetStateImpl>
    implements _$$ResetStateImplCopyWith<$Res> {
  __$$ResetStateImplCopyWithImpl(
      _$ResetStateImpl _value, $Res Function(_$ResetStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ResetStateImpl implements _ResetState {
  const _$ResetStateImpl();

  @override
  String toString() {
    return 'BusSettingEvent.resetState()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleBusSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleBusSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return resetState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return resetState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)? getVehicleBusSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)? updateVehicleBusSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleBusSetting value) getVehicleBusSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleBusSetting value)
        updateVehicleBusSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return resetState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return resetState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleBusSetting value)? getVehicleBusSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleBusSetting value)? updateVehicleBusSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState(this);
    }
    return orElse();
  }
}

abstract class _ResetState implements BusSettingEvent {
  const factory _ResetState() = _$ResetStateImpl;
}

/// @nodoc
mixin _$BusSettingState {
  bool get isLoading => throw _privateConstructorUsedError;
  ManageVehicleFailure? get failure => throw _privateConstructorUsedError;
  List<VechileSetting> get listVechileSetting =>
      throw _privateConstructorUsedError;
  List<VechileSetting> get listVechileSettingFilter =>
      throw _privateConstructorUsedError;
  bool get isLoadingMore => throw _privateConstructorUsedError;
  bool get hasReachedMax => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  String get searchText => throw _privateConstructorUsedError;
  Map<String, Map<String, bool>> get vehicleSwitchState =>
      throw _privateConstructorUsedError;
  bool? get updateSuccess => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $BusSettingStateCopyWith<BusSettingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BusSettingStateCopyWith<$Res> {
  factory $BusSettingStateCopyWith(
          BusSettingState value, $Res Function(BusSettingState) then) =
      _$BusSettingStateCopyWithImpl<$Res, BusSettingState>;
  @useResult
  $Res call(
      {bool isLoading,
      ManageVehicleFailure? failure,
      List<VechileSetting> listVechileSetting,
      List<VechileSetting> listVechileSettingFilter,
      bool isLoadingMore,
      bool hasReachedMax,
      int currentPage,
      String searchText,
      Map<String, Map<String, bool>> vehicleSwitchState,
      bool? updateSuccess,
      int total});

  $ManageVehicleFailureCopyWith<$Res>? get failure;
}

/// @nodoc
class _$BusSettingStateCopyWithImpl<$Res, $Val extends BusSettingState>
    implements $BusSettingStateCopyWith<$Res> {
  _$BusSettingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? failure = freezed,
    Object? listVechileSetting = null,
    Object? listVechileSettingFilter = null,
    Object? isLoadingMore = null,
    Object? hasReachedMax = null,
    Object? currentPage = null,
    Object? searchText = null,
    Object? vehicleSwitchState = null,
    Object? updateSuccess = freezed,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      failure: freezed == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      listVechileSetting: null == listVechileSetting
          ? _value.listVechileSetting
          : listVechileSetting // ignore: cast_nullable_to_non_nullable
              as List<VechileSetting>,
      listVechileSettingFilter: null == listVechileSettingFilter
          ? _value.listVechileSettingFilter
          : listVechileSettingFilter // ignore: cast_nullable_to_non_nullable
              as List<VechileSetting>,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      hasReachedMax: null == hasReachedMax
          ? _value.hasReachedMax
          : hasReachedMax // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleSwitchState: null == vehicleSwitchState
          ? _value.vehicleSwitchState
          : vehicleSwitchState // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, bool>>,
      updateSuccess: freezed == updateSuccess
          ? _value.updateSuccess
          : updateSuccess // ignore: cast_nullable_to_non_nullable
              as bool?,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ManageVehicleFailureCopyWith<$Res>? get failure {
    if (_value.failure == null) {
      return null;
    }

    return $ManageVehicleFailureCopyWith<$Res>(_value.failure!, (value) {
      return _then(_value.copyWith(failure: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BusSettingStateImplCopyWith<$Res>
    implements $BusSettingStateCopyWith<$Res> {
  factory _$$BusSettingStateImplCopyWith(_$BusSettingStateImpl value,
          $Res Function(_$BusSettingStateImpl) then) =
      __$$BusSettingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      ManageVehicleFailure? failure,
      List<VechileSetting> listVechileSetting,
      List<VechileSetting> listVechileSettingFilter,
      bool isLoadingMore,
      bool hasReachedMax,
      int currentPage,
      String searchText,
      Map<String, Map<String, bool>> vehicleSwitchState,
      bool? updateSuccess,
      int total});

  @override
  $ManageVehicleFailureCopyWith<$Res>? get failure;
}

/// @nodoc
class __$$BusSettingStateImplCopyWithImpl<$Res>
    extends _$BusSettingStateCopyWithImpl<$Res, _$BusSettingStateImpl>
    implements _$$BusSettingStateImplCopyWith<$Res> {
  __$$BusSettingStateImplCopyWithImpl(
      _$BusSettingStateImpl _value, $Res Function(_$BusSettingStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? failure = freezed,
    Object? listVechileSetting = null,
    Object? listVechileSettingFilter = null,
    Object? isLoadingMore = null,
    Object? hasReachedMax = null,
    Object? currentPage = null,
    Object? searchText = null,
    Object? vehicleSwitchState = null,
    Object? updateSuccess = freezed,
    Object? total = null,
  }) {
    return _then(_$BusSettingStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      failure: freezed == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      listVechileSetting: null == listVechileSetting
          ? _value._listVechileSetting
          : listVechileSetting // ignore: cast_nullable_to_non_nullable
              as List<VechileSetting>,
      listVechileSettingFilter: null == listVechileSettingFilter
          ? _value._listVechileSettingFilter
          : listVechileSettingFilter // ignore: cast_nullable_to_non_nullable
              as List<VechileSetting>,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      hasReachedMax: null == hasReachedMax
          ? _value.hasReachedMax
          : hasReachedMax // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleSwitchState: null == vehicleSwitchState
          ? _value._vehicleSwitchState
          : vehicleSwitchState // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, bool>>,
      updateSuccess: freezed == updateSuccess
          ? _value.updateSuccess
          : updateSuccess // ignore: cast_nullable_to_non_nullable
              as bool?,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$BusSettingStateImpl implements _BusSettingState {
  const _$BusSettingStateImpl(
      {required this.isLoading,
      this.failure,
      required final List<VechileSetting> listVechileSetting,
      required final List<VechileSetting> listVechileSettingFilter,
      this.isLoadingMore = false,
      this.hasReachedMax = false,
      this.currentPage = 1,
      required this.searchText,
      final Map<String, Map<String, bool>> vehicleSwitchState = const {},
      this.updateSuccess,
      required this.total})
      : _listVechileSetting = listVechileSetting,
        _listVechileSettingFilter = listVechileSettingFilter,
        _vehicleSwitchState = vehicleSwitchState;

  @override
  final bool isLoading;
  @override
  final ManageVehicleFailure? failure;
  final List<VechileSetting> _listVechileSetting;
  @override
  List<VechileSetting> get listVechileSetting {
    if (_listVechileSetting is EqualUnmodifiableListView)
      return _listVechileSetting;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVechileSetting);
  }

  final List<VechileSetting> _listVechileSettingFilter;
  @override
  List<VechileSetting> get listVechileSettingFilter {
    if (_listVechileSettingFilter is EqualUnmodifiableListView)
      return _listVechileSettingFilter;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVechileSettingFilter);
  }

  @override
  @JsonKey()
  final bool isLoadingMore;
  @override
  @JsonKey()
  final bool hasReachedMax;
  @override
  @JsonKey()
  final int currentPage;
  @override
  final String searchText;
  final Map<String, Map<String, bool>> _vehicleSwitchState;
  @override
  @JsonKey()
  Map<String, Map<String, bool>> get vehicleSwitchState {
    if (_vehicleSwitchState is EqualUnmodifiableMapView)
      return _vehicleSwitchState;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_vehicleSwitchState);
  }

  @override
  final bool? updateSuccess;
  @override
  final int total;

  @override
  String toString() {
    return 'BusSettingState(isLoading: $isLoading, failure: $failure, listVechileSetting: $listVechileSetting, listVechileSettingFilter: $listVechileSettingFilter, isLoadingMore: $isLoadingMore, hasReachedMax: $hasReachedMax, currentPage: $currentPage, searchText: $searchText, vehicleSwitchState: $vehicleSwitchState, updateSuccess: $updateSuccess, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BusSettingStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.failure, failure) || other.failure == failure) &&
            const DeepCollectionEquality()
                .equals(other._listVechileSetting, _listVechileSetting) &&
            const DeepCollectionEquality().equals(
                other._listVechileSettingFilter, _listVechileSettingFilter) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.hasReachedMax, hasReachedMax) ||
                other.hasReachedMax == hasReachedMax) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText) &&
            const DeepCollectionEquality()
                .equals(other._vehicleSwitchState, _vehicleSwitchState) &&
            (identical(other.updateSuccess, updateSuccess) ||
                other.updateSuccess == updateSuccess) &&
            (identical(other.total, total) || other.total == total));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      failure,
      const DeepCollectionEquality().hash(_listVechileSetting),
      const DeepCollectionEquality().hash(_listVechileSettingFilter),
      isLoadingMore,
      hasReachedMax,
      currentPage,
      searchText,
      const DeepCollectionEquality().hash(_vehicleSwitchState),
      updateSuccess,
      total);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BusSettingStateImplCopyWith<_$BusSettingStateImpl> get copyWith =>
      __$$BusSettingStateImplCopyWithImpl<_$BusSettingStateImpl>(
          this, _$identity);
}

abstract class _BusSettingState implements BusSettingState {
  const factory _BusSettingState(
      {required final bool isLoading,
      final ManageVehicleFailure? failure,
      required final List<VechileSetting> listVechileSetting,
      required final List<VechileSetting> listVechileSettingFilter,
      final bool isLoadingMore,
      final bool hasReachedMax,
      final int currentPage,
      required final String searchText,
      final Map<String, Map<String, bool>> vehicleSwitchState,
      final bool? updateSuccess,
      required final int total}) = _$BusSettingStateImpl;

  @override
  bool get isLoading;
  @override
  ManageVehicleFailure? get failure;
  @override
  List<VechileSetting> get listVechileSetting;
  @override
  List<VechileSetting> get listVechileSettingFilter;
  @override
  bool get isLoadingMore;
  @override
  bool get hasReachedMax;
  @override
  int get currentPage;
  @override
  String get searchText;
  @override
  Map<String, Map<String, bool>> get vehicleSwitchState;
  @override
  bool? get updateSuccess;
  @override
  int get total;
  @override
  @JsonKey(ignore: true)
  _$$BusSettingStateImplCopyWith<_$BusSettingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
