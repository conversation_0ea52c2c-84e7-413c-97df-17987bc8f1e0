// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'detail_edit_vehicle_group_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DetailEditVehicleGroupEvent {
  String get id => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) getDetail,
    required TResult Function(String name, String id) update,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? getDetail,
    TResult? Function(String name, String id)? update,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? getDetail,
    TResult Function(String name, String id)? update,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_Update value) update,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_Update value)? update,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_Update value)? update,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DetailEditVehicleGroupEventCopyWith<DetailEditVehicleGroupEvent>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DetailEditVehicleGroupEventCopyWith<$Res> {
  factory $DetailEditVehicleGroupEventCopyWith(
          DetailEditVehicleGroupEvent value,
          $Res Function(DetailEditVehicleGroupEvent) then) =
      _$DetailEditVehicleGroupEventCopyWithImpl<$Res,
          DetailEditVehicleGroupEvent>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class _$DetailEditVehicleGroupEventCopyWithImpl<$Res,
        $Val extends DetailEditVehicleGroupEvent>
    implements $DetailEditVehicleGroupEventCopyWith<$Res> {
  _$DetailEditVehicleGroupEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetDetailImplCopyWith<$Res>
    implements $DetailEditVehicleGroupEventCopyWith<$Res> {
  factory _$$GetDetailImplCopyWith(
          _$GetDetailImpl value, $Res Function(_$GetDetailImpl) then) =
      __$$GetDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$GetDetailImplCopyWithImpl<$Res>
    extends _$DetailEditVehicleGroupEventCopyWithImpl<$Res, _$GetDetailImpl>
    implements _$$GetDetailImplCopyWith<$Res> {
  __$$GetDetailImplCopyWithImpl(
      _$GetDetailImpl _value, $Res Function(_$GetDetailImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$GetDetailImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetDetailImpl implements _GetDetail {
  const _$GetDetailImpl({required this.id});

  @override
  final String id;

  @override
  String toString() {
    return 'DetailEditVehicleGroupEvent.getDetail(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetDetailImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetDetailImplCopyWith<_$GetDetailImpl> get copyWith =>
      __$$GetDetailImplCopyWithImpl<_$GetDetailImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) getDetail,
    required TResult Function(String name, String id) update,
  }) {
    return getDetail(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? getDetail,
    TResult? Function(String name, String id)? update,
  }) {
    return getDetail?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? getDetail,
    TResult Function(String name, String id)? update,
    required TResult orElse(),
  }) {
    if (getDetail != null) {
      return getDetail(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_Update value) update,
  }) {
    return getDetail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_Update value)? update,
  }) {
    return getDetail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_Update value)? update,
    required TResult orElse(),
  }) {
    if (getDetail != null) {
      return getDetail(this);
    }
    return orElse();
  }
}

abstract class _GetDetail implements DetailEditVehicleGroupEvent {
  const factory _GetDetail({required final String id}) = _$GetDetailImpl;

  @override
  String get id;
  @override
  @JsonKey(ignore: true)
  _$$GetDetailImplCopyWith<_$GetDetailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateImplCopyWith<$Res>
    implements $DetailEditVehicleGroupEventCopyWith<$Res> {
  factory _$$UpdateImplCopyWith(
          _$UpdateImpl value, $Res Function(_$UpdateImpl) then) =
      __$$UpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String id});
}

/// @nodoc
class __$$UpdateImplCopyWithImpl<$Res>
    extends _$DetailEditVehicleGroupEventCopyWithImpl<$Res, _$UpdateImpl>
    implements _$$UpdateImplCopyWith<$Res> {
  __$$UpdateImplCopyWithImpl(
      _$UpdateImpl _value, $Res Function(_$UpdateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? id = null,
  }) {
    return _then(_$UpdateImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateImpl implements _Update {
  const _$UpdateImpl({required this.name, required this.id});

  @override
  final String name;
  @override
  final String id;

  @override
  String toString() {
    return 'DetailEditVehicleGroupEvent.update(name: $name, id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, name, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateImplCopyWith<_$UpdateImpl> get copyWith =>
      __$$UpdateImplCopyWithImpl<_$UpdateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String id) getDetail,
    required TResult Function(String name, String id) update,
  }) {
    return update(name, id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String id)? getDetail,
    TResult? Function(String name, String id)? update,
  }) {
    return update?.call(name, id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String id)? getDetail,
    TResult Function(String name, String id)? update,
    required TResult orElse(),
  }) {
    if (update != null) {
      return update(name, id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetDetail value) getDetail,
    required TResult Function(_Update value) update,
  }) {
    return update(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetDetail value)? getDetail,
    TResult? Function(_Update value)? update,
  }) {
    return update?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetDetail value)? getDetail,
    TResult Function(_Update value)? update,
    required TResult orElse(),
  }) {
    if (update != null) {
      return update(this);
    }
    return orElse();
  }
}

abstract class _Update implements DetailEditVehicleGroupEvent {
  const factory _Update(
      {required final String name, required final String id}) = _$UpdateImpl;

  String get name;
  @override
  String get id;
  @override
  @JsonKey(ignore: true)
  _$$UpdateImplCopyWith<_$UpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DetailEditVehicleGroupState {
  MVehicleDetailGroupResponse? get detailVehicleGroup =>
      throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isUpdating => throw _privateConstructorUsedError;
  ManageVehicleFailure? get detailFail => throw _privateConstructorUsedError;
  ManageVehicleFailure? get updateFail => throw _privateConstructorUsedError;
  bool get isUpdatingSuccess => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DetailEditVehicleGroupStateCopyWith<DetailEditVehicleGroupState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DetailEditVehicleGroupStateCopyWith<$Res> {
  factory $DetailEditVehicleGroupStateCopyWith(
          DetailEditVehicleGroupState value,
          $Res Function(DetailEditVehicleGroupState) then) =
      _$DetailEditVehicleGroupStateCopyWithImpl<$Res,
          DetailEditVehicleGroupState>;
  @useResult
  $Res call(
      {MVehicleDetailGroupResponse? detailVehicleGroup,
      bool isLoading,
      bool isUpdating,
      ManageVehicleFailure? detailFail,
      ManageVehicleFailure? updateFail,
      bool isUpdatingSuccess});

  $MVehicleDetailGroupResponseCopyWith<$Res>? get detailVehicleGroup;
  $ManageVehicleFailureCopyWith<$Res>? get detailFail;
  $ManageVehicleFailureCopyWith<$Res>? get updateFail;
}

/// @nodoc
class _$DetailEditVehicleGroupStateCopyWithImpl<$Res,
        $Val extends DetailEditVehicleGroupState>
    implements $DetailEditVehicleGroupStateCopyWith<$Res> {
  _$DetailEditVehicleGroupStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? detailVehicleGroup = freezed,
    Object? isLoading = null,
    Object? isUpdating = null,
    Object? detailFail = freezed,
    Object? updateFail = freezed,
    Object? isUpdatingSuccess = null,
  }) {
    return _then(_value.copyWith(
      detailVehicleGroup: freezed == detailVehicleGroup
          ? _value.detailVehicleGroup
          : detailVehicleGroup // ignore: cast_nullable_to_non_nullable
              as MVehicleDetailGroupResponse?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isUpdating: null == isUpdating
          ? _value.isUpdating
          : isUpdating // ignore: cast_nullable_to_non_nullable
              as bool,
      detailFail: freezed == detailFail
          ? _value.detailFail
          : detailFail // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      updateFail: freezed == updateFail
          ? _value.updateFail
          : updateFail // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      isUpdatingSuccess: null == isUpdatingSuccess
          ? _value.isUpdatingSuccess
          : isUpdatingSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MVehicleDetailGroupResponseCopyWith<$Res>? get detailVehicleGroup {
    if (_value.detailVehicleGroup == null) {
      return null;
    }

    return $MVehicleDetailGroupResponseCopyWith<$Res>(
        _value.detailVehicleGroup!, (value) {
      return _then(_value.copyWith(detailVehicleGroup: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ManageVehicleFailureCopyWith<$Res>? get detailFail {
    if (_value.detailFail == null) {
      return null;
    }

    return $ManageVehicleFailureCopyWith<$Res>(_value.detailFail!, (value) {
      return _then(_value.copyWith(detailFail: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ManageVehicleFailureCopyWith<$Res>? get updateFail {
    if (_value.updateFail == null) {
      return null;
    }

    return $ManageVehicleFailureCopyWith<$Res>(_value.updateFail!, (value) {
      return _then(_value.copyWith(updateFail: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DetailEditVehicleGroupStateImplCopyWith<$Res>
    implements $DetailEditVehicleGroupStateCopyWith<$Res> {
  factory _$$DetailEditVehicleGroupStateImplCopyWith(
          _$DetailEditVehicleGroupStateImpl value,
          $Res Function(_$DetailEditVehicleGroupStateImpl) then) =
      __$$DetailEditVehicleGroupStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MVehicleDetailGroupResponse? detailVehicleGroup,
      bool isLoading,
      bool isUpdating,
      ManageVehicleFailure? detailFail,
      ManageVehicleFailure? updateFail,
      bool isUpdatingSuccess});

  @override
  $MVehicleDetailGroupResponseCopyWith<$Res>? get detailVehicleGroup;
  @override
  $ManageVehicleFailureCopyWith<$Res>? get detailFail;
  @override
  $ManageVehicleFailureCopyWith<$Res>? get updateFail;
}

/// @nodoc
class __$$DetailEditVehicleGroupStateImplCopyWithImpl<$Res>
    extends _$DetailEditVehicleGroupStateCopyWithImpl<$Res,
        _$DetailEditVehicleGroupStateImpl>
    implements _$$DetailEditVehicleGroupStateImplCopyWith<$Res> {
  __$$DetailEditVehicleGroupStateImplCopyWithImpl(
      _$DetailEditVehicleGroupStateImpl _value,
      $Res Function(_$DetailEditVehicleGroupStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? detailVehicleGroup = freezed,
    Object? isLoading = null,
    Object? isUpdating = null,
    Object? detailFail = freezed,
    Object? updateFail = freezed,
    Object? isUpdatingSuccess = null,
  }) {
    return _then(_$DetailEditVehicleGroupStateImpl(
      detailVehicleGroup: freezed == detailVehicleGroup
          ? _value.detailVehicleGroup
          : detailVehicleGroup // ignore: cast_nullable_to_non_nullable
              as MVehicleDetailGroupResponse?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isUpdating: null == isUpdating
          ? _value.isUpdating
          : isUpdating // ignore: cast_nullable_to_non_nullable
              as bool,
      detailFail: freezed == detailFail
          ? _value.detailFail
          : detailFail // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      updateFail: freezed == updateFail
          ? _value.updateFail
          : updateFail // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      isUpdatingSuccess: null == isUpdatingSuccess
          ? _value.isUpdatingSuccess
          : isUpdatingSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$DetailEditVehicleGroupStateImpl
    implements _DetailEditVehicleGroupState {
  const _$DetailEditVehicleGroupStateImpl(
      {required this.detailVehicleGroup,
      required this.isLoading,
      required this.isUpdating,
      required this.detailFail,
      required this.updateFail,
      required this.isUpdatingSuccess});

  @override
  final MVehicleDetailGroupResponse? detailVehicleGroup;
  @override
  final bool isLoading;
  @override
  final bool isUpdating;
  @override
  final ManageVehicleFailure? detailFail;
  @override
  final ManageVehicleFailure? updateFail;
  @override
  final bool isUpdatingSuccess;

  @override
  String toString() {
    return 'DetailEditVehicleGroupState(detailVehicleGroup: $detailVehicleGroup, isLoading: $isLoading, isUpdating: $isUpdating, detailFail: $detailFail, updateFail: $updateFail, isUpdatingSuccess: $isUpdatingSuccess)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DetailEditVehicleGroupStateImpl &&
            (identical(other.detailVehicleGroup, detailVehicleGroup) ||
                other.detailVehicleGroup == detailVehicleGroup) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isUpdating, isUpdating) ||
                other.isUpdating == isUpdating) &&
            (identical(other.detailFail, detailFail) ||
                other.detailFail == detailFail) &&
            (identical(other.updateFail, updateFail) ||
                other.updateFail == updateFail) &&
            (identical(other.isUpdatingSuccess, isUpdatingSuccess) ||
                other.isUpdatingSuccess == isUpdatingSuccess));
  }

  @override
  int get hashCode => Object.hash(runtimeType, detailVehicleGroup, isLoading,
      isUpdating, detailFail, updateFail, isUpdatingSuccess);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DetailEditVehicleGroupStateImplCopyWith<_$DetailEditVehicleGroupStateImpl>
      get copyWith => __$$DetailEditVehicleGroupStateImplCopyWithImpl<
          _$DetailEditVehicleGroupStateImpl>(this, _$identity);
}

abstract class _DetailEditVehicleGroupState
    implements DetailEditVehicleGroupState {
  const factory _DetailEditVehicleGroupState(
          {required final MVehicleDetailGroupResponse? detailVehicleGroup,
          required final bool isLoading,
          required final bool isUpdating,
          required final ManageVehicleFailure? detailFail,
          required final ManageVehicleFailure? updateFail,
          required final bool isUpdatingSuccess}) =
      _$DetailEditVehicleGroupStateImpl;

  @override
  MVehicleDetailGroupResponse? get detailVehicleGroup;
  @override
  bool get isLoading;
  @override
  bool get isUpdating;
  @override
  ManageVehicleFailure? get detailFail;
  @override
  ManageVehicleFailure? get updateFail;
  @override
  bool get isUpdatingSuccess;
  @override
  @JsonKey(ignore: true)
  _$$DetailEditVehicleGroupStateImplCopyWith<_$DetailEditVehicleGroupStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
