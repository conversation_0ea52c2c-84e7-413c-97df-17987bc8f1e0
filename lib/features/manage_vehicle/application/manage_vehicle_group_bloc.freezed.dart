// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_vehicle_group_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ManageVehicleGroupEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getAllVehicleGroup,
    required TResult Function(String searchKey) searchVehicleGroup,
    required TResult Function() clearSearchList,
    required TResult Function(String id) delete,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getAllVehicleGroup,
    TResult? Function(String searchKey)? searchVehicleGroup,
    TResult? Function()? clearSearchList,
    TResult? Function(String id)? delete,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getAllVehicleGroup,
    TResult Function(String searchKey)? searchVehicleGroup,
    TResult Function()? clearSearchList,
    TResult Function(String id)? delete,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetAllVehicleGroup value) getAllVehicleGroup,
    required TResult Function(_SearchList value) searchVehicleGroup,
    required TResult Function(_ClearSearchList value) clearSearchList,
    required TResult Function(_DeleteGroup value) delete,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetAllVehicleGroup value)? getAllVehicleGroup,
    TResult? Function(_SearchList value)? searchVehicleGroup,
    TResult? Function(_ClearSearchList value)? clearSearchList,
    TResult? Function(_DeleteGroup value)? delete,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetAllVehicleGroup value)? getAllVehicleGroup,
    TResult Function(_SearchList value)? searchVehicleGroup,
    TResult Function(_ClearSearchList value)? clearSearchList,
    TResult Function(_DeleteGroup value)? delete,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageVehicleGroupEventCopyWith<$Res> {
  factory $ManageVehicleGroupEventCopyWith(ManageVehicleGroupEvent value,
          $Res Function(ManageVehicleGroupEvent) then) =
      _$ManageVehicleGroupEventCopyWithImpl<$Res, ManageVehicleGroupEvent>;
}

/// @nodoc
class _$ManageVehicleGroupEventCopyWithImpl<$Res,
        $Val extends ManageVehicleGroupEvent>
    implements $ManageVehicleGroupEventCopyWith<$Res> {
  _$ManageVehicleGroupEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$GetAllVehicleGroupImplCopyWith<$Res> {
  factory _$$GetAllVehicleGroupImplCopyWith(_$GetAllVehicleGroupImpl value,
          $Res Function(_$GetAllVehicleGroupImpl) then) =
      __$$GetAllVehicleGroupImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetAllVehicleGroupImplCopyWithImpl<$Res>
    extends _$ManageVehicleGroupEventCopyWithImpl<$Res,
        _$GetAllVehicleGroupImpl>
    implements _$$GetAllVehicleGroupImplCopyWith<$Res> {
  __$$GetAllVehicleGroupImplCopyWithImpl(_$GetAllVehicleGroupImpl _value,
      $Res Function(_$GetAllVehicleGroupImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$GetAllVehicleGroupImpl implements _GetAllVehicleGroup {
  const _$GetAllVehicleGroupImpl();

  @override
  String toString() {
    return 'ManageVehicleGroupEvent.getAllVehicleGroup()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetAllVehicleGroupImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getAllVehicleGroup,
    required TResult Function(String searchKey) searchVehicleGroup,
    required TResult Function() clearSearchList,
    required TResult Function(String id) delete,
  }) {
    return getAllVehicleGroup();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getAllVehicleGroup,
    TResult? Function(String searchKey)? searchVehicleGroup,
    TResult? Function()? clearSearchList,
    TResult? Function(String id)? delete,
  }) {
    return getAllVehicleGroup?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getAllVehicleGroup,
    TResult Function(String searchKey)? searchVehicleGroup,
    TResult Function()? clearSearchList,
    TResult Function(String id)? delete,
    required TResult orElse(),
  }) {
    if (getAllVehicleGroup != null) {
      return getAllVehicleGroup();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetAllVehicleGroup value) getAllVehicleGroup,
    required TResult Function(_SearchList value) searchVehicleGroup,
    required TResult Function(_ClearSearchList value) clearSearchList,
    required TResult Function(_DeleteGroup value) delete,
  }) {
    return getAllVehicleGroup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetAllVehicleGroup value)? getAllVehicleGroup,
    TResult? Function(_SearchList value)? searchVehicleGroup,
    TResult? Function(_ClearSearchList value)? clearSearchList,
    TResult? Function(_DeleteGroup value)? delete,
  }) {
    return getAllVehicleGroup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetAllVehicleGroup value)? getAllVehicleGroup,
    TResult Function(_SearchList value)? searchVehicleGroup,
    TResult Function(_ClearSearchList value)? clearSearchList,
    TResult Function(_DeleteGroup value)? delete,
    required TResult orElse(),
  }) {
    if (getAllVehicleGroup != null) {
      return getAllVehicleGroup(this);
    }
    return orElse();
  }
}

abstract class _GetAllVehicleGroup implements ManageVehicleGroupEvent {
  const factory _GetAllVehicleGroup() = _$GetAllVehicleGroupImpl;
}

/// @nodoc
abstract class _$$SearchListImplCopyWith<$Res> {
  factory _$$SearchListImplCopyWith(
          _$SearchListImpl value, $Res Function(_$SearchListImpl) then) =
      __$$SearchListImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String searchKey});
}

/// @nodoc
class __$$SearchListImplCopyWithImpl<$Res>
    extends _$ManageVehicleGroupEventCopyWithImpl<$Res, _$SearchListImpl>
    implements _$$SearchListImplCopyWith<$Res> {
  __$$SearchListImplCopyWithImpl(
      _$SearchListImpl _value, $Res Function(_$SearchListImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchKey = null,
  }) {
    return _then(_$SearchListImpl(
      searchKey: null == searchKey
          ? _value.searchKey
          : searchKey // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchListImpl implements _SearchList {
  const _$SearchListImpl({required this.searchKey});

  @override
  final String searchKey;

  @override
  String toString() {
    return 'ManageVehicleGroupEvent.searchVehicleGroup(searchKey: $searchKey)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchListImpl &&
            (identical(other.searchKey, searchKey) ||
                other.searchKey == searchKey));
  }

  @override
  int get hashCode => Object.hash(runtimeType, searchKey);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchListImplCopyWith<_$SearchListImpl> get copyWith =>
      __$$SearchListImplCopyWithImpl<_$SearchListImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getAllVehicleGroup,
    required TResult Function(String searchKey) searchVehicleGroup,
    required TResult Function() clearSearchList,
    required TResult Function(String id) delete,
  }) {
    return searchVehicleGroup(searchKey);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getAllVehicleGroup,
    TResult? Function(String searchKey)? searchVehicleGroup,
    TResult? Function()? clearSearchList,
    TResult? Function(String id)? delete,
  }) {
    return searchVehicleGroup?.call(searchKey);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getAllVehicleGroup,
    TResult Function(String searchKey)? searchVehicleGroup,
    TResult Function()? clearSearchList,
    TResult Function(String id)? delete,
    required TResult orElse(),
  }) {
    if (searchVehicleGroup != null) {
      return searchVehicleGroup(searchKey);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetAllVehicleGroup value) getAllVehicleGroup,
    required TResult Function(_SearchList value) searchVehicleGroup,
    required TResult Function(_ClearSearchList value) clearSearchList,
    required TResult Function(_DeleteGroup value) delete,
  }) {
    return searchVehicleGroup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetAllVehicleGroup value)? getAllVehicleGroup,
    TResult? Function(_SearchList value)? searchVehicleGroup,
    TResult? Function(_ClearSearchList value)? clearSearchList,
    TResult? Function(_DeleteGroup value)? delete,
  }) {
    return searchVehicleGroup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetAllVehicleGroup value)? getAllVehicleGroup,
    TResult Function(_SearchList value)? searchVehicleGroup,
    TResult Function(_ClearSearchList value)? clearSearchList,
    TResult Function(_DeleteGroup value)? delete,
    required TResult orElse(),
  }) {
    if (searchVehicleGroup != null) {
      return searchVehicleGroup(this);
    }
    return orElse();
  }
}

abstract class _SearchList implements ManageVehicleGroupEvent {
  const factory _SearchList({required final String searchKey}) =
      _$SearchListImpl;

  String get searchKey;
  @JsonKey(ignore: true)
  _$$SearchListImplCopyWith<_$SearchListImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearSearchListImplCopyWith<$Res> {
  factory _$$ClearSearchListImplCopyWith(_$ClearSearchListImpl value,
          $Res Function(_$ClearSearchListImpl) then) =
      __$$ClearSearchListImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearSearchListImplCopyWithImpl<$Res>
    extends _$ManageVehicleGroupEventCopyWithImpl<$Res, _$ClearSearchListImpl>
    implements _$$ClearSearchListImplCopyWith<$Res> {
  __$$ClearSearchListImplCopyWithImpl(
      _$ClearSearchListImpl _value, $Res Function(_$ClearSearchListImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearSearchListImpl implements _ClearSearchList {
  const _$ClearSearchListImpl();

  @override
  String toString() {
    return 'ManageVehicleGroupEvent.clearSearchList()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearSearchListImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getAllVehicleGroup,
    required TResult Function(String searchKey) searchVehicleGroup,
    required TResult Function() clearSearchList,
    required TResult Function(String id) delete,
  }) {
    return clearSearchList();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getAllVehicleGroup,
    TResult? Function(String searchKey)? searchVehicleGroup,
    TResult? Function()? clearSearchList,
    TResult? Function(String id)? delete,
  }) {
    return clearSearchList?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getAllVehicleGroup,
    TResult Function(String searchKey)? searchVehicleGroup,
    TResult Function()? clearSearchList,
    TResult Function(String id)? delete,
    required TResult orElse(),
  }) {
    if (clearSearchList != null) {
      return clearSearchList();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetAllVehicleGroup value) getAllVehicleGroup,
    required TResult Function(_SearchList value) searchVehicleGroup,
    required TResult Function(_ClearSearchList value) clearSearchList,
    required TResult Function(_DeleteGroup value) delete,
  }) {
    return clearSearchList(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetAllVehicleGroup value)? getAllVehicleGroup,
    TResult? Function(_SearchList value)? searchVehicleGroup,
    TResult? Function(_ClearSearchList value)? clearSearchList,
    TResult? Function(_DeleteGroup value)? delete,
  }) {
    return clearSearchList?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetAllVehicleGroup value)? getAllVehicleGroup,
    TResult Function(_SearchList value)? searchVehicleGroup,
    TResult Function(_ClearSearchList value)? clearSearchList,
    TResult Function(_DeleteGroup value)? delete,
    required TResult orElse(),
  }) {
    if (clearSearchList != null) {
      return clearSearchList(this);
    }
    return orElse();
  }
}

abstract class _ClearSearchList implements ManageVehicleGroupEvent {
  const factory _ClearSearchList() = _$ClearSearchListImpl;
}

/// @nodoc
abstract class _$$DeleteGroupImplCopyWith<$Res> {
  factory _$$DeleteGroupImplCopyWith(
          _$DeleteGroupImpl value, $Res Function(_$DeleteGroupImpl) then) =
      __$$DeleteGroupImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeleteGroupImplCopyWithImpl<$Res>
    extends _$ManageVehicleGroupEventCopyWithImpl<$Res, _$DeleteGroupImpl>
    implements _$$DeleteGroupImplCopyWith<$Res> {
  __$$DeleteGroupImplCopyWithImpl(
      _$DeleteGroupImpl _value, $Res Function(_$DeleteGroupImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeleteGroupImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteGroupImpl implements _DeleteGroup {
  const _$DeleteGroupImpl({required this.id});

  @override
  final String id;

  @override
  String toString() {
    return 'ManageVehicleGroupEvent.delete(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteGroupImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteGroupImplCopyWith<_$DeleteGroupImpl> get copyWith =>
      __$$DeleteGroupImplCopyWithImpl<_$DeleteGroupImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getAllVehicleGroup,
    required TResult Function(String searchKey) searchVehicleGroup,
    required TResult Function() clearSearchList,
    required TResult Function(String id) delete,
  }) {
    return delete(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getAllVehicleGroup,
    TResult? Function(String searchKey)? searchVehicleGroup,
    TResult? Function()? clearSearchList,
    TResult? Function(String id)? delete,
  }) {
    return delete?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getAllVehicleGroup,
    TResult Function(String searchKey)? searchVehicleGroup,
    TResult Function()? clearSearchList,
    TResult Function(String id)? delete,
    required TResult orElse(),
  }) {
    if (delete != null) {
      return delete(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetAllVehicleGroup value) getAllVehicleGroup,
    required TResult Function(_SearchList value) searchVehicleGroup,
    required TResult Function(_ClearSearchList value) clearSearchList,
    required TResult Function(_DeleteGroup value) delete,
  }) {
    return delete(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetAllVehicleGroup value)? getAllVehicleGroup,
    TResult? Function(_SearchList value)? searchVehicleGroup,
    TResult? Function(_ClearSearchList value)? clearSearchList,
    TResult? Function(_DeleteGroup value)? delete,
  }) {
    return delete?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetAllVehicleGroup value)? getAllVehicleGroup,
    TResult Function(_SearchList value)? searchVehicleGroup,
    TResult Function(_ClearSearchList value)? clearSearchList,
    TResult Function(_DeleteGroup value)? delete,
    required TResult orElse(),
  }) {
    if (delete != null) {
      return delete(this);
    }
    return orElse();
  }
}

abstract class _DeleteGroup implements ManageVehicleGroupEvent {
  const factory _DeleteGroup({required final String id}) = _$DeleteGroupImpl;

  String get id;
  @JsonKey(ignore: true)
  _$$DeleteGroupImplCopyWith<_$DeleteGroupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ManageVehicleGroupState {
  List<MVehicleGroup> get listVehicleGroup =>
      throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  ManageVehicleFailure? get failure => throw _privateConstructorUsedError;
  bool get isDeleting => throw _privateConstructorUsedError;
  bool get isDeleteSuccess => throw _privateConstructorUsedError;
  ManageVehicleFailure? get deleteFailure => throw _privateConstructorUsedError;
  List<MVehicleGroup> get listVehicleGroupSearched =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ManageVehicleGroupStateCopyWith<ManageVehicleGroupState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageVehicleGroupStateCopyWith<$Res> {
  factory $ManageVehicleGroupStateCopyWith(ManageVehicleGroupState value,
          $Res Function(ManageVehicleGroupState) then) =
      _$ManageVehicleGroupStateCopyWithImpl<$Res, ManageVehicleGroupState>;
  @useResult
  $Res call(
      {List<MVehicleGroup> listVehicleGroup,
      bool isLoading,
      ManageVehicleFailure? failure,
      bool isDeleting,
      bool isDeleteSuccess,
      ManageVehicleFailure? deleteFailure,
      List<MVehicleGroup> listVehicleGroupSearched});

  $ManageVehicleFailureCopyWith<$Res>? get failure;
  $ManageVehicleFailureCopyWith<$Res>? get deleteFailure;
}

/// @nodoc
class _$ManageVehicleGroupStateCopyWithImpl<$Res,
        $Val extends ManageVehicleGroupState>
    implements $ManageVehicleGroupStateCopyWith<$Res> {
  _$ManageVehicleGroupStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicleGroup = null,
    Object? isLoading = null,
    Object? failure = freezed,
    Object? isDeleting = null,
    Object? isDeleteSuccess = null,
    Object? deleteFailure = freezed,
    Object? listVehicleGroupSearched = null,
  }) {
    return _then(_value.copyWith(
      listVehicleGroup: null == listVehicleGroup
          ? _value.listVehicleGroup
          : listVehicleGroup // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroup>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      failure: freezed == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      isDeleting: null == isDeleting
          ? _value.isDeleting
          : isDeleting // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeleteSuccess: null == isDeleteSuccess
          ? _value.isDeleteSuccess
          : isDeleteSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      deleteFailure: freezed == deleteFailure
          ? _value.deleteFailure
          : deleteFailure // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      listVehicleGroupSearched: null == listVehicleGroupSearched
          ? _value.listVehicleGroupSearched
          : listVehicleGroupSearched // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroup>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ManageVehicleFailureCopyWith<$Res>? get failure {
    if (_value.failure == null) {
      return null;
    }

    return $ManageVehicleFailureCopyWith<$Res>(_value.failure!, (value) {
      return _then(_value.copyWith(failure: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ManageVehicleFailureCopyWith<$Res>? get deleteFailure {
    if (_value.deleteFailure == null) {
      return null;
    }

    return $ManageVehicleFailureCopyWith<$Res>(_value.deleteFailure!, (value) {
      return _then(_value.copyWith(deleteFailure: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ManageVehicleGroupStateImplCopyWith<$Res>
    implements $ManageVehicleGroupStateCopyWith<$Res> {
  factory _$$ManageVehicleGroupStateImplCopyWith(
          _$ManageVehicleGroupStateImpl value,
          $Res Function(_$ManageVehicleGroupStateImpl) then) =
      __$$ManageVehicleGroupStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<MVehicleGroup> listVehicleGroup,
      bool isLoading,
      ManageVehicleFailure? failure,
      bool isDeleting,
      bool isDeleteSuccess,
      ManageVehicleFailure? deleteFailure,
      List<MVehicleGroup> listVehicleGroupSearched});

  @override
  $ManageVehicleFailureCopyWith<$Res>? get failure;
  @override
  $ManageVehicleFailureCopyWith<$Res>? get deleteFailure;
}

/// @nodoc
class __$$ManageVehicleGroupStateImplCopyWithImpl<$Res>
    extends _$ManageVehicleGroupStateCopyWithImpl<$Res,
        _$ManageVehicleGroupStateImpl>
    implements _$$ManageVehicleGroupStateImplCopyWith<$Res> {
  __$$ManageVehicleGroupStateImplCopyWithImpl(
      _$ManageVehicleGroupStateImpl _value,
      $Res Function(_$ManageVehicleGroupStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicleGroup = null,
    Object? isLoading = null,
    Object? failure = freezed,
    Object? isDeleting = null,
    Object? isDeleteSuccess = null,
    Object? deleteFailure = freezed,
    Object? listVehicleGroupSearched = null,
  }) {
    return _then(_$ManageVehicleGroupStateImpl(
      listVehicleGroup: null == listVehicleGroup
          ? _value._listVehicleGroup
          : listVehicleGroup // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroup>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      failure: freezed == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      isDeleting: null == isDeleting
          ? _value.isDeleting
          : isDeleting // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeleteSuccess: null == isDeleteSuccess
          ? _value.isDeleteSuccess
          : isDeleteSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      deleteFailure: freezed == deleteFailure
          ? _value.deleteFailure
          : deleteFailure // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      listVehicleGroupSearched: null == listVehicleGroupSearched
          ? _value._listVehicleGroupSearched
          : listVehicleGroupSearched // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroup>,
    ));
  }
}

/// @nodoc

class _$ManageVehicleGroupStateImpl implements _ManageVehicleGroupState {
  const _$ManageVehicleGroupStateImpl(
      {required final List<MVehicleGroup> listVehicleGroup,
      required this.isLoading,
      required this.failure,
      required this.isDeleting,
      required this.isDeleteSuccess,
      required this.deleteFailure,
      required final List<MVehicleGroup> listVehicleGroupSearched})
      : _listVehicleGroup = listVehicleGroup,
        _listVehicleGroupSearched = listVehicleGroupSearched;

  final List<MVehicleGroup> _listVehicleGroup;
  @override
  List<MVehicleGroup> get listVehicleGroup {
    if (_listVehicleGroup is EqualUnmodifiableListView)
      return _listVehicleGroup;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicleGroup);
  }

  @override
  final bool isLoading;
  @override
  final ManageVehicleFailure? failure;
  @override
  final bool isDeleting;
  @override
  final bool isDeleteSuccess;
  @override
  final ManageVehicleFailure? deleteFailure;
  final List<MVehicleGroup> _listVehicleGroupSearched;
  @override
  List<MVehicleGroup> get listVehicleGroupSearched {
    if (_listVehicleGroupSearched is EqualUnmodifiableListView)
      return _listVehicleGroupSearched;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicleGroupSearched);
  }

  @override
  String toString() {
    return 'ManageVehicleGroupState(listVehicleGroup: $listVehicleGroup, isLoading: $isLoading, failure: $failure, isDeleting: $isDeleting, isDeleteSuccess: $isDeleteSuccess, deleteFailure: $deleteFailure, listVehicleGroupSearched: $listVehicleGroupSearched)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ManageVehicleGroupStateImpl &&
            const DeepCollectionEquality()
                .equals(other._listVehicleGroup, _listVehicleGroup) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.failure, failure) || other.failure == failure) &&
            (identical(other.isDeleting, isDeleting) ||
                other.isDeleting == isDeleting) &&
            (identical(other.isDeleteSuccess, isDeleteSuccess) ||
                other.isDeleteSuccess == isDeleteSuccess) &&
            (identical(other.deleteFailure, deleteFailure) ||
                other.deleteFailure == deleteFailure) &&
            const DeepCollectionEquality().equals(
                other._listVehicleGroupSearched, _listVehicleGroupSearched));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_listVehicleGroup),
      isLoading,
      failure,
      isDeleting,
      isDeleteSuccess,
      deleteFailure,
      const DeepCollectionEquality().hash(_listVehicleGroupSearched));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ManageVehicleGroupStateImplCopyWith<_$ManageVehicleGroupStateImpl>
      get copyWith => __$$ManageVehicleGroupStateImplCopyWithImpl<
          _$ManageVehicleGroupStateImpl>(this, _$identity);
}

abstract class _ManageVehicleGroupState implements ManageVehicleGroupState {
  const factory _ManageVehicleGroupState(
          {required final List<MVehicleGroup> listVehicleGroup,
          required final bool isLoading,
          required final ManageVehicleFailure? failure,
          required final bool isDeleting,
          required final bool isDeleteSuccess,
          required final ManageVehicleFailure? deleteFailure,
          required final List<MVehicleGroup> listVehicleGroupSearched}) =
      _$ManageVehicleGroupStateImpl;

  @override
  List<MVehicleGroup> get listVehicleGroup;
  @override
  bool get isLoading;
  @override
  ManageVehicleFailure? get failure;
  @override
  bool get isDeleting;
  @override
  bool get isDeleteSuccess;
  @override
  ManageVehicleFailure? get deleteFailure;
  @override
  List<MVehicleGroup> get listVehicleGroupSearched;
  @override
  @JsonKey(ignore: true)
  _$$ManageVehicleGroupStateImplCopyWith<_$ManageVehicleGroupStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
