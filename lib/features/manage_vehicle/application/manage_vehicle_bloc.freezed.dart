// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_vehicle_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ManageVehicleEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getAllVehicle,
    required TResult Function(String searchKey) searchVehicle,
    required TResult Function() clearSearchList,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getAllVehicle,
    TResult? Function(String searchKey)? searchVehicle,
    TResult? Function()? clearSearchList,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getAllVehicle,
    TResult Function(String searchKey)? searchVehicle,
    TResult Function()? clearSearchList,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetAllVehicle value) getAllVehicle,
    required TResult Function(_SearchList value) searchVehicle,
    required TResult Function(_ClearSearchList value) clearSearchList,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetAllVehicle value)? getAllVehicle,
    TResult? Function(_SearchList value)? searchVehicle,
    TResult? Function(_ClearSearchList value)? clearSearchList,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetAllVehicle value)? getAllVehicle,
    TResult Function(_SearchList value)? searchVehicle,
    TResult Function(_ClearSearchList value)? clearSearchList,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageVehicleEventCopyWith<$Res> {
  factory $ManageVehicleEventCopyWith(
          ManageVehicleEvent value, $Res Function(ManageVehicleEvent) then) =
      _$ManageVehicleEventCopyWithImpl<$Res, ManageVehicleEvent>;
}

/// @nodoc
class _$ManageVehicleEventCopyWithImpl<$Res, $Val extends ManageVehicleEvent>
    implements $ManageVehicleEventCopyWith<$Res> {
  _$ManageVehicleEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$GetAllVehicleImplCopyWith<$Res> {
  factory _$$GetAllVehicleImplCopyWith(
          _$GetAllVehicleImpl value, $Res Function(_$GetAllVehicleImpl) then) =
      __$$GetAllVehicleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetAllVehicleImplCopyWithImpl<$Res>
    extends _$ManageVehicleEventCopyWithImpl<$Res, _$GetAllVehicleImpl>
    implements _$$GetAllVehicleImplCopyWith<$Res> {
  __$$GetAllVehicleImplCopyWithImpl(
      _$GetAllVehicleImpl _value, $Res Function(_$GetAllVehicleImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$GetAllVehicleImpl implements _GetAllVehicle {
  const _$GetAllVehicleImpl();

  @override
  String toString() {
    return 'ManageVehicleEvent.getAllVehicle()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetAllVehicleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getAllVehicle,
    required TResult Function(String searchKey) searchVehicle,
    required TResult Function() clearSearchList,
  }) {
    return getAllVehicle();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getAllVehicle,
    TResult? Function(String searchKey)? searchVehicle,
    TResult? Function()? clearSearchList,
  }) {
    return getAllVehicle?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getAllVehicle,
    TResult Function(String searchKey)? searchVehicle,
    TResult Function()? clearSearchList,
    required TResult orElse(),
  }) {
    if (getAllVehicle != null) {
      return getAllVehicle();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetAllVehicle value) getAllVehicle,
    required TResult Function(_SearchList value) searchVehicle,
    required TResult Function(_ClearSearchList value) clearSearchList,
  }) {
    return getAllVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetAllVehicle value)? getAllVehicle,
    TResult? Function(_SearchList value)? searchVehicle,
    TResult? Function(_ClearSearchList value)? clearSearchList,
  }) {
    return getAllVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetAllVehicle value)? getAllVehicle,
    TResult Function(_SearchList value)? searchVehicle,
    TResult Function(_ClearSearchList value)? clearSearchList,
    required TResult orElse(),
  }) {
    if (getAllVehicle != null) {
      return getAllVehicle(this);
    }
    return orElse();
  }
}

abstract class _GetAllVehicle implements ManageVehicleEvent {
  const factory _GetAllVehicle() = _$GetAllVehicleImpl;
}

/// @nodoc
abstract class _$$SearchListImplCopyWith<$Res> {
  factory _$$SearchListImplCopyWith(
          _$SearchListImpl value, $Res Function(_$SearchListImpl) then) =
      __$$SearchListImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String searchKey});
}

/// @nodoc
class __$$SearchListImplCopyWithImpl<$Res>
    extends _$ManageVehicleEventCopyWithImpl<$Res, _$SearchListImpl>
    implements _$$SearchListImplCopyWith<$Res> {
  __$$SearchListImplCopyWithImpl(
      _$SearchListImpl _value, $Res Function(_$SearchListImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchKey = null,
  }) {
    return _then(_$SearchListImpl(
      searchKey: null == searchKey
          ? _value.searchKey
          : searchKey // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchListImpl implements _SearchList {
  const _$SearchListImpl({required this.searchKey});

  @override
  final String searchKey;

  @override
  String toString() {
    return 'ManageVehicleEvent.searchVehicle(searchKey: $searchKey)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchListImpl &&
            (identical(other.searchKey, searchKey) ||
                other.searchKey == searchKey));
  }

  @override
  int get hashCode => Object.hash(runtimeType, searchKey);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchListImplCopyWith<_$SearchListImpl> get copyWith =>
      __$$SearchListImplCopyWithImpl<_$SearchListImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getAllVehicle,
    required TResult Function(String searchKey) searchVehicle,
    required TResult Function() clearSearchList,
  }) {
    return searchVehicle(searchKey);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getAllVehicle,
    TResult? Function(String searchKey)? searchVehicle,
    TResult? Function()? clearSearchList,
  }) {
    return searchVehicle?.call(searchKey);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getAllVehicle,
    TResult Function(String searchKey)? searchVehicle,
    TResult Function()? clearSearchList,
    required TResult orElse(),
  }) {
    if (searchVehicle != null) {
      return searchVehicle(searchKey);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetAllVehicle value) getAllVehicle,
    required TResult Function(_SearchList value) searchVehicle,
    required TResult Function(_ClearSearchList value) clearSearchList,
  }) {
    return searchVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetAllVehicle value)? getAllVehicle,
    TResult? Function(_SearchList value)? searchVehicle,
    TResult? Function(_ClearSearchList value)? clearSearchList,
  }) {
    return searchVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetAllVehicle value)? getAllVehicle,
    TResult Function(_SearchList value)? searchVehicle,
    TResult Function(_ClearSearchList value)? clearSearchList,
    required TResult orElse(),
  }) {
    if (searchVehicle != null) {
      return searchVehicle(this);
    }
    return orElse();
  }
}

abstract class _SearchList implements ManageVehicleEvent {
  const factory _SearchList({required final String searchKey}) =
      _$SearchListImpl;

  String get searchKey;
  @JsonKey(ignore: true)
  _$$SearchListImplCopyWith<_$SearchListImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearSearchListImplCopyWith<$Res> {
  factory _$$ClearSearchListImplCopyWith(_$ClearSearchListImpl value,
          $Res Function(_$ClearSearchListImpl) then) =
      __$$ClearSearchListImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearSearchListImplCopyWithImpl<$Res>
    extends _$ManageVehicleEventCopyWithImpl<$Res, _$ClearSearchListImpl>
    implements _$$ClearSearchListImplCopyWith<$Res> {
  __$$ClearSearchListImplCopyWithImpl(
      _$ClearSearchListImpl _value, $Res Function(_$ClearSearchListImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearSearchListImpl implements _ClearSearchList {
  const _$ClearSearchListImpl();

  @override
  String toString() {
    return 'ManageVehicleEvent.clearSearchList()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearSearchListImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getAllVehicle,
    required TResult Function(String searchKey) searchVehicle,
    required TResult Function() clearSearchList,
  }) {
    return clearSearchList();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getAllVehicle,
    TResult? Function(String searchKey)? searchVehicle,
    TResult? Function()? clearSearchList,
  }) {
    return clearSearchList?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getAllVehicle,
    TResult Function(String searchKey)? searchVehicle,
    TResult Function()? clearSearchList,
    required TResult orElse(),
  }) {
    if (clearSearchList != null) {
      return clearSearchList();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetAllVehicle value) getAllVehicle,
    required TResult Function(_SearchList value) searchVehicle,
    required TResult Function(_ClearSearchList value) clearSearchList,
  }) {
    return clearSearchList(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetAllVehicle value)? getAllVehicle,
    TResult? Function(_SearchList value)? searchVehicle,
    TResult? Function(_ClearSearchList value)? clearSearchList,
  }) {
    return clearSearchList?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetAllVehicle value)? getAllVehicle,
    TResult Function(_SearchList value)? searchVehicle,
    TResult Function(_ClearSearchList value)? clearSearchList,
    required TResult orElse(),
  }) {
    if (clearSearchList != null) {
      return clearSearchList(this);
    }
    return orElse();
  }
}

abstract class _ClearSearchList implements ManageVehicleEvent {
  const factory _ClearSearchList() = _$ClearSearchListImpl;
}

/// @nodoc
mixin _$ManageVehicleState {
  List<MVehicle> get listVehicle => throw _privateConstructorUsedError;
  List<MVehicleGroupDTO> get listGroupVehicle =>
      throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  ManageVehicleFailure? get failure => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError; //Search
  List<MVehicleGroupDTO> get listGroupVehicleSearched =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ManageVehicleStateCopyWith<ManageVehicleState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageVehicleStateCopyWith<$Res> {
  factory $ManageVehicleStateCopyWith(
          ManageVehicleState value, $Res Function(ManageVehicleState) then) =
      _$ManageVehicleStateCopyWithImpl<$Res, ManageVehicleState>;
  @useResult
  $Res call(
      {List<MVehicle> listVehicle,
      List<MVehicleGroupDTO> listGroupVehicle,
      bool isLoading,
      ManageVehicleFailure? failure,
      int total,
      List<MVehicleGroupDTO> listGroupVehicleSearched});

  $ManageVehicleFailureCopyWith<$Res>? get failure;
}

/// @nodoc
class _$ManageVehicleStateCopyWithImpl<$Res, $Val extends ManageVehicleState>
    implements $ManageVehicleStateCopyWith<$Res> {
  _$ManageVehicleStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicle = null,
    Object? listGroupVehicle = null,
    Object? isLoading = null,
    Object? failure = freezed,
    Object? total = null,
    Object? listGroupVehicleSearched = null,
  }) {
    return _then(_value.copyWith(
      listVehicle: null == listVehicle
          ? _value.listVehicle
          : listVehicle // ignore: cast_nullable_to_non_nullable
              as List<MVehicle>,
      listGroupVehicle: null == listGroupVehicle
          ? _value.listGroupVehicle
          : listGroupVehicle // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroupDTO>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      failure: freezed == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      listGroupVehicleSearched: null == listGroupVehicleSearched
          ? _value.listGroupVehicleSearched
          : listGroupVehicleSearched // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroupDTO>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ManageVehicleFailureCopyWith<$Res>? get failure {
    if (_value.failure == null) {
      return null;
    }

    return $ManageVehicleFailureCopyWith<$Res>(_value.failure!, (value) {
      return _then(_value.copyWith(failure: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ManageVehicleStateImplCopyWith<$Res>
    implements $ManageVehicleStateCopyWith<$Res> {
  factory _$$ManageVehicleStateImplCopyWith(_$ManageVehicleStateImpl value,
          $Res Function(_$ManageVehicleStateImpl) then) =
      __$$ManageVehicleStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<MVehicle> listVehicle,
      List<MVehicleGroupDTO> listGroupVehicle,
      bool isLoading,
      ManageVehicleFailure? failure,
      int total,
      List<MVehicleGroupDTO> listGroupVehicleSearched});

  @override
  $ManageVehicleFailureCopyWith<$Res>? get failure;
}

/// @nodoc
class __$$ManageVehicleStateImplCopyWithImpl<$Res>
    extends _$ManageVehicleStateCopyWithImpl<$Res, _$ManageVehicleStateImpl>
    implements _$$ManageVehicleStateImplCopyWith<$Res> {
  __$$ManageVehicleStateImplCopyWithImpl(_$ManageVehicleStateImpl _value,
      $Res Function(_$ManageVehicleStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listVehicle = null,
    Object? listGroupVehicle = null,
    Object? isLoading = null,
    Object? failure = freezed,
    Object? total = null,
    Object? listGroupVehicleSearched = null,
  }) {
    return _then(_$ManageVehicleStateImpl(
      listVehicle: null == listVehicle
          ? _value._listVehicle
          : listVehicle // ignore: cast_nullable_to_non_nullable
              as List<MVehicle>,
      listGroupVehicle: null == listGroupVehicle
          ? _value._listGroupVehicle
          : listGroupVehicle // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroupDTO>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      failure: freezed == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      listGroupVehicleSearched: null == listGroupVehicleSearched
          ? _value._listGroupVehicleSearched
          : listGroupVehicleSearched // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroupDTO>,
    ));
  }
}

/// @nodoc

class _$ManageVehicleStateImpl implements _ManageVehicleState {
  const _$ManageVehicleStateImpl(
      {required final List<MVehicle> listVehicle,
      required final List<MVehicleGroupDTO> listGroupVehicle,
      required this.isLoading,
      required this.failure,
      required this.total,
      required final List<MVehicleGroupDTO> listGroupVehicleSearched})
      : _listVehicle = listVehicle,
        _listGroupVehicle = listGroupVehicle,
        _listGroupVehicleSearched = listGroupVehicleSearched;

  final List<MVehicle> _listVehicle;
  @override
  List<MVehicle> get listVehicle {
    if (_listVehicle is EqualUnmodifiableListView) return _listVehicle;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicle);
  }

  final List<MVehicleGroupDTO> _listGroupVehicle;
  @override
  List<MVehicleGroupDTO> get listGroupVehicle {
    if (_listGroupVehicle is EqualUnmodifiableListView)
      return _listGroupVehicle;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listGroupVehicle);
  }

  @override
  final bool isLoading;
  @override
  final ManageVehicleFailure? failure;
  @override
  final int total;
//Search
  final List<MVehicleGroupDTO> _listGroupVehicleSearched;
//Search
  @override
  List<MVehicleGroupDTO> get listGroupVehicleSearched {
    if (_listGroupVehicleSearched is EqualUnmodifiableListView)
      return _listGroupVehicleSearched;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listGroupVehicleSearched);
  }

  @override
  String toString() {
    return 'ManageVehicleState(listVehicle: $listVehicle, listGroupVehicle: $listGroupVehicle, isLoading: $isLoading, failure: $failure, total: $total, listGroupVehicleSearched: $listGroupVehicleSearched)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ManageVehicleStateImpl &&
            const DeepCollectionEquality()
                .equals(other._listVehicle, _listVehicle) &&
            const DeepCollectionEquality()
                .equals(other._listGroupVehicle, _listGroupVehicle) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.failure, failure) || other.failure == failure) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(
                other._listGroupVehicleSearched, _listGroupVehicleSearched));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_listVehicle),
      const DeepCollectionEquality().hash(_listGroupVehicle),
      isLoading,
      failure,
      total,
      const DeepCollectionEquality().hash(_listGroupVehicleSearched));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ManageVehicleStateImplCopyWith<_$ManageVehicleStateImpl> get copyWith =>
      __$$ManageVehicleStateImplCopyWithImpl<_$ManageVehicleStateImpl>(
          this, _$identity);
}

abstract class _ManageVehicleState implements ManageVehicleState {
  const factory _ManageVehicleState(
          {required final List<MVehicle> listVehicle,
          required final List<MVehicleGroupDTO> listGroupVehicle,
          required final bool isLoading,
          required final ManageVehicleFailure? failure,
          required final int total,
          required final List<MVehicleGroupDTO> listGroupVehicleSearched}) =
      _$ManageVehicleStateImpl;

  @override
  List<MVehicle> get listVehicle;
  @override
  List<MVehicleGroupDTO> get listGroupVehicle;
  @override
  bool get isLoading;
  @override
  ManageVehicleFailure? get failure;
  @override
  int get total;
  @override //Search
  List<MVehicleGroupDTO> get listGroupVehicleSearched;
  @override
  @JsonKey(ignore: true)
  _$$ManageVehicleStateImplCopyWith<_$ManageVehicleStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
