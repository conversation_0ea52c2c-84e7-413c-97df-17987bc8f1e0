// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'long_term_care_setting_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LongTermCareSettingEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleLongTermCareSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleLongTermCareSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleLongTermCareSetting value)
        getVehicleLongTermCareSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleLongTermCareSetting value)
        updateVehicleLongTermCareSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LongTermCareSettingEventCopyWith<$Res> {
  factory $LongTermCareSettingEventCopyWith(LongTermCareSettingEvent value,
          $Res Function(LongTermCareSettingEvent) then) =
      _$LongTermCareSettingEventCopyWithImpl<$Res, LongTermCareSettingEvent>;
}

/// @nodoc
class _$LongTermCareSettingEventCopyWithImpl<$Res,
        $Val extends LongTermCareSettingEvent>
    implements $LongTermCareSettingEventCopyWith<$Res> {
  _$LongTermCareSettingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$GetVehicleLongTermCareSettingImplCopyWith<$Res> {
  factory _$$GetVehicleLongTermCareSettingImplCopyWith(
          _$GetVehicleLongTermCareSettingImpl value,
          $Res Function(_$GetVehicleLongTermCareSettingImpl) then) =
      __$$GetVehicleLongTermCareSettingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({MVehicleSettingRequest request});

  $MVehicleSettingRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$GetVehicleLongTermCareSettingImplCopyWithImpl<$Res>
    extends _$LongTermCareSettingEventCopyWithImpl<$Res,
        _$GetVehicleLongTermCareSettingImpl>
    implements _$$GetVehicleLongTermCareSettingImplCopyWith<$Res> {
  __$$GetVehicleLongTermCareSettingImplCopyWithImpl(
      _$GetVehicleLongTermCareSettingImpl _value,
      $Res Function(_$GetVehicleLongTermCareSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$GetVehicleLongTermCareSettingImpl(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as MVehicleSettingRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $MVehicleSettingRequestCopyWith<$Res> get request {
    return $MVehicleSettingRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$GetVehicleLongTermCareSettingImpl
    implements _GetVehicleLongTermCareSetting {
  const _$GetVehicleLongTermCareSettingImpl({required this.request});

  @override
  final MVehicleSettingRequest request;

  @override
  String toString() {
    return 'LongTermCareSettingEvent.getVehicleLongTermCareSetting(request: $request)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetVehicleLongTermCareSettingImpl &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetVehicleLongTermCareSettingImplCopyWith<
          _$GetVehicleLongTermCareSettingImpl>
      get copyWith => __$$GetVehicleLongTermCareSettingImplCopyWithImpl<
          _$GetVehicleLongTermCareSettingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleLongTermCareSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleLongTermCareSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return getVehicleLongTermCareSetting(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return getVehicleLongTermCareSetting?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (getVehicleLongTermCareSetting != null) {
      return getVehicleLongTermCareSetting(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleLongTermCareSetting value)
        getVehicleLongTermCareSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleLongTermCareSetting value)
        updateVehicleLongTermCareSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return getVehicleLongTermCareSetting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return getVehicleLongTermCareSetting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (getVehicleLongTermCareSetting != null) {
      return getVehicleLongTermCareSetting(this);
    }
    return orElse();
  }
}

abstract class _GetVehicleLongTermCareSetting
    implements LongTermCareSettingEvent {
  const factory _GetVehicleLongTermCareSetting(
          {required final MVehicleSettingRequest request}) =
      _$GetVehicleLongTermCareSettingImpl;

  MVehicleSettingRequest get request;
  @JsonKey(ignore: true)
  _$$GetVehicleLongTermCareSettingImplCopyWith<
          _$GetVehicleLongTermCareSettingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadMoreVehicleImplCopyWith<$Res> {
  factory _$$LoadMoreVehicleImplCopyWith(_$LoadMoreVehicleImpl value,
          $Res Function(_$LoadMoreVehicleImpl) then) =
      __$$LoadMoreVehicleImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int pageSize});
}

/// @nodoc
class __$$LoadMoreVehicleImplCopyWithImpl<$Res>
    extends _$LongTermCareSettingEventCopyWithImpl<$Res, _$LoadMoreVehicleImpl>
    implements _$$LoadMoreVehicleImplCopyWith<$Res> {
  __$$LoadMoreVehicleImplCopyWithImpl(
      _$LoadMoreVehicleImpl _value, $Res Function(_$LoadMoreVehicleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
  }) {
    return _then(_$LoadMoreVehicleImpl(
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$LoadMoreVehicleImpl implements _LoadMoreVehicle {
  const _$LoadMoreVehicleImpl({this.pageSize = 10});

  @override
  @JsonKey()
  final int pageSize;

  @override
  String toString() {
    return 'LongTermCareSettingEvent.loadMoreVehicle(pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadMoreVehicleImpl &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @override
  int get hashCode => Object.hash(runtimeType, pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadMoreVehicleImplCopyWith<_$LoadMoreVehicleImpl> get copyWith =>
      __$$LoadMoreVehicleImplCopyWithImpl<_$LoadMoreVehicleImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleLongTermCareSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleLongTermCareSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return loadMoreVehicle(pageSize);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return loadMoreVehicle?.call(pageSize);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (loadMoreVehicle != null) {
      return loadMoreVehicle(pageSize);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleLongTermCareSetting value)
        getVehicleLongTermCareSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleLongTermCareSetting value)
        updateVehicleLongTermCareSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return loadMoreVehicle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return loadMoreVehicle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (loadMoreVehicle != null) {
      return loadMoreVehicle(this);
    }
    return orElse();
  }
}

abstract class _LoadMoreVehicle implements LongTermCareSettingEvent {
  const factory _LoadMoreVehicle({final int pageSize}) = _$LoadMoreVehicleImpl;

  int get pageSize;
  @JsonKey(ignore: true)
  _$$LoadMoreVehicleImplCopyWith<_$LoadMoreVehicleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OnScrolledToBottomEventImplCopyWith<$Res> {
  factory _$$OnScrolledToBottomEventImplCopyWith(
          _$OnScrolledToBottomEventImpl value,
          $Res Function(_$OnScrolledToBottomEventImpl) then) =
      __$$OnScrolledToBottomEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int pageSize});
}

/// @nodoc
class __$$OnScrolledToBottomEventImplCopyWithImpl<$Res>
    extends _$LongTermCareSettingEventCopyWithImpl<$Res,
        _$OnScrolledToBottomEventImpl>
    implements _$$OnScrolledToBottomEventImplCopyWith<$Res> {
  __$$OnScrolledToBottomEventImplCopyWithImpl(
      _$OnScrolledToBottomEventImpl _value,
      $Res Function(_$OnScrolledToBottomEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
  }) {
    return _then(_$OnScrolledToBottomEventImpl(
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$OnScrolledToBottomEventImpl implements _OnScrolledToBottomEvent {
  const _$OnScrolledToBottomEventImpl({this.pageSize = 10});

  @override
  @JsonKey()
  final int pageSize;

  @override
  String toString() {
    return 'LongTermCareSettingEvent.onScrolledToBottom(pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnScrolledToBottomEventImpl &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @override
  int get hashCode => Object.hash(runtimeType, pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OnScrolledToBottomEventImplCopyWith<_$OnScrolledToBottomEventImpl>
      get copyWith => __$$OnScrolledToBottomEventImplCopyWithImpl<
          _$OnScrolledToBottomEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleLongTermCareSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleLongTermCareSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return onScrolledToBottom(pageSize);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return onScrolledToBottom?.call(pageSize);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (onScrolledToBottom != null) {
      return onScrolledToBottom(pageSize);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleLongTermCareSetting value)
        getVehicleLongTermCareSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleLongTermCareSetting value)
        updateVehicleLongTermCareSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return onScrolledToBottom(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return onScrolledToBottom?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (onScrolledToBottom != null) {
      return onScrolledToBottom(this);
    }
    return orElse();
  }
}

abstract class _OnScrolledToBottomEvent implements LongTermCareSettingEvent {
  const factory _OnScrolledToBottomEvent({final int pageSize}) =
      _$OnScrolledToBottomEventImpl;

  int get pageSize;
  @JsonKey(ignore: true)
  _$$OnScrolledToBottomEventImplCopyWith<_$OnScrolledToBottomEventImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchLicensePlateImplCopyWith<$Res> {
  factory _$$SearchLicensePlateImplCopyWith(_$SearchLicensePlateImpl value,
          $Res Function(_$SearchLicensePlateImpl) then) =
      __$$SearchLicensePlateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String searchText});
}

/// @nodoc
class __$$SearchLicensePlateImplCopyWithImpl<$Res>
    extends _$LongTermCareSettingEventCopyWithImpl<$Res,
        _$SearchLicensePlateImpl>
    implements _$$SearchLicensePlateImplCopyWith<$Res> {
  __$$SearchLicensePlateImplCopyWithImpl(_$SearchLicensePlateImpl _value,
      $Res Function(_$SearchLicensePlateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchText = null,
  }) {
    return _then(_$SearchLicensePlateImpl(
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchLicensePlateImpl implements _SearchLicensePlate {
  const _$SearchLicensePlateImpl({required this.searchText});

  @override
  final String searchText;

  @override
  String toString() {
    return 'LongTermCareSettingEvent.searchLicensePlate(searchText: $searchText)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchLicensePlateImpl &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText));
  }

  @override
  int get hashCode => Object.hash(runtimeType, searchText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchLicensePlateImplCopyWith<_$SearchLicensePlateImpl> get copyWith =>
      __$$SearchLicensePlateImplCopyWithImpl<_$SearchLicensePlateImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleLongTermCareSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleLongTermCareSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return searchLicensePlate(searchText);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return searchLicensePlate?.call(searchText);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (searchLicensePlate != null) {
      return searchLicensePlate(searchText);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleLongTermCareSetting value)
        getVehicleLongTermCareSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleLongTermCareSetting value)
        updateVehicleLongTermCareSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return searchLicensePlate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return searchLicensePlate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (searchLicensePlate != null) {
      return searchLicensePlate(this);
    }
    return orElse();
  }
}

abstract class _SearchLicensePlate implements LongTermCareSettingEvent {
  const factory _SearchLicensePlate({required final String searchText}) =
      _$SearchLicensePlateImpl;

  String get searchText;
  @JsonKey(ignore: true)
  _$$SearchLicensePlateImplCopyWith<_$SearchLicensePlateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateVehicleLongTermCareSettingImplCopyWith<$Res> {
  factory _$$UpdateVehicleLongTermCareSettingImplCopyWith(
          _$UpdateVehicleLongTermCareSettingImpl value,
          $Res Function(_$UpdateVehicleLongTermCareSettingImpl) then) =
      __$$UpdateVehicleLongTermCareSettingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({MUpdateSendDataRequest request});

  $MUpdateSendDataRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$UpdateVehicleLongTermCareSettingImplCopyWithImpl<$Res>
    extends _$LongTermCareSettingEventCopyWithImpl<$Res,
        _$UpdateVehicleLongTermCareSettingImpl>
    implements _$$UpdateVehicleLongTermCareSettingImplCopyWith<$Res> {
  __$$UpdateVehicleLongTermCareSettingImplCopyWithImpl(
      _$UpdateVehicleLongTermCareSettingImpl _value,
      $Res Function(_$UpdateVehicleLongTermCareSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$UpdateVehicleLongTermCareSettingImpl(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as MUpdateSendDataRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $MUpdateSendDataRequestCopyWith<$Res> get request {
    return $MUpdateSendDataRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$UpdateVehicleLongTermCareSettingImpl
    implements _UpdateVehicleLongTermCareSetting {
  const _$UpdateVehicleLongTermCareSettingImpl({required this.request});

  @override
  final MUpdateSendDataRequest request;

  @override
  String toString() {
    return 'LongTermCareSettingEvent.updateVehicleLongTermCareSetting(request: $request)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateVehicleLongTermCareSettingImpl &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateVehicleLongTermCareSettingImplCopyWith<
          _$UpdateVehicleLongTermCareSettingImpl>
      get copyWith => __$$UpdateVehicleLongTermCareSettingImplCopyWithImpl<
          _$UpdateVehicleLongTermCareSettingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleLongTermCareSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleLongTermCareSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return updateVehicleLongTermCareSetting(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return updateVehicleLongTermCareSetting?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (updateVehicleLongTermCareSetting != null) {
      return updateVehicleLongTermCareSetting(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleLongTermCareSetting value)
        getVehicleLongTermCareSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleLongTermCareSetting value)
        updateVehicleLongTermCareSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return updateVehicleLongTermCareSetting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return updateVehicleLongTermCareSetting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (updateVehicleLongTermCareSetting != null) {
      return updateVehicleLongTermCareSetting(this);
    }
    return orElse();
  }
}

abstract class _UpdateVehicleLongTermCareSetting
    implements LongTermCareSettingEvent {
  const factory _UpdateVehicleLongTermCareSetting(
          {required final MUpdateSendDataRequest request}) =
      _$UpdateVehicleLongTermCareSettingImpl;

  MUpdateSendDataRequest get request;
  @JsonKey(ignore: true)
  _$$UpdateVehicleLongTermCareSettingImplCopyWith<
          _$UpdateVehicleLongTermCareSettingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateVehicleSwitchImplCopyWith<$Res> {
  factory _$$UpdateVehicleSwitchImplCopyWith(_$UpdateVehicleSwitchImpl value,
          $Res Function(_$UpdateVehicleSwitchImpl) then) =
      __$$UpdateVehicleSwitchImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Map<String, Map<String, bool>> switchState});
}

/// @nodoc
class __$$UpdateVehicleSwitchImplCopyWithImpl<$Res>
    extends _$LongTermCareSettingEventCopyWithImpl<$Res,
        _$UpdateVehicleSwitchImpl>
    implements _$$UpdateVehicleSwitchImplCopyWith<$Res> {
  __$$UpdateVehicleSwitchImplCopyWithImpl(_$UpdateVehicleSwitchImpl _value,
      $Res Function(_$UpdateVehicleSwitchImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? switchState = null,
  }) {
    return _then(_$UpdateVehicleSwitchImpl(
      switchState: null == switchState
          ? _value._switchState
          : switchState // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, bool>>,
    ));
  }
}

/// @nodoc

class _$UpdateVehicleSwitchImpl implements _UpdateVehicleSwitch {
  const _$UpdateVehicleSwitchImpl(
      {required final Map<String, Map<String, bool>> switchState})
      : _switchState = switchState;

  final Map<String, Map<String, bool>> _switchState;
  @override
  Map<String, Map<String, bool>> get switchState {
    if (_switchState is EqualUnmodifiableMapView) return _switchState;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_switchState);
  }

  @override
  String toString() {
    return 'LongTermCareSettingEvent.updateVehicleSwitch(switchState: $switchState)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateVehicleSwitchImpl &&
            const DeepCollectionEquality()
                .equals(other._switchState, _switchState));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_switchState));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateVehicleSwitchImplCopyWith<_$UpdateVehicleSwitchImpl> get copyWith =>
      __$$UpdateVehicleSwitchImplCopyWithImpl<_$UpdateVehicleSwitchImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleLongTermCareSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleLongTermCareSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return updateVehicleSwitch(switchState);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return updateVehicleSwitch?.call(switchState);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (updateVehicleSwitch != null) {
      return updateVehicleSwitch(switchState);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleLongTermCareSetting value)
        getVehicleLongTermCareSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleLongTermCareSetting value)
        updateVehicleLongTermCareSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return updateVehicleSwitch(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return updateVehicleSwitch?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (updateVehicleSwitch != null) {
      return updateVehicleSwitch(this);
    }
    return orElse();
  }
}

abstract class _UpdateVehicleSwitch implements LongTermCareSettingEvent {
  const factory _UpdateVehicleSwitch(
          {required final Map<String, Map<String, bool>> switchState}) =
      _$UpdateVehicleSwitchImpl;

  Map<String, Map<String, bool>> get switchState;
  @JsonKey(ignore: true)
  _$$UpdateVehicleSwitchImplCopyWith<_$UpdateVehicleSwitchImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetStateImplCopyWith<$Res> {
  factory _$$ResetStateImplCopyWith(
          _$ResetStateImpl value, $Res Function(_$ResetStateImpl) then) =
      __$$ResetStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetStateImplCopyWithImpl<$Res>
    extends _$LongTermCareSettingEventCopyWithImpl<$Res, _$ResetStateImpl>
    implements _$$ResetStateImplCopyWith<$Res> {
  __$$ResetStateImplCopyWithImpl(
      _$ResetStateImpl _value, $Res Function(_$ResetStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ResetStateImpl implements _ResetState {
  const _$ResetStateImpl();

  @override
  String toString() {
    return 'LongTermCareSettingEvent.resetState()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MVehicleSettingRequest request)
        getVehicleLongTermCareSetting,
    required TResult Function(int pageSize) loadMoreVehicle,
    required TResult Function(int pageSize) onScrolledToBottom,
    required TResult Function(String searchText) searchLicensePlate,
    required TResult Function(MUpdateSendDataRequest request)
        updateVehicleLongTermCareSetting,
    required TResult Function(Map<String, Map<String, bool>> switchState)
        updateVehicleSwitch,
    required TResult Function() resetState,
  }) {
    return resetState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult? Function(int pageSize)? loadMoreVehicle,
    TResult? Function(int pageSize)? onScrolledToBottom,
    TResult? Function(String searchText)? searchLicensePlate,
    TResult? Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult? Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult? Function()? resetState,
  }) {
    return resetState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MVehicleSettingRequest request)?
        getVehicleLongTermCareSetting,
    TResult Function(int pageSize)? loadMoreVehicle,
    TResult Function(int pageSize)? onScrolledToBottom,
    TResult Function(String searchText)? searchLicensePlate,
    TResult Function(MUpdateSendDataRequest request)?
        updateVehicleLongTermCareSetting,
    TResult Function(Map<String, Map<String, bool>> switchState)?
        updateVehicleSwitch,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetVehicleLongTermCareSetting value)
        getVehicleLongTermCareSetting,
    required TResult Function(_LoadMoreVehicle value) loadMoreVehicle,
    required TResult Function(_OnScrolledToBottomEvent value)
        onScrolledToBottom,
    required TResult Function(_SearchLicensePlate value) searchLicensePlate,
    required TResult Function(_UpdateVehicleLongTermCareSetting value)
        updateVehicleLongTermCareSetting,
    required TResult Function(_UpdateVehicleSwitch value) updateVehicleSwitch,
    required TResult Function(_ResetState value) resetState,
  }) {
    return resetState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult? Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult? Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult? Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult? Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult? Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return resetState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetVehicleLongTermCareSetting value)?
        getVehicleLongTermCareSetting,
    TResult Function(_LoadMoreVehicle value)? loadMoreVehicle,
    TResult Function(_OnScrolledToBottomEvent value)? onScrolledToBottom,
    TResult Function(_SearchLicensePlate value)? searchLicensePlate,
    TResult Function(_UpdateVehicleLongTermCareSetting value)?
        updateVehicleLongTermCareSetting,
    TResult Function(_UpdateVehicleSwitch value)? updateVehicleSwitch,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState(this);
    }
    return orElse();
  }
}

abstract class _ResetState implements LongTermCareSettingEvent {
  const factory _ResetState() = _$ResetStateImpl;
}

/// @nodoc
mixin _$LongTermCareSettingState {
  bool get isLoading => throw _privateConstructorUsedError;
  ManageVehicleFailure? get failure => throw _privateConstructorUsedError;
  List<VechileSetting> get listVechileSetting =>
      throw _privateConstructorUsedError;
  List<VechileSetting> get listVechileSettingFilter =>
      throw _privateConstructorUsedError;
  bool get isLoadingMore => throw _privateConstructorUsedError;
  bool get hasReachedMax => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  String get searchText => throw _privateConstructorUsedError;
  Map<String, Map<String, bool>> get vehicleSwitchState =>
      throw _privateConstructorUsedError;
  bool? get updateSuccess => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $LongTermCareSettingStateCopyWith<LongTermCareSettingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LongTermCareSettingStateCopyWith<$Res> {
  factory $LongTermCareSettingStateCopyWith(LongTermCareSettingState value,
          $Res Function(LongTermCareSettingState) then) =
      _$LongTermCareSettingStateCopyWithImpl<$Res, LongTermCareSettingState>;
  @useResult
  $Res call(
      {bool isLoading,
      ManageVehicleFailure? failure,
      List<VechileSetting> listVechileSetting,
      List<VechileSetting> listVechileSettingFilter,
      bool isLoadingMore,
      bool hasReachedMax,
      int currentPage,
      String searchText,
      Map<String, Map<String, bool>> vehicleSwitchState,
      bool? updateSuccess,
      int total});

  $ManageVehicleFailureCopyWith<$Res>? get failure;
}

/// @nodoc
class _$LongTermCareSettingStateCopyWithImpl<$Res,
        $Val extends LongTermCareSettingState>
    implements $LongTermCareSettingStateCopyWith<$Res> {
  _$LongTermCareSettingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? failure = freezed,
    Object? listVechileSetting = null,
    Object? listVechileSettingFilter = null,
    Object? isLoadingMore = null,
    Object? hasReachedMax = null,
    Object? currentPage = null,
    Object? searchText = null,
    Object? vehicleSwitchState = null,
    Object? updateSuccess = freezed,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      failure: freezed == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      listVechileSetting: null == listVechileSetting
          ? _value.listVechileSetting
          : listVechileSetting // ignore: cast_nullable_to_non_nullable
              as List<VechileSetting>,
      listVechileSettingFilter: null == listVechileSettingFilter
          ? _value.listVechileSettingFilter
          : listVechileSettingFilter // ignore: cast_nullable_to_non_nullable
              as List<VechileSetting>,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      hasReachedMax: null == hasReachedMax
          ? _value.hasReachedMax
          : hasReachedMax // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleSwitchState: null == vehicleSwitchState
          ? _value.vehicleSwitchState
          : vehicleSwitchState // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, bool>>,
      updateSuccess: freezed == updateSuccess
          ? _value.updateSuccess
          : updateSuccess // ignore: cast_nullable_to_non_nullable
              as bool?,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ManageVehicleFailureCopyWith<$Res>? get failure {
    if (_value.failure == null) {
      return null;
    }

    return $ManageVehicleFailureCopyWith<$Res>(_value.failure!, (value) {
      return _then(_value.copyWith(failure: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LongTermCareSettingImplCopyWith<$Res>
    implements $LongTermCareSettingStateCopyWith<$Res> {
  factory _$$LongTermCareSettingImplCopyWith(_$LongTermCareSettingImpl value,
          $Res Function(_$LongTermCareSettingImpl) then) =
      __$$LongTermCareSettingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      ManageVehicleFailure? failure,
      List<VechileSetting> listVechileSetting,
      List<VechileSetting> listVechileSettingFilter,
      bool isLoadingMore,
      bool hasReachedMax,
      int currentPage,
      String searchText,
      Map<String, Map<String, bool>> vehicleSwitchState,
      bool? updateSuccess,
      int total});

  @override
  $ManageVehicleFailureCopyWith<$Res>? get failure;
}

/// @nodoc
class __$$LongTermCareSettingImplCopyWithImpl<$Res>
    extends _$LongTermCareSettingStateCopyWithImpl<$Res,
        _$LongTermCareSettingImpl>
    implements _$$LongTermCareSettingImplCopyWith<$Res> {
  __$$LongTermCareSettingImplCopyWithImpl(_$LongTermCareSettingImpl _value,
      $Res Function(_$LongTermCareSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? failure = freezed,
    Object? listVechileSetting = null,
    Object? listVechileSettingFilter = null,
    Object? isLoadingMore = null,
    Object? hasReachedMax = null,
    Object? currentPage = null,
    Object? searchText = null,
    Object? vehicleSwitchState = null,
    Object? updateSuccess = freezed,
    Object? total = null,
  }) {
    return _then(_$LongTermCareSettingImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      failure: freezed == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as ManageVehicleFailure?,
      listVechileSetting: null == listVechileSetting
          ? _value._listVechileSetting
          : listVechileSetting // ignore: cast_nullable_to_non_nullable
              as List<VechileSetting>,
      listVechileSettingFilter: null == listVechileSettingFilter
          ? _value._listVechileSettingFilter
          : listVechileSettingFilter // ignore: cast_nullable_to_non_nullable
              as List<VechileSetting>,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      hasReachedMax: null == hasReachedMax
          ? _value.hasReachedMax
          : hasReachedMax // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleSwitchState: null == vehicleSwitchState
          ? _value._vehicleSwitchState
          : vehicleSwitchState // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, bool>>,
      updateSuccess: freezed == updateSuccess
          ? _value.updateSuccess
          : updateSuccess // ignore: cast_nullable_to_non_nullable
              as bool?,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$LongTermCareSettingImpl implements _LongTermCareSetting {
  const _$LongTermCareSettingImpl(
      {required this.isLoading,
      this.failure,
      required final List<VechileSetting> listVechileSetting,
      required final List<VechileSetting> listVechileSettingFilter,
      this.isLoadingMore = false,
      this.hasReachedMax = false,
      this.currentPage = 1,
      required this.searchText,
      final Map<String, Map<String, bool>> vehicleSwitchState = const {},
      this.updateSuccess,
      required this.total})
      : _listVechileSetting = listVechileSetting,
        _listVechileSettingFilter = listVechileSettingFilter,
        _vehicleSwitchState = vehicleSwitchState;

  @override
  final bool isLoading;
  @override
  final ManageVehicleFailure? failure;
  final List<VechileSetting> _listVechileSetting;
  @override
  List<VechileSetting> get listVechileSetting {
    if (_listVechileSetting is EqualUnmodifiableListView)
      return _listVechileSetting;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVechileSetting);
  }

  final List<VechileSetting> _listVechileSettingFilter;
  @override
  List<VechileSetting> get listVechileSettingFilter {
    if (_listVechileSettingFilter is EqualUnmodifiableListView)
      return _listVechileSettingFilter;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVechileSettingFilter);
  }

  @override
  @JsonKey()
  final bool isLoadingMore;
  @override
  @JsonKey()
  final bool hasReachedMax;
  @override
  @JsonKey()
  final int currentPage;
  @override
  final String searchText;
  final Map<String, Map<String, bool>> _vehicleSwitchState;
  @override
  @JsonKey()
  Map<String, Map<String, bool>> get vehicleSwitchState {
    if (_vehicleSwitchState is EqualUnmodifiableMapView)
      return _vehicleSwitchState;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_vehicleSwitchState);
  }

  @override
  final bool? updateSuccess;
  @override
  final int total;

  @override
  String toString() {
    return 'LongTermCareSettingState(isLoading: $isLoading, failure: $failure, listVechileSetting: $listVechileSetting, listVechileSettingFilter: $listVechileSettingFilter, isLoadingMore: $isLoadingMore, hasReachedMax: $hasReachedMax, currentPage: $currentPage, searchText: $searchText, vehicleSwitchState: $vehicleSwitchState, updateSuccess: $updateSuccess, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LongTermCareSettingImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.failure, failure) || other.failure == failure) &&
            const DeepCollectionEquality()
                .equals(other._listVechileSetting, _listVechileSetting) &&
            const DeepCollectionEquality().equals(
                other._listVechileSettingFilter, _listVechileSettingFilter) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.hasReachedMax, hasReachedMax) ||
                other.hasReachedMax == hasReachedMax) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText) &&
            const DeepCollectionEquality()
                .equals(other._vehicleSwitchState, _vehicleSwitchState) &&
            (identical(other.updateSuccess, updateSuccess) ||
                other.updateSuccess == updateSuccess) &&
            (identical(other.total, total) || other.total == total));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      failure,
      const DeepCollectionEquality().hash(_listVechileSetting),
      const DeepCollectionEquality().hash(_listVechileSettingFilter),
      isLoadingMore,
      hasReachedMax,
      currentPage,
      searchText,
      const DeepCollectionEquality().hash(_vehicleSwitchState),
      updateSuccess,
      total);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LongTermCareSettingImplCopyWith<_$LongTermCareSettingImpl> get copyWith =>
      __$$LongTermCareSettingImplCopyWithImpl<_$LongTermCareSettingImpl>(
          this, _$identity);
}

abstract class _LongTermCareSetting implements LongTermCareSettingState {
  const factory _LongTermCareSetting(
      {required final bool isLoading,
      final ManageVehicleFailure? failure,
      required final List<VechileSetting> listVechileSetting,
      required final List<VechileSetting> listVechileSettingFilter,
      final bool isLoadingMore,
      final bool hasReachedMax,
      final int currentPage,
      required final String searchText,
      final Map<String, Map<String, bool>> vehicleSwitchState,
      final bool? updateSuccess,
      required final int total}) = _$LongTermCareSettingImpl;

  @override
  bool get isLoading;
  @override
  ManageVehicleFailure? get failure;
  @override
  List<VechileSetting> get listVechileSetting;
  @override
  List<VechileSetting> get listVechileSettingFilter;
  @override
  bool get isLoadingMore;
  @override
  bool get hasReachedMax;
  @override
  int get currentPage;
  @override
  String get searchText;
  @override
  Map<String, Map<String, bool>> get vehicleSwitchState;
  @override
  bool? get updateSuccess;
  @override
  int get total;
  @override
  @JsonKey(ignore: true)
  _$$LongTermCareSettingImplCopyWith<_$LongTermCareSettingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
