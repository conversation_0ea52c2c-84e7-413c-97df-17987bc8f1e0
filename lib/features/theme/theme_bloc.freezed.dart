// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'theme_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ThemeEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() changeTheme,
    required TResult Function(AvemaPrimaryColor primaryColor)
        changePrimaryColor,
    required TResult Function() getDefaultTheme,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? changeTheme,
    TResult? Function(AvemaPrimaryColor primaryColor)? changePrimaryColor,
    TResult? Function()? getDefaultTheme,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? changeTheme,
    TResult Function(AvemaPrimaryColor primaryColor)? changePrimaryColor,
    TResult Function()? getDefaultTheme,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeTheme value) changeTheme,
    required TResult Function(_ChangePrimaryColor value) changePrimaryColor,
    required TResult Function(_GetDefaultTheme value) getDefaultTheme,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeTheme value)? changeTheme,
    TResult? Function(_ChangePrimaryColor value)? changePrimaryColor,
    TResult? Function(_GetDefaultTheme value)? getDefaultTheme,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeTheme value)? changeTheme,
    TResult Function(_ChangePrimaryColor value)? changePrimaryColor,
    TResult Function(_GetDefaultTheme value)? getDefaultTheme,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ThemeEventCopyWith<$Res> {
  factory $ThemeEventCopyWith(
          ThemeEvent value, $Res Function(ThemeEvent) then) =
      _$ThemeEventCopyWithImpl<$Res, ThemeEvent>;
}

/// @nodoc
class _$ThemeEventCopyWithImpl<$Res, $Val extends ThemeEvent>
    implements $ThemeEventCopyWith<$Res> {
  _$ThemeEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$ChangeThemeImplCopyWith<$Res> {
  factory _$$ChangeThemeImplCopyWith(
          _$ChangeThemeImpl value, $Res Function(_$ChangeThemeImpl) then) =
      __$$ChangeThemeImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ChangeThemeImplCopyWithImpl<$Res>
    extends _$ThemeEventCopyWithImpl<$Res, _$ChangeThemeImpl>
    implements _$$ChangeThemeImplCopyWith<$Res> {
  __$$ChangeThemeImplCopyWithImpl(
      _$ChangeThemeImpl _value, $Res Function(_$ChangeThemeImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ChangeThemeImpl implements _ChangeTheme {
  const _$ChangeThemeImpl();

  @override
  String toString() {
    return 'ThemeEvent.changeTheme()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ChangeThemeImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() changeTheme,
    required TResult Function(AvemaPrimaryColor primaryColor)
        changePrimaryColor,
    required TResult Function() getDefaultTheme,
  }) {
    return changeTheme();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? changeTheme,
    TResult? Function(AvemaPrimaryColor primaryColor)? changePrimaryColor,
    TResult? Function()? getDefaultTheme,
  }) {
    return changeTheme?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? changeTheme,
    TResult Function(AvemaPrimaryColor primaryColor)? changePrimaryColor,
    TResult Function()? getDefaultTheme,
    required TResult orElse(),
  }) {
    if (changeTheme != null) {
      return changeTheme();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeTheme value) changeTheme,
    required TResult Function(_ChangePrimaryColor value) changePrimaryColor,
    required TResult Function(_GetDefaultTheme value) getDefaultTheme,
  }) {
    return changeTheme(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeTheme value)? changeTheme,
    TResult? Function(_ChangePrimaryColor value)? changePrimaryColor,
    TResult? Function(_GetDefaultTheme value)? getDefaultTheme,
  }) {
    return changeTheme?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeTheme value)? changeTheme,
    TResult Function(_ChangePrimaryColor value)? changePrimaryColor,
    TResult Function(_GetDefaultTheme value)? getDefaultTheme,
    required TResult orElse(),
  }) {
    if (changeTheme != null) {
      return changeTheme(this);
    }
    return orElse();
  }
}

abstract class _ChangeTheme implements ThemeEvent {
  const factory _ChangeTheme() = _$ChangeThemeImpl;
}

/// @nodoc
abstract class _$$ChangePrimaryColorImplCopyWith<$Res> {
  factory _$$ChangePrimaryColorImplCopyWith(_$ChangePrimaryColorImpl value,
          $Res Function(_$ChangePrimaryColorImpl) then) =
      __$$ChangePrimaryColorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AvemaPrimaryColor primaryColor});
}

/// @nodoc
class __$$ChangePrimaryColorImplCopyWithImpl<$Res>
    extends _$ThemeEventCopyWithImpl<$Res, _$ChangePrimaryColorImpl>
    implements _$$ChangePrimaryColorImplCopyWith<$Res> {
  __$$ChangePrimaryColorImplCopyWithImpl(_$ChangePrimaryColorImpl _value,
      $Res Function(_$ChangePrimaryColorImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? primaryColor = null,
  }) {
    return _then(_$ChangePrimaryColorImpl(
      primaryColor: null == primaryColor
          ? _value.primaryColor
          : primaryColor // ignore: cast_nullable_to_non_nullable
              as AvemaPrimaryColor,
    ));
  }
}

/// @nodoc

class _$ChangePrimaryColorImpl implements _ChangePrimaryColor {
  const _$ChangePrimaryColorImpl({required this.primaryColor});

  @override
  final AvemaPrimaryColor primaryColor;

  @override
  String toString() {
    return 'ThemeEvent.changePrimaryColor(primaryColor: $primaryColor)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangePrimaryColorImpl &&
            (identical(other.primaryColor, primaryColor) ||
                other.primaryColor == primaryColor));
  }

  @override
  int get hashCode => Object.hash(runtimeType, primaryColor);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangePrimaryColorImplCopyWith<_$ChangePrimaryColorImpl> get copyWith =>
      __$$ChangePrimaryColorImplCopyWithImpl<_$ChangePrimaryColorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() changeTheme,
    required TResult Function(AvemaPrimaryColor primaryColor)
        changePrimaryColor,
    required TResult Function() getDefaultTheme,
  }) {
    return changePrimaryColor(primaryColor);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? changeTheme,
    TResult? Function(AvemaPrimaryColor primaryColor)? changePrimaryColor,
    TResult? Function()? getDefaultTheme,
  }) {
    return changePrimaryColor?.call(primaryColor);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? changeTheme,
    TResult Function(AvemaPrimaryColor primaryColor)? changePrimaryColor,
    TResult Function()? getDefaultTheme,
    required TResult orElse(),
  }) {
    if (changePrimaryColor != null) {
      return changePrimaryColor(primaryColor);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeTheme value) changeTheme,
    required TResult Function(_ChangePrimaryColor value) changePrimaryColor,
    required TResult Function(_GetDefaultTheme value) getDefaultTheme,
  }) {
    return changePrimaryColor(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeTheme value)? changeTheme,
    TResult? Function(_ChangePrimaryColor value)? changePrimaryColor,
    TResult? Function(_GetDefaultTheme value)? getDefaultTheme,
  }) {
    return changePrimaryColor?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeTheme value)? changeTheme,
    TResult Function(_ChangePrimaryColor value)? changePrimaryColor,
    TResult Function(_GetDefaultTheme value)? getDefaultTheme,
    required TResult orElse(),
  }) {
    if (changePrimaryColor != null) {
      return changePrimaryColor(this);
    }
    return orElse();
  }
}

abstract class _ChangePrimaryColor implements ThemeEvent {
  const factory _ChangePrimaryColor(
          {required final AvemaPrimaryColor primaryColor}) =
      _$ChangePrimaryColorImpl;

  AvemaPrimaryColor get primaryColor;
  @JsonKey(ignore: true)
  _$$ChangePrimaryColorImplCopyWith<_$ChangePrimaryColorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetDefaultThemeImplCopyWith<$Res> {
  factory _$$GetDefaultThemeImplCopyWith(_$GetDefaultThemeImpl value,
          $Res Function(_$GetDefaultThemeImpl) then) =
      __$$GetDefaultThemeImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetDefaultThemeImplCopyWithImpl<$Res>
    extends _$ThemeEventCopyWithImpl<$Res, _$GetDefaultThemeImpl>
    implements _$$GetDefaultThemeImplCopyWith<$Res> {
  __$$GetDefaultThemeImplCopyWithImpl(
      _$GetDefaultThemeImpl _value, $Res Function(_$GetDefaultThemeImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$GetDefaultThemeImpl implements _GetDefaultTheme {
  const _$GetDefaultThemeImpl();

  @override
  String toString() {
    return 'ThemeEvent.getDefaultTheme()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetDefaultThemeImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() changeTheme,
    required TResult Function(AvemaPrimaryColor primaryColor)
        changePrimaryColor,
    required TResult Function() getDefaultTheme,
  }) {
    return getDefaultTheme();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? changeTheme,
    TResult? Function(AvemaPrimaryColor primaryColor)? changePrimaryColor,
    TResult? Function()? getDefaultTheme,
  }) {
    return getDefaultTheme?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? changeTheme,
    TResult Function(AvemaPrimaryColor primaryColor)? changePrimaryColor,
    TResult Function()? getDefaultTheme,
    required TResult orElse(),
  }) {
    if (getDefaultTheme != null) {
      return getDefaultTheme();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeTheme value) changeTheme,
    required TResult Function(_ChangePrimaryColor value) changePrimaryColor,
    required TResult Function(_GetDefaultTheme value) getDefaultTheme,
  }) {
    return getDefaultTheme(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeTheme value)? changeTheme,
    TResult? Function(_ChangePrimaryColor value)? changePrimaryColor,
    TResult? Function(_GetDefaultTheme value)? getDefaultTheme,
  }) {
    return getDefaultTheme?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeTheme value)? changeTheme,
    TResult Function(_ChangePrimaryColor value)? changePrimaryColor,
    TResult Function(_GetDefaultTheme value)? getDefaultTheme,
    required TResult orElse(),
  }) {
    if (getDefaultTheme != null) {
      return getDefaultTheme(this);
    }
    return orElse();
  }
}

abstract class _GetDefaultTheme implements ThemeEvent {
  const factory _GetDefaultTheme() = _$GetDefaultThemeImpl;
}

/// @nodoc
mixin _$ThemeState {
  AvemaTypeTheme get theme => throw _privateConstructorUsedError;
  AvemaPrimaryColor get primaryColor => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ThemeStateCopyWith<ThemeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ThemeStateCopyWith<$Res> {
  factory $ThemeStateCopyWith(
          ThemeState value, $Res Function(ThemeState) then) =
      _$ThemeStateCopyWithImpl<$Res, ThemeState>;
  @useResult
  $Res call({AvemaTypeTheme theme, AvemaPrimaryColor primaryColor});
}

/// @nodoc
class _$ThemeStateCopyWithImpl<$Res, $Val extends ThemeState>
    implements $ThemeStateCopyWith<$Res> {
  _$ThemeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? theme = null,
    Object? primaryColor = null,
  }) {
    return _then(_value.copyWith(
      theme: null == theme
          ? _value.theme
          : theme // ignore: cast_nullable_to_non_nullable
              as AvemaTypeTheme,
      primaryColor: null == primaryColor
          ? _value.primaryColor
          : primaryColor // ignore: cast_nullable_to_non_nullable
              as AvemaPrimaryColor,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ThemeStateImplCopyWith<$Res>
    implements $ThemeStateCopyWith<$Res> {
  factory _$$ThemeStateImplCopyWith(
          _$ThemeStateImpl value, $Res Function(_$ThemeStateImpl) then) =
      __$$ThemeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({AvemaTypeTheme theme, AvemaPrimaryColor primaryColor});
}

/// @nodoc
class __$$ThemeStateImplCopyWithImpl<$Res>
    extends _$ThemeStateCopyWithImpl<$Res, _$ThemeStateImpl>
    implements _$$ThemeStateImplCopyWith<$Res> {
  __$$ThemeStateImplCopyWithImpl(
      _$ThemeStateImpl _value, $Res Function(_$ThemeStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? theme = null,
    Object? primaryColor = null,
  }) {
    return _then(_$ThemeStateImpl(
      theme: null == theme
          ? _value.theme
          : theme // ignore: cast_nullable_to_non_nullable
              as AvemaTypeTheme,
      primaryColor: null == primaryColor
          ? _value.primaryColor
          : primaryColor // ignore: cast_nullable_to_non_nullable
              as AvemaPrimaryColor,
    ));
  }
}

/// @nodoc

class _$ThemeStateImpl implements _ThemeState {
  const _$ThemeStateImpl({required this.theme, required this.primaryColor});

  @override
  final AvemaTypeTheme theme;
  @override
  final AvemaPrimaryColor primaryColor;

  @override
  String toString() {
    return 'ThemeState(theme: $theme, primaryColor: $primaryColor)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ThemeStateImpl &&
            (identical(other.theme, theme) || other.theme == theme) &&
            (identical(other.primaryColor, primaryColor) ||
                other.primaryColor == primaryColor));
  }

  @override
  int get hashCode => Object.hash(runtimeType, theme, primaryColor);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ThemeStateImplCopyWith<_$ThemeStateImpl> get copyWith =>
      __$$ThemeStateImplCopyWithImpl<_$ThemeStateImpl>(this, _$identity);
}

abstract class _ThemeState implements ThemeState {
  const factory _ThemeState(
      {required final AvemaTypeTheme theme,
      required final AvemaPrimaryColor primaryColor}) = _$ThemeStateImpl;

  @override
  AvemaTypeTheme get theme;
  @override
  AvemaPrimaryColor get primaryColor;
  @override
  @JsonKey(ignore: true)
  _$$ThemeStateImplCopyWith<_$ThemeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
