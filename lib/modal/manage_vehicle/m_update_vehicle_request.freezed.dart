// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_update_vehicle_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MUpdateVehicleRequest _$MUpdateVehicleRequestFromJson(
    Map<String, dynamic> json) {
  return _MUpdateVehicleRequest.fromJson(json);
}

/// @nodoc
mixin _$MUpdateVehicleRequest {
  String get id => throw _privateConstructorUsedError;
  String get plate => throw _privateConstructorUsedError;
  int get icon => throw _privateConstructorUsedError;
  int get maxSpeed => throw _privateConstructorUsedError;
  int get productYear => throw _privateConstructorUsedError;
  bool get soldVehicle => throw _privateConstructorUsedError;
  String get enginNo => throw _privateConstructorUsedError;
  String get vin => throw _privateConstructorUsedError;
  String get vehicleTypeId => throw _privateConstructorUsedError;
  String get vehicleGroupId => throw _privateConstructorUsedError;
  int get fuelPer100Km => throw _privateConstructorUsedError;
  int get fuelIdle => throw _privateConstructorUsedError;
  int get fuelIgnition => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MUpdateVehicleRequestCopyWith<MUpdateVehicleRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MUpdateVehicleRequestCopyWith<$Res> {
  factory $MUpdateVehicleRequestCopyWith(MUpdateVehicleRequest value,
          $Res Function(MUpdateVehicleRequest) then) =
      _$MUpdateVehicleRequestCopyWithImpl<$Res, MUpdateVehicleRequest>;
  @useResult
  $Res call(
      {String id,
      String plate,
      int icon,
      int maxSpeed,
      int productYear,
      bool soldVehicle,
      String enginNo,
      String vin,
      String vehicleTypeId,
      String vehicleGroupId,
      int fuelPer100Km,
      int fuelIdle,
      int fuelIgnition});
}

/// @nodoc
class _$MUpdateVehicleRequestCopyWithImpl<$Res,
        $Val extends MUpdateVehicleRequest>
    implements $MUpdateVehicleRequestCopyWith<$Res> {
  _$MUpdateVehicleRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? icon = null,
    Object? maxSpeed = null,
    Object? productYear = null,
    Object? soldVehicle = null,
    Object? enginNo = null,
    Object? vin = null,
    Object? vehicleTypeId = null,
    Object? vehicleGroupId = null,
    Object? fuelPer100Km = null,
    Object? fuelIdle = null,
    Object? fuelIgnition = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      productYear: null == productYear
          ? _value.productYear
          : productYear // ignore: cast_nullable_to_non_nullable
              as int,
      soldVehicle: null == soldVehicle
          ? _value.soldVehicle
          : soldVehicle // ignore: cast_nullable_to_non_nullable
              as bool,
      enginNo: null == enginNo
          ? _value.enginNo
          : enginNo // ignore: cast_nullable_to_non_nullable
              as String,
      vin: null == vin
          ? _value.vin
          : vin // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleTypeId: null == vehicleTypeId
          ? _value.vehicleTypeId
          : vehicleTypeId // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleGroupId: null == vehicleGroupId
          ? _value.vehicleGroupId
          : vehicleGroupId // ignore: cast_nullable_to_non_nullable
              as String,
      fuelPer100Km: null == fuelPer100Km
          ? _value.fuelPer100Km
          : fuelPer100Km // ignore: cast_nullable_to_non_nullable
              as int,
      fuelIdle: null == fuelIdle
          ? _value.fuelIdle
          : fuelIdle // ignore: cast_nullable_to_non_nullable
              as int,
      fuelIgnition: null == fuelIgnition
          ? _value.fuelIgnition
          : fuelIgnition // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MUpdateVehicleRequestImplCopyWith<$Res>
    implements $MUpdateVehicleRequestCopyWith<$Res> {
  factory _$$MUpdateVehicleRequestImplCopyWith(
          _$MUpdateVehicleRequestImpl value,
          $Res Function(_$MUpdateVehicleRequestImpl) then) =
      __$$MUpdateVehicleRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String plate,
      int icon,
      int maxSpeed,
      int productYear,
      bool soldVehicle,
      String enginNo,
      String vin,
      String vehicleTypeId,
      String vehicleGroupId,
      int fuelPer100Km,
      int fuelIdle,
      int fuelIgnition});
}

/// @nodoc
class __$$MUpdateVehicleRequestImplCopyWithImpl<$Res>
    extends _$MUpdateVehicleRequestCopyWithImpl<$Res,
        _$MUpdateVehicleRequestImpl>
    implements _$$MUpdateVehicleRequestImplCopyWith<$Res> {
  __$$MUpdateVehicleRequestImplCopyWithImpl(_$MUpdateVehicleRequestImpl _value,
      $Res Function(_$MUpdateVehicleRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? icon = null,
    Object? maxSpeed = null,
    Object? productYear = null,
    Object? soldVehicle = null,
    Object? enginNo = null,
    Object? vin = null,
    Object? vehicleTypeId = null,
    Object? vehicleGroupId = null,
    Object? fuelPer100Km = null,
    Object? fuelIdle = null,
    Object? fuelIgnition = null,
  }) {
    return _then(_$MUpdateVehicleRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      productYear: null == productYear
          ? _value.productYear
          : productYear // ignore: cast_nullable_to_non_nullable
              as int,
      soldVehicle: null == soldVehicle
          ? _value.soldVehicle
          : soldVehicle // ignore: cast_nullable_to_non_nullable
              as bool,
      enginNo: null == enginNo
          ? _value.enginNo
          : enginNo // ignore: cast_nullable_to_non_nullable
              as String,
      vin: null == vin
          ? _value.vin
          : vin // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleTypeId: null == vehicleTypeId
          ? _value.vehicleTypeId
          : vehicleTypeId // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleGroupId: null == vehicleGroupId
          ? _value.vehicleGroupId
          : vehicleGroupId // ignore: cast_nullable_to_non_nullable
              as String,
      fuelPer100Km: null == fuelPer100Km
          ? _value.fuelPer100Km
          : fuelPer100Km // ignore: cast_nullable_to_non_nullable
              as int,
      fuelIdle: null == fuelIdle
          ? _value.fuelIdle
          : fuelIdle // ignore: cast_nullable_to_non_nullable
              as int,
      fuelIgnition: null == fuelIgnition
          ? _value.fuelIgnition
          : fuelIgnition // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MUpdateVehicleRequestImpl extends _MUpdateVehicleRequest {
  const _$MUpdateVehicleRequestImpl(
      {required this.id,
      required this.plate,
      required this.icon,
      required this.maxSpeed,
      required this.productYear,
      required this.soldVehicle,
      required this.enginNo,
      required this.vin,
      required this.vehicleTypeId,
      required this.vehicleGroupId,
      required this.fuelPer100Km,
      required this.fuelIdle,
      required this.fuelIgnition})
      : super._();

  factory _$MUpdateVehicleRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$MUpdateVehicleRequestImplFromJson(json);

  @override
  final String id;
  @override
  final String plate;
  @override
  final int icon;
  @override
  final int maxSpeed;
  @override
  final int productYear;
  @override
  final bool soldVehicle;
  @override
  final String enginNo;
  @override
  final String vin;
  @override
  final String vehicleTypeId;
  @override
  final String vehicleGroupId;
  @override
  final int fuelPer100Km;
  @override
  final int fuelIdle;
  @override
  final int fuelIgnition;

  @override
  String toString() {
    return 'MUpdateVehicleRequest(id: $id, plate: $plate, icon: $icon, maxSpeed: $maxSpeed, productYear: $productYear, soldVehicle: $soldVehicle, enginNo: $enginNo, vin: $vin, vehicleTypeId: $vehicleTypeId, vehicleGroupId: $vehicleGroupId, fuelPer100Km: $fuelPer100Km, fuelIdle: $fuelIdle, fuelIgnition: $fuelIgnition)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MUpdateVehicleRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.plate, plate) || other.plate == plate) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.maxSpeed, maxSpeed) ||
                other.maxSpeed == maxSpeed) &&
            (identical(other.productYear, productYear) ||
                other.productYear == productYear) &&
            (identical(other.soldVehicle, soldVehicle) ||
                other.soldVehicle == soldVehicle) &&
            (identical(other.enginNo, enginNo) || other.enginNo == enginNo) &&
            (identical(other.vin, vin) || other.vin == vin) &&
            (identical(other.vehicleTypeId, vehicleTypeId) ||
                other.vehicleTypeId == vehicleTypeId) &&
            (identical(other.vehicleGroupId, vehicleGroupId) ||
                other.vehicleGroupId == vehicleGroupId) &&
            (identical(other.fuelPer100Km, fuelPer100Km) ||
                other.fuelPer100Km == fuelPer100Km) &&
            (identical(other.fuelIdle, fuelIdle) ||
                other.fuelIdle == fuelIdle) &&
            (identical(other.fuelIgnition, fuelIgnition) ||
                other.fuelIgnition == fuelIgnition));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      plate,
      icon,
      maxSpeed,
      productYear,
      soldVehicle,
      enginNo,
      vin,
      vehicleTypeId,
      vehicleGroupId,
      fuelPer100Km,
      fuelIdle,
      fuelIgnition);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MUpdateVehicleRequestImplCopyWith<_$MUpdateVehicleRequestImpl>
      get copyWith => __$$MUpdateVehicleRequestImplCopyWithImpl<
          _$MUpdateVehicleRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MUpdateVehicleRequestImplToJson(
      this,
    );
  }
}

abstract class _MUpdateVehicleRequest extends MUpdateVehicleRequest {
  const factory _MUpdateVehicleRequest(
      {required final String id,
      required final String plate,
      required final int icon,
      required final int maxSpeed,
      required final int productYear,
      required final bool soldVehicle,
      required final String enginNo,
      required final String vin,
      required final String vehicleTypeId,
      required final String vehicleGroupId,
      required final int fuelPer100Km,
      required final int fuelIdle,
      required final int fuelIgnition}) = _$MUpdateVehicleRequestImpl;
  const _MUpdateVehicleRequest._() : super._();

  factory _MUpdateVehicleRequest.fromJson(Map<String, dynamic> json) =
      _$MUpdateVehicleRequestImpl.fromJson;

  @override
  String get id;
  @override
  String get plate;
  @override
  int get icon;
  @override
  int get maxSpeed;
  @override
  int get productYear;
  @override
  bool get soldVehicle;
  @override
  String get enginNo;
  @override
  String get vin;
  @override
  String get vehicleTypeId;
  @override
  String get vehicleGroupId;
  @override
  int get fuelPer100Km;
  @override
  int get fuelIdle;
  @override
  int get fuelIgnition;
  @override
  @JsonKey(ignore: true)
  _$$MUpdateVehicleRequestImplCopyWith<_$MUpdateVehicleRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
