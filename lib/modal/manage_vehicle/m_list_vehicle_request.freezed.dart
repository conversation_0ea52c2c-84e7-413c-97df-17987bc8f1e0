// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_list_vehicle_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MListVehicleModalRequest _$MListVehicleModalRequestFromJson(
    Map<String, dynamic> json) {
  return _MListVehicleModalRequest.fromJson(json);
}

/// @nodoc
mixin _$MListVehicleModalRequest {
  int get pageIndex => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  String get orderBy => throw _privateConstructorUsedError;
  String get searchTerm => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MListVehicleModalRequestCopyWith<MListVehicleModalRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MListVehicleModalRequestCopyWith<$Res> {
  factory $MListVehicleModalRequestCopyWith(MListVehicleModalRequest value,
          $Res Function(MListVehicleModalRequest) then) =
      _$MListVehicleModalRequestCopyWithImpl<$Res, MListVehicleModalRequest>;
  @useResult
  $Res call({int pageIndex, int pageSize, String orderBy, String searchTerm});
}

/// @nodoc
class _$MListVehicleModalRequestCopyWithImpl<$Res,
        $Val extends MListVehicleModalRequest>
    implements $MListVehicleModalRequestCopyWith<$Res> {
  _$MListVehicleModalRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? orderBy = null,
    Object? searchTerm = null,
  }) {
    return _then(_value.copyWith(
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      orderBy: null == orderBy
          ? _value.orderBy
          : orderBy // ignore: cast_nullable_to_non_nullable
              as String,
      searchTerm: null == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MListVehicleModalRequestImplCopyWith<$Res>
    implements $MListVehicleModalRequestCopyWith<$Res> {
  factory _$$MListVehicleModalRequestImplCopyWith(
          _$MListVehicleModalRequestImpl value,
          $Res Function(_$MListVehicleModalRequestImpl) then) =
      __$$MListVehicleModalRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int pageIndex, int pageSize, String orderBy, String searchTerm});
}

/// @nodoc
class __$$MListVehicleModalRequestImplCopyWithImpl<$Res>
    extends _$MListVehicleModalRequestCopyWithImpl<$Res,
        _$MListVehicleModalRequestImpl>
    implements _$$MListVehicleModalRequestImplCopyWith<$Res> {
  __$$MListVehicleModalRequestImplCopyWithImpl(
      _$MListVehicleModalRequestImpl _value,
      $Res Function(_$MListVehicleModalRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? orderBy = null,
    Object? searchTerm = null,
  }) {
    return _then(_$MListVehicleModalRequestImpl(
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      orderBy: null == orderBy
          ? _value.orderBy
          : orderBy // ignore: cast_nullable_to_non_nullable
              as String,
      searchTerm: null == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MListVehicleModalRequestImpl extends _MListVehicleModalRequest {
  const _$MListVehicleModalRequestImpl(
      {required this.pageIndex,
      required this.pageSize,
      required this.orderBy,
      required this.searchTerm})
      : super._();

  factory _$MListVehicleModalRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$MListVehicleModalRequestImplFromJson(json);

  @override
  final int pageIndex;
  @override
  final int pageSize;
  @override
  final String orderBy;
  @override
  final String searchTerm;

  @override
  String toString() {
    return 'MListVehicleModalRequest(pageIndex: $pageIndex, pageSize: $pageSize, orderBy: $orderBy, searchTerm: $searchTerm)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MListVehicleModalRequestImpl &&
            (identical(other.pageIndex, pageIndex) ||
                other.pageIndex == pageIndex) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.orderBy, orderBy) || other.orderBy == orderBy) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, pageIndex, pageSize, orderBy, searchTerm);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MListVehicleModalRequestImplCopyWith<_$MListVehicleModalRequestImpl>
      get copyWith => __$$MListVehicleModalRequestImplCopyWithImpl<
          _$MListVehicleModalRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MListVehicleModalRequestImplToJson(
      this,
    );
  }
}

abstract class _MListVehicleModalRequest extends MListVehicleModalRequest {
  const factory _MListVehicleModalRequest(
      {required final int pageIndex,
      required final int pageSize,
      required final String orderBy,
      required final String searchTerm}) = _$MListVehicleModalRequestImpl;
  const _MListVehicleModalRequest._() : super._();

  factory _MListVehicleModalRequest.fromJson(Map<String, dynamic> json) =
      _$MListVehicleModalRequestImpl.fromJson;

  @override
  int get pageIndex;
  @override
  int get pageSize;
  @override
  String get orderBy;
  @override
  String get searchTerm;
  @override
  @JsonKey(ignore: true)
  _$$MListVehicleModalRequestImplCopyWith<_$MListVehicleModalRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
