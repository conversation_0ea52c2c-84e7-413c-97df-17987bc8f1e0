// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'm_list_vehicle_group_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MListVehicleGroupRequestImpl _$$MListVehicleGroupRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$MListVehicleGroupRequestImpl(
      pageIndex: (json['pageIndex'] as num).toInt(),
      pageSize: (json['pageSize'] as num).toInt(),
      orderBy: json['orderBy'] as String,
      searchTerm: json['searchTerm'] as String,
    );

Map<String, dynamic> _$$MListVehicleGroupRequestImplToJson(
        _$MListVehicleGroupRequestImpl instance) =>
    <String, dynamic>{
      'pageIndex': instance.pageIndex,
      'pageSize': instance.pageSize,
      'orderBy': instance.orderBy,
      'searchTerm': instance.searchTerm,
    };
