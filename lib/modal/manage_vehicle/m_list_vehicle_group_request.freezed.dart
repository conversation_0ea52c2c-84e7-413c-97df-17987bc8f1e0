// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_list_vehicle_group_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MListVehicleGroupRequest _$MListVehicleGroupRequestFromJson(
    Map<String, dynamic> json) {
  return _MListVehicleGroupRequest.fromJson(json);
}

/// @nodoc
mixin _$MListVehicleGroupRequest {
  int get pageIndex => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  String get orderBy => throw _privateConstructorUsedError;
  String get searchTerm => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MListVehicleGroupRequestCopyWith<MListVehicleGroupRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MListVehicleGroupRequestCopyWith<$Res> {
  factory $MListVehicleGroupRequestCopyWith(MListVehicleGroupRequest value,
          $Res Function(MListVehicleGroupRequest) then) =
      _$MListVehicleGroupRequestCopyWithImpl<$Res, MListVehicleGroupRequest>;
  @useResult
  $Res call({int pageIndex, int pageSize, String orderBy, String searchTerm});
}

/// @nodoc
class _$MListVehicleGroupRequestCopyWithImpl<$Res,
        $Val extends MListVehicleGroupRequest>
    implements $MListVehicleGroupRequestCopyWith<$Res> {
  _$MListVehicleGroupRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? orderBy = null,
    Object? searchTerm = null,
  }) {
    return _then(_value.copyWith(
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      orderBy: null == orderBy
          ? _value.orderBy
          : orderBy // ignore: cast_nullable_to_non_nullable
              as String,
      searchTerm: null == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MListVehicleGroupRequestImplCopyWith<$Res>
    implements $MListVehicleGroupRequestCopyWith<$Res> {
  factory _$$MListVehicleGroupRequestImplCopyWith(
          _$MListVehicleGroupRequestImpl value,
          $Res Function(_$MListVehicleGroupRequestImpl) then) =
      __$$MListVehicleGroupRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int pageIndex, int pageSize, String orderBy, String searchTerm});
}

/// @nodoc
class __$$MListVehicleGroupRequestImplCopyWithImpl<$Res>
    extends _$MListVehicleGroupRequestCopyWithImpl<$Res,
        _$MListVehicleGroupRequestImpl>
    implements _$$MListVehicleGroupRequestImplCopyWith<$Res> {
  __$$MListVehicleGroupRequestImplCopyWithImpl(
      _$MListVehicleGroupRequestImpl _value,
      $Res Function(_$MListVehicleGroupRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? orderBy = null,
    Object? searchTerm = null,
  }) {
    return _then(_$MListVehicleGroupRequestImpl(
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      orderBy: null == orderBy
          ? _value.orderBy
          : orderBy // ignore: cast_nullable_to_non_nullable
              as String,
      searchTerm: null == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MListVehicleGroupRequestImpl extends _MListVehicleGroupRequest {
  const _$MListVehicleGroupRequestImpl(
      {required this.pageIndex,
      required this.pageSize,
      required this.orderBy,
      required this.searchTerm})
      : super._();

  factory _$MListVehicleGroupRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$MListVehicleGroupRequestImplFromJson(json);

  @override
  final int pageIndex;
  @override
  final int pageSize;
  @override
  final String orderBy;
  @override
  final String searchTerm;

  @override
  String toString() {
    return 'MListVehicleGroupRequest(pageIndex: $pageIndex, pageSize: $pageSize, orderBy: $orderBy, searchTerm: $searchTerm)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MListVehicleGroupRequestImpl &&
            (identical(other.pageIndex, pageIndex) ||
                other.pageIndex == pageIndex) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.orderBy, orderBy) || other.orderBy == orderBy) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, pageIndex, pageSize, orderBy, searchTerm);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MListVehicleGroupRequestImplCopyWith<_$MListVehicleGroupRequestImpl>
      get copyWith => __$$MListVehicleGroupRequestImplCopyWithImpl<
          _$MListVehicleGroupRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MListVehicleGroupRequestImplToJson(
      this,
    );
  }
}

abstract class _MListVehicleGroupRequest extends MListVehicleGroupRequest {
  const factory _MListVehicleGroupRequest(
      {required final int pageIndex,
      required final int pageSize,
      required final String orderBy,
      required final String searchTerm}) = _$MListVehicleGroupRequestImpl;
  const _MListVehicleGroupRequest._() : super._();

  factory _MListVehicleGroupRequest.fromJson(Map<String, dynamic> json) =
      _$MListVehicleGroupRequestImpl.fromJson;

  @override
  int get pageIndex;
  @override
  int get pageSize;
  @override
  String get orderBy;
  @override
  String get searchTerm;
  @override
  @JsonKey(ignore: true)
  _$$MListVehicleGroupRequestImplCopyWith<_$MListVehicleGroupRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
