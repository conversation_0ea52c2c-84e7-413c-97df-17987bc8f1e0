// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'm_list_vehicle_group_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MListVehicleGroupResponseImpl _$$MListVehicleGroupResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$MListVehicleGroupResponseImpl(
      total: (json['total'] as num).toInt(),
      data: (json['data'] as List<dynamic>)
          .map((e) => MVehicleGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$MListVehicleGroupResponseImplToJson(
        _$MListVehicleGroupResponseImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'data': instance.data,
    };

_$MVehicleGroupImpl _$$MVehicleGroupImplFromJson(Map<String, dynamic> json) =>
    _$MVehicleGroupImpl(
      id: json['id'] as String,
      name: json['name'] as String,
    );

Map<String, dynamic> _$$MVehicleGroupImplToJson(_$MVehicleGroupImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
    };
