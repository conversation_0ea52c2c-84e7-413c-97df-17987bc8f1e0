// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_vehicle_setting_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MVehicleSettingResponse _$MVehicleSettingResponseFromJson(
    Map<String, dynamic> json) {
  return _MVehicleSettingResponse.fromJson(json);
}

/// @nodoc
mixin _$MVehicleSettingResponse {
  int get total => throw _privateConstructorUsedError;
  List<VechileSetting> get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MVehicleSettingResponseCopyWith<MVehicleSettingResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MVehicleSettingResponseCopyWith<$Res> {
  factory $MVehicleSettingResponseCopyWith(MVehicleSettingResponse value,
          $Res Function(MVehicleSettingResponse) then) =
      _$MVehicleSettingResponseCopyWithImpl<$Res, MVehicleSettingResponse>;
  @useResult
  $Res call({int total, List<VechileSetting> data});
}

/// @nodoc
class _$MVehicleSettingResponseCopyWithImpl<$Res,
        $Val extends MVehicleSettingResponse>
    implements $MVehicleSettingResponseCopyWith<$Res> {
  _$MVehicleSettingResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<VechileSetting>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MVehicleSettingResponseImplCopyWith<$Res>
    implements $MVehicleSettingResponseCopyWith<$Res> {
  factory _$$MVehicleSettingResponseImplCopyWith(
          _$MVehicleSettingResponseImpl value,
          $Res Function(_$MVehicleSettingResponseImpl) then) =
      __$$MVehicleSettingResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, List<VechileSetting> data});
}

/// @nodoc
class __$$MVehicleSettingResponseImplCopyWithImpl<$Res>
    extends _$MVehicleSettingResponseCopyWithImpl<$Res,
        _$MVehicleSettingResponseImpl>
    implements _$$MVehicleSettingResponseImplCopyWith<$Res> {
  __$$MVehicleSettingResponseImplCopyWithImpl(
      _$MVehicleSettingResponseImpl _value,
      $Res Function(_$MVehicleSettingResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_$MVehicleSettingResponseImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<VechileSetting>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MVehicleSettingResponseImpl extends _MVehicleSettingResponse {
  const _$MVehicleSettingResponseImpl(
      {this.total = 0, final List<VechileSetting> data = const []})
      : _data = data,
        super._();

  factory _$MVehicleSettingResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MVehicleSettingResponseImplFromJson(json);

  @override
  @JsonKey()
  final int total;
  final List<VechileSetting> _data;
  @override
  @JsonKey()
  List<VechileSetting> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'MVehicleSettingResponse(total: $total, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleSettingResponseImpl &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, total, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MVehicleSettingResponseImplCopyWith<_$MVehicleSettingResponseImpl>
      get copyWith => __$$MVehicleSettingResponseImplCopyWithImpl<
          _$MVehicleSettingResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MVehicleSettingResponseImplToJson(
      this,
    );
  }
}

abstract class _MVehicleSettingResponse extends MVehicleSettingResponse {
  const factory _MVehicleSettingResponse(
      {final int total,
      final List<VechileSetting> data}) = _$MVehicleSettingResponseImpl;
  const _MVehicleSettingResponse._() : super._();

  factory _MVehicleSettingResponse.fromJson(Map<String, dynamic> json) =
      _$MVehicleSettingResponseImpl.fromJson;

  @override
  int get total;
  @override
  List<VechileSetting> get data;
  @override
  @JsonKey(ignore: true)
  _$$MVehicleSettingResponseImplCopyWith<_$MVehicleSettingResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

VechileSetting _$VechileSettingFromJson(Map<String, dynamic> json) {
  return _VechileSetting.fromJson(json);
}

/// @nodoc
mixin _$VechileSetting {
  @JsonKey(name: 'Id')
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'Icon')
  int get icon => throw _privateConstructorUsedError;
  @JsonKey(name: 'VehicleType')
  String get vehicleType => throw _privateConstructorUsedError;
  @JsonKey(name: 'VehicleGroup')
  String get vehicleGroup => throw _privateConstructorUsedError;
  @JsonKey(name: 'Plate')
  String get plate => throw _privateConstructorUsedError;
  @JsonKey(name: 'Company')
  String get company => throw _privateConstructorUsedError;
  @JsonKey(name: 'DeviceType')
  String get deviceType => throw _privateConstructorUsedError;
  @JsonKey(name: 'SendVehicle')
  bool get sendVehicle => throw _privateConstructorUsedError;
  @JsonKey(name: 'SendDriver')
  bool get sendDriver => throw _privateConstructorUsedError;
  @JsonKey(name: 'Driver')
  dynamic get driver => throw _privateConstructorUsedError;
  @JsonKey(name: 'DriverNo')
  dynamic get driverNo => throw _privateConstructorUsedError;
  @JsonKey(name: 'DriverLicenseNo')
  dynamic get driverLicenseNo => throw _privateConstructorUsedError;
  @JsonKey(name: 'GovmtGpsTime')
  int get govmtGpsTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'GovmtStatus')
  String get govmtStatus => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VechileSettingCopyWith<VechileSetting> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VechileSettingCopyWith<$Res> {
  factory $VechileSettingCopyWith(
          VechileSetting value, $Res Function(VechileSetting) then) =
      _$VechileSettingCopyWithImpl<$Res, VechileSetting>;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String id,
      @JsonKey(name: 'Icon') int icon,
      @JsonKey(name: 'VehicleType') String vehicleType,
      @JsonKey(name: 'VehicleGroup') String vehicleGroup,
      @JsonKey(name: 'Plate') String plate,
      @JsonKey(name: 'Company') String company,
      @JsonKey(name: 'DeviceType') String deviceType,
      @JsonKey(name: 'SendVehicle') bool sendVehicle,
      @JsonKey(name: 'SendDriver') bool sendDriver,
      @JsonKey(name: 'Driver') dynamic driver,
      @JsonKey(name: 'DriverNo') dynamic driverNo,
      @JsonKey(name: 'DriverLicenseNo') dynamic driverLicenseNo,
      @JsonKey(name: 'GovmtGpsTime') int govmtGpsTime,
      @JsonKey(name: 'GovmtStatus') String govmtStatus});
}

/// @nodoc
class _$VechileSettingCopyWithImpl<$Res, $Val extends VechileSetting>
    implements $VechileSettingCopyWith<$Res> {
  _$VechileSettingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? icon = null,
    Object? vehicleType = null,
    Object? vehicleGroup = null,
    Object? plate = null,
    Object? company = null,
    Object? deviceType = null,
    Object? sendVehicle = null,
    Object? sendDriver = null,
    Object? driver = freezed,
    Object? driverNo = freezed,
    Object? driverLicenseNo = freezed,
    Object? govmtGpsTime = null,
    Object? govmtStatus = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleType: null == vehicleType
          ? _value.vehicleType
          : vehicleType // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleGroup: null == vehicleGroup
          ? _value.vehicleGroup
          : vehicleGroup // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      company: null == company
          ? _value.company
          : company // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String,
      sendVehicle: null == sendVehicle
          ? _value.sendVehicle
          : sendVehicle // ignore: cast_nullable_to_non_nullable
              as bool,
      sendDriver: null == sendDriver
          ? _value.sendDriver
          : sendDriver // ignore: cast_nullable_to_non_nullable
              as bool,
      driver: freezed == driver
          ? _value.driver
          : driver // ignore: cast_nullable_to_non_nullable
              as dynamic,
      driverNo: freezed == driverNo
          ? _value.driverNo
          : driverNo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      driverLicenseNo: freezed == driverLicenseNo
          ? _value.driverLicenseNo
          : driverLicenseNo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      govmtGpsTime: null == govmtGpsTime
          ? _value.govmtGpsTime
          : govmtGpsTime // ignore: cast_nullable_to_non_nullable
              as int,
      govmtStatus: null == govmtStatus
          ? _value.govmtStatus
          : govmtStatus // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VechileSettingImplCopyWith<$Res>
    implements $VechileSettingCopyWith<$Res> {
  factory _$$VechileSettingImplCopyWith(_$VechileSettingImpl value,
          $Res Function(_$VechileSettingImpl) then) =
      __$$VechileSettingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String id,
      @JsonKey(name: 'Icon') int icon,
      @JsonKey(name: 'VehicleType') String vehicleType,
      @JsonKey(name: 'VehicleGroup') String vehicleGroup,
      @JsonKey(name: 'Plate') String plate,
      @JsonKey(name: 'Company') String company,
      @JsonKey(name: 'DeviceType') String deviceType,
      @JsonKey(name: 'SendVehicle') bool sendVehicle,
      @JsonKey(name: 'SendDriver') bool sendDriver,
      @JsonKey(name: 'Driver') dynamic driver,
      @JsonKey(name: 'DriverNo') dynamic driverNo,
      @JsonKey(name: 'DriverLicenseNo') dynamic driverLicenseNo,
      @JsonKey(name: 'GovmtGpsTime') int govmtGpsTime,
      @JsonKey(name: 'GovmtStatus') String govmtStatus});
}

/// @nodoc
class __$$VechileSettingImplCopyWithImpl<$Res>
    extends _$VechileSettingCopyWithImpl<$Res, _$VechileSettingImpl>
    implements _$$VechileSettingImplCopyWith<$Res> {
  __$$VechileSettingImplCopyWithImpl(
      _$VechileSettingImpl _value, $Res Function(_$VechileSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? icon = null,
    Object? vehicleType = null,
    Object? vehicleGroup = null,
    Object? plate = null,
    Object? company = null,
    Object? deviceType = null,
    Object? sendVehicle = null,
    Object? sendDriver = null,
    Object? driver = freezed,
    Object? driverNo = freezed,
    Object? driverLicenseNo = freezed,
    Object? govmtGpsTime = null,
    Object? govmtStatus = null,
  }) {
    return _then(_$VechileSettingImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleType: null == vehicleType
          ? _value.vehicleType
          : vehicleType // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleGroup: null == vehicleGroup
          ? _value.vehicleGroup
          : vehicleGroup // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      company: null == company
          ? _value.company
          : company // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String,
      sendVehicle: null == sendVehicle
          ? _value.sendVehicle
          : sendVehicle // ignore: cast_nullable_to_non_nullable
              as bool,
      sendDriver: null == sendDriver
          ? _value.sendDriver
          : sendDriver // ignore: cast_nullable_to_non_nullable
              as bool,
      driver: freezed == driver
          ? _value.driver
          : driver // ignore: cast_nullable_to_non_nullable
              as dynamic,
      driverNo: freezed == driverNo
          ? _value.driverNo
          : driverNo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      driverLicenseNo: freezed == driverLicenseNo
          ? _value.driverLicenseNo
          : driverLicenseNo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      govmtGpsTime: null == govmtGpsTime
          ? _value.govmtGpsTime
          : govmtGpsTime // ignore: cast_nullable_to_non_nullable
              as int,
      govmtStatus: null == govmtStatus
          ? _value.govmtStatus
          : govmtStatus // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VechileSettingImpl extends _VechileSetting {
  const _$VechileSettingImpl(
      {@JsonKey(name: 'Id') this.id = "",
      @JsonKey(name: 'Icon') this.icon = 0,
      @JsonKey(name: 'VehicleType') this.vehicleType = "",
      @JsonKey(name: 'VehicleGroup') this.vehicleGroup = "",
      @JsonKey(name: 'Plate') this.plate = "",
      @JsonKey(name: 'Company') this.company = "",
      @JsonKey(name: 'DeviceType') this.deviceType = "",
      @JsonKey(name: 'SendVehicle') this.sendVehicle = false,
      @JsonKey(name: 'SendDriver') this.sendDriver = false,
      @JsonKey(name: 'Driver') this.driver,
      @JsonKey(name: 'DriverNo') this.driverNo,
      @JsonKey(name: 'DriverLicenseNo') this.driverLicenseNo,
      @JsonKey(name: 'GovmtGpsTime') this.govmtGpsTime = 0,
      @JsonKey(name: 'GovmtStatus') this.govmtStatus = ""})
      : super._();

  factory _$VechileSettingImpl.fromJson(Map<String, dynamic> json) =>
      _$$VechileSettingImplFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final String id;
  @override
  @JsonKey(name: 'Icon')
  final int icon;
  @override
  @JsonKey(name: 'VehicleType')
  final String vehicleType;
  @override
  @JsonKey(name: 'VehicleGroup')
  final String vehicleGroup;
  @override
  @JsonKey(name: 'Plate')
  final String plate;
  @override
  @JsonKey(name: 'Company')
  final String company;
  @override
  @JsonKey(name: 'DeviceType')
  final String deviceType;
  @override
  @JsonKey(name: 'SendVehicle')
  final bool sendVehicle;
  @override
  @JsonKey(name: 'SendDriver')
  final bool sendDriver;
  @override
  @JsonKey(name: 'Driver')
  final dynamic driver;
  @override
  @JsonKey(name: 'DriverNo')
  final dynamic driverNo;
  @override
  @JsonKey(name: 'DriverLicenseNo')
  final dynamic driverLicenseNo;
  @override
  @JsonKey(name: 'GovmtGpsTime')
  final int govmtGpsTime;
  @override
  @JsonKey(name: 'GovmtStatus')
  final String govmtStatus;

  @override
  String toString() {
    return 'VechileSetting(id: $id, icon: $icon, vehicleType: $vehicleType, vehicleGroup: $vehicleGroup, plate: $plate, company: $company, deviceType: $deviceType, sendVehicle: $sendVehicle, sendDriver: $sendDriver, driver: $driver, driverNo: $driverNo, driverLicenseNo: $driverLicenseNo, govmtGpsTime: $govmtGpsTime, govmtStatus: $govmtStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VechileSettingImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.vehicleType, vehicleType) ||
                other.vehicleType == vehicleType) &&
            (identical(other.vehicleGroup, vehicleGroup) ||
                other.vehicleGroup == vehicleGroup) &&
            (identical(other.plate, plate) || other.plate == plate) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.sendVehicle, sendVehicle) ||
                other.sendVehicle == sendVehicle) &&
            (identical(other.sendDriver, sendDriver) ||
                other.sendDriver == sendDriver) &&
            const DeepCollectionEquality().equals(other.driver, driver) &&
            const DeepCollectionEquality().equals(other.driverNo, driverNo) &&
            const DeepCollectionEquality()
                .equals(other.driverLicenseNo, driverLicenseNo) &&
            (identical(other.govmtGpsTime, govmtGpsTime) ||
                other.govmtGpsTime == govmtGpsTime) &&
            (identical(other.govmtStatus, govmtStatus) ||
                other.govmtStatus == govmtStatus));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      icon,
      vehicleType,
      vehicleGroup,
      plate,
      company,
      deviceType,
      sendVehicle,
      sendDriver,
      const DeepCollectionEquality().hash(driver),
      const DeepCollectionEquality().hash(driverNo),
      const DeepCollectionEquality().hash(driverLicenseNo),
      govmtGpsTime,
      govmtStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VechileSettingImplCopyWith<_$VechileSettingImpl> get copyWith =>
      __$$VechileSettingImplCopyWithImpl<_$VechileSettingImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VechileSettingImplToJson(
      this,
    );
  }
}

abstract class _VechileSetting extends VechileSetting {
  const factory _VechileSetting(
          {@JsonKey(name: 'Id') final String id,
          @JsonKey(name: 'Icon') final int icon,
          @JsonKey(name: 'VehicleType') final String vehicleType,
          @JsonKey(name: 'VehicleGroup') final String vehicleGroup,
          @JsonKey(name: 'Plate') final String plate,
          @JsonKey(name: 'Company') final String company,
          @JsonKey(name: 'DeviceType') final String deviceType,
          @JsonKey(name: 'SendVehicle') final bool sendVehicle,
          @JsonKey(name: 'SendDriver') final bool sendDriver,
          @JsonKey(name: 'Driver') final dynamic driver,
          @JsonKey(name: 'DriverNo') final dynamic driverNo,
          @JsonKey(name: 'DriverLicenseNo') final dynamic driverLicenseNo,
          @JsonKey(name: 'GovmtGpsTime') final int govmtGpsTime,
          @JsonKey(name: 'GovmtStatus') final String govmtStatus}) =
      _$VechileSettingImpl;
  const _VechileSetting._() : super._();

  factory _VechileSetting.fromJson(Map<String, dynamic> json) =
      _$VechileSettingImpl.fromJson;

  @override
  @JsonKey(name: 'Id')
  String get id;
  @override
  @JsonKey(name: 'Icon')
  int get icon;
  @override
  @JsonKey(name: 'VehicleType')
  String get vehicleType;
  @override
  @JsonKey(name: 'VehicleGroup')
  String get vehicleGroup;
  @override
  @JsonKey(name: 'Plate')
  String get plate;
  @override
  @JsonKey(name: 'Company')
  String get company;
  @override
  @JsonKey(name: 'DeviceType')
  String get deviceType;
  @override
  @JsonKey(name: 'SendVehicle')
  bool get sendVehicle;
  @override
  @JsonKey(name: 'SendDriver')
  bool get sendDriver;
  @override
  @JsonKey(name: 'Driver')
  dynamic get driver;
  @override
  @JsonKey(name: 'DriverNo')
  dynamic get driverNo;
  @override
  @JsonKey(name: 'DriverLicenseNo')
  dynamic get driverLicenseNo;
  @override
  @JsonKey(name: 'GovmtGpsTime')
  int get govmtGpsTime;
  @override
  @JsonKey(name: 'GovmtStatus')
  String get govmtStatus;
  @override
  @JsonKey(ignore: true)
  _$$VechileSettingImplCopyWith<_$VechileSettingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
