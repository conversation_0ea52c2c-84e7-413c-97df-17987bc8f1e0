// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'm_vehicle_setting_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MVehicleSettingRequestImpl _$$MVehicleSettingRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$MVehicleSettingRequestImpl(
      pageIndex: (json['pageIndex'] as num).toInt(),
      pageSize: (json['pageSize'] as num).toInt(),
      orderBy: json['orderBy'] as String? ?? '',
      searchTerm: json['searchTerm'] as String? ?? '',
    );

Map<String, dynamic> _$$MVehicleSettingRequestImplToJson(
        _$MVehicleSettingRequestImpl instance) =>
    <String, dynamic>{
      'pageIndex': instance.pageIndex,
      'pageSize': instance.pageSize,
      'orderBy': instance.orderBy,
      'searchTerm': instance.searchTerm,
    };
