// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'm_update_vehicle_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MUpdateVehicleRequestImpl _$$MUpdateVehicleRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$MUpdateVehicleRequestImpl(
      id: json['id'] as String,
      plate: json['plate'] as String,
      icon: (json['icon'] as num).toInt(),
      maxSpeed: (json['maxSpeed'] as num).toInt(),
      productYear: (json['productYear'] as num).toInt(),
      soldVehicle: json['soldVehicle'] as bool,
      enginNo: json['enginNo'] as String,
      vin: json['vin'] as String,
      vehicleTypeId: json['vehicleTypeId'] as String,
      vehicleGroupId: json['vehicleGroupId'] as String,
      fuelPer100Km: (json['fuelPer100Km'] as num).toInt(),
      fuelIdle: (json['fuelIdle'] as num).toInt(),
      fuelIgnition: (json['fuelIgnition'] as num).toInt(),
    );

Map<String, dynamic> _$$MUpdateVehicleRequestImplToJson(
        _$MUpdateVehicleRequestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'plate': instance.plate,
      'icon': instance.icon,
      'maxSpeed': instance.maxSpeed,
      'productYear': instance.productYear,
      'soldVehicle': instance.soldVehicle,
      'enginNo': instance.enginNo,
      'vin': instance.vin,
      'vehicleTypeId': instance.vehicleTypeId,
      'vehicleGroupId': instance.vehicleGroupId,
      'fuelPer100Km': instance.fuelPer100Km,
      'fuelIdle': instance.fuelIdle,
      'fuelIgnition': instance.fuelIgnition,
    };
