// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_special_cargo_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MSpecialCargoResponse _$MSpecialCargoResponseFromJson(
    Map<String, dynamic> json) {
  return _MSpecialCargoResponse.fromJson(json);
}

/// @nodoc
mixin _$MSpecialCargoResponse {
  int get total => throw _privateConstructorUsedError;
  List<SpecialCargo> get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MSpecialCargoResponseCopyWith<MSpecialCargoResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MSpecialCargoResponseCopyWith<$Res> {
  factory $MSpecialCargoResponseCopyWith(MSpecialCargoResponse value,
          $Res Function(MSpecialCargoResponse) then) =
      _$MSpecialCargoResponseCopyWithImpl<$Res, MSpecialCargoResponse>;
  @useResult
  $Res call({int total, List<SpecialCargo> data});
}

/// @nodoc
class _$MSpecialCargoResponseCopyWithImpl<$Res,
        $Val extends MSpecialCargoResponse>
    implements $MSpecialCargoResponseCopyWith<$Res> {
  _$MSpecialCargoResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<SpecialCargo>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MSpecialCargoResponseImplCopyWith<$Res>
    implements $MSpecialCargoResponseCopyWith<$Res> {
  factory _$$MSpecialCargoResponseImplCopyWith(
          _$MSpecialCargoResponseImpl value,
          $Res Function(_$MSpecialCargoResponseImpl) then) =
      __$$MSpecialCargoResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, List<SpecialCargo> data});
}

/// @nodoc
class __$$MSpecialCargoResponseImplCopyWithImpl<$Res>
    extends _$MSpecialCargoResponseCopyWithImpl<$Res,
        _$MSpecialCargoResponseImpl>
    implements _$$MSpecialCargoResponseImplCopyWith<$Res> {
  __$$MSpecialCargoResponseImplCopyWithImpl(_$MSpecialCargoResponseImpl _value,
      $Res Function(_$MSpecialCargoResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_$MSpecialCargoResponseImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<SpecialCargo>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MSpecialCargoResponseImpl extends _MSpecialCargoResponse {
  const _$MSpecialCargoResponseImpl(
      {this.total = 0, final List<SpecialCargo> data = const []})
      : _data = data,
        super._();

  factory _$MSpecialCargoResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MSpecialCargoResponseImplFromJson(json);

  @override
  @JsonKey()
  final int total;
  final List<SpecialCargo> _data;
  @override
  @JsonKey()
  List<SpecialCargo> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'MSpecialCargoResponse(total: $total, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MSpecialCargoResponseImpl &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, total, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MSpecialCargoResponseImplCopyWith<_$MSpecialCargoResponseImpl>
      get copyWith => __$$MSpecialCargoResponseImplCopyWithImpl<
          _$MSpecialCargoResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MSpecialCargoResponseImplToJson(
      this,
    );
  }
}

abstract class _MSpecialCargoResponse extends MSpecialCargoResponse {
  const factory _MSpecialCargoResponse(
      {final int total,
      final List<SpecialCargo> data}) = _$MSpecialCargoResponseImpl;
  const _MSpecialCargoResponse._() : super._();

  factory _MSpecialCargoResponse.fromJson(Map<String, dynamic> json) =
      _$MSpecialCargoResponseImpl.fromJson;

  @override
  int get total;
  @override
  List<SpecialCargo> get data;
  @override
  @JsonKey(ignore: true)
  _$$MSpecialCargoResponseImplCopyWith<_$MSpecialCargoResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SpecialCargo _$SpecialCargoFromJson(Map<String, dynamic> json) {
  return _SpecialCargo.fromJson(json);
}

/// @nodoc
mixin _$SpecialCargo {
  @JsonKey(name: 'Id')
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'Icon')
  int get icon => throw _privateConstructorUsedError;
  @JsonKey(name: 'VehicleType')
  String get vehicleType => throw _privateConstructorUsedError;
  @JsonKey(name: 'VehicleGroup')
  String get vehicleGroup => throw _privateConstructorUsedError;
  @JsonKey(name: 'Plate')
  String get plate => throw _privateConstructorUsedError;
  @JsonKey(name: 'Company')
  String get company => throw _privateConstructorUsedError;
  @JsonKey(name: 'DeviceType')
  String get deviceType => throw _privateConstructorUsedError;
  @JsonKey(name: 'Field1')
  bool get field1 => throw _privateConstructorUsedError;
  @JsonKey(name: 'Field2')
  String get field2 => throw _privateConstructorUsedError;
  @JsonKey(name: 'Field3')
  String get field3 => throw _privateConstructorUsedError;
  @JsonKey(name: 'Field4')
  String get field4 => throw _privateConstructorUsedError;
  @JsonKey(name: 'Field5')
  String get field5 => throw _privateConstructorUsedError;
  @JsonKey(name: 'Field6')
  String get field6 => throw _privateConstructorUsedError;
  @JsonKey(name: 'Field7')
  String get field7 => throw _privateConstructorUsedError;
  @JsonKey(name: 'GovmtGpsTime')
  int get govmtGpsTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'GovmtStatus')
  String get govmtStatus => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpecialCargoCopyWith<SpecialCargo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpecialCargoCopyWith<$Res> {
  factory $SpecialCargoCopyWith(
          SpecialCargo value, $Res Function(SpecialCargo) then) =
      _$SpecialCargoCopyWithImpl<$Res, SpecialCargo>;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String id,
      @JsonKey(name: 'Icon') int icon,
      @JsonKey(name: 'VehicleType') String vehicleType,
      @JsonKey(name: 'VehicleGroup') String vehicleGroup,
      @JsonKey(name: 'Plate') String plate,
      @JsonKey(name: 'Company') String company,
      @JsonKey(name: 'DeviceType') String deviceType,
      @JsonKey(name: 'Field1') bool field1,
      @JsonKey(name: 'Field2') String field2,
      @JsonKey(name: 'Field3') String field3,
      @JsonKey(name: 'Field4') String field4,
      @JsonKey(name: 'Field5') String field5,
      @JsonKey(name: 'Field6') String field6,
      @JsonKey(name: 'Field7') String field7,
      @JsonKey(name: 'GovmtGpsTime') int govmtGpsTime,
      @JsonKey(name: 'GovmtStatus') String govmtStatus});
}

/// @nodoc
class _$SpecialCargoCopyWithImpl<$Res, $Val extends SpecialCargo>
    implements $SpecialCargoCopyWith<$Res> {
  _$SpecialCargoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? icon = null,
    Object? vehicleType = null,
    Object? vehicleGroup = null,
    Object? plate = null,
    Object? company = null,
    Object? deviceType = null,
    Object? field1 = null,
    Object? field2 = null,
    Object? field3 = null,
    Object? field4 = null,
    Object? field5 = null,
    Object? field6 = null,
    Object? field7 = null,
    Object? govmtGpsTime = null,
    Object? govmtStatus = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleType: null == vehicleType
          ? _value.vehicleType
          : vehicleType // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleGroup: null == vehicleGroup
          ? _value.vehicleGroup
          : vehicleGroup // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      company: null == company
          ? _value.company
          : company // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String,
      field1: null == field1
          ? _value.field1
          : field1 // ignore: cast_nullable_to_non_nullable
              as bool,
      field2: null == field2
          ? _value.field2
          : field2 // ignore: cast_nullable_to_non_nullable
              as String,
      field3: null == field3
          ? _value.field3
          : field3 // ignore: cast_nullable_to_non_nullable
              as String,
      field4: null == field4
          ? _value.field4
          : field4 // ignore: cast_nullable_to_non_nullable
              as String,
      field5: null == field5
          ? _value.field5
          : field5 // ignore: cast_nullable_to_non_nullable
              as String,
      field6: null == field6
          ? _value.field6
          : field6 // ignore: cast_nullable_to_non_nullable
              as String,
      field7: null == field7
          ? _value.field7
          : field7 // ignore: cast_nullable_to_non_nullable
              as String,
      govmtGpsTime: null == govmtGpsTime
          ? _value.govmtGpsTime
          : govmtGpsTime // ignore: cast_nullable_to_non_nullable
              as int,
      govmtStatus: null == govmtStatus
          ? _value.govmtStatus
          : govmtStatus // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SpecialCargoImplCopyWith<$Res>
    implements $SpecialCargoCopyWith<$Res> {
  factory _$$SpecialCargoImplCopyWith(
          _$SpecialCargoImpl value, $Res Function(_$SpecialCargoImpl) then) =
      __$$SpecialCargoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String id,
      @JsonKey(name: 'Icon') int icon,
      @JsonKey(name: 'VehicleType') String vehicleType,
      @JsonKey(name: 'VehicleGroup') String vehicleGroup,
      @JsonKey(name: 'Plate') String plate,
      @JsonKey(name: 'Company') String company,
      @JsonKey(name: 'DeviceType') String deviceType,
      @JsonKey(name: 'Field1') bool field1,
      @JsonKey(name: 'Field2') String field2,
      @JsonKey(name: 'Field3') String field3,
      @JsonKey(name: 'Field4') String field4,
      @JsonKey(name: 'Field5') String field5,
      @JsonKey(name: 'Field6') String field6,
      @JsonKey(name: 'Field7') String field7,
      @JsonKey(name: 'GovmtGpsTime') int govmtGpsTime,
      @JsonKey(name: 'GovmtStatus') String govmtStatus});
}

/// @nodoc
class __$$SpecialCargoImplCopyWithImpl<$Res>
    extends _$SpecialCargoCopyWithImpl<$Res, _$SpecialCargoImpl>
    implements _$$SpecialCargoImplCopyWith<$Res> {
  __$$SpecialCargoImplCopyWithImpl(
      _$SpecialCargoImpl _value, $Res Function(_$SpecialCargoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? icon = null,
    Object? vehicleType = null,
    Object? vehicleGroup = null,
    Object? plate = null,
    Object? company = null,
    Object? deviceType = null,
    Object? field1 = null,
    Object? field2 = null,
    Object? field3 = null,
    Object? field4 = null,
    Object? field5 = null,
    Object? field6 = null,
    Object? field7 = null,
    Object? govmtGpsTime = null,
    Object? govmtStatus = null,
  }) {
    return _then(_$SpecialCargoImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleType: null == vehicleType
          ? _value.vehicleType
          : vehicleType // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleGroup: null == vehicleGroup
          ? _value.vehicleGroup
          : vehicleGroup // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      company: null == company
          ? _value.company
          : company // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String,
      field1: null == field1
          ? _value.field1
          : field1 // ignore: cast_nullable_to_non_nullable
              as bool,
      field2: null == field2
          ? _value.field2
          : field2 // ignore: cast_nullable_to_non_nullable
              as String,
      field3: null == field3
          ? _value.field3
          : field3 // ignore: cast_nullable_to_non_nullable
              as String,
      field4: null == field4
          ? _value.field4
          : field4 // ignore: cast_nullable_to_non_nullable
              as String,
      field5: null == field5
          ? _value.field5
          : field5 // ignore: cast_nullable_to_non_nullable
              as String,
      field6: null == field6
          ? _value.field6
          : field6 // ignore: cast_nullable_to_non_nullable
              as String,
      field7: null == field7
          ? _value.field7
          : field7 // ignore: cast_nullable_to_non_nullable
              as String,
      govmtGpsTime: null == govmtGpsTime
          ? _value.govmtGpsTime
          : govmtGpsTime // ignore: cast_nullable_to_non_nullable
              as int,
      govmtStatus: null == govmtStatus
          ? _value.govmtStatus
          : govmtStatus // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpecialCargoImpl extends _SpecialCargo {
  const _$SpecialCargoImpl(
      {@JsonKey(name: 'Id') this.id = "",
      @JsonKey(name: 'Icon') this.icon = 0,
      @JsonKey(name: 'VehicleType') this.vehicleType = "",
      @JsonKey(name: 'VehicleGroup') this.vehicleGroup = "",
      @JsonKey(name: 'Plate') this.plate = "",
      @JsonKey(name: 'Company') this.company = "",
      @JsonKey(name: 'DeviceType') this.deviceType = "",
      @JsonKey(name: 'Field1') this.field1 = false,
      @JsonKey(name: 'Field2') this.field2 = "",
      @JsonKey(name: 'Field3') this.field3 = "",
      @JsonKey(name: 'Field4') this.field4 = "",
      @JsonKey(name: 'Field5') this.field5 = "",
      @JsonKey(name: 'Field6') this.field6 = "",
      @JsonKey(name: 'Field7') this.field7 = "",
      @JsonKey(name: 'GovmtGpsTime') this.govmtGpsTime = 0,
      @JsonKey(name: 'GovmtStatus') this.govmtStatus = ""})
      : super._();

  factory _$SpecialCargoImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpecialCargoImplFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final String id;
  @override
  @JsonKey(name: 'Icon')
  final int icon;
  @override
  @JsonKey(name: 'VehicleType')
  final String vehicleType;
  @override
  @JsonKey(name: 'VehicleGroup')
  final String vehicleGroup;
  @override
  @JsonKey(name: 'Plate')
  final String plate;
  @override
  @JsonKey(name: 'Company')
  final String company;
  @override
  @JsonKey(name: 'DeviceType')
  final String deviceType;
  @override
  @JsonKey(name: 'Field1')
  final bool field1;
  @override
  @JsonKey(name: 'Field2')
  final String field2;
  @override
  @JsonKey(name: 'Field3')
  final String field3;
  @override
  @JsonKey(name: 'Field4')
  final String field4;
  @override
  @JsonKey(name: 'Field5')
  final String field5;
  @override
  @JsonKey(name: 'Field6')
  final String field6;
  @override
  @JsonKey(name: 'Field7')
  final String field7;
  @override
  @JsonKey(name: 'GovmtGpsTime')
  final int govmtGpsTime;
  @override
  @JsonKey(name: 'GovmtStatus')
  final String govmtStatus;

  @override
  String toString() {
    return 'SpecialCargo(id: $id, icon: $icon, vehicleType: $vehicleType, vehicleGroup: $vehicleGroup, plate: $plate, company: $company, deviceType: $deviceType, field1: $field1, field2: $field2, field3: $field3, field4: $field4, field5: $field5, field6: $field6, field7: $field7, govmtGpsTime: $govmtGpsTime, govmtStatus: $govmtStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpecialCargoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.vehicleType, vehicleType) ||
                other.vehicleType == vehicleType) &&
            (identical(other.vehicleGroup, vehicleGroup) ||
                other.vehicleGroup == vehicleGroup) &&
            (identical(other.plate, plate) || other.plate == plate) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.field1, field1) || other.field1 == field1) &&
            (identical(other.field2, field2) || other.field2 == field2) &&
            (identical(other.field3, field3) || other.field3 == field3) &&
            (identical(other.field4, field4) || other.field4 == field4) &&
            (identical(other.field5, field5) || other.field5 == field5) &&
            (identical(other.field6, field6) || other.field6 == field6) &&
            (identical(other.field7, field7) || other.field7 == field7) &&
            (identical(other.govmtGpsTime, govmtGpsTime) ||
                other.govmtGpsTime == govmtGpsTime) &&
            (identical(other.govmtStatus, govmtStatus) ||
                other.govmtStatus == govmtStatus));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      icon,
      vehicleType,
      vehicleGroup,
      plate,
      company,
      deviceType,
      field1,
      field2,
      field3,
      field4,
      field5,
      field6,
      field7,
      govmtGpsTime,
      govmtStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SpecialCargoImplCopyWith<_$SpecialCargoImpl> get copyWith =>
      __$$SpecialCargoImplCopyWithImpl<_$SpecialCargoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpecialCargoImplToJson(
      this,
    );
  }
}

abstract class _SpecialCargo extends SpecialCargo {
  const factory _SpecialCargo(
          {@JsonKey(name: 'Id') final String id,
          @JsonKey(name: 'Icon') final int icon,
          @JsonKey(name: 'VehicleType') final String vehicleType,
          @JsonKey(name: 'VehicleGroup') final String vehicleGroup,
          @JsonKey(name: 'Plate') final String plate,
          @JsonKey(name: 'Company') final String company,
          @JsonKey(name: 'DeviceType') final String deviceType,
          @JsonKey(name: 'Field1') final bool field1,
          @JsonKey(name: 'Field2') final String field2,
          @JsonKey(name: 'Field3') final String field3,
          @JsonKey(name: 'Field4') final String field4,
          @JsonKey(name: 'Field5') final String field5,
          @JsonKey(name: 'Field6') final String field6,
          @JsonKey(name: 'Field7') final String field7,
          @JsonKey(name: 'GovmtGpsTime') final int govmtGpsTime,
          @JsonKey(name: 'GovmtStatus') final String govmtStatus}) =
      _$SpecialCargoImpl;
  const _SpecialCargo._() : super._();

  factory _SpecialCargo.fromJson(Map<String, dynamic> json) =
      _$SpecialCargoImpl.fromJson;

  @override
  @JsonKey(name: 'Id')
  String get id;
  @override
  @JsonKey(name: 'Icon')
  int get icon;
  @override
  @JsonKey(name: 'VehicleType')
  String get vehicleType;
  @override
  @JsonKey(name: 'VehicleGroup')
  String get vehicleGroup;
  @override
  @JsonKey(name: 'Plate')
  String get plate;
  @override
  @JsonKey(name: 'Company')
  String get company;
  @override
  @JsonKey(name: 'DeviceType')
  String get deviceType;
  @override
  @JsonKey(name: 'Field1')
  bool get field1;
  @override
  @JsonKey(name: 'Field2')
  String get field2;
  @override
  @JsonKey(name: 'Field3')
  String get field3;
  @override
  @JsonKey(name: 'Field4')
  String get field4;
  @override
  @JsonKey(name: 'Field5')
  String get field5;
  @override
  @JsonKey(name: 'Field6')
  String get field6;
  @override
  @JsonKey(name: 'Field7')
  String get field7;
  @override
  @JsonKey(name: 'GovmtGpsTime')
  int get govmtGpsTime;
  @override
  @JsonKey(name: 'GovmtStatus')
  String get govmtStatus;
  @override
  @JsonKey(ignore: true)
  _$$SpecialCargoImplCopyWith<_$SpecialCargoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
