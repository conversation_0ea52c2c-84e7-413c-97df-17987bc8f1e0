// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_update_vehicle_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MUpdateVehicleResponse _$MUpdateVehicleResponseFromJson(
    Map<String, dynamic> json) {
  return _MUpdateVehicleResponse.fromJson(json);
}

/// @nodoc
mixin _$MUpdateVehicleResponse {
  int get total => throw _privateConstructorUsedError;
  MVehicle get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MUpdateVehicleResponseCopyWith<MUpdateVehicleResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MUpdateVehicleResponseCopyWith<$Res> {
  factory $MUpdateVehicleResponseCopyWith(MUpdateVehicleResponse value,
          $Res Function(MUpdateVehicleResponse) then) =
      _$MUpdateVehicleResponseCopyWithImpl<$Res, MUpdateVehicleResponse>;
  @useResult
  $Res call({int total, MVehicle data});

  $MVehicleCopyWith<$Res> get data;
}

/// @nodoc
class _$MUpdateVehicleResponseCopyWithImpl<$Res,
        $Val extends MUpdateVehicleResponse>
    implements $MUpdateVehicleResponseCopyWith<$Res> {
  _$MUpdateVehicleResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as MVehicle,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MVehicleCopyWith<$Res> get data {
    return $MVehicleCopyWith<$Res>(_value.data, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MUpdateVehicleResponseImplCopyWith<$Res>
    implements $MUpdateVehicleResponseCopyWith<$Res> {
  factory _$$MUpdateVehicleResponseImplCopyWith(
          _$MUpdateVehicleResponseImpl value,
          $Res Function(_$MUpdateVehicleResponseImpl) then) =
      __$$MUpdateVehicleResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, MVehicle data});

  @override
  $MVehicleCopyWith<$Res> get data;
}

/// @nodoc
class __$$MUpdateVehicleResponseImplCopyWithImpl<$Res>
    extends _$MUpdateVehicleResponseCopyWithImpl<$Res,
        _$MUpdateVehicleResponseImpl>
    implements _$$MUpdateVehicleResponseImplCopyWith<$Res> {
  __$$MUpdateVehicleResponseImplCopyWithImpl(
      _$MUpdateVehicleResponseImpl _value,
      $Res Function(_$MUpdateVehicleResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_$MUpdateVehicleResponseImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as MVehicle,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MUpdateVehicleResponseImpl extends _MUpdateVehicleResponse {
  const _$MUpdateVehicleResponseImpl({required this.total, required this.data})
      : super._();

  factory _$MUpdateVehicleResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MUpdateVehicleResponseImplFromJson(json);

  @override
  final int total;
  @override
  final MVehicle data;

  @override
  String toString() {
    return 'MUpdateVehicleResponse(total: $total, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MUpdateVehicleResponseImpl &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, total, data);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MUpdateVehicleResponseImplCopyWith<_$MUpdateVehicleResponseImpl>
      get copyWith => __$$MUpdateVehicleResponseImplCopyWithImpl<
          _$MUpdateVehicleResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MUpdateVehicleResponseImplToJson(
      this,
    );
  }
}

abstract class _MUpdateVehicleResponse extends MUpdateVehicleResponse {
  const factory _MUpdateVehicleResponse(
      {required final int total,
      required final MVehicle data}) = _$MUpdateVehicleResponseImpl;
  const _MUpdateVehicleResponse._() : super._();

  factory _MUpdateVehicleResponse.fromJson(Map<String, dynamic> json) =
      _$MUpdateVehicleResponseImpl.fromJson;

  @override
  int get total;
  @override
  MVehicle get data;
  @override
  @JsonKey(ignore: true)
  _$$MUpdateVehicleResponseImplCopyWith<_$MUpdateVehicleResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
