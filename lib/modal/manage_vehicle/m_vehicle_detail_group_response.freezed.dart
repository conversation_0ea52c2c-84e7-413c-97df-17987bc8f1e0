// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_vehicle_detail_group_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MVehicleDetailGroupResponse _$MVehicleDetailGroupResponseFromJson(
    Map<String, dynamic> json) {
  return _MVehicleDetailGroupResponse.fromJson(json);
}

/// @nodoc
mixin _$MVehicleDetailGroupResponse {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MVehicleDetailGroupResponseCopyWith<MVehicleDetailGroupResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MVehicleDetailGroupResponseCopyWith<$Res> {
  factory $MVehicleDetailGroupResponseCopyWith(
          MVehicleDetailGroupResponse value,
          $Res Function(MVehicleDetailGroupResponse) then) =
      _$MVehicleDetailGroupResponseCopyWithImpl<$Res,
          MVehicleDetailGroupResponse>;
  @useResult
  $Res call({String id, String name});
}

/// @nodoc
class _$MVehicleDetailGroupResponseCopyWithImpl<$Res,
        $Val extends MVehicleDetailGroupResponse>
    implements $MVehicleDetailGroupResponseCopyWith<$Res> {
  _$MVehicleDetailGroupResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MVehicleDetailGroupResponseImplCopyWith<$Res>
    implements $MVehicleDetailGroupResponseCopyWith<$Res> {
  factory _$$MVehicleDetailGroupResponseImplCopyWith(
          _$MVehicleDetailGroupResponseImpl value,
          $Res Function(_$MVehicleDetailGroupResponseImpl) then) =
      __$$MVehicleDetailGroupResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String name});
}

/// @nodoc
class __$$MVehicleDetailGroupResponseImplCopyWithImpl<$Res>
    extends _$MVehicleDetailGroupResponseCopyWithImpl<$Res,
        _$MVehicleDetailGroupResponseImpl>
    implements _$$MVehicleDetailGroupResponseImplCopyWith<$Res> {
  __$$MVehicleDetailGroupResponseImplCopyWithImpl(
      _$MVehicleDetailGroupResponseImpl _value,
      $Res Function(_$MVehicleDetailGroupResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
  }) {
    return _then(_$MVehicleDetailGroupResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MVehicleDetailGroupResponseImpl extends _MVehicleDetailGroupResponse {
  const _$MVehicleDetailGroupResponseImpl(
      {required this.id, required this.name})
      : super._();

  factory _$MVehicleDetailGroupResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$MVehicleDetailGroupResponseImplFromJson(json);

  @override
  final String id;
  @override
  final String name;

  @override
  String toString() {
    return 'MVehicleDetailGroupResponse(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleDetailGroupResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MVehicleDetailGroupResponseImplCopyWith<_$MVehicleDetailGroupResponseImpl>
      get copyWith => __$$MVehicleDetailGroupResponseImplCopyWithImpl<
          _$MVehicleDetailGroupResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MVehicleDetailGroupResponseImplToJson(
      this,
    );
  }
}

abstract class _MVehicleDetailGroupResponse
    extends MVehicleDetailGroupResponse {
  const factory _MVehicleDetailGroupResponse(
      {required final String id,
      required final String name}) = _$MVehicleDetailGroupResponseImpl;
  const _MVehicleDetailGroupResponse._() : super._();

  factory _MVehicleDetailGroupResponse.fromJson(Map<String, dynamic> json) =
      _$MVehicleDetailGroupResponseImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  @JsonKey(ignore: true)
  _$$MVehicleDetailGroupResponseImplCopyWith<_$MVehicleDetailGroupResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
