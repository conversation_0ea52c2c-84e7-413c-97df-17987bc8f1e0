// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_create_vehicle_group_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MCreateVehicleGroupRequest _$MCreateVehicleGroupRequestFromJson(
    Map<String, dynamic> json) {
  return _MCreateVehicleGroupRequest.fromJson(json);
}

/// @nodoc
mixin _$MCreateVehicleGroupRequest {
  String get name => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MCreateVehicleGroupRequestCopyWith<MCreateVehicleGroupRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MCreateVehicleGroupRequestCopyWith<$Res> {
  factory $MCreateVehicleGroupRequestCopyWith(MCreateVehicleGroupRequest value,
          $Res Function(MCreateVehicleGroupRequest) then) =
      _$MCreateVehicleGroupRequestCopyWithImpl<$Res,
          MCreateVehicleGroupRequest>;
  @useResult
  $Res call({String name});
}

/// @nodoc
class _$MCreateVehicleGroupRequestCopyWithImpl<$Res,
        $Val extends MCreateVehicleGroupRequest>
    implements $MCreateVehicleGroupRequestCopyWith<$Res> {
  _$MCreateVehicleGroupRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MCreateVehicleGroupRequestImplCopyWith<$Res>
    implements $MCreateVehicleGroupRequestCopyWith<$Res> {
  factory _$$MCreateVehicleGroupRequestImplCopyWith(
          _$MCreateVehicleGroupRequestImpl value,
          $Res Function(_$MCreateVehicleGroupRequestImpl) then) =
      __$$MCreateVehicleGroupRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name});
}

/// @nodoc
class __$$MCreateVehicleGroupRequestImplCopyWithImpl<$Res>
    extends _$MCreateVehicleGroupRequestCopyWithImpl<$Res,
        _$MCreateVehicleGroupRequestImpl>
    implements _$$MCreateVehicleGroupRequestImplCopyWith<$Res> {
  __$$MCreateVehicleGroupRequestImplCopyWithImpl(
      _$MCreateVehicleGroupRequestImpl _value,
      $Res Function(_$MCreateVehicleGroupRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
  }) {
    return _then(_$MCreateVehicleGroupRequestImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MCreateVehicleGroupRequestImpl extends _MCreateVehicleGroupRequest {
  const _$MCreateVehicleGroupRequestImpl({required this.name}) : super._();

  factory _$MCreateVehicleGroupRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$MCreateVehicleGroupRequestImplFromJson(json);

  @override
  final String name;

  @override
  String toString() {
    return 'MCreateVehicleGroupRequest(name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MCreateVehicleGroupRequestImpl &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MCreateVehicleGroupRequestImplCopyWith<_$MCreateVehicleGroupRequestImpl>
      get copyWith => __$$MCreateVehicleGroupRequestImplCopyWithImpl<
          _$MCreateVehicleGroupRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MCreateVehicleGroupRequestImplToJson(
      this,
    );
  }
}

abstract class _MCreateVehicleGroupRequest extends MCreateVehicleGroupRequest {
  const factory _MCreateVehicleGroupRequest({required final String name}) =
      _$MCreateVehicleGroupRequestImpl;
  const _MCreateVehicleGroupRequest._() : super._();

  factory _MCreateVehicleGroupRequest.fromJson(Map<String, dynamic> json) =
      _$MCreateVehicleGroupRequestImpl.fromJson;

  @override
  String get name;
  @override
  @JsonKey(ignore: true)
  _$$MCreateVehicleGroupRequestImplCopyWith<_$MCreateVehicleGroupRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
