// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_vehicle_detail_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MVehicleDetailResponse _$MVehicleDetailResponseFromJson(
    Map<String, dynamic> json) {
  return _MVehicleDetailResponse.fromJson(json);
}

/// @nodoc
mixin _$MVehicleDetailResponse {
  String get id => throw _privateConstructorUsedError;
  String get plate => throw _privateConstructorUsedError;
  int get icon => throw _privateConstructorUsedError;
  int get maxSpeed => throw _privateConstructorUsedError;
  int get productYear => throw _privateConstructorUsedError;
  bool get soldVehicle => throw _privateConstructorUsedError;
  String? get enginNo => throw _privateConstructorUsedError;
  String? get vin => throw _privateConstructorUsedError;
  String? get vehicleTypeId => throw _privateConstructorUsedError;
  String? get vehicleGroupId => throw _privateConstructorUsedError;
  String? get fuelTypeId => throw _privateConstructorUsedError;
  int get fuelPer100Km => throw _privateConstructorUsedError;
  int get fuelIdle => throw _privateConstructorUsedError;
  int get fuelIgnition => throw _privateConstructorUsedError;
  String? get fields => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MVehicleDetailResponseCopyWith<MVehicleDetailResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MVehicleDetailResponseCopyWith<$Res> {
  factory $MVehicleDetailResponseCopyWith(MVehicleDetailResponse value,
          $Res Function(MVehicleDetailResponse) then) =
      _$MVehicleDetailResponseCopyWithImpl<$Res, MVehicleDetailResponse>;
  @useResult
  $Res call(
      {String id,
      String plate,
      int icon,
      int maxSpeed,
      int productYear,
      bool soldVehicle,
      String? enginNo,
      String? vin,
      String? vehicleTypeId,
      String? vehicleGroupId,
      String? fuelTypeId,
      int fuelPer100Km,
      int fuelIdle,
      int fuelIgnition,
      String? fields});
}

/// @nodoc
class _$MVehicleDetailResponseCopyWithImpl<$Res,
        $Val extends MVehicleDetailResponse>
    implements $MVehicleDetailResponseCopyWith<$Res> {
  _$MVehicleDetailResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? icon = null,
    Object? maxSpeed = null,
    Object? productYear = null,
    Object? soldVehicle = null,
    Object? enginNo = freezed,
    Object? vin = freezed,
    Object? vehicleTypeId = freezed,
    Object? vehicleGroupId = freezed,
    Object? fuelTypeId = freezed,
    Object? fuelPer100Km = null,
    Object? fuelIdle = null,
    Object? fuelIgnition = null,
    Object? fields = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      productYear: null == productYear
          ? _value.productYear
          : productYear // ignore: cast_nullable_to_non_nullable
              as int,
      soldVehicle: null == soldVehicle
          ? _value.soldVehicle
          : soldVehicle // ignore: cast_nullable_to_non_nullable
              as bool,
      enginNo: freezed == enginNo
          ? _value.enginNo
          : enginNo // ignore: cast_nullable_to_non_nullable
              as String?,
      vin: freezed == vin
          ? _value.vin
          : vin // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleTypeId: freezed == vehicleTypeId
          ? _value.vehicleTypeId
          : vehicleTypeId // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleGroupId: freezed == vehicleGroupId
          ? _value.vehicleGroupId
          : vehicleGroupId // ignore: cast_nullable_to_non_nullable
              as String?,
      fuelTypeId: freezed == fuelTypeId
          ? _value.fuelTypeId
          : fuelTypeId // ignore: cast_nullable_to_non_nullable
              as String?,
      fuelPer100Km: null == fuelPer100Km
          ? _value.fuelPer100Km
          : fuelPer100Km // ignore: cast_nullable_to_non_nullable
              as int,
      fuelIdle: null == fuelIdle
          ? _value.fuelIdle
          : fuelIdle // ignore: cast_nullable_to_non_nullable
              as int,
      fuelIgnition: null == fuelIgnition
          ? _value.fuelIgnition
          : fuelIgnition // ignore: cast_nullable_to_non_nullable
              as int,
      fields: freezed == fields
          ? _value.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MVehicleDetailResponseImplCopyWith<$Res>
    implements $MVehicleDetailResponseCopyWith<$Res> {
  factory _$$MVehicleDetailResponseImplCopyWith(
          _$MVehicleDetailResponseImpl value,
          $Res Function(_$MVehicleDetailResponseImpl) then) =
      __$$MVehicleDetailResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String plate,
      int icon,
      int maxSpeed,
      int productYear,
      bool soldVehicle,
      String? enginNo,
      String? vin,
      String? vehicleTypeId,
      String? vehicleGroupId,
      String? fuelTypeId,
      int fuelPer100Km,
      int fuelIdle,
      int fuelIgnition,
      String? fields});
}

/// @nodoc
class __$$MVehicleDetailResponseImplCopyWithImpl<$Res>
    extends _$MVehicleDetailResponseCopyWithImpl<$Res,
        _$MVehicleDetailResponseImpl>
    implements _$$MVehicleDetailResponseImplCopyWith<$Res> {
  __$$MVehicleDetailResponseImplCopyWithImpl(
      _$MVehicleDetailResponseImpl _value,
      $Res Function(_$MVehicleDetailResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? icon = null,
    Object? maxSpeed = null,
    Object? productYear = null,
    Object? soldVehicle = null,
    Object? enginNo = freezed,
    Object? vin = freezed,
    Object? vehicleTypeId = freezed,
    Object? vehicleGroupId = freezed,
    Object? fuelTypeId = freezed,
    Object? fuelPer100Km = null,
    Object? fuelIdle = null,
    Object? fuelIgnition = null,
    Object? fields = freezed,
  }) {
    return _then(_$MVehicleDetailResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      productYear: null == productYear
          ? _value.productYear
          : productYear // ignore: cast_nullable_to_non_nullable
              as int,
      soldVehicle: null == soldVehicle
          ? _value.soldVehicle
          : soldVehicle // ignore: cast_nullable_to_non_nullable
              as bool,
      enginNo: freezed == enginNo
          ? _value.enginNo
          : enginNo // ignore: cast_nullable_to_non_nullable
              as String?,
      vin: freezed == vin
          ? _value.vin
          : vin // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleTypeId: freezed == vehicleTypeId
          ? _value.vehicleTypeId
          : vehicleTypeId // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleGroupId: freezed == vehicleGroupId
          ? _value.vehicleGroupId
          : vehicleGroupId // ignore: cast_nullable_to_non_nullable
              as String?,
      fuelTypeId: freezed == fuelTypeId
          ? _value.fuelTypeId
          : fuelTypeId // ignore: cast_nullable_to_non_nullable
              as String?,
      fuelPer100Km: null == fuelPer100Km
          ? _value.fuelPer100Km
          : fuelPer100Km // ignore: cast_nullable_to_non_nullable
              as int,
      fuelIdle: null == fuelIdle
          ? _value.fuelIdle
          : fuelIdle // ignore: cast_nullable_to_non_nullable
              as int,
      fuelIgnition: null == fuelIgnition
          ? _value.fuelIgnition
          : fuelIgnition // ignore: cast_nullable_to_non_nullable
              as int,
      fields: freezed == fields
          ? _value.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MVehicleDetailResponseImpl extends _MVehicleDetailResponse {
  const _$MVehicleDetailResponseImpl(
      {required this.id,
      required this.plate,
      required this.icon,
      required this.maxSpeed,
      required this.productYear,
      required this.soldVehicle,
      required this.enginNo,
      required this.vin,
      required this.vehicleTypeId,
      required this.vehicleGroupId,
      required this.fuelTypeId,
      required this.fuelPer100Km,
      required this.fuelIdle,
      required this.fuelIgnition,
      required this.fields})
      : super._();

  factory _$MVehicleDetailResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MVehicleDetailResponseImplFromJson(json);

  @override
  final String id;
  @override
  final String plate;
  @override
  final int icon;
  @override
  final int maxSpeed;
  @override
  final int productYear;
  @override
  final bool soldVehicle;
  @override
  final String? enginNo;
  @override
  final String? vin;
  @override
  final String? vehicleTypeId;
  @override
  final String? vehicleGroupId;
  @override
  final String? fuelTypeId;
  @override
  final int fuelPer100Km;
  @override
  final int fuelIdle;
  @override
  final int fuelIgnition;
  @override
  final String? fields;

  @override
  String toString() {
    return 'MVehicleDetailResponse(id: $id, plate: $plate, icon: $icon, maxSpeed: $maxSpeed, productYear: $productYear, soldVehicle: $soldVehicle, enginNo: $enginNo, vin: $vin, vehicleTypeId: $vehicleTypeId, vehicleGroupId: $vehicleGroupId, fuelTypeId: $fuelTypeId, fuelPer100Km: $fuelPer100Km, fuelIdle: $fuelIdle, fuelIgnition: $fuelIgnition, fields: $fields)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleDetailResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.plate, plate) || other.plate == plate) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.maxSpeed, maxSpeed) ||
                other.maxSpeed == maxSpeed) &&
            (identical(other.productYear, productYear) ||
                other.productYear == productYear) &&
            (identical(other.soldVehicle, soldVehicle) ||
                other.soldVehicle == soldVehicle) &&
            (identical(other.enginNo, enginNo) || other.enginNo == enginNo) &&
            (identical(other.vin, vin) || other.vin == vin) &&
            (identical(other.vehicleTypeId, vehicleTypeId) ||
                other.vehicleTypeId == vehicleTypeId) &&
            (identical(other.vehicleGroupId, vehicleGroupId) ||
                other.vehicleGroupId == vehicleGroupId) &&
            (identical(other.fuelTypeId, fuelTypeId) ||
                other.fuelTypeId == fuelTypeId) &&
            (identical(other.fuelPer100Km, fuelPer100Km) ||
                other.fuelPer100Km == fuelPer100Km) &&
            (identical(other.fuelIdle, fuelIdle) ||
                other.fuelIdle == fuelIdle) &&
            (identical(other.fuelIgnition, fuelIgnition) ||
                other.fuelIgnition == fuelIgnition) &&
            (identical(other.fields, fields) || other.fields == fields));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      plate,
      icon,
      maxSpeed,
      productYear,
      soldVehicle,
      enginNo,
      vin,
      vehicleTypeId,
      vehicleGroupId,
      fuelTypeId,
      fuelPer100Km,
      fuelIdle,
      fuelIgnition,
      fields);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MVehicleDetailResponseImplCopyWith<_$MVehicleDetailResponseImpl>
      get copyWith => __$$MVehicleDetailResponseImplCopyWithImpl<
          _$MVehicleDetailResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MVehicleDetailResponseImplToJson(
      this,
    );
  }
}

abstract class _MVehicleDetailResponse extends MVehicleDetailResponse {
  const factory _MVehicleDetailResponse(
      {required final String id,
      required final String plate,
      required final int icon,
      required final int maxSpeed,
      required final int productYear,
      required final bool soldVehicle,
      required final String? enginNo,
      required final String? vin,
      required final String? vehicleTypeId,
      required final String? vehicleGroupId,
      required final String? fuelTypeId,
      required final int fuelPer100Km,
      required final int fuelIdle,
      required final int fuelIgnition,
      required final String? fields}) = _$MVehicleDetailResponseImpl;
  const _MVehicleDetailResponse._() : super._();

  factory _MVehicleDetailResponse.fromJson(Map<String, dynamic> json) =
      _$MVehicleDetailResponseImpl.fromJson;

  @override
  String get id;
  @override
  String get plate;
  @override
  int get icon;
  @override
  int get maxSpeed;
  @override
  int get productYear;
  @override
  bool get soldVehicle;
  @override
  String? get enginNo;
  @override
  String? get vin;
  @override
  String? get vehicleTypeId;
  @override
  String? get vehicleGroupId;
  @override
  String? get fuelTypeId;
  @override
  int get fuelPer100Km;
  @override
  int get fuelIdle;
  @override
  int get fuelIgnition;
  @override
  String? get fields;
  @override
  @JsonKey(ignore: true)
  _$$MVehicleDetailResponseImplCopyWith<_$MVehicleDetailResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
