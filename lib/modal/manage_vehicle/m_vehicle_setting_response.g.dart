// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'm_vehicle_setting_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MVehicleSettingResponseImpl _$$MVehicleSettingResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$MVehicleSettingResponseImpl(
      total: (json['total'] as num?)?.toInt() ?? 0,
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => VechileSetting.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$MVehicleSettingResponseImplToJson(
        _$MVehicleSettingResponseImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'data': instance.data,
    };

_$VechileSettingImpl _$$VechileSettingImplFromJson(Map<String, dynamic> json) =>
    _$VechileSettingImpl(
      id: json['Id'] as String? ?? "",
      icon: (json['Icon'] as num?)?.toInt() ?? 0,
      vehicleType: json['VehicleType'] as String? ?? "",
      vehicleGroup: json['VehicleGroup'] as String? ?? "",
      plate: json['Plate'] as String? ?? "",
      company: json['Company'] as String? ?? "",
      deviceType: json['DeviceType'] as String? ?? "",
      sendVehicle: json['SendVehicle'] as bool? ?? false,
      sendDriver: json['SendDriver'] as bool? ?? false,
      driver: json['Driver'],
      driverNo: json['DriverNo'],
      driverLicenseNo: json['DriverLicenseNo'],
      govmtGpsTime: (json['GovmtGpsTime'] as num?)?.toInt() ?? 0,
      govmtStatus: json['GovmtStatus'] as String? ?? "",
    );

Map<String, dynamic> _$$VechileSettingImplToJson(
        _$VechileSettingImpl instance) =>
    <String, dynamic>{
      'Id': instance.id,
      'Icon': instance.icon,
      'VehicleType': instance.vehicleType,
      'VehicleGroup': instance.vehicleGroup,
      'Plate': instance.plate,
      'Company': instance.company,
      'DeviceType': instance.deviceType,
      'SendVehicle': instance.sendVehicle,
      'SendDriver': instance.sendDriver,
      'Driver': instance.driver,
      'DriverNo': instance.driverNo,
      'DriverLicenseNo': instance.driverLicenseNo,
      'GovmtGpsTime': instance.govmtGpsTime,
      'GovmtStatus': instance.govmtStatus,
    };
