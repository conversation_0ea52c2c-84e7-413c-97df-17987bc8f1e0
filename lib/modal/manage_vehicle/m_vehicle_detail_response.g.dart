// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'm_vehicle_detail_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MVehicleDetailResponseImpl _$$MVehicleDetailResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$MVehicleDetailResponseImpl(
      id: json['id'] as String,
      plate: json['plate'] as String,
      icon: (json['icon'] as num).toInt(),
      maxSpeed: (json['maxSpeed'] as num).toInt(),
      productYear: (json['productYear'] as num).toInt(),
      soldVehicle: json['soldVehicle'] as bool,
      enginNo: json['enginNo'] as String?,
      vin: json['vin'] as String?,
      vehicleTypeId: json['vehicleTypeId'] as String?,
      vehicleGroupId: json['vehicleGroupId'] as String?,
      fuelTypeId: json['fuelTypeId'] as String?,
      fuelPer100Km: (json['fuelPer100Km'] as num).toInt(),
      fuelIdle: (json['fuelIdle'] as num).toInt(),
      fuelIgnition: (json['fuelIgnition'] as num).toInt(),
      fields: json['fields'] as String?,
    );

Map<String, dynamic> _$$MVehicleDetailResponseImplToJson(
        _$MVehicleDetailResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'plate': instance.plate,
      'icon': instance.icon,
      'maxSpeed': instance.maxSpeed,
      'productYear': instance.productYear,
      'soldVehicle': instance.soldVehicle,
      'enginNo': instance.enginNo,
      'vin': instance.vin,
      'vehicleTypeId': instance.vehicleTypeId,
      'vehicleGroupId': instance.vehicleGroupId,
      'fuelTypeId': instance.fuelTypeId,
      'fuelPer100Km': instance.fuelPer100Km,
      'fuelIdle': instance.fuelIdle,
      'fuelIgnition': instance.fuelIgnition,
      'fields': instance.fields,
    };
