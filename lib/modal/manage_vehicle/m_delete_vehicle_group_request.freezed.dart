// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_delete_vehicle_group_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MDeleteVehicleGroupRequest _$MDeleteVehicleGroupRequestFromJson(
    Map<String, dynamic> json) {
  return _MDeleteVehicleGroupRequest.fromJson(json);
}

/// @nodoc
mixin _$MDeleteVehicleGroupRequest {
  String get id => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MDeleteVehicleGroupRequestCopyWith<MDeleteVehicleGroupRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MDeleteVehicleGroupRequestCopyWith<$Res> {
  factory $MDeleteVehicleGroupRequestCopyWith(MDeleteVehicleGroupRequest value,
          $Res Function(MDeleteVehicleGroupRequest) then) =
      _$MDeleteVehicleGroupRequestCopyWithImpl<$Res,
          MDeleteVehicleGroupRequest>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class _$MDeleteVehicleGroupRequestCopyWithImpl<$Res,
        $Val extends MDeleteVehicleGroupRequest>
    implements $MDeleteVehicleGroupRequestCopyWith<$Res> {
  _$MDeleteVehicleGroupRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MDeleteVehicleGroupRequestImplCopyWith<$Res>
    implements $MDeleteVehicleGroupRequestCopyWith<$Res> {
  factory _$$MDeleteVehicleGroupRequestImplCopyWith(
          _$MDeleteVehicleGroupRequestImpl value,
          $Res Function(_$MDeleteVehicleGroupRequestImpl) then) =
      __$$MDeleteVehicleGroupRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$MDeleteVehicleGroupRequestImplCopyWithImpl<$Res>
    extends _$MDeleteVehicleGroupRequestCopyWithImpl<$Res,
        _$MDeleteVehicleGroupRequestImpl>
    implements _$$MDeleteVehicleGroupRequestImplCopyWith<$Res> {
  __$$MDeleteVehicleGroupRequestImplCopyWithImpl(
      _$MDeleteVehicleGroupRequestImpl _value,
      $Res Function(_$MDeleteVehicleGroupRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$MDeleteVehicleGroupRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MDeleteVehicleGroupRequestImpl extends _MDeleteVehicleGroupRequest {
  const _$MDeleteVehicleGroupRequestImpl({required this.id}) : super._();

  factory _$MDeleteVehicleGroupRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$MDeleteVehicleGroupRequestImplFromJson(json);

  @override
  final String id;

  @override
  String toString() {
    return 'MDeleteVehicleGroupRequest(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MDeleteVehicleGroupRequestImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MDeleteVehicleGroupRequestImplCopyWith<_$MDeleteVehicleGroupRequestImpl>
      get copyWith => __$$MDeleteVehicleGroupRequestImplCopyWithImpl<
          _$MDeleteVehicleGroupRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MDeleteVehicleGroupRequestImplToJson(
      this,
    );
  }
}

abstract class _MDeleteVehicleGroupRequest extends MDeleteVehicleGroupRequest {
  const factory _MDeleteVehicleGroupRequest({required final String id}) =
      _$MDeleteVehicleGroupRequestImpl;
  const _MDeleteVehicleGroupRequest._() : super._();

  factory _MDeleteVehicleGroupRequest.fromJson(Map<String, dynamic> json) =
      _$MDeleteVehicleGroupRequestImpl.fromJson;

  @override
  String get id;
  @override
  @JsonKey(ignore: true)
  _$$MDeleteVehicleGroupRequestImplCopyWith<_$MDeleteVehicleGroupRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
