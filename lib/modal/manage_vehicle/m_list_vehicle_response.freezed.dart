// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_list_vehicle_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MListVehicleModalResponse _$MListVehicleModalResponseFromJson(
    Map<String, dynamic> json) {
  return _MListVehicleModalResponse.fromJson(json);
}

/// @nodoc
mixin _$MListVehicleModalResponse {
  int get total => throw _privateConstructorUsedError;
  List<MVehicle> get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MListVehicleModalResponseCopyWith<MListVehicleModalResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MListVehicleModalResponseCopyWith<$Res> {
  factory $MListVehicleModalResponseCopyWith(MListVehicleModalResponse value,
          $Res Function(MListVehicleModalResponse) then) =
      _$MListVehicleModalResponseCopyWithImpl<$Res, MListVehicleModalResponse>;
  @useResult
  $Res call({int total, List<MVehicle> data});
}

/// @nodoc
class _$MListVehicleModalResponseCopyWithImpl<$Res,
        $Val extends MListVehicleModalResponse>
    implements $MListVehicleModalResponseCopyWith<$Res> {
  _$MListVehicleModalResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<MVehicle>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MListVehicleModalResponseImplCopyWith<$Res>
    implements $MListVehicleModalResponseCopyWith<$Res> {
  factory _$$MListVehicleModalResponseImplCopyWith(
          _$MListVehicleModalResponseImpl value,
          $Res Function(_$MListVehicleModalResponseImpl) then) =
      __$$MListVehicleModalResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, List<MVehicle> data});
}

/// @nodoc
class __$$MListVehicleModalResponseImplCopyWithImpl<$Res>
    extends _$MListVehicleModalResponseCopyWithImpl<$Res,
        _$MListVehicleModalResponseImpl>
    implements _$$MListVehicleModalResponseImplCopyWith<$Res> {
  __$$MListVehicleModalResponseImplCopyWithImpl(
      _$MListVehicleModalResponseImpl _value,
      $Res Function(_$MListVehicleModalResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_$MListVehicleModalResponseImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<MVehicle>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MListVehicleModalResponseImpl extends _MListVehicleModalResponse {
  const _$MListVehicleModalResponseImpl(
      {required this.total, required final List<MVehicle> data})
      : _data = data,
        super._();

  factory _$MListVehicleModalResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MListVehicleModalResponseImplFromJson(json);

  @override
  final int total;
  final List<MVehicle> _data;
  @override
  List<MVehicle> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'MListVehicleModalResponse(total: $total, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MListVehicleModalResponseImpl &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, total, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MListVehicleModalResponseImplCopyWith<_$MListVehicleModalResponseImpl>
      get copyWith => __$$MListVehicleModalResponseImplCopyWithImpl<
          _$MListVehicleModalResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MListVehicleModalResponseImplToJson(
      this,
    );
  }
}

abstract class _MListVehicleModalResponse extends MListVehicleModalResponse {
  const factory _MListVehicleModalResponse(
      {required final int total,
      required final List<MVehicle> data}) = _$MListVehicleModalResponseImpl;
  const _MListVehicleModalResponse._() : super._();

  factory _MListVehicleModalResponse.fromJson(Map<String, dynamic> json) =
      _$MListVehicleModalResponseImpl.fromJson;

  @override
  int get total;
  @override
  List<MVehicle> get data;
  @override
  @JsonKey(ignore: true)
  _$$MListVehicleModalResponseImplCopyWith<_$MListVehicleModalResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

MVehicle _$MVehicleFromJson(Map<String, dynamic> json) {
  return _MVehicle.fromJson(json);
}

/// @nodoc
mixin _$MVehicle {
  String get id => throw _privateConstructorUsedError;
  String get plate => throw _privateConstructorUsedError;
  int get icon => throw _privateConstructorUsedError;
  int get maxSpeed => throw _privateConstructorUsedError;
  int get productYear => throw _privateConstructorUsedError;
  bool get soldVehicle => throw _privateConstructorUsedError;
  String? get enginNo => throw _privateConstructorUsedError;
  String? get vin => throw _privateConstructorUsedError;
  String? get vehicleType => throw _privateConstructorUsedError;
  String? get vehicleGroup => throw _privateConstructorUsedError;
  String? get deviceType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MVehicleCopyWith<MVehicle> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MVehicleCopyWith<$Res> {
  factory $MVehicleCopyWith(MVehicle value, $Res Function(MVehicle) then) =
      _$MVehicleCopyWithImpl<$Res, MVehicle>;
  @useResult
  $Res call(
      {String id,
      String plate,
      int icon,
      int maxSpeed,
      int productYear,
      bool soldVehicle,
      String? enginNo,
      String? vin,
      String? vehicleType,
      String? vehicleGroup,
      String? deviceType});
}

/// @nodoc
class _$MVehicleCopyWithImpl<$Res, $Val extends MVehicle>
    implements $MVehicleCopyWith<$Res> {
  _$MVehicleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? icon = null,
    Object? maxSpeed = null,
    Object? productYear = null,
    Object? soldVehicle = null,
    Object? enginNo = freezed,
    Object? vin = freezed,
    Object? vehicleType = freezed,
    Object? vehicleGroup = freezed,
    Object? deviceType = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      productYear: null == productYear
          ? _value.productYear
          : productYear // ignore: cast_nullable_to_non_nullable
              as int,
      soldVehicle: null == soldVehicle
          ? _value.soldVehicle
          : soldVehicle // ignore: cast_nullable_to_non_nullable
              as bool,
      enginNo: freezed == enginNo
          ? _value.enginNo
          : enginNo // ignore: cast_nullable_to_non_nullable
              as String?,
      vin: freezed == vin
          ? _value.vin
          : vin // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleType: freezed == vehicleType
          ? _value.vehicleType
          : vehicleType // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleGroup: freezed == vehicleGroup
          ? _value.vehicleGroup
          : vehicleGroup // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceType: freezed == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MVehicleImplCopyWith<$Res>
    implements $MVehicleCopyWith<$Res> {
  factory _$$MVehicleImplCopyWith(
          _$MVehicleImpl value, $Res Function(_$MVehicleImpl) then) =
      __$$MVehicleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String plate,
      int icon,
      int maxSpeed,
      int productYear,
      bool soldVehicle,
      String? enginNo,
      String? vin,
      String? vehicleType,
      String? vehicleGroup,
      String? deviceType});
}

/// @nodoc
class __$$MVehicleImplCopyWithImpl<$Res>
    extends _$MVehicleCopyWithImpl<$Res, _$MVehicleImpl>
    implements _$$MVehicleImplCopyWith<$Res> {
  __$$MVehicleImplCopyWithImpl(
      _$MVehicleImpl _value, $Res Function(_$MVehicleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? icon = null,
    Object? maxSpeed = null,
    Object? productYear = null,
    Object? soldVehicle = null,
    Object? enginNo = freezed,
    Object? vin = freezed,
    Object? vehicleType = freezed,
    Object? vehicleGroup = freezed,
    Object? deviceType = freezed,
  }) {
    return _then(_$MVehicleImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      productYear: null == productYear
          ? _value.productYear
          : productYear // ignore: cast_nullable_to_non_nullable
              as int,
      soldVehicle: null == soldVehicle
          ? _value.soldVehicle
          : soldVehicle // ignore: cast_nullable_to_non_nullable
              as bool,
      enginNo: freezed == enginNo
          ? _value.enginNo
          : enginNo // ignore: cast_nullable_to_non_nullable
              as String?,
      vin: freezed == vin
          ? _value.vin
          : vin // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleType: freezed == vehicleType
          ? _value.vehicleType
          : vehicleType // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleGroup: freezed == vehicleGroup
          ? _value.vehicleGroup
          : vehicleGroup // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceType: freezed == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MVehicleImpl extends _MVehicle {
  const _$MVehicleImpl(
      {required this.id,
      required this.plate,
      required this.icon,
      required this.maxSpeed,
      required this.productYear,
      required this.soldVehicle,
      required this.enginNo,
      required this.vin,
      required this.vehicleType,
      required this.vehicleGroup,
      required this.deviceType})
      : super._();

  factory _$MVehicleImpl.fromJson(Map<String, dynamic> json) =>
      _$$MVehicleImplFromJson(json);

  @override
  final String id;
  @override
  final String plate;
  @override
  final int icon;
  @override
  final int maxSpeed;
  @override
  final int productYear;
  @override
  final bool soldVehicle;
  @override
  final String? enginNo;
  @override
  final String? vin;
  @override
  final String? vehicleType;
  @override
  final String? vehicleGroup;
  @override
  final String? deviceType;

  @override
  String toString() {
    return 'MVehicle(id: $id, plate: $plate, icon: $icon, maxSpeed: $maxSpeed, productYear: $productYear, soldVehicle: $soldVehicle, enginNo: $enginNo, vin: $vin, vehicleType: $vehicleType, vehicleGroup: $vehicleGroup, deviceType: $deviceType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.plate, plate) || other.plate == plate) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.maxSpeed, maxSpeed) ||
                other.maxSpeed == maxSpeed) &&
            (identical(other.productYear, productYear) ||
                other.productYear == productYear) &&
            (identical(other.soldVehicle, soldVehicle) ||
                other.soldVehicle == soldVehicle) &&
            (identical(other.enginNo, enginNo) || other.enginNo == enginNo) &&
            (identical(other.vin, vin) || other.vin == vin) &&
            (identical(other.vehicleType, vehicleType) ||
                other.vehicleType == vehicleType) &&
            (identical(other.vehicleGroup, vehicleGroup) ||
                other.vehicleGroup == vehicleGroup) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      plate,
      icon,
      maxSpeed,
      productYear,
      soldVehicle,
      enginNo,
      vin,
      vehicleType,
      vehicleGroup,
      deviceType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MVehicleImplCopyWith<_$MVehicleImpl> get copyWith =>
      __$$MVehicleImplCopyWithImpl<_$MVehicleImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MVehicleImplToJson(
      this,
    );
  }
}

abstract class _MVehicle extends MVehicle {
  const factory _MVehicle(
      {required final String id,
      required final String plate,
      required final int icon,
      required final int maxSpeed,
      required final int productYear,
      required final bool soldVehicle,
      required final String? enginNo,
      required final String? vin,
      required final String? vehicleType,
      required final String? vehicleGroup,
      required final String? deviceType}) = _$MVehicleImpl;
  const _MVehicle._() : super._();

  factory _MVehicle.fromJson(Map<String, dynamic> json) =
      _$MVehicleImpl.fromJson;

  @override
  String get id;
  @override
  String get plate;
  @override
  int get icon;
  @override
  int get maxSpeed;
  @override
  int get productYear;
  @override
  bool get soldVehicle;
  @override
  String? get enginNo;
  @override
  String? get vin;
  @override
  String? get vehicleType;
  @override
  String? get vehicleGroup;
  @override
  String? get deviceType;
  @override
  @JsonKey(ignore: true)
  _$$MVehicleImplCopyWith<_$MVehicleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
