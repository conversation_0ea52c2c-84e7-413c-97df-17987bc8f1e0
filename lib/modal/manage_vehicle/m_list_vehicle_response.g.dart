// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'm_list_vehicle_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MListVehicleModalResponseImpl _$$MListVehicleModalResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$MListVehicleModalResponseImpl(
      total: (json['total'] as num).toInt(),
      data: (json['data'] as List<dynamic>)
          .map((e) => MVehicle.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$MListVehicleModalResponseImplToJson(
        _$MListVehicleModalResponseImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'data': instance.data,
    };

_$MVehicleImpl _$$MVehicleImplFromJson(Map<String, dynamic> json) =>
    _$MVehicleImpl(
      id: json['id'] as String,
      plate: json['plate'] as String,
      icon: (json['icon'] as num).toInt(),
      maxSpeed: (json['maxSpeed'] as num).toInt(),
      productYear: (json['productYear'] as num).toInt(),
      soldVehicle: json['soldVehicle'] as bool,
      enginNo: json['enginNo'] as String?,
      vin: json['vin'] as String?,
      vehicleType: json['vehicleType'] as String?,
      vehicleGroup: json['vehicleGroup'] as String?,
      deviceType: json['deviceType'] as String?,
    );

Map<String, dynamic> _$$MVehicleImplToJson(_$MVehicleImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'plate': instance.plate,
      'icon': instance.icon,
      'maxSpeed': instance.maxSpeed,
      'productYear': instance.productYear,
      'soldVehicle': instance.soldVehicle,
      'enginNo': instance.enginNo,
      'vin': instance.vin,
      'vehicleType': instance.vehicleType,
      'vehicleGroup': instance.vehicleGroup,
      'deviceType': instance.deviceType,
    };
