// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_list_vehicle_group_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MListVehicleGroupResponse _$MListVehicleGroupResponseFromJson(
    Map<String, dynamic> json) {
  return _MListVehicleGroupResponse.fromJson(json);
}

/// @nodoc
mixin _$MListVehicleGroupResponse {
  int get total => throw _privateConstructorUsedError;
  List<MVehicleGroup> get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MListVehicleGroupResponseCopyWith<MListVehicleGroupResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MListVehicleGroupResponseCopyWith<$Res> {
  factory $MListVehicleGroupResponseCopyWith(MListVehicleGroupResponse value,
          $Res Function(MListVehicleGroupResponse) then) =
      _$MListVehicleGroupResponseCopyWithImpl<$Res, MListVehicleGroupResponse>;
  @useResult
  $Res call({int total, List<MVehicleGroup> data});
}

/// @nodoc
class _$MListVehicleGroupResponseCopyWithImpl<$Res,
        $Val extends MListVehicleGroupResponse>
    implements $MListVehicleGroupResponseCopyWith<$Res> {
  _$MListVehicleGroupResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroup>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MListVehicleGroupResponseImplCopyWith<$Res>
    implements $MListVehicleGroupResponseCopyWith<$Res> {
  factory _$$MListVehicleGroupResponseImplCopyWith(
          _$MListVehicleGroupResponseImpl value,
          $Res Function(_$MListVehicleGroupResponseImpl) then) =
      __$$MListVehicleGroupResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, List<MVehicleGroup> data});
}

/// @nodoc
class __$$MListVehicleGroupResponseImplCopyWithImpl<$Res>
    extends _$MListVehicleGroupResponseCopyWithImpl<$Res,
        _$MListVehicleGroupResponseImpl>
    implements _$$MListVehicleGroupResponseImplCopyWith<$Res> {
  __$$MListVehicleGroupResponseImplCopyWithImpl(
      _$MListVehicleGroupResponseImpl _value,
      $Res Function(_$MListVehicleGroupResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_$MListVehicleGroupResponseImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<MVehicleGroup>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MListVehicleGroupResponseImpl extends _MListVehicleGroupResponse {
  const _$MListVehicleGroupResponseImpl(
      {required this.total, required final List<MVehicleGroup> data})
      : _data = data,
        super._();

  factory _$MListVehicleGroupResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MListVehicleGroupResponseImplFromJson(json);

  @override
  final int total;
  final List<MVehicleGroup> _data;
  @override
  List<MVehicleGroup> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'MListVehicleGroupResponse(total: $total, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MListVehicleGroupResponseImpl &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, total, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MListVehicleGroupResponseImplCopyWith<_$MListVehicleGroupResponseImpl>
      get copyWith => __$$MListVehicleGroupResponseImplCopyWithImpl<
          _$MListVehicleGroupResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MListVehicleGroupResponseImplToJson(
      this,
    );
  }
}

abstract class _MListVehicleGroupResponse extends MListVehicleGroupResponse {
  const factory _MListVehicleGroupResponse(
          {required final int total, required final List<MVehicleGroup> data}) =
      _$MListVehicleGroupResponseImpl;
  const _MListVehicleGroupResponse._() : super._();

  factory _MListVehicleGroupResponse.fromJson(Map<String, dynamic> json) =
      _$MListVehicleGroupResponseImpl.fromJson;

  @override
  int get total;
  @override
  List<MVehicleGroup> get data;
  @override
  @JsonKey(ignore: true)
  _$$MListVehicleGroupResponseImplCopyWith<_$MListVehicleGroupResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

MVehicleGroup _$MVehicleGroupFromJson(Map<String, dynamic> json) {
  return _MVehicleGroup.fromJson(json);
}

/// @nodoc
mixin _$MVehicleGroup {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MVehicleGroupCopyWith<MVehicleGroup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MVehicleGroupCopyWith<$Res> {
  factory $MVehicleGroupCopyWith(
          MVehicleGroup value, $Res Function(MVehicleGroup) then) =
      _$MVehicleGroupCopyWithImpl<$Res, MVehicleGroup>;
  @useResult
  $Res call({String id, String name});
}

/// @nodoc
class _$MVehicleGroupCopyWithImpl<$Res, $Val extends MVehicleGroup>
    implements $MVehicleGroupCopyWith<$Res> {
  _$MVehicleGroupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MVehicleGroupImplCopyWith<$Res>
    implements $MVehicleGroupCopyWith<$Res> {
  factory _$$MVehicleGroupImplCopyWith(
          _$MVehicleGroupImpl value, $Res Function(_$MVehicleGroupImpl) then) =
      __$$MVehicleGroupImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String name});
}

/// @nodoc
class __$$MVehicleGroupImplCopyWithImpl<$Res>
    extends _$MVehicleGroupCopyWithImpl<$Res, _$MVehicleGroupImpl>
    implements _$$MVehicleGroupImplCopyWith<$Res> {
  __$$MVehicleGroupImplCopyWithImpl(
      _$MVehicleGroupImpl _value, $Res Function(_$MVehicleGroupImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
  }) {
    return _then(_$MVehicleGroupImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MVehicleGroupImpl extends _MVehicleGroup {
  const _$MVehicleGroupImpl({required this.id, required this.name}) : super._();

  factory _$MVehicleGroupImpl.fromJson(Map<String, dynamic> json) =>
      _$$MVehicleGroupImplFromJson(json);

  @override
  final String id;
  @override
  final String name;

  @override
  String toString() {
    return 'MVehicleGroup(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleGroupImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MVehicleGroupImplCopyWith<_$MVehicleGroupImpl> get copyWith =>
      __$$MVehicleGroupImplCopyWithImpl<_$MVehicleGroupImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MVehicleGroupImplToJson(
      this,
    );
  }
}

abstract class _MVehicleGroup extends MVehicleGroup {
  const factory _MVehicleGroup(
      {required final String id,
      required final String name}) = _$MVehicleGroupImpl;
  const _MVehicleGroup._() : super._();

  factory _MVehicleGroup.fromJson(Map<String, dynamic> json) =
      _$MVehicleGroupImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  @JsonKey(ignore: true)
  _$$MVehicleGroupImplCopyWith<_$MVehicleGroupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
