// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'm_special_cargo_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MSpecialCargoResponseImpl _$$MSpecialCargoResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$MSpecialCargoResponseImpl(
      total: (json['total'] as num?)?.toInt() ?? 0,
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => SpecialCargo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$MSpecialCargoResponseImplToJson(
        _$MSpecialCargoResponseImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'data': instance.data,
    };

_$SpecialCargoImpl _$$SpecialCargoImplFromJson(Map<String, dynamic> json) =>
    _$SpecialCargoImpl(
      id: json['Id'] as String? ?? "",
      icon: (json['Icon'] as num?)?.toInt() ?? 0,
      vehicleType: json['VehicleType'] as String? ?? "",
      vehicleGroup: json['VehicleGroup'] as String? ?? "",
      plate: json['Plate'] as String? ?? "",
      company: json['Company'] as String? ?? "",
      deviceType: json['DeviceType'] as String? ?? "",
      field1: json['Field1'] as bool? ?? false,
      field2: json['Field2'] as String? ?? "",
      field3: json['Field3'] as String? ?? "",
      field4: json['Field4'] as String? ?? "",
      field5: json['Field5'] as String? ?? "",
      field6: json['Field6'] as String? ?? "",
      field7: json['Field7'] as String? ?? "",
      govmtGpsTime: (json['GovmtGpsTime'] as num?)?.toInt() ?? 0,
      govmtStatus: json['GovmtStatus'] as String? ?? "",
    );

Map<String, dynamic> _$$SpecialCargoImplToJson(_$SpecialCargoImpl instance) =>
    <String, dynamic>{
      'Id': instance.id,
      'Icon': instance.icon,
      'VehicleType': instance.vehicleType,
      'VehicleGroup': instance.vehicleGroup,
      'Plate': instance.plate,
      'Company': instance.company,
      'DeviceType': instance.deviceType,
      'Field1': instance.field1,
      'Field2': instance.field2,
      'Field3': instance.field3,
      'Field4': instance.field4,
      'Field5': instance.field5,
      'Field6': instance.field6,
      'Field7': instance.field7,
      'GovmtGpsTime': instance.govmtGpsTime,
      'GovmtStatus': instance.govmtStatus,
    };
