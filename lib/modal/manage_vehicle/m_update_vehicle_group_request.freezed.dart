// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_update_vehicle_group_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MUpdateVehicleGroupRequest _$MUpdateVehicleGroupRequestFromJson(
    Map<String, dynamic> json) {
  return _MUpdateVehicleGroupRequest.fromJson(json);
}

/// @nodoc
mixin _$MUpdateVehicleGroupRequest {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MUpdateVehicleGroupRequestCopyWith<MUpdateVehicleGroupRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MUpdateVehicleGroupRequestCopyWith<$Res> {
  factory $MUpdateVehicleGroupRequestCopyWith(MUpdateVehicleGroupRequest value,
          $Res Function(MUpdateVehicleGroupRequest) then) =
      _$MUpdateVehicleGroupRequestCopyWithImpl<$Res,
          MUpdateVehicleGroupRequest>;
  @useResult
  $Res call({String id, String name});
}

/// @nodoc
class _$MUpdateVehicleGroupRequestCopyWithImpl<$Res,
        $Val extends MUpdateVehicleGroupRequest>
    implements $MUpdateVehicleGroupRequestCopyWith<$Res> {
  _$MUpdateVehicleGroupRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MUpdateVehicleGroupRequestImplCopyWith<$Res>
    implements $MUpdateVehicleGroupRequestCopyWith<$Res> {
  factory _$$MUpdateVehicleGroupRequestImplCopyWith(
          _$MUpdateVehicleGroupRequestImpl value,
          $Res Function(_$MUpdateVehicleGroupRequestImpl) then) =
      __$$MUpdateVehicleGroupRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String name});
}

/// @nodoc
class __$$MUpdateVehicleGroupRequestImplCopyWithImpl<$Res>
    extends _$MUpdateVehicleGroupRequestCopyWithImpl<$Res,
        _$MUpdateVehicleGroupRequestImpl>
    implements _$$MUpdateVehicleGroupRequestImplCopyWith<$Res> {
  __$$MUpdateVehicleGroupRequestImplCopyWithImpl(
      _$MUpdateVehicleGroupRequestImpl _value,
      $Res Function(_$MUpdateVehicleGroupRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
  }) {
    return _then(_$MUpdateVehicleGroupRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MUpdateVehicleGroupRequestImpl extends _MUpdateVehicleGroupRequest {
  const _$MUpdateVehicleGroupRequestImpl({required this.id, required this.name})
      : super._();

  factory _$MUpdateVehicleGroupRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$MUpdateVehicleGroupRequestImplFromJson(json);

  @override
  final String id;
  @override
  final String name;

  @override
  String toString() {
    return 'MUpdateVehicleGroupRequest(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MUpdateVehicleGroupRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MUpdateVehicleGroupRequestImplCopyWith<_$MUpdateVehicleGroupRequestImpl>
      get copyWith => __$$MUpdateVehicleGroupRequestImplCopyWithImpl<
          _$MUpdateVehicleGroupRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MUpdateVehicleGroupRequestImplToJson(
      this,
    );
  }
}

abstract class _MUpdateVehicleGroupRequest extends MUpdateVehicleGroupRequest {
  const factory _MUpdateVehicleGroupRequest(
      {required final String id,
      required final String name}) = _$MUpdateVehicleGroupRequestImpl;
  const _MUpdateVehicleGroupRequest._() : super._();

  factory _MUpdateVehicleGroupRequest.fromJson(Map<String, dynamic> json) =
      _$MUpdateVehicleGroupRequestImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  @JsonKey(ignore: true)
  _$$MUpdateVehicleGroupRequestImplCopyWith<_$MUpdateVehicleGroupRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
