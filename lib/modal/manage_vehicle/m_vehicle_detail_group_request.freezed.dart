// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_vehicle_detail_group_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MVehicleDetailGroupRequest _$MVehicleDetailGroupRequestFromJson(
    Map<String, dynamic> json) {
  return _MVehicleDetailGroupRequest.fromJson(json);
}

/// @nodoc
mixin _$MVehicleDetailGroupRequest {
  String get id => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MVehicleDetailGroupRequestCopyWith<MVehicleDetailGroupRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MVehicleDetailGroupRequestCopyWith<$Res> {
  factory $MVehicleDetailGroupRequestCopyWith(MVehicleDetailGroupRequest value,
          $Res Function(MVehicleDetailGroupRequest) then) =
      _$MVehicleDetailGroupRequestCopyWithImpl<$Res,
          MVehicleDetailGroupRequest>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class _$MVehicleDetailGroupRequestCopyWithImpl<$Res,
        $Val extends MVehicleDetailGroupRequest>
    implements $MVehicleDetailGroupRequestCopyWith<$Res> {
  _$MVehicleDetailGroupRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MVehicleDetailGroupRequestImplCopyWith<$Res>
    implements $MVehicleDetailGroupRequestCopyWith<$Res> {
  factory _$$MVehicleDetailGroupRequestImplCopyWith(
          _$MVehicleDetailGroupRequestImpl value,
          $Res Function(_$MVehicleDetailGroupRequestImpl) then) =
      __$$MVehicleDetailGroupRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$MVehicleDetailGroupRequestImplCopyWithImpl<$Res>
    extends _$MVehicleDetailGroupRequestCopyWithImpl<$Res,
        _$MVehicleDetailGroupRequestImpl>
    implements _$$MVehicleDetailGroupRequestImplCopyWith<$Res> {
  __$$MVehicleDetailGroupRequestImplCopyWithImpl(
      _$MVehicleDetailGroupRequestImpl _value,
      $Res Function(_$MVehicleDetailGroupRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$MVehicleDetailGroupRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MVehicleDetailGroupRequestImpl extends _MVehicleDetailGroupRequest {
  const _$MVehicleDetailGroupRequestImpl({required this.id}) : super._();

  factory _$MVehicleDetailGroupRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$MVehicleDetailGroupRequestImplFromJson(json);

  @override
  final String id;

  @override
  String toString() {
    return 'MVehicleDetailGroupRequest(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleDetailGroupRequestImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MVehicleDetailGroupRequestImplCopyWith<_$MVehicleDetailGroupRequestImpl>
      get copyWith => __$$MVehicleDetailGroupRequestImplCopyWithImpl<
          _$MVehicleDetailGroupRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MVehicleDetailGroupRequestImplToJson(
      this,
    );
  }
}

abstract class _MVehicleDetailGroupRequest extends MVehicleDetailGroupRequest {
  const factory _MVehicleDetailGroupRequest({required final String id}) =
      _$MVehicleDetailGroupRequestImpl;
  const _MVehicleDetailGroupRequest._() : super._();

  factory _MVehicleDetailGroupRequest.fromJson(Map<String, dynamic> json) =
      _$MVehicleDetailGroupRequestImpl.fromJson;

  @override
  String get id;
  @override
  @JsonKey(ignore: true)
  _$$MVehicleDetailGroupRequestImplCopyWith<_$MVehicleDetailGroupRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
