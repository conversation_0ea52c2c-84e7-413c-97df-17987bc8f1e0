// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_vehicle_detail_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MVehicleDetailRequest _$MVehicleDetailRequestFromJson(
    Map<String, dynamic> json) {
  return _MVehicleDetailRequest.fromJson(json);
}

/// @nodoc
mixin _$MVehicleDetailRequest {
  String get id => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MVehicleDetailRequestCopyWith<MVehicleDetailRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MVehicleDetailRequestCopyWith<$Res> {
  factory $MVehicleDetailRequestCopyWith(MVehicleDetailRequest value,
          $Res Function(MVehicleDetailRequest) then) =
      _$MVehicleDetailRequestCopyWithImpl<$Res, MVehicleDetailRequest>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class _$MVehicleDetailRequestCopyWithImpl<$Res,
        $Val extends MVehicleDetailRequest>
    implements $MVehicleDetailRequestCopyWith<$Res> {
  _$MVehicleDetailRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MVehicleDetailRequestImplCopyWith<$Res>
    implements $MVehicleDetailRequestCopyWith<$Res> {
  factory _$$MVehicleDetailRequestImplCopyWith(
          _$MVehicleDetailRequestImpl value,
          $Res Function(_$MVehicleDetailRequestImpl) then) =
      __$$MVehicleDetailRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$MVehicleDetailRequestImplCopyWithImpl<$Res>
    extends _$MVehicleDetailRequestCopyWithImpl<$Res,
        _$MVehicleDetailRequestImpl>
    implements _$$MVehicleDetailRequestImplCopyWith<$Res> {
  __$$MVehicleDetailRequestImplCopyWithImpl(_$MVehicleDetailRequestImpl _value,
      $Res Function(_$MVehicleDetailRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$MVehicleDetailRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MVehicleDetailRequestImpl extends _MVehicleDetailRequest {
  const _$MVehicleDetailRequestImpl({required this.id}) : super._();

  factory _$MVehicleDetailRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$MVehicleDetailRequestImplFromJson(json);

  @override
  final String id;

  @override
  String toString() {
    return 'MVehicleDetailRequest(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleDetailRequestImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MVehicleDetailRequestImplCopyWith<_$MVehicleDetailRequestImpl>
      get copyWith => __$$MVehicleDetailRequestImplCopyWithImpl<
          _$MVehicleDetailRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MVehicleDetailRequestImplToJson(
      this,
    );
  }
}

abstract class _MVehicleDetailRequest extends MVehicleDetailRequest {
  const factory _MVehicleDetailRequest({required final String id}) =
      _$MVehicleDetailRequestImpl;
  const _MVehicleDetailRequest._() : super._();

  factory _MVehicleDetailRequest.fromJson(Map<String, dynamic> json) =
      _$MVehicleDetailRequestImpl.fromJson;

  @override
  String get id;
  @override
  @JsonKey(ignore: true)
  _$$MVehicleDetailRequestImplCopyWith<_$MVehicleDetailRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
