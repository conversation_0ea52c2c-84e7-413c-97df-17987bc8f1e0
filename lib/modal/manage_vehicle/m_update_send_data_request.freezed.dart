// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_update_send_data_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MUpdateSendDataRequest _$MUpdateSendDataRequestFromJson(
    Map<String, dynamic> json) {
  return _MUpdateSendDataRequest.fromJson(json);
}

/// @nodoc
mixin _$MUpdateSendDataRequest {
  String get id => throw _privateConstructorUsedError;
  Object get updateFields => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MUpdateSendDataRequestCopyWith<MUpdateSendDataRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MUpdateSendDataRequestCopyWith<$Res> {
  factory $MUpdateSendDataRequestCopyWith(MUpdateSendDataRequest value,
          $Res Function(MUpdateSendDataRequest) then) =
      _$MUpdateSendDataRequestCopyWithImpl<$Res, MUpdateSendDataRequest>;
  @useResult
  $Res call({String id, Object updateFields});
}

/// @nodoc
class _$MUpdateSendDataRequestCopyWithImpl<$Res,
        $Val extends MUpdateSendDataRequest>
    implements $MUpdateSendDataRequestCopyWith<$Res> {
  _$MUpdateSendDataRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? updateFields = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      updateFields: null == updateFields ? _value.updateFields : updateFields,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MUpdateSendDataRequestImplCopyWith<$Res>
    implements $MUpdateSendDataRequestCopyWith<$Res> {
  factory _$$MUpdateSendDataRequestImplCopyWith(
          _$MUpdateSendDataRequestImpl value,
          $Res Function(_$MUpdateSendDataRequestImpl) then) =
      __$$MUpdateSendDataRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, Object updateFields});
}

/// @nodoc
class __$$MUpdateSendDataRequestImplCopyWithImpl<$Res>
    extends _$MUpdateSendDataRequestCopyWithImpl<$Res,
        _$MUpdateSendDataRequestImpl>
    implements _$$MUpdateSendDataRequestImplCopyWith<$Res> {
  __$$MUpdateSendDataRequestImplCopyWithImpl(
      _$MUpdateSendDataRequestImpl _value,
      $Res Function(_$MUpdateSendDataRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? updateFields = null,
  }) {
    return _then(_$MUpdateSendDataRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      updateFields: null == updateFields ? _value.updateFields : updateFields,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MUpdateSendDataRequestImpl extends _MUpdateSendDataRequest {
  const _$MUpdateSendDataRequestImpl(
      {required this.id, required this.updateFields})
      : super._();

  factory _$MUpdateSendDataRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$MUpdateSendDataRequestImplFromJson(json);

  @override
  final String id;
  @override
  final Object updateFields;

  @override
  String toString() {
    return 'MUpdateSendDataRequest(id: $id, updateFields: $updateFields)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MUpdateSendDataRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality()
                .equals(other.updateFields, updateFields));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(updateFields));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MUpdateSendDataRequestImplCopyWith<_$MUpdateSendDataRequestImpl>
      get copyWith => __$$MUpdateSendDataRequestImplCopyWithImpl<
          _$MUpdateSendDataRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MUpdateSendDataRequestImplToJson(
      this,
    );
  }
}

abstract class _MUpdateSendDataRequest extends MUpdateSendDataRequest {
  const factory _MUpdateSendDataRequest(
      {required final String id,
      required final Object updateFields}) = _$MUpdateSendDataRequestImpl;
  const _MUpdateSendDataRequest._() : super._();

  factory _MUpdateSendDataRequest.fromJson(Map<String, dynamic> json) =
      _$MUpdateSendDataRequestImpl.fromJson;

  @override
  String get id;
  @override
  Object get updateFields;
  @override
  @JsonKey(ignore: true)
  _$$MUpdateSendDataRequestImplCopyWith<_$MUpdateSendDataRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
