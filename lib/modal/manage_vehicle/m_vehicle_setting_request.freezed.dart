// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'm_vehicle_setting_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MVehicleSettingRequest _$MVehicleSettingRequestFromJson(
    Map<String, dynamic> json) {
  return _MVehicleSettingRequest.fromJson(json);
}

/// @nodoc
mixin _$MVehicleSettingRequest {
  int get pageIndex => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  String get orderBy => throw _privateConstructorUsedError;
  String get searchTerm => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MVehicleSettingRequestCopyWith<MVehicleSettingRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MVehicleSettingRequestCopyWith<$Res> {
  factory $MVehicleSettingRequestCopyWith(MVehicleSettingRequest value,
          $Res Function(MVehicleSettingRequest) then) =
      _$MVehicleSettingRequestCopyWithImpl<$Res, MVehicleSettingRequest>;
  @useResult
  $Res call({int pageIndex, int pageSize, String orderBy, String searchTerm});
}

/// @nodoc
class _$MVehicleSettingRequestCopyWithImpl<$Res,
        $Val extends MVehicleSettingRequest>
    implements $MVehicleSettingRequestCopyWith<$Res> {
  _$MVehicleSettingRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? orderBy = null,
    Object? searchTerm = null,
  }) {
    return _then(_value.copyWith(
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      orderBy: null == orderBy
          ? _value.orderBy
          : orderBy // ignore: cast_nullable_to_non_nullable
              as String,
      searchTerm: null == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MVehicleSettingRequestImplCopyWith<$Res>
    implements $MVehicleSettingRequestCopyWith<$Res> {
  factory _$$MVehicleSettingRequestImplCopyWith(
          _$MVehicleSettingRequestImpl value,
          $Res Function(_$MVehicleSettingRequestImpl) then) =
      __$$MVehicleSettingRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int pageIndex, int pageSize, String orderBy, String searchTerm});
}

/// @nodoc
class __$$MVehicleSettingRequestImplCopyWithImpl<$Res>
    extends _$MVehicleSettingRequestCopyWithImpl<$Res,
        _$MVehicleSettingRequestImpl>
    implements _$$MVehicleSettingRequestImplCopyWith<$Res> {
  __$$MVehicleSettingRequestImplCopyWithImpl(
      _$MVehicleSettingRequestImpl _value,
      $Res Function(_$MVehicleSettingRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? orderBy = null,
    Object? searchTerm = null,
  }) {
    return _then(_$MVehicleSettingRequestImpl(
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      orderBy: null == orderBy
          ? _value.orderBy
          : orderBy // ignore: cast_nullable_to_non_nullable
              as String,
      searchTerm: null == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MVehicleSettingRequestImpl extends _MVehicleSettingRequest {
  const _$MVehicleSettingRequestImpl(
      {required this.pageIndex,
      required this.pageSize,
      this.orderBy = '',
      this.searchTerm = ''})
      : super._();

  factory _$MVehicleSettingRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$MVehicleSettingRequestImplFromJson(json);

  @override
  final int pageIndex;
  @override
  final int pageSize;
  @override
  @JsonKey()
  final String orderBy;
  @override
  @JsonKey()
  final String searchTerm;

  @override
  String toString() {
    return 'MVehicleSettingRequest(pageIndex: $pageIndex, pageSize: $pageSize, orderBy: $orderBy, searchTerm: $searchTerm)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MVehicleSettingRequestImpl &&
            (identical(other.pageIndex, pageIndex) ||
                other.pageIndex == pageIndex) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.orderBy, orderBy) || other.orderBy == orderBy) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, pageIndex, pageSize, orderBy, searchTerm);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MVehicleSettingRequestImplCopyWith<_$MVehicleSettingRequestImpl>
      get copyWith => __$$MVehicleSettingRequestImplCopyWithImpl<
          _$MVehicleSettingRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MVehicleSettingRequestImplToJson(
      this,
    );
  }
}

abstract class _MVehicleSettingRequest extends MVehicleSettingRequest {
  const factory _MVehicleSettingRequest(
      {required final int pageIndex,
      required final int pageSize,
      final String orderBy,
      final String searchTerm}) = _$MVehicleSettingRequestImpl;
  const _MVehicleSettingRequest._() : super._();

  factory _MVehicleSettingRequest.fromJson(Map<String, dynamic> json) =
      _$MVehicleSettingRequestImpl.fromJson;

  @override
  int get pageIndex;
  @override
  int get pageSize;
  @override
  String get orderBy;
  @override
  String get searchTerm;
  @override
  @JsonKey(ignore: true)
  _$$MVehicleSettingRequestImplCopyWith<_$MVehicleSettingRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
