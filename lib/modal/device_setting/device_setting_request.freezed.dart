// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_setting_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ListDeviceSettingRequest _$ListDeviceSettingRequestFromJson(
    Map<String, dynamic> json) {
  return _ListDeviceSettingRequest.fromJson(json);
}

/// @nodoc
mixin _$ListDeviceSettingRequest {
  String get orderBy => throw _privateConstructorUsedError;
  String get searchTerm => throw _privateConstructorUsedError;
  String get vehicleId => throw _privateConstructorUsedError;
  int get pageIndex => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  int get deviceType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListDeviceSettingRequestCopyWith<ListDeviceSettingRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListDeviceSettingRequestCopyWith<$Res> {
  factory $ListDeviceSettingRequestCopyWith(ListDeviceSettingRequest value,
          $Res Function(ListDeviceSettingRequest) then) =
      _$ListDeviceSettingRequestCopyWithImpl<$Res, ListDeviceSettingRequest>;
  @useResult
  $Res call(
      {String orderBy,
      String searchTerm,
      String vehicleId,
      int pageIndex,
      int pageSize,
      int deviceType});
}

/// @nodoc
class _$ListDeviceSettingRequestCopyWithImpl<$Res,
        $Val extends ListDeviceSettingRequest>
    implements $ListDeviceSettingRequestCopyWith<$Res> {
  _$ListDeviceSettingRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderBy = null,
    Object? searchTerm = null,
    Object? vehicleId = null,
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? deviceType = null,
  }) {
    return _then(_value.copyWith(
      orderBy: null == orderBy
          ? _value.orderBy
          : orderBy // ignore: cast_nullable_to_non_nullable
              as String,
      searchTerm: null == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ListDeviceSettingRequestImplCopyWith<$Res>
    implements $ListDeviceSettingRequestCopyWith<$Res> {
  factory _$$ListDeviceSettingRequestImplCopyWith(
          _$ListDeviceSettingRequestImpl value,
          $Res Function(_$ListDeviceSettingRequestImpl) then) =
      __$$ListDeviceSettingRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String orderBy,
      String searchTerm,
      String vehicleId,
      int pageIndex,
      int pageSize,
      int deviceType});
}

/// @nodoc
class __$$ListDeviceSettingRequestImplCopyWithImpl<$Res>
    extends _$ListDeviceSettingRequestCopyWithImpl<$Res,
        _$ListDeviceSettingRequestImpl>
    implements _$$ListDeviceSettingRequestImplCopyWith<$Res> {
  __$$ListDeviceSettingRequestImplCopyWithImpl(
      _$ListDeviceSettingRequestImpl _value,
      $Res Function(_$ListDeviceSettingRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderBy = null,
    Object? searchTerm = null,
    Object? vehicleId = null,
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? deviceType = null,
  }) {
    return _then(_$ListDeviceSettingRequestImpl(
      orderBy: null == orderBy
          ? _value.orderBy
          : orderBy // ignore: cast_nullable_to_non_nullable
              as String,
      searchTerm: null == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListDeviceSettingRequestImpl extends _ListDeviceSettingRequest {
  const _$ListDeviceSettingRequestImpl(
      {required this.orderBy,
      required this.searchTerm,
      required this.vehicleId,
      required this.pageIndex,
      required this.pageSize,
      required this.deviceType})
      : super._();

  factory _$ListDeviceSettingRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListDeviceSettingRequestImplFromJson(json);

  @override
  final String orderBy;
  @override
  final String searchTerm;
  @override
  final String vehicleId;
  @override
  final int pageIndex;
  @override
  final int pageSize;
  @override
  final int deviceType;

  @override
  String toString() {
    return 'ListDeviceSettingRequest(orderBy: $orderBy, searchTerm: $searchTerm, vehicleId: $vehicleId, pageIndex: $pageIndex, pageSize: $pageSize, deviceType: $deviceType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListDeviceSettingRequestImpl &&
            (identical(other.orderBy, orderBy) || other.orderBy == orderBy) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm) &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.pageIndex, pageIndex) ||
                other.pageIndex == pageIndex) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, orderBy, searchTerm, vehicleId,
      pageIndex, pageSize, deviceType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListDeviceSettingRequestImplCopyWith<_$ListDeviceSettingRequestImpl>
      get copyWith => __$$ListDeviceSettingRequestImplCopyWithImpl<
          _$ListDeviceSettingRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListDeviceSettingRequestImplToJson(
      this,
    );
  }
}

abstract class _ListDeviceSettingRequest extends ListDeviceSettingRequest {
  const factory _ListDeviceSettingRequest(
      {required final String orderBy,
      required final String searchTerm,
      required final String vehicleId,
      required final int pageIndex,
      required final int pageSize,
      required final int deviceType}) = _$ListDeviceSettingRequestImpl;
  const _ListDeviceSettingRequest._() : super._();

  factory _ListDeviceSettingRequest.fromJson(Map<String, dynamic> json) =
      _$ListDeviceSettingRequestImpl.fromJson;

  @override
  String get orderBy;
  @override
  String get searchTerm;
  @override
  String get vehicleId;
  @override
  int get pageIndex;
  @override
  int get pageSize;
  @override
  int get deviceType;
  @override
  @JsonKey(ignore: true)
  _$$ListDeviceSettingRequestImplCopyWith<_$ListDeviceSettingRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
