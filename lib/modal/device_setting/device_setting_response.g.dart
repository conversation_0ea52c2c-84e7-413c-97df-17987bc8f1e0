// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_setting_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ListDeviceSettingResponseImpl _$$ListDeviceSettingResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ListDeviceSettingResponseImpl(
      total: (json['total'] as num).toInt(),
      data: (json['data'] as List<dynamic>)
          .map((e) => DeviceSetting.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ListDeviceSettingResponseImplToJson(
        _$ListDeviceSettingResponseImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'data': instance.data,
    };

_$DeviceSettingImpl _$$DeviceSettingImplFromJson(Map<String, dynamic> json) =>
    _$DeviceSettingImpl(
      id: json['id'] as String,
      deviceType: json['deviceType'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      lastModified: json['lastModified'] as String,
      duplicate: json['duplicate'] as bool,
      checked: json['checked'] as bool,
    );

Map<String, dynamic> _$$DeviceSettingImplToJson(_$DeviceSettingImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'deviceType': instance.deviceType,
      'name': instance.name,
      'description': instance.description,
      'lastModified': instance.lastModified,
      'duplicate': instance.duplicate,
      'checked': instance.checked,
    };
