// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'update_device_setting_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UpdateDeviceSettingRequest _$UpdateDeviceSettingRequestFromJson(
    Map<String, dynamic> json) {
  return _UpdateDeviceSettingRequest.fromJson(json);
}

/// @nodoc
mixin _$UpdateDeviceSettingRequest {
  String get vehicleId => throw _privateConstructorUsedError;
  String get settingId => throw _privateConstructorUsedError;
  bool get active => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UpdateDeviceSettingRequestCopyWith<UpdateDeviceSettingRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateDeviceSettingRequestCopyWith<$Res> {
  factory $UpdateDeviceSettingRequestCopyWith(UpdateDeviceSettingRequest value,
          $Res Function(UpdateDeviceSettingRequest) then) =
      _$UpdateDeviceSettingRequestCopyWithImpl<$Res,
          UpdateDeviceSettingRequest>;
  @useResult
  $Res call({String vehicleId, String settingId, bool active});
}

/// @nodoc
class _$UpdateDeviceSettingRequestCopyWithImpl<$Res,
        $Val extends UpdateDeviceSettingRequest>
    implements $UpdateDeviceSettingRequestCopyWith<$Res> {
  _$UpdateDeviceSettingRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? settingId = null,
    Object? active = null,
  }) {
    return _then(_value.copyWith(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      settingId: null == settingId
          ? _value.settingId
          : settingId // ignore: cast_nullable_to_non_nullable
              as String,
      active: null == active
          ? _value.active
          : active // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateDeviceSettingRequestImplCopyWith<$Res>
    implements $UpdateDeviceSettingRequestCopyWith<$Res> {
  factory _$$UpdateDeviceSettingRequestImplCopyWith(
          _$UpdateDeviceSettingRequestImpl value,
          $Res Function(_$UpdateDeviceSettingRequestImpl) then) =
      __$$UpdateDeviceSettingRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String vehicleId, String settingId, bool active});
}

/// @nodoc
class __$$UpdateDeviceSettingRequestImplCopyWithImpl<$Res>
    extends _$UpdateDeviceSettingRequestCopyWithImpl<$Res,
        _$UpdateDeviceSettingRequestImpl>
    implements _$$UpdateDeviceSettingRequestImplCopyWith<$Res> {
  __$$UpdateDeviceSettingRequestImplCopyWithImpl(
      _$UpdateDeviceSettingRequestImpl _value,
      $Res Function(_$UpdateDeviceSettingRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? settingId = null,
    Object? active = null,
  }) {
    return _then(_$UpdateDeviceSettingRequestImpl(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      settingId: null == settingId
          ? _value.settingId
          : settingId // ignore: cast_nullable_to_non_nullable
              as String,
      active: null == active
          ? _value.active
          : active // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateDeviceSettingRequestImpl extends _UpdateDeviceSettingRequest {
  const _$UpdateDeviceSettingRequestImpl(
      {required this.vehicleId, required this.settingId, required this.active})
      : super._();

  factory _$UpdateDeviceSettingRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$UpdateDeviceSettingRequestImplFromJson(json);

  @override
  final String vehicleId;
  @override
  final String settingId;
  @override
  final bool active;

  @override
  String toString() {
    return 'UpdateDeviceSettingRequest(vehicleId: $vehicleId, settingId: $settingId, active: $active)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateDeviceSettingRequestImpl &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.settingId, settingId) ||
                other.settingId == settingId) &&
            (identical(other.active, active) || other.active == active));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, vehicleId, settingId, active);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateDeviceSettingRequestImplCopyWith<_$UpdateDeviceSettingRequestImpl>
      get copyWith => __$$UpdateDeviceSettingRequestImplCopyWithImpl<
          _$UpdateDeviceSettingRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateDeviceSettingRequestImplToJson(
      this,
    );
  }
}

abstract class _UpdateDeviceSettingRequest extends UpdateDeviceSettingRequest {
  const factory _UpdateDeviceSettingRequest(
      {required final String vehicleId,
      required final String settingId,
      required final bool active}) = _$UpdateDeviceSettingRequestImpl;
  const _UpdateDeviceSettingRequest._() : super._();

  factory _UpdateDeviceSettingRequest.fromJson(Map<String, dynamic> json) =
      _$UpdateDeviceSettingRequestImpl.fromJson;

  @override
  String get vehicleId;
  @override
  String get settingId;
  @override
  bool get active;
  @override
  @JsonKey(ignore: true)
  _$$UpdateDeviceSettingRequestImplCopyWith<_$UpdateDeviceSettingRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
