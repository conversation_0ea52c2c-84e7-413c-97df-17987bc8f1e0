// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_setting_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ListDeviceSettingRequestImpl _$$ListDeviceSettingRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ListDeviceSettingRequestImpl(
      orderBy: json['orderBy'] as String,
      searchTerm: json['searchTerm'] as String,
      vehicleId: json['vehicleId'] as String,
      pageIndex: (json['pageIndex'] as num).toInt(),
      pageSize: (json['pageSize'] as num).toInt(),
      deviceType: (json['deviceType'] as num).toInt(),
    );

Map<String, dynamic> _$$ListDeviceSettingRequestImplToJson(
        _$ListDeviceSettingRequestImpl instance) =>
    <String, dynamic>{
      'orderBy': instance.orderBy,
      'searchTerm': instance.searchTerm,
      'vehicleId': instance.vehicleId,
      'pageIndex': instance.pageIndex,
      'pageSize': instance.pageSize,
      'deviceType': instance.deviceType,
    };
