// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_setting_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ListDeviceSettingResponse _$ListDeviceSettingResponseFromJson(
    Map<String, dynamic> json) {
  return _ListDeviceSettingResponse.fromJson(json);
}

/// @nodoc
mixin _$ListDeviceSettingResponse {
  int get total => throw _privateConstructorUsedError;
  List<DeviceSetting> get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListDeviceSettingResponseCopyWith<ListDeviceSettingResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListDeviceSettingResponseCopyWith<$Res> {
  factory $ListDeviceSettingResponseCopyWith(ListDeviceSettingResponse value,
          $Res Function(ListDeviceSettingResponse) then) =
      _$ListDeviceSettingResponseCopyWithImpl<$Res, ListDeviceSettingResponse>;
  @useResult
  $Res call({int total, List<DeviceSetting> data});
}

/// @nodoc
class _$ListDeviceSettingResponseCopyWithImpl<$Res,
        $Val extends ListDeviceSettingResponse>
    implements $ListDeviceSettingResponseCopyWith<$Res> {
  _$ListDeviceSettingResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<DeviceSetting>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ListDeviceSettingResponseImplCopyWith<$Res>
    implements $ListDeviceSettingResponseCopyWith<$Res> {
  factory _$$ListDeviceSettingResponseImplCopyWith(
          _$ListDeviceSettingResponseImpl value,
          $Res Function(_$ListDeviceSettingResponseImpl) then) =
      __$$ListDeviceSettingResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, List<DeviceSetting> data});
}

/// @nodoc
class __$$ListDeviceSettingResponseImplCopyWithImpl<$Res>
    extends _$ListDeviceSettingResponseCopyWithImpl<$Res,
        _$ListDeviceSettingResponseImpl>
    implements _$$ListDeviceSettingResponseImplCopyWith<$Res> {
  __$$ListDeviceSettingResponseImplCopyWithImpl(
      _$ListDeviceSettingResponseImpl _value,
      $Res Function(_$ListDeviceSettingResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_$ListDeviceSettingResponseImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<DeviceSetting>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListDeviceSettingResponseImpl extends _ListDeviceSettingResponse {
  const _$ListDeviceSettingResponseImpl(
      {required this.total, required final List<DeviceSetting> data})
      : _data = data,
        super._();

  factory _$ListDeviceSettingResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListDeviceSettingResponseImplFromJson(json);

  @override
  final int total;
  final List<DeviceSetting> _data;
  @override
  List<DeviceSetting> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'ListDeviceSettingResponse(total: $total, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListDeviceSettingResponseImpl &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, total, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListDeviceSettingResponseImplCopyWith<_$ListDeviceSettingResponseImpl>
      get copyWith => __$$ListDeviceSettingResponseImplCopyWithImpl<
          _$ListDeviceSettingResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListDeviceSettingResponseImplToJson(
      this,
    );
  }
}

abstract class _ListDeviceSettingResponse extends ListDeviceSettingResponse {
  const factory _ListDeviceSettingResponse(
          {required final int total, required final List<DeviceSetting> data}) =
      _$ListDeviceSettingResponseImpl;
  const _ListDeviceSettingResponse._() : super._();

  factory _ListDeviceSettingResponse.fromJson(Map<String, dynamic> json) =
      _$ListDeviceSettingResponseImpl.fromJson;

  @override
  int get total;
  @override
  List<DeviceSetting> get data;
  @override
  @JsonKey(ignore: true)
  _$$ListDeviceSettingResponseImplCopyWith<_$ListDeviceSettingResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

DeviceSetting _$DeviceSettingFromJson(Map<String, dynamic> json) {
  return _DeviceSetting.fromJson(json);
}

/// @nodoc
mixin _$DeviceSetting {
  String get id => throw _privateConstructorUsedError;
  String get deviceType => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String get lastModified => throw _privateConstructorUsedError;
  bool get duplicate => throw _privateConstructorUsedError;
  bool get checked => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DeviceSettingCopyWith<DeviceSetting> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceSettingCopyWith<$Res> {
  factory $DeviceSettingCopyWith(
          DeviceSetting value, $Res Function(DeviceSetting) then) =
      _$DeviceSettingCopyWithImpl<$Res, DeviceSetting>;
  @useResult
  $Res call(
      {String id,
      String deviceType,
      String name,
      String? description,
      String lastModified,
      bool duplicate,
      bool checked});
}

/// @nodoc
class _$DeviceSettingCopyWithImpl<$Res, $Val extends DeviceSetting>
    implements $DeviceSettingCopyWith<$Res> {
  _$DeviceSettingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? deviceType = null,
    Object? name = null,
    Object? description = freezed,
    Object? lastModified = null,
    Object? duplicate = null,
    Object? checked = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModified: null == lastModified
          ? _value.lastModified
          : lastModified // ignore: cast_nullable_to_non_nullable
              as String,
      duplicate: null == duplicate
          ? _value.duplicate
          : duplicate // ignore: cast_nullable_to_non_nullable
              as bool,
      checked: null == checked
          ? _value.checked
          : checked // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeviceSettingImplCopyWith<$Res>
    implements $DeviceSettingCopyWith<$Res> {
  factory _$$DeviceSettingImplCopyWith(
          _$DeviceSettingImpl value, $Res Function(_$DeviceSettingImpl) then) =
      __$$DeviceSettingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String deviceType,
      String name,
      String? description,
      String lastModified,
      bool duplicate,
      bool checked});
}

/// @nodoc
class __$$DeviceSettingImplCopyWithImpl<$Res>
    extends _$DeviceSettingCopyWithImpl<$Res, _$DeviceSettingImpl>
    implements _$$DeviceSettingImplCopyWith<$Res> {
  __$$DeviceSettingImplCopyWithImpl(
      _$DeviceSettingImpl _value, $Res Function(_$DeviceSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? deviceType = null,
    Object? name = null,
    Object? description = freezed,
    Object? lastModified = null,
    Object? duplicate = null,
    Object? checked = null,
  }) {
    return _then(_$DeviceSettingImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModified: null == lastModified
          ? _value.lastModified
          : lastModified // ignore: cast_nullable_to_non_nullable
              as String,
      duplicate: null == duplicate
          ? _value.duplicate
          : duplicate // ignore: cast_nullable_to_non_nullable
              as bool,
      checked: null == checked
          ? _value.checked
          : checked // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceSettingImpl extends _DeviceSetting {
  const _$DeviceSettingImpl(
      {required this.id,
      required this.deviceType,
      required this.name,
      required this.description,
      required this.lastModified,
      required this.duplicate,
      required this.checked})
      : super._();

  factory _$DeviceSettingImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceSettingImplFromJson(json);

  @override
  final String id;
  @override
  final String deviceType;
  @override
  final String name;
  @override
  final String? description;
  @override
  final String lastModified;
  @override
  final bool duplicate;
  @override
  final bool checked;

  @override
  String toString() {
    return 'DeviceSetting(id: $id, deviceType: $deviceType, name: $name, description: $description, lastModified: $lastModified, duplicate: $duplicate, checked: $checked)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceSettingImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.lastModified, lastModified) ||
                other.lastModified == lastModified) &&
            (identical(other.duplicate, duplicate) ||
                other.duplicate == duplicate) &&
            (identical(other.checked, checked) || other.checked == checked));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, deviceType, name,
      description, lastModified, duplicate, checked);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceSettingImplCopyWith<_$DeviceSettingImpl> get copyWith =>
      __$$DeviceSettingImplCopyWithImpl<_$DeviceSettingImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceSettingImplToJson(
      this,
    );
  }
}

abstract class _DeviceSetting extends DeviceSetting {
  const factory _DeviceSetting(
      {required final String id,
      required final String deviceType,
      required final String name,
      required final String? description,
      required final String lastModified,
      required final bool duplicate,
      required final bool checked}) = _$DeviceSettingImpl;
  const _DeviceSetting._() : super._();

  factory _DeviceSetting.fromJson(Map<String, dynamic> json) =
      _$DeviceSettingImpl.fromJson;

  @override
  String get id;
  @override
  String get deviceType;
  @override
  String get name;
  @override
  String? get description;
  @override
  String get lastModified;
  @override
  bool get duplicate;
  @override
  bool get checked;
  @override
  @JsonKey(ignore: true)
  _$$DeviceSettingImplCopyWith<_$DeviceSettingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
