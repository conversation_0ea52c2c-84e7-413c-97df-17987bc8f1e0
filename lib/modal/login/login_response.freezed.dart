// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AvemaUser _$AvemaUserFromJson(Map<String, dynamic> json) {
  return _AvemaUser.fromJson(json);
}

/// @nodoc
mixin _$AvemaUser {
  String get accessToken => throw _privateConstructorUsedError;
  String? get password => throw _privateConstructorUsedError;
  String? get appEmail => throw _privateConstructorUsedError;
  String? get appPhone => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AvemaUserCopyWith<AvemaUser> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AvemaUserCopyWith<$Res> {
  factory $AvemaUserCopyWith(AvemaUser value, $Res Function(AvemaUser) then) =
      _$AvemaUserCopyWithImpl<$Res, AvemaUser>;
  @useResult
  $Res call(
      {String accessToken,
      String? password,
      String? appEmail,
      String? appPhone});
}

/// @nodoc
class _$AvemaUserCopyWithImpl<$Res, $Val extends AvemaUser>
    implements $AvemaUserCopyWith<$Res> {
  _$AvemaUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? password = freezed,
    Object? appEmail = freezed,
    Object? appPhone = freezed,
  }) {
    return _then(_value.copyWith(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      appEmail: freezed == appEmail
          ? _value.appEmail
          : appEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      appPhone: freezed == appPhone
          ? _value.appPhone
          : appPhone // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AvemaUserImplCopyWith<$Res>
    implements $AvemaUserCopyWith<$Res> {
  factory _$$AvemaUserImplCopyWith(
          _$AvemaUserImpl value, $Res Function(_$AvemaUserImpl) then) =
      __$$AvemaUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String accessToken,
      String? password,
      String? appEmail,
      String? appPhone});
}

/// @nodoc
class __$$AvemaUserImplCopyWithImpl<$Res>
    extends _$AvemaUserCopyWithImpl<$Res, _$AvemaUserImpl>
    implements _$$AvemaUserImplCopyWith<$Res> {
  __$$AvemaUserImplCopyWithImpl(
      _$AvemaUserImpl _value, $Res Function(_$AvemaUserImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? password = freezed,
    Object? appEmail = freezed,
    Object? appPhone = freezed,
  }) {
    return _then(_$AvemaUserImpl(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      appEmail: freezed == appEmail
          ? _value.appEmail
          : appEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      appPhone: freezed == appPhone
          ? _value.appPhone
          : appPhone // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AvemaUserImpl extends _AvemaUser {
  const _$AvemaUserImpl(
      {required this.accessToken,
      required this.password,
      required this.appEmail,
      required this.appPhone})
      : super._();

  factory _$AvemaUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$AvemaUserImplFromJson(json);

  @override
  final String accessToken;
  @override
  final String? password;
  @override
  final String? appEmail;
  @override
  final String? appPhone;

  @override
  String toString() {
    return 'AvemaUser(accessToken: $accessToken, password: $password, appEmail: $appEmail, appPhone: $appPhone)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AvemaUserImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.appEmail, appEmail) ||
                other.appEmail == appEmail) &&
            (identical(other.appPhone, appPhone) ||
                other.appPhone == appPhone));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, accessToken, password, appEmail, appPhone);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AvemaUserImplCopyWith<_$AvemaUserImpl> get copyWith =>
      __$$AvemaUserImplCopyWithImpl<_$AvemaUserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AvemaUserImplToJson(
      this,
    );
  }
}

abstract class _AvemaUser extends AvemaUser {
  const factory _AvemaUser(
      {required final String accessToken,
      required final String? password,
      required final String? appEmail,
      required final String? appPhone}) = _$AvemaUserImpl;
  const _AvemaUser._() : super._();

  factory _AvemaUser.fromJson(Map<String, dynamic> json) =
      _$AvemaUserImpl.fromJson;

  @override
  String get accessToken;
  @override
  String? get password;
  @override
  String? get appEmail;
  @override
  String? get appPhone;
  @override
  @JsonKey(ignore: true)
  _$$AvemaUserImplCopyWith<_$AvemaUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
