// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_password_modal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UsernameAndPassword _$UsernameAndPasswordFromJson(Map<String, dynamic> json) {
  return _UsernameAndPassword.fromJson(json);
}

/// @nodoc
mixin _$UsernameAndPassword {
  String get username => throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UsernameAndPasswordCopyWith<UsernameAndPassword> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UsernameAndPasswordCopyWith<$Res> {
  factory $UsernameAndPasswordCopyWith(
          UsernameAndPassword value, $Res Function(UsernameAndPassword) then) =
      _$UsernameAndPasswordCopyWithImpl<$Res, UsernameAndPassword>;
  @useResult
  $Res call({String username, String password});
}

/// @nodoc
class _$UsernameAndPasswordCopyWithImpl<$Res, $Val extends UsernameAndPassword>
    implements $UsernameAndPasswordCopyWith<$Res> {
  _$UsernameAndPasswordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? password = null,
  }) {
    return _then(_value.copyWith(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UsernameAndPasswordImplCopyWith<$Res>
    implements $UsernameAndPasswordCopyWith<$Res> {
  factory _$$UsernameAndPasswordImplCopyWith(_$UsernameAndPasswordImpl value,
          $Res Function(_$UsernameAndPasswordImpl) then) =
      __$$UsernameAndPasswordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String username, String password});
}

/// @nodoc
class __$$UsernameAndPasswordImplCopyWithImpl<$Res>
    extends _$UsernameAndPasswordCopyWithImpl<$Res, _$UsernameAndPasswordImpl>
    implements _$$UsernameAndPasswordImplCopyWith<$Res> {
  __$$UsernameAndPasswordImplCopyWithImpl(_$UsernameAndPasswordImpl _value,
      $Res Function(_$UsernameAndPasswordImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? password = null,
  }) {
    return _then(_$UsernameAndPasswordImpl(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UsernameAndPasswordImpl extends _UsernameAndPassword {
  const _$UsernameAndPasswordImpl(
      {required this.username, required this.password})
      : super._();

  factory _$UsernameAndPasswordImpl.fromJson(Map<String, dynamic> json) =>
      _$$UsernameAndPasswordImplFromJson(json);

  @override
  final String username;
  @override
  final String password;

  @override
  String toString() {
    return 'UsernameAndPassword(username: $username, password: $password)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UsernameAndPasswordImpl &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, username, password);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UsernameAndPasswordImplCopyWith<_$UsernameAndPasswordImpl> get copyWith =>
      __$$UsernameAndPasswordImplCopyWithImpl<_$UsernameAndPasswordImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UsernameAndPasswordImplToJson(
      this,
    );
  }
}

abstract class _UsernameAndPassword extends UsernameAndPassword {
  const factory _UsernameAndPassword(
      {required final String username,
      required final String password}) = _$UsernameAndPasswordImpl;
  const _UsernameAndPassword._() : super._();

  factory _UsernameAndPassword.fromJson(Map<String, dynamic> json) =
      _$UsernameAndPasswordImpl.fromJson;

  @override
  String get username;
  @override
  String get password;
  @override
  @JsonKey(ignore: true)
  _$$UsernameAndPasswordImplCopyWith<_$UsernameAndPasswordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
