// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'monitor_vehicle_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MonitorVehicleResponseImpl _$$MonitorVehicleResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$MonitorVehicleResponseImpl(
      systemTime: (json['systemTime'] as num).toInt(),
      currentTime: (json['currentTime'] as num).toInt(),
      total: (json['total'] as num).toInt(),
      vehicles: (json['vehicles'] as List<dynamic>)
          .map((e) => Vehicle.fromJson(e as Map<String, dynamic>))
          .toList(),
      refreshInfos: (json['refreshInfos'] as List<dynamic>)
          .map((e) => ExtraInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$MonitorVehicleResponseImplToJson(
        _$MonitorVehicleResponseImpl instance) =>
    <String, dynamic>{
      'systemTime': instance.systemTime,
      'currentTime': instance.currentTime,
      'total': instance.total,
      'vehicles': instance.vehicles,
      'refreshInfos': instance.refreshInfos,
    };

_$VehicleImpl _$$VehicleImplFromJson(Map<String, dynamic> json) =>
    _$VehicleImpl(
      id: json['id'] as String,
      groupName: json['groupName'] as String,
      typeName: json['typeName'] as String,
      plate: json['plate'] as String,
      vin: json['vin'] as String?,
      simId: json['simId'] as String?,
      gpsMileage: (json['gpsMileage'] as num).toInt(),
      lastMaxSpeed: (json['lastMaxSpeed'] as num).toInt(),
      lastGpsTime: (json['lastGpsTime'] as num).toInt(),
      lastSpeed: (json['lastSpeed'] as num).toInt(),
      lastRegionId: (json['lastRegionId'] as num).toInt(),
      lastX: (json['lastX'] as num).toInt(),
      lastY: (json['lastY'] as num).toInt(),
      lastStatus: (json['lastStatus'] as num).toInt(),
      lastInfo: json['lastInfo'] as String,
      lastHeading: (json['lastHeading'] as num).toInt(),
      lastSatellite: (json['lastSatellite'] as num).toInt(),
      lastIsAccOn: (json['lastIsAccOn'] as num).toInt(),
      lastRoadName: json['lastRoadName'] as String?,
      inputs: json['inputs'] == null
          ? null
          : Inputs.fromJson(json['inputs'] as Map<String, dynamic>),
      lastRoadSpeed: (json['lastRoadSpeed'] as num).toInt(),
      driver: json['driver'] == null
          ? null
          : Driver.fromJson(json['driver'] as Map<String, dynamic>),
      lastDeviceTypeId: (json['lastDeviceTypeId'] as num).toInt(),
      lastImei: json['lastImei'] as String,
      lastCamInterval: (json['lastCamInterval'] as num).toInt(),
      lastInput: (json['lastInput'] as num?)?.toInt(),
      lastOutput: (json['lastOutput'] as num?)?.toInt(),
      trigger: json['trigger'] as String?,
      totalGpsMileage: (json['totalGpsMileage'] as num).toInt(),
      pingTime: (json['pingTime'] as num).toInt(),
      vehicleIcon: (json['vehicleIcon'] as num).toInt(),
      idleTime: (json['idleTime'] as num).toInt(),
      sleepInterval: (json['sleepInterval'] as num).toInt(),
      accOffTime: (json['accOffTime'] as num).toInt(),
      sleepTime: (json['sleepTime'] as num).toInt(),
      trip: json['trip'] == null
          ? null
          : Trip.fromJson(json['trip'] as Map<String, dynamic>),
      tripReport: json['tripReport'] as String?,
      daily: json['daily'] == null
          ? null
          : Daily.fromJson(json['daily'] as Map<String, dynamic>),
      sensors: (json['sensors'] as List<dynamic>?)
          ?.map((e) => Sensor.fromJson(e as Map<String, dynamic>))
          .toList(),
      image: json['image'] == null
          ? null
          : ImageDriver.fromJson(json['image'] as Map<String, dynamic>),
      lastGeometry: json['lastGeometry'] as String?,
      keys: json['keys'] as String?,
      voltage: (json['voltage'] as num).toInt(),
      voltagePercentage: (json['voltagePercentage'] as num).toInt(),
      speed: (json['speed'] as num).toInt(),
      vehicleMaxSpeed: (json['vehicleMaxSpeed'] as num).toInt(),
      address: json['address'] as String?,
      online: json['online'] as bool,
      status: json['status'] as String,
      statusColor: json['statusColor'] as String,
      signal: json['signal'] as String,
      mdvr: json['mdvr'] as bool,
      inputInfo:
          (json['inputInfo'] as List<dynamic>).map((e) => e as bool).toList(),
      outputInfo:
          (json['outputInfo'] as List<dynamic>).map((e) => e as bool).toList(),
      listLiveSetting: (json['listLiveSetting'] as List<dynamic>?)
          ?.map((e) => VehicleSetting.fromJson(e as Map<String, dynamic>))
          .toList(),
      vehicleSensorMask: (json['vehicleSensorMask'] as num).toInt(),
    );

Map<String, dynamic> _$$VehicleImplToJson(_$VehicleImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'groupName': instance.groupName,
      'typeName': instance.typeName,
      'plate': instance.plate,
      'vin': instance.vin,
      'simId': instance.simId,
      'gpsMileage': instance.gpsMileage,
      'lastMaxSpeed': instance.lastMaxSpeed,
      'lastGpsTime': instance.lastGpsTime,
      'lastSpeed': instance.lastSpeed,
      'lastRegionId': instance.lastRegionId,
      'lastX': instance.lastX,
      'lastY': instance.lastY,
      'lastStatus': instance.lastStatus,
      'lastInfo': instance.lastInfo,
      'lastHeading': instance.lastHeading,
      'lastSatellite': instance.lastSatellite,
      'lastIsAccOn': instance.lastIsAccOn,
      'lastRoadName': instance.lastRoadName,
      'inputs': instance.inputs,
      'lastRoadSpeed': instance.lastRoadSpeed,
      'driver': instance.driver,
      'lastDeviceTypeId': instance.lastDeviceTypeId,
      'lastImei': instance.lastImei,
      'lastCamInterval': instance.lastCamInterval,
      'lastInput': instance.lastInput,
      'lastOutput': instance.lastOutput,
      'trigger': instance.trigger,
      'totalGpsMileage': instance.totalGpsMileage,
      'pingTime': instance.pingTime,
      'vehicleIcon': instance.vehicleIcon,
      'idleTime': instance.idleTime,
      'sleepInterval': instance.sleepInterval,
      'accOffTime': instance.accOffTime,
      'sleepTime': instance.sleepTime,
      'trip': instance.trip,
      'tripReport': instance.tripReport,
      'daily': instance.daily,
      'sensors': instance.sensors,
      'image': instance.image,
      'lastGeometry': instance.lastGeometry,
      'keys': instance.keys,
      'voltage': instance.voltage,
      'voltagePercentage': instance.voltagePercentage,
      'speed': instance.speed,
      'vehicleMaxSpeed': instance.vehicleMaxSpeed,
      'address': instance.address,
      'online': instance.online,
      'status': instance.status,
      'statusColor': instance.statusColor,
      'signal': instance.signal,
      'mdvr': instance.mdvr,
      'inputInfo': instance.inputInfo,
      'outputInfo': instance.outputInfo,
      'listLiveSetting': instance.listLiveSetting,
      'vehicleSensorMask': instance.vehicleSensorMask,
    };

_$InputsImpl _$$InputsImplFromJson(Map<String, dynamic> json) => _$InputsImpl(
      lastInput1: (json['lastInput1'] as num).toInt(),
      lastInput2: (json['lastInput2'] as num).toInt(),
      lastInput3: (json['lastInput3'] as num).toInt(),
      lastInput4: (json['lastInput4'] as num).toInt(),
    );

Map<String, dynamic> _$$InputsImplToJson(_$InputsImpl instance) =>
    <String, dynamic>{
      'lastInput1': instance.lastInput1,
      'lastInput2': instance.lastInput2,
      'lastInput3': instance.lastInput3,
      'lastInput4': instance.lastInput4,
    };

_$DriverImpl _$$DriverImplFromJson(Map<String, dynamic> json) => _$DriverImpl(
      id: json['id'] as String,
      name: json['name'] as String?,
      phone: json['phone'] as String?,
      licenseNo: json['licenseNo'] as String?,
      expiredDate: (json['expiredDate'] as num).toInt(),
      phone2: json['phone2'] as String?,
    );

Map<String, dynamic> _$$DriverImplToJson(_$DriverImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phone': instance.phone,
      'licenseNo': instance.licenseNo,
      'expiredDate': instance.expiredDate,
      'phone2': instance.phone2,
    };

_$TripImpl _$$TripImplFromJson(Map<String, dynamic> json) => _$TripImpl(
      fromTime: (json['fromTime'] as num).toInt(),
      toTime: (json['toTime'] as num).toInt(),
      gpsMileage: (json['gpsMileage'] as num).toInt(),
    );

Map<String, dynamic> _$$TripImplToJson(_$TripImpl instance) =>
    <String, dynamic>{
      'fromTime': instance.fromTime,
      'toTime': instance.toTime,
      'gpsMileage': instance.gpsMileage,
    };

_$DailyImpl _$$DailyImplFromJson(Map<String, dynamic> json) => _$DailyImpl(
      stopCount: (json['stopCount'] as num).toInt(),
      idleCount: (json['idleCount'] as num).toInt(),
      doorCloseCount: (json['doorCloseCount'] as num).toInt(),
      doorOpenCount: (json['doorOpenCount'] as num).toInt(),
      gpsMileage: (json['gpsMileage'] as num).toInt(),
      runTime: (json['runTime'] as num).toInt(),
      overSpeed: (json['overSpeed'] as num).toInt(),
      accTime: (json['accTime'] as num).toInt(),
      maxSpeed: (json['maxSpeed'] as num).toInt(),
      idleTime: (json['idleTime'] as num).toInt(),
      stopTime: (json['stopTime'] as num).toInt(),
    );

Map<String, dynamic> _$$DailyImplToJson(_$DailyImpl instance) =>
    <String, dynamic>{
      'stopCount': instance.stopCount,
      'idleCount': instance.idleCount,
      'doorCloseCount': instance.doorCloseCount,
      'doorOpenCount': instance.doorOpenCount,
      'gpsMileage': instance.gpsMileage,
      'runTime': instance.runTime,
      'overSpeed': instance.overSpeed,
      'accTime': instance.accTime,
      'maxSpeed': instance.maxSpeed,
      'idleTime': instance.idleTime,
      'stopTime': instance.stopTime,
    };

_$ExtraInfoImpl _$$ExtraInfoImplFromJson(Map<String, dynamic> json) =>
    _$ExtraInfoImpl(
      status: json['status'] as String,
      quantity: (json['quantity'] as num).toInt(),
    );

Map<String, dynamic> _$$ExtraInfoImplToJson(_$ExtraInfoImpl instance) =>
    <String, dynamic>{
      'status': instance.status,
      'quantity': instance.quantity,
    };

_$ImageDriverImpl _$$ImageDriverImplFromJson(Map<String, dynamic> json) =>
    _$ImageDriverImpl(
      url: json['url'] as String?,
      time: (json['time'] as num).toInt(),
    );

Map<String, dynamic> _$$ImageDriverImplToJson(_$ImageDriverImpl instance) =>
    <String, dynamic>{
      'url': instance.url,
      'time': instance.time,
    };

_$SensorImpl _$$SensorImplFromJson(Map<String, dynamic> json) => _$SensorImpl(
      type: (json['type'] as num).toInt(),
      time: (json['time'] as num).toInt(),
      percent: (json['percent'] as num).toInt(),
      data: json['data'] as String?,
      unit: json['unit'] as String,
      typeName: json['typeName'] as String,
      value: (json['value'] as num).toInt(),
    );

Map<String, dynamic> _$$SensorImplToJson(_$SensorImpl instance) =>
    <String, dynamic>{
      'type': instance.type,
      'time': instance.time,
      'percent': instance.percent,
      'data': instance.data,
      'unit': instance.unit,
      'typeName': instance.typeName,
      'value': instance.value,
    };
