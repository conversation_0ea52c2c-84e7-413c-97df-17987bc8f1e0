// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'monitor_vehicle_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MonitorVehicleResponse _$MonitorVehicleResponseFromJson(
    Map<String, dynamic> json) {
  return _MonitorVehicleResponse.fromJson(json);
}

/// @nodoc
mixin _$MonitorVehicleResponse {
  int get systemTime => throw _privateConstructorUsedError;
  int get currentTime => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;
  List<Vehicle> get vehicles => throw _privateConstructorUsedError;
  List<ExtraInfo> get refreshInfos => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MonitorVehicleResponseCopyWith<MonitorVehicleResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MonitorVehicleResponseCopyWith<$Res> {
  factory $MonitorVehicleResponseCopyWith(MonitorVehicleResponse value,
          $Res Function(MonitorVehicleResponse) then) =
      _$MonitorVehicleResponseCopyWithImpl<$Res, MonitorVehicleResponse>;
  @useResult
  $Res call(
      {int systemTime,
      int currentTime,
      int total,
      List<Vehicle> vehicles,
      List<ExtraInfo> refreshInfos});
}

/// @nodoc
class _$MonitorVehicleResponseCopyWithImpl<$Res,
        $Val extends MonitorVehicleResponse>
    implements $MonitorVehicleResponseCopyWith<$Res> {
  _$MonitorVehicleResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? systemTime = null,
    Object? currentTime = null,
    Object? total = null,
    Object? vehicles = null,
    Object? refreshInfos = null,
  }) {
    return _then(_value.copyWith(
      systemTime: null == systemTime
          ? _value.systemTime
          : systemTime // ignore: cast_nullable_to_non_nullable
              as int,
      currentTime: null == currentTime
          ? _value.currentTime
          : currentTime // ignore: cast_nullable_to_non_nullable
              as int,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      vehicles: null == vehicles
          ? _value.vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      refreshInfos: null == refreshInfos
          ? _value.refreshInfos
          : refreshInfos // ignore: cast_nullable_to_non_nullable
              as List<ExtraInfo>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MonitorVehicleResponseImplCopyWith<$Res>
    implements $MonitorVehicleResponseCopyWith<$Res> {
  factory _$$MonitorVehicleResponseImplCopyWith(
          _$MonitorVehicleResponseImpl value,
          $Res Function(_$MonitorVehicleResponseImpl) then) =
      __$$MonitorVehicleResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int systemTime,
      int currentTime,
      int total,
      List<Vehicle> vehicles,
      List<ExtraInfo> refreshInfos});
}

/// @nodoc
class __$$MonitorVehicleResponseImplCopyWithImpl<$Res>
    extends _$MonitorVehicleResponseCopyWithImpl<$Res,
        _$MonitorVehicleResponseImpl>
    implements _$$MonitorVehicleResponseImplCopyWith<$Res> {
  __$$MonitorVehicleResponseImplCopyWithImpl(
      _$MonitorVehicleResponseImpl _value,
      $Res Function(_$MonitorVehicleResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? systemTime = null,
    Object? currentTime = null,
    Object? total = null,
    Object? vehicles = null,
    Object? refreshInfos = null,
  }) {
    return _then(_$MonitorVehicleResponseImpl(
      systemTime: null == systemTime
          ? _value.systemTime
          : systemTime // ignore: cast_nullable_to_non_nullable
              as int,
      currentTime: null == currentTime
          ? _value.currentTime
          : currentTime // ignore: cast_nullable_to_non_nullable
              as int,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      vehicles: null == vehicles
          ? _value._vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      refreshInfos: null == refreshInfos
          ? _value._refreshInfos
          : refreshInfos // ignore: cast_nullable_to_non_nullable
              as List<ExtraInfo>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MonitorVehicleResponseImpl extends _MonitorVehicleResponse {
  const _$MonitorVehicleResponseImpl(
      {required this.systemTime,
      required this.currentTime,
      required this.total,
      required final List<Vehicle> vehicles,
      required final List<ExtraInfo> refreshInfos})
      : _vehicles = vehicles,
        _refreshInfos = refreshInfos,
        super._();

  factory _$MonitorVehicleResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MonitorVehicleResponseImplFromJson(json);

  @override
  final int systemTime;
  @override
  final int currentTime;
  @override
  final int total;
  final List<Vehicle> _vehicles;
  @override
  List<Vehicle> get vehicles {
    if (_vehicles is EqualUnmodifiableListView) return _vehicles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vehicles);
  }

  final List<ExtraInfo> _refreshInfos;
  @override
  List<ExtraInfo> get refreshInfos {
    if (_refreshInfos is EqualUnmodifiableListView) return _refreshInfos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_refreshInfos);
  }

  @override
  String toString() {
    return 'MonitorVehicleResponse(systemTime: $systemTime, currentTime: $currentTime, total: $total, vehicles: $vehicles, refreshInfos: $refreshInfos)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonitorVehicleResponseImpl &&
            (identical(other.systemTime, systemTime) ||
                other.systemTime == systemTime) &&
            (identical(other.currentTime, currentTime) ||
                other.currentTime == currentTime) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._vehicles, _vehicles) &&
            const DeepCollectionEquality()
                .equals(other._refreshInfos, _refreshInfos));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      systemTime,
      currentTime,
      total,
      const DeepCollectionEquality().hash(_vehicles),
      const DeepCollectionEquality().hash(_refreshInfos));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MonitorVehicleResponseImplCopyWith<_$MonitorVehicleResponseImpl>
      get copyWith => __$$MonitorVehicleResponseImplCopyWithImpl<
          _$MonitorVehicleResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MonitorVehicleResponseImplToJson(
      this,
    );
  }
}

abstract class _MonitorVehicleResponse extends MonitorVehicleResponse {
  const factory _MonitorVehicleResponse(
          {required final int systemTime,
          required final int currentTime,
          required final int total,
          required final List<Vehicle> vehicles,
          required final List<ExtraInfo> refreshInfos}) =
      _$MonitorVehicleResponseImpl;
  const _MonitorVehicleResponse._() : super._();

  factory _MonitorVehicleResponse.fromJson(Map<String, dynamic> json) =
      _$MonitorVehicleResponseImpl.fromJson;

  @override
  int get systemTime;
  @override
  int get currentTime;
  @override
  int get total;
  @override
  List<Vehicle> get vehicles;
  @override
  List<ExtraInfo> get refreshInfos;
  @override
  @JsonKey(ignore: true)
  _$$MonitorVehicleResponseImplCopyWith<_$MonitorVehicleResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

Vehicle _$VehicleFromJson(Map<String, dynamic> json) {
  return _Vehicle.fromJson(json);
}

/// @nodoc
mixin _$Vehicle {
  String get id => throw _privateConstructorUsedError;
  String get groupName => throw _privateConstructorUsedError; //vehicle Group Id
  String get typeName => throw _privateConstructorUsedError;
  String get plate => throw _privateConstructorUsedError;
  String? get vin => throw _privateConstructorUsedError;
  String? get simId => throw _privateConstructorUsedError;
  int get gpsMileage => throw _privateConstructorUsedError;
  int get lastMaxSpeed => throw _privateConstructorUsedError;
  int get lastGpsTime => throw _privateConstructorUsedError;
  int get lastSpeed => throw _privateConstructorUsedError;
  int get lastRegionId => throw _privateConstructorUsedError;
  int get lastX => throw _privateConstructorUsedError;
  int get lastY => throw _privateConstructorUsedError;
  int get lastStatus => throw _privateConstructorUsedError;
  String get lastInfo => throw _privateConstructorUsedError;
  int get lastHeading => throw _privateConstructorUsedError;
  int get lastSatellite => throw _privateConstructorUsedError;
  int get lastIsAccOn => throw _privateConstructorUsedError;
  String? get lastRoadName => throw _privateConstructorUsedError;
  Inputs? get inputs => throw _privateConstructorUsedError;
  int get lastRoadSpeed => throw _privateConstructorUsedError;
  Driver? get driver => throw _privateConstructorUsedError;
  int get lastDeviceTypeId => throw _privateConstructorUsedError;
  String get lastImei => throw _privateConstructorUsedError;
  int get lastCamInterval => throw _privateConstructorUsedError;
  int? get lastInput => throw _privateConstructorUsedError;
  int? get lastOutput => throw _privateConstructorUsedError;
  String? get trigger => throw _privateConstructorUsedError;
  int get totalGpsMileage => throw _privateConstructorUsedError;
  int get pingTime => throw _privateConstructorUsedError;
  int get vehicleIcon => throw _privateConstructorUsedError;
  int get idleTime => throw _privateConstructorUsedError;
  int get sleepInterval => throw _privateConstructorUsedError;
  int get accOffTime => throw _privateConstructorUsedError;
  int get sleepTime => throw _privateConstructorUsedError;
  Trip? get trip => throw _privateConstructorUsedError;
  String? get tripReport => throw _privateConstructorUsedError;
  Daily? get daily => throw _privateConstructorUsedError;
  List<Sensor>? get sensors => throw _privateConstructorUsedError;
  ImageDriver? get image => throw _privateConstructorUsedError;
  String? get lastGeometry => throw _privateConstructorUsedError;
  String? get keys => throw _privateConstructorUsedError;
  int get voltage => throw _privateConstructorUsedError;
  int get voltagePercentage => throw _privateConstructorUsedError;
  int get speed => throw _privateConstructorUsedError;
  int get vehicleMaxSpeed => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  bool get online => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String get statusColor => throw _privateConstructorUsedError;
  String get signal => throw _privateConstructorUsedError;
  bool get mdvr => throw _privateConstructorUsedError;
  List<bool> get inputInfo => throw _privateConstructorUsedError;
  List<bool> get outputInfo => throw _privateConstructorUsedError;
  List<VehicleSetting>? get listLiveSetting =>
      throw _privateConstructorUsedError;
  int get vehicleSensorMask => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VehicleCopyWith<Vehicle> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleCopyWith<$Res> {
  factory $VehicleCopyWith(Vehicle value, $Res Function(Vehicle) then) =
      _$VehicleCopyWithImpl<$Res, Vehicle>;
  @useResult
  $Res call(
      {String id,
      String groupName,
      String typeName,
      String plate,
      String? vin,
      String? simId,
      int gpsMileage,
      int lastMaxSpeed,
      int lastGpsTime,
      int lastSpeed,
      int lastRegionId,
      int lastX,
      int lastY,
      int lastStatus,
      String lastInfo,
      int lastHeading,
      int lastSatellite,
      int lastIsAccOn,
      String? lastRoadName,
      Inputs? inputs,
      int lastRoadSpeed,
      Driver? driver,
      int lastDeviceTypeId,
      String lastImei,
      int lastCamInterval,
      int? lastInput,
      int? lastOutput,
      String? trigger,
      int totalGpsMileage,
      int pingTime,
      int vehicleIcon,
      int idleTime,
      int sleepInterval,
      int accOffTime,
      int sleepTime,
      Trip? trip,
      String? tripReport,
      Daily? daily,
      List<Sensor>? sensors,
      ImageDriver? image,
      String? lastGeometry,
      String? keys,
      int voltage,
      int voltagePercentage,
      int speed,
      int vehicleMaxSpeed,
      String? address,
      bool online,
      String status,
      String statusColor,
      String signal,
      bool mdvr,
      List<bool> inputInfo,
      List<bool> outputInfo,
      List<VehicleSetting>? listLiveSetting,
      int vehicleSensorMask});

  $InputsCopyWith<$Res>? get inputs;
  $DriverCopyWith<$Res>? get driver;
  $TripCopyWith<$Res>? get trip;
  $DailyCopyWith<$Res>? get daily;
  $ImageDriverCopyWith<$Res>? get image;
}

/// @nodoc
class _$VehicleCopyWithImpl<$Res, $Val extends Vehicle>
    implements $VehicleCopyWith<$Res> {
  _$VehicleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? groupName = null,
    Object? typeName = null,
    Object? plate = null,
    Object? vin = freezed,
    Object? simId = freezed,
    Object? gpsMileage = null,
    Object? lastMaxSpeed = null,
    Object? lastGpsTime = null,
    Object? lastSpeed = null,
    Object? lastRegionId = null,
    Object? lastX = null,
    Object? lastY = null,
    Object? lastStatus = null,
    Object? lastInfo = null,
    Object? lastHeading = null,
    Object? lastSatellite = null,
    Object? lastIsAccOn = null,
    Object? lastRoadName = freezed,
    Object? inputs = freezed,
    Object? lastRoadSpeed = null,
    Object? driver = freezed,
    Object? lastDeviceTypeId = null,
    Object? lastImei = null,
    Object? lastCamInterval = null,
    Object? lastInput = freezed,
    Object? lastOutput = freezed,
    Object? trigger = freezed,
    Object? totalGpsMileage = null,
    Object? pingTime = null,
    Object? vehicleIcon = null,
    Object? idleTime = null,
    Object? sleepInterval = null,
    Object? accOffTime = null,
    Object? sleepTime = null,
    Object? trip = freezed,
    Object? tripReport = freezed,
    Object? daily = freezed,
    Object? sensors = freezed,
    Object? image = freezed,
    Object? lastGeometry = freezed,
    Object? keys = freezed,
    Object? voltage = null,
    Object? voltagePercentage = null,
    Object? speed = null,
    Object? vehicleMaxSpeed = null,
    Object? address = freezed,
    Object? online = null,
    Object? status = null,
    Object? statusColor = null,
    Object? signal = null,
    Object? mdvr = null,
    Object? inputInfo = null,
    Object? outputInfo = null,
    Object? listLiveSetting = freezed,
    Object? vehicleSensorMask = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      groupName: null == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String,
      typeName: null == typeName
          ? _value.typeName
          : typeName // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      vin: freezed == vin
          ? _value.vin
          : vin // ignore: cast_nullable_to_non_nullable
              as String?,
      simId: freezed == simId
          ? _value.simId
          : simId // ignore: cast_nullable_to_non_nullable
              as String?,
      gpsMileage: null == gpsMileage
          ? _value.gpsMileage
          : gpsMileage // ignore: cast_nullable_to_non_nullable
              as int,
      lastMaxSpeed: null == lastMaxSpeed
          ? _value.lastMaxSpeed
          : lastMaxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      lastGpsTime: null == lastGpsTime
          ? _value.lastGpsTime
          : lastGpsTime // ignore: cast_nullable_to_non_nullable
              as int,
      lastSpeed: null == lastSpeed
          ? _value.lastSpeed
          : lastSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      lastRegionId: null == lastRegionId
          ? _value.lastRegionId
          : lastRegionId // ignore: cast_nullable_to_non_nullable
              as int,
      lastX: null == lastX
          ? _value.lastX
          : lastX // ignore: cast_nullable_to_non_nullable
              as int,
      lastY: null == lastY
          ? _value.lastY
          : lastY // ignore: cast_nullable_to_non_nullable
              as int,
      lastStatus: null == lastStatus
          ? _value.lastStatus
          : lastStatus // ignore: cast_nullable_to_non_nullable
              as int,
      lastInfo: null == lastInfo
          ? _value.lastInfo
          : lastInfo // ignore: cast_nullable_to_non_nullable
              as String,
      lastHeading: null == lastHeading
          ? _value.lastHeading
          : lastHeading // ignore: cast_nullable_to_non_nullable
              as int,
      lastSatellite: null == lastSatellite
          ? _value.lastSatellite
          : lastSatellite // ignore: cast_nullable_to_non_nullable
              as int,
      lastIsAccOn: null == lastIsAccOn
          ? _value.lastIsAccOn
          : lastIsAccOn // ignore: cast_nullable_to_non_nullable
              as int,
      lastRoadName: freezed == lastRoadName
          ? _value.lastRoadName
          : lastRoadName // ignore: cast_nullable_to_non_nullable
              as String?,
      inputs: freezed == inputs
          ? _value.inputs
          : inputs // ignore: cast_nullable_to_non_nullable
              as Inputs?,
      lastRoadSpeed: null == lastRoadSpeed
          ? _value.lastRoadSpeed
          : lastRoadSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      driver: freezed == driver
          ? _value.driver
          : driver // ignore: cast_nullable_to_non_nullable
              as Driver?,
      lastDeviceTypeId: null == lastDeviceTypeId
          ? _value.lastDeviceTypeId
          : lastDeviceTypeId // ignore: cast_nullable_to_non_nullable
              as int,
      lastImei: null == lastImei
          ? _value.lastImei
          : lastImei // ignore: cast_nullable_to_non_nullable
              as String,
      lastCamInterval: null == lastCamInterval
          ? _value.lastCamInterval
          : lastCamInterval // ignore: cast_nullable_to_non_nullable
              as int,
      lastInput: freezed == lastInput
          ? _value.lastInput
          : lastInput // ignore: cast_nullable_to_non_nullable
              as int?,
      lastOutput: freezed == lastOutput
          ? _value.lastOutput
          : lastOutput // ignore: cast_nullable_to_non_nullable
              as int?,
      trigger: freezed == trigger
          ? _value.trigger
          : trigger // ignore: cast_nullable_to_non_nullable
              as String?,
      totalGpsMileage: null == totalGpsMileage
          ? _value.totalGpsMileage
          : totalGpsMileage // ignore: cast_nullable_to_non_nullable
              as int,
      pingTime: null == pingTime
          ? _value.pingTime
          : pingTime // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleIcon: null == vehicleIcon
          ? _value.vehicleIcon
          : vehicleIcon // ignore: cast_nullable_to_non_nullable
              as int,
      idleTime: null == idleTime
          ? _value.idleTime
          : idleTime // ignore: cast_nullable_to_non_nullable
              as int,
      sleepInterval: null == sleepInterval
          ? _value.sleepInterval
          : sleepInterval // ignore: cast_nullable_to_non_nullable
              as int,
      accOffTime: null == accOffTime
          ? _value.accOffTime
          : accOffTime // ignore: cast_nullable_to_non_nullable
              as int,
      sleepTime: null == sleepTime
          ? _value.sleepTime
          : sleepTime // ignore: cast_nullable_to_non_nullable
              as int,
      trip: freezed == trip
          ? _value.trip
          : trip // ignore: cast_nullable_to_non_nullable
              as Trip?,
      tripReport: freezed == tripReport
          ? _value.tripReport
          : tripReport // ignore: cast_nullable_to_non_nullable
              as String?,
      daily: freezed == daily
          ? _value.daily
          : daily // ignore: cast_nullable_to_non_nullable
              as Daily?,
      sensors: freezed == sensors
          ? _value.sensors
          : sensors // ignore: cast_nullable_to_non_nullable
              as List<Sensor>?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as ImageDriver?,
      lastGeometry: freezed == lastGeometry
          ? _value.lastGeometry
          : lastGeometry // ignore: cast_nullable_to_non_nullable
              as String?,
      keys: freezed == keys
          ? _value.keys
          : keys // ignore: cast_nullable_to_non_nullable
              as String?,
      voltage: null == voltage
          ? _value.voltage
          : voltage // ignore: cast_nullable_to_non_nullable
              as int,
      voltagePercentage: null == voltagePercentage
          ? _value.voltagePercentage
          : voltagePercentage // ignore: cast_nullable_to_non_nullable
              as int,
      speed: null == speed
          ? _value.speed
          : speed // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleMaxSpeed: null == vehicleMaxSpeed
          ? _value.vehicleMaxSpeed
          : vehicleMaxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      online: null == online
          ? _value.online
          : online // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      statusColor: null == statusColor
          ? _value.statusColor
          : statusColor // ignore: cast_nullable_to_non_nullable
              as String,
      signal: null == signal
          ? _value.signal
          : signal // ignore: cast_nullable_to_non_nullable
              as String,
      mdvr: null == mdvr
          ? _value.mdvr
          : mdvr // ignore: cast_nullable_to_non_nullable
              as bool,
      inputInfo: null == inputInfo
          ? _value.inputInfo
          : inputInfo // ignore: cast_nullable_to_non_nullable
              as List<bool>,
      outputInfo: null == outputInfo
          ? _value.outputInfo
          : outputInfo // ignore: cast_nullable_to_non_nullable
              as List<bool>,
      listLiveSetting: freezed == listLiveSetting
          ? _value.listLiveSetting
          : listLiveSetting // ignore: cast_nullable_to_non_nullable
              as List<VehicleSetting>?,
      vehicleSensorMask: null == vehicleSensorMask
          ? _value.vehicleSensorMask
          : vehicleSensorMask // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $InputsCopyWith<$Res>? get inputs {
    if (_value.inputs == null) {
      return null;
    }

    return $InputsCopyWith<$Res>(_value.inputs!, (value) {
      return _then(_value.copyWith(inputs: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $DriverCopyWith<$Res>? get driver {
    if (_value.driver == null) {
      return null;
    }

    return $DriverCopyWith<$Res>(_value.driver!, (value) {
      return _then(_value.copyWith(driver: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $TripCopyWith<$Res>? get trip {
    if (_value.trip == null) {
      return null;
    }

    return $TripCopyWith<$Res>(_value.trip!, (value) {
      return _then(_value.copyWith(trip: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $DailyCopyWith<$Res>? get daily {
    if (_value.daily == null) {
      return null;
    }

    return $DailyCopyWith<$Res>(_value.daily!, (value) {
      return _then(_value.copyWith(daily: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ImageDriverCopyWith<$Res>? get image {
    if (_value.image == null) {
      return null;
    }

    return $ImageDriverCopyWith<$Res>(_value.image!, (value) {
      return _then(_value.copyWith(image: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VehicleImplCopyWith<$Res> implements $VehicleCopyWith<$Res> {
  factory _$$VehicleImplCopyWith(
          _$VehicleImpl value, $Res Function(_$VehicleImpl) then) =
      __$$VehicleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String groupName,
      String typeName,
      String plate,
      String? vin,
      String? simId,
      int gpsMileage,
      int lastMaxSpeed,
      int lastGpsTime,
      int lastSpeed,
      int lastRegionId,
      int lastX,
      int lastY,
      int lastStatus,
      String lastInfo,
      int lastHeading,
      int lastSatellite,
      int lastIsAccOn,
      String? lastRoadName,
      Inputs? inputs,
      int lastRoadSpeed,
      Driver? driver,
      int lastDeviceTypeId,
      String lastImei,
      int lastCamInterval,
      int? lastInput,
      int? lastOutput,
      String? trigger,
      int totalGpsMileage,
      int pingTime,
      int vehicleIcon,
      int idleTime,
      int sleepInterval,
      int accOffTime,
      int sleepTime,
      Trip? trip,
      String? tripReport,
      Daily? daily,
      List<Sensor>? sensors,
      ImageDriver? image,
      String? lastGeometry,
      String? keys,
      int voltage,
      int voltagePercentage,
      int speed,
      int vehicleMaxSpeed,
      String? address,
      bool online,
      String status,
      String statusColor,
      String signal,
      bool mdvr,
      List<bool> inputInfo,
      List<bool> outputInfo,
      List<VehicleSetting>? listLiveSetting,
      int vehicleSensorMask});

  @override
  $InputsCopyWith<$Res>? get inputs;
  @override
  $DriverCopyWith<$Res>? get driver;
  @override
  $TripCopyWith<$Res>? get trip;
  @override
  $DailyCopyWith<$Res>? get daily;
  @override
  $ImageDriverCopyWith<$Res>? get image;
}

/// @nodoc
class __$$VehicleImplCopyWithImpl<$Res>
    extends _$VehicleCopyWithImpl<$Res, _$VehicleImpl>
    implements _$$VehicleImplCopyWith<$Res> {
  __$$VehicleImplCopyWithImpl(
      _$VehicleImpl _value, $Res Function(_$VehicleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? groupName = null,
    Object? typeName = null,
    Object? plate = null,
    Object? vin = freezed,
    Object? simId = freezed,
    Object? gpsMileage = null,
    Object? lastMaxSpeed = null,
    Object? lastGpsTime = null,
    Object? lastSpeed = null,
    Object? lastRegionId = null,
    Object? lastX = null,
    Object? lastY = null,
    Object? lastStatus = null,
    Object? lastInfo = null,
    Object? lastHeading = null,
    Object? lastSatellite = null,
    Object? lastIsAccOn = null,
    Object? lastRoadName = freezed,
    Object? inputs = freezed,
    Object? lastRoadSpeed = null,
    Object? driver = freezed,
    Object? lastDeviceTypeId = null,
    Object? lastImei = null,
    Object? lastCamInterval = null,
    Object? lastInput = freezed,
    Object? lastOutput = freezed,
    Object? trigger = freezed,
    Object? totalGpsMileage = null,
    Object? pingTime = null,
    Object? vehicleIcon = null,
    Object? idleTime = null,
    Object? sleepInterval = null,
    Object? accOffTime = null,
    Object? sleepTime = null,
    Object? trip = freezed,
    Object? tripReport = freezed,
    Object? daily = freezed,
    Object? sensors = freezed,
    Object? image = freezed,
    Object? lastGeometry = freezed,
    Object? keys = freezed,
    Object? voltage = null,
    Object? voltagePercentage = null,
    Object? speed = null,
    Object? vehicleMaxSpeed = null,
    Object? address = freezed,
    Object? online = null,
    Object? status = null,
    Object? statusColor = null,
    Object? signal = null,
    Object? mdvr = null,
    Object? inputInfo = null,
    Object? outputInfo = null,
    Object? listLiveSetting = freezed,
    Object? vehicleSensorMask = null,
  }) {
    return _then(_$VehicleImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      groupName: null == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String,
      typeName: null == typeName
          ? _value.typeName
          : typeName // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      vin: freezed == vin
          ? _value.vin
          : vin // ignore: cast_nullable_to_non_nullable
              as String?,
      simId: freezed == simId
          ? _value.simId
          : simId // ignore: cast_nullable_to_non_nullable
              as String?,
      gpsMileage: null == gpsMileage
          ? _value.gpsMileage
          : gpsMileage // ignore: cast_nullable_to_non_nullable
              as int,
      lastMaxSpeed: null == lastMaxSpeed
          ? _value.lastMaxSpeed
          : lastMaxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      lastGpsTime: null == lastGpsTime
          ? _value.lastGpsTime
          : lastGpsTime // ignore: cast_nullable_to_non_nullable
              as int,
      lastSpeed: null == lastSpeed
          ? _value.lastSpeed
          : lastSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      lastRegionId: null == lastRegionId
          ? _value.lastRegionId
          : lastRegionId // ignore: cast_nullable_to_non_nullable
              as int,
      lastX: null == lastX
          ? _value.lastX
          : lastX // ignore: cast_nullable_to_non_nullable
              as int,
      lastY: null == lastY
          ? _value.lastY
          : lastY // ignore: cast_nullable_to_non_nullable
              as int,
      lastStatus: null == lastStatus
          ? _value.lastStatus
          : lastStatus // ignore: cast_nullable_to_non_nullable
              as int,
      lastInfo: null == lastInfo
          ? _value.lastInfo
          : lastInfo // ignore: cast_nullable_to_non_nullable
              as String,
      lastHeading: null == lastHeading
          ? _value.lastHeading
          : lastHeading // ignore: cast_nullable_to_non_nullable
              as int,
      lastSatellite: null == lastSatellite
          ? _value.lastSatellite
          : lastSatellite // ignore: cast_nullable_to_non_nullable
              as int,
      lastIsAccOn: null == lastIsAccOn
          ? _value.lastIsAccOn
          : lastIsAccOn // ignore: cast_nullable_to_non_nullable
              as int,
      lastRoadName: freezed == lastRoadName
          ? _value.lastRoadName
          : lastRoadName // ignore: cast_nullable_to_non_nullable
              as String?,
      inputs: freezed == inputs
          ? _value.inputs
          : inputs // ignore: cast_nullable_to_non_nullable
              as Inputs?,
      lastRoadSpeed: null == lastRoadSpeed
          ? _value.lastRoadSpeed
          : lastRoadSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      driver: freezed == driver
          ? _value.driver
          : driver // ignore: cast_nullable_to_non_nullable
              as Driver?,
      lastDeviceTypeId: null == lastDeviceTypeId
          ? _value.lastDeviceTypeId
          : lastDeviceTypeId // ignore: cast_nullable_to_non_nullable
              as int,
      lastImei: null == lastImei
          ? _value.lastImei
          : lastImei // ignore: cast_nullable_to_non_nullable
              as String,
      lastCamInterval: null == lastCamInterval
          ? _value.lastCamInterval
          : lastCamInterval // ignore: cast_nullable_to_non_nullable
              as int,
      lastInput: freezed == lastInput
          ? _value.lastInput
          : lastInput // ignore: cast_nullable_to_non_nullable
              as int?,
      lastOutput: freezed == lastOutput
          ? _value.lastOutput
          : lastOutput // ignore: cast_nullable_to_non_nullable
              as int?,
      trigger: freezed == trigger
          ? _value.trigger
          : trigger // ignore: cast_nullable_to_non_nullable
              as String?,
      totalGpsMileage: null == totalGpsMileage
          ? _value.totalGpsMileage
          : totalGpsMileage // ignore: cast_nullable_to_non_nullable
              as int,
      pingTime: null == pingTime
          ? _value.pingTime
          : pingTime // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleIcon: null == vehicleIcon
          ? _value.vehicleIcon
          : vehicleIcon // ignore: cast_nullable_to_non_nullable
              as int,
      idleTime: null == idleTime
          ? _value.idleTime
          : idleTime // ignore: cast_nullable_to_non_nullable
              as int,
      sleepInterval: null == sleepInterval
          ? _value.sleepInterval
          : sleepInterval // ignore: cast_nullable_to_non_nullable
              as int,
      accOffTime: null == accOffTime
          ? _value.accOffTime
          : accOffTime // ignore: cast_nullable_to_non_nullable
              as int,
      sleepTime: null == sleepTime
          ? _value.sleepTime
          : sleepTime // ignore: cast_nullable_to_non_nullable
              as int,
      trip: freezed == trip
          ? _value.trip
          : trip // ignore: cast_nullable_to_non_nullable
              as Trip?,
      tripReport: freezed == tripReport
          ? _value.tripReport
          : tripReport // ignore: cast_nullable_to_non_nullable
              as String?,
      daily: freezed == daily
          ? _value.daily
          : daily // ignore: cast_nullable_to_non_nullable
              as Daily?,
      sensors: freezed == sensors
          ? _value._sensors
          : sensors // ignore: cast_nullable_to_non_nullable
              as List<Sensor>?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as ImageDriver?,
      lastGeometry: freezed == lastGeometry
          ? _value.lastGeometry
          : lastGeometry // ignore: cast_nullable_to_non_nullable
              as String?,
      keys: freezed == keys
          ? _value.keys
          : keys // ignore: cast_nullable_to_non_nullable
              as String?,
      voltage: null == voltage
          ? _value.voltage
          : voltage // ignore: cast_nullable_to_non_nullable
              as int,
      voltagePercentage: null == voltagePercentage
          ? _value.voltagePercentage
          : voltagePercentage // ignore: cast_nullable_to_non_nullable
              as int,
      speed: null == speed
          ? _value.speed
          : speed // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleMaxSpeed: null == vehicleMaxSpeed
          ? _value.vehicleMaxSpeed
          : vehicleMaxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      online: null == online
          ? _value.online
          : online // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      statusColor: null == statusColor
          ? _value.statusColor
          : statusColor // ignore: cast_nullable_to_non_nullable
              as String,
      signal: null == signal
          ? _value.signal
          : signal // ignore: cast_nullable_to_non_nullable
              as String,
      mdvr: null == mdvr
          ? _value.mdvr
          : mdvr // ignore: cast_nullable_to_non_nullable
              as bool,
      inputInfo: null == inputInfo
          ? _value._inputInfo
          : inputInfo // ignore: cast_nullable_to_non_nullable
              as List<bool>,
      outputInfo: null == outputInfo
          ? _value._outputInfo
          : outputInfo // ignore: cast_nullable_to_non_nullable
              as List<bool>,
      listLiveSetting: freezed == listLiveSetting
          ? _value._listLiveSetting
          : listLiveSetting // ignore: cast_nullable_to_non_nullable
              as List<VehicleSetting>?,
      vehicleSensorMask: null == vehicleSensorMask
          ? _value.vehicleSensorMask
          : vehicleSensorMask // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleImpl extends _Vehicle {
  const _$VehicleImpl(
      {required this.id,
      required this.groupName,
      required this.typeName,
      required this.plate,
      required this.vin,
      required this.simId,
      required this.gpsMileage,
      required this.lastMaxSpeed,
      required this.lastGpsTime,
      required this.lastSpeed,
      required this.lastRegionId,
      required this.lastX,
      required this.lastY,
      required this.lastStatus,
      required this.lastInfo,
      required this.lastHeading,
      required this.lastSatellite,
      required this.lastIsAccOn,
      required this.lastRoadName,
      required this.inputs,
      required this.lastRoadSpeed,
      required this.driver,
      required this.lastDeviceTypeId,
      required this.lastImei,
      required this.lastCamInterval,
      required this.lastInput,
      required this.lastOutput,
      required this.trigger,
      required this.totalGpsMileage,
      required this.pingTime,
      required this.vehicleIcon,
      required this.idleTime,
      required this.sleepInterval,
      required this.accOffTime,
      required this.sleepTime,
      required this.trip,
      required this.tripReport,
      required this.daily,
      required final List<Sensor>? sensors,
      required this.image,
      required this.lastGeometry,
      required this.keys,
      required this.voltage,
      required this.voltagePercentage,
      required this.speed,
      required this.vehicleMaxSpeed,
      required this.address,
      required this.online,
      required this.status,
      required this.statusColor,
      required this.signal,
      required this.mdvr,
      required final List<bool> inputInfo,
      required final List<bool> outputInfo,
      required final List<VehicleSetting>? listLiveSetting,
      required this.vehicleSensorMask})
      : _sensors = sensors,
        _inputInfo = inputInfo,
        _outputInfo = outputInfo,
        _listLiveSetting = listLiveSetting,
        super._();

  factory _$VehicleImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleImplFromJson(json);

  @override
  final String id;
  @override
  final String groupName;
//vehicle Group Id
  @override
  final String typeName;
  @override
  final String plate;
  @override
  final String? vin;
  @override
  final String? simId;
  @override
  final int gpsMileage;
  @override
  final int lastMaxSpeed;
  @override
  final int lastGpsTime;
  @override
  final int lastSpeed;
  @override
  final int lastRegionId;
  @override
  final int lastX;
  @override
  final int lastY;
  @override
  final int lastStatus;
  @override
  final String lastInfo;
  @override
  final int lastHeading;
  @override
  final int lastSatellite;
  @override
  final int lastIsAccOn;
  @override
  final String? lastRoadName;
  @override
  final Inputs? inputs;
  @override
  final int lastRoadSpeed;
  @override
  final Driver? driver;
  @override
  final int lastDeviceTypeId;
  @override
  final String lastImei;
  @override
  final int lastCamInterval;
  @override
  final int? lastInput;
  @override
  final int? lastOutput;
  @override
  final String? trigger;
  @override
  final int totalGpsMileage;
  @override
  final int pingTime;
  @override
  final int vehicleIcon;
  @override
  final int idleTime;
  @override
  final int sleepInterval;
  @override
  final int accOffTime;
  @override
  final int sleepTime;
  @override
  final Trip? trip;
  @override
  final String? tripReport;
  @override
  final Daily? daily;
  final List<Sensor>? _sensors;
  @override
  List<Sensor>? get sensors {
    final value = _sensors;
    if (value == null) return null;
    if (_sensors is EqualUnmodifiableListView) return _sensors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ImageDriver? image;
  @override
  final String? lastGeometry;
  @override
  final String? keys;
  @override
  final int voltage;
  @override
  final int voltagePercentage;
  @override
  final int speed;
  @override
  final int vehicleMaxSpeed;
  @override
  final String? address;
  @override
  final bool online;
  @override
  final String status;
  @override
  final String statusColor;
  @override
  final String signal;
  @override
  final bool mdvr;
  final List<bool> _inputInfo;
  @override
  List<bool> get inputInfo {
    if (_inputInfo is EqualUnmodifiableListView) return _inputInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_inputInfo);
  }

  final List<bool> _outputInfo;
  @override
  List<bool> get outputInfo {
    if (_outputInfo is EqualUnmodifiableListView) return _outputInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_outputInfo);
  }

  final List<VehicleSetting>? _listLiveSetting;
  @override
  List<VehicleSetting>? get listLiveSetting {
    final value = _listLiveSetting;
    if (value == null) return null;
    if (_listLiveSetting is EqualUnmodifiableListView) return _listLiveSetting;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int vehicleSensorMask;

  @override
  String toString() {
    return 'Vehicle(id: $id, groupName: $groupName, typeName: $typeName, plate: $plate, vin: $vin, simId: $simId, gpsMileage: $gpsMileage, lastMaxSpeed: $lastMaxSpeed, lastGpsTime: $lastGpsTime, lastSpeed: $lastSpeed, lastRegionId: $lastRegionId, lastX: $lastX, lastY: $lastY, lastStatus: $lastStatus, lastInfo: $lastInfo, lastHeading: $lastHeading, lastSatellite: $lastSatellite, lastIsAccOn: $lastIsAccOn, lastRoadName: $lastRoadName, inputs: $inputs, lastRoadSpeed: $lastRoadSpeed, driver: $driver, lastDeviceTypeId: $lastDeviceTypeId, lastImei: $lastImei, lastCamInterval: $lastCamInterval, lastInput: $lastInput, lastOutput: $lastOutput, trigger: $trigger, totalGpsMileage: $totalGpsMileage, pingTime: $pingTime, vehicleIcon: $vehicleIcon, idleTime: $idleTime, sleepInterval: $sleepInterval, accOffTime: $accOffTime, sleepTime: $sleepTime, trip: $trip, tripReport: $tripReport, daily: $daily, sensors: $sensors, image: $image, lastGeometry: $lastGeometry, keys: $keys, voltage: $voltage, voltagePercentage: $voltagePercentage, speed: $speed, vehicleMaxSpeed: $vehicleMaxSpeed, address: $address, online: $online, status: $status, statusColor: $statusColor, signal: $signal, mdvr: $mdvr, inputInfo: $inputInfo, outputInfo: $outputInfo, listLiveSetting: $listLiveSetting, vehicleSensorMask: $vehicleSensorMask)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.groupName, groupName) ||
                other.groupName == groupName) &&
            (identical(other.typeName, typeName) ||
                other.typeName == typeName) &&
            (identical(other.plate, plate) || other.plate == plate) &&
            (identical(other.vin, vin) || other.vin == vin) &&
            (identical(other.simId, simId) || other.simId == simId) &&
            (identical(other.gpsMileage, gpsMileage) ||
                other.gpsMileage == gpsMileage) &&
            (identical(other.lastMaxSpeed, lastMaxSpeed) ||
                other.lastMaxSpeed == lastMaxSpeed) &&
            (identical(other.lastGpsTime, lastGpsTime) ||
                other.lastGpsTime == lastGpsTime) &&
            (identical(other.lastSpeed, lastSpeed) ||
                other.lastSpeed == lastSpeed) &&
            (identical(other.lastRegionId, lastRegionId) ||
                other.lastRegionId == lastRegionId) &&
            (identical(other.lastX, lastX) || other.lastX == lastX) &&
            (identical(other.lastY, lastY) || other.lastY == lastY) &&
            (identical(other.lastStatus, lastStatus) ||
                other.lastStatus == lastStatus) &&
            (identical(other.lastInfo, lastInfo) ||
                other.lastInfo == lastInfo) &&
            (identical(other.lastHeading, lastHeading) ||
                other.lastHeading == lastHeading) &&
            (identical(other.lastSatellite, lastSatellite) ||
                other.lastSatellite == lastSatellite) &&
            (identical(other.lastIsAccOn, lastIsAccOn) ||
                other.lastIsAccOn == lastIsAccOn) &&
            (identical(other.lastRoadName, lastRoadName) ||
                other.lastRoadName == lastRoadName) &&
            (identical(other.inputs, inputs) || other.inputs == inputs) &&
            (identical(other.lastRoadSpeed, lastRoadSpeed) ||
                other.lastRoadSpeed == lastRoadSpeed) &&
            (identical(other.driver, driver) || other.driver == driver) &&
            (identical(other.lastDeviceTypeId, lastDeviceTypeId) ||
                other.lastDeviceTypeId == lastDeviceTypeId) &&
            (identical(other.lastImei, lastImei) ||
                other.lastImei == lastImei) &&
            (identical(other.lastCamInterval, lastCamInterval) ||
                other.lastCamInterval == lastCamInterval) &&
            (identical(other.lastInput, lastInput) ||
                other.lastInput == lastInput) &&
            (identical(other.lastOutput, lastOutput) ||
                other.lastOutput == lastOutput) &&
            (identical(other.trigger, trigger) || other.trigger == trigger) &&
            (identical(other.totalGpsMileage, totalGpsMileage) ||
                other.totalGpsMileage == totalGpsMileage) &&
            (identical(other.pingTime, pingTime) ||
                other.pingTime == pingTime) &&
            (identical(other.vehicleIcon, vehicleIcon) ||
                other.vehicleIcon == vehicleIcon) &&
            (identical(other.idleTime, idleTime) ||
                other.idleTime == idleTime) &&
            (identical(other.sleepInterval, sleepInterval) ||
                other.sleepInterval == sleepInterval) &&
            (identical(other.accOffTime, accOffTime) ||
                other.accOffTime == accOffTime) &&
            (identical(other.sleepTime, sleepTime) ||
                other.sleepTime == sleepTime) &&
            (identical(other.trip, trip) || other.trip == trip) &&
            (identical(other.tripReport, tripReport) ||
                other.tripReport == tripReport) &&
            (identical(other.daily, daily) || other.daily == daily) &&
            const DeepCollectionEquality().equals(other._sensors, _sensors) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.lastGeometry, lastGeometry) ||
                other.lastGeometry == lastGeometry) &&
            (identical(other.keys, keys) || other.keys == keys) &&
            (identical(other.voltage, voltage) || other.voltage == voltage) &&
            (identical(other.voltagePercentage, voltagePercentage) ||
                other.voltagePercentage == voltagePercentage) &&
            (identical(other.speed, speed) || other.speed == speed) &&
            (identical(other.vehicleMaxSpeed, vehicleMaxSpeed) ||
                other.vehicleMaxSpeed == vehicleMaxSpeed) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.online, online) || other.online == online) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.statusColor, statusColor) ||
                other.statusColor == statusColor) &&
            (identical(other.signal, signal) || other.signal == signal) &&
            (identical(other.mdvr, mdvr) || other.mdvr == mdvr) &&
            const DeepCollectionEquality()
                .equals(other._inputInfo, _inputInfo) &&
            const DeepCollectionEquality()
                .equals(other._outputInfo, _outputInfo) &&
            const DeepCollectionEquality()
                .equals(other._listLiveSetting, _listLiveSetting) &&
            (identical(other.vehicleSensorMask, vehicleSensorMask) ||
                other.vehicleSensorMask == vehicleSensorMask));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        groupName,
        typeName,
        plate,
        vin,
        simId,
        gpsMileage,
        lastMaxSpeed,
        lastGpsTime,
        lastSpeed,
        lastRegionId,
        lastX,
        lastY,
        lastStatus,
        lastInfo,
        lastHeading,
        lastSatellite,
        lastIsAccOn,
        lastRoadName,
        inputs,
        lastRoadSpeed,
        driver,
        lastDeviceTypeId,
        lastImei,
        lastCamInterval,
        lastInput,
        lastOutput,
        trigger,
        totalGpsMileage,
        pingTime,
        vehicleIcon,
        idleTime,
        sleepInterval,
        accOffTime,
        sleepTime,
        trip,
        tripReport,
        daily,
        const DeepCollectionEquality().hash(_sensors),
        image,
        lastGeometry,
        keys,
        voltage,
        voltagePercentage,
        speed,
        vehicleMaxSpeed,
        address,
        online,
        status,
        statusColor,
        signal,
        mdvr,
        const DeepCollectionEquality().hash(_inputInfo),
        const DeepCollectionEquality().hash(_outputInfo),
        const DeepCollectionEquality().hash(_listLiveSetting),
        vehicleSensorMask
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleImplCopyWith<_$VehicleImpl> get copyWith =>
      __$$VehicleImplCopyWithImpl<_$VehicleImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleImplToJson(
      this,
    );
  }
}

abstract class _Vehicle extends Vehicle {
  const factory _Vehicle(
      {required final String id,
      required final String groupName,
      required final String typeName,
      required final String plate,
      required final String? vin,
      required final String? simId,
      required final int gpsMileage,
      required final int lastMaxSpeed,
      required final int lastGpsTime,
      required final int lastSpeed,
      required final int lastRegionId,
      required final int lastX,
      required final int lastY,
      required final int lastStatus,
      required final String lastInfo,
      required final int lastHeading,
      required final int lastSatellite,
      required final int lastIsAccOn,
      required final String? lastRoadName,
      required final Inputs? inputs,
      required final int lastRoadSpeed,
      required final Driver? driver,
      required final int lastDeviceTypeId,
      required final String lastImei,
      required final int lastCamInterval,
      required final int? lastInput,
      required final int? lastOutput,
      required final String? trigger,
      required final int totalGpsMileage,
      required final int pingTime,
      required final int vehicleIcon,
      required final int idleTime,
      required final int sleepInterval,
      required final int accOffTime,
      required final int sleepTime,
      required final Trip? trip,
      required final String? tripReport,
      required final Daily? daily,
      required final List<Sensor>? sensors,
      required final ImageDriver? image,
      required final String? lastGeometry,
      required final String? keys,
      required final int voltage,
      required final int voltagePercentage,
      required final int speed,
      required final int vehicleMaxSpeed,
      required final String? address,
      required final bool online,
      required final String status,
      required final String statusColor,
      required final String signal,
      required final bool mdvr,
      required final List<bool> inputInfo,
      required final List<bool> outputInfo,
      required final List<VehicleSetting>? listLiveSetting,
      required final int vehicleSensorMask}) = _$VehicleImpl;
  const _Vehicle._() : super._();

  factory _Vehicle.fromJson(Map<String, dynamic> json) = _$VehicleImpl.fromJson;

  @override
  String get id;
  @override
  String get groupName;
  @override //vehicle Group Id
  String get typeName;
  @override
  String get plate;
  @override
  String? get vin;
  @override
  String? get simId;
  @override
  int get gpsMileage;
  @override
  int get lastMaxSpeed;
  @override
  int get lastGpsTime;
  @override
  int get lastSpeed;
  @override
  int get lastRegionId;
  @override
  int get lastX;
  @override
  int get lastY;
  @override
  int get lastStatus;
  @override
  String get lastInfo;
  @override
  int get lastHeading;
  @override
  int get lastSatellite;
  @override
  int get lastIsAccOn;
  @override
  String? get lastRoadName;
  @override
  Inputs? get inputs;
  @override
  int get lastRoadSpeed;
  @override
  Driver? get driver;
  @override
  int get lastDeviceTypeId;
  @override
  String get lastImei;
  @override
  int get lastCamInterval;
  @override
  int? get lastInput;
  @override
  int? get lastOutput;
  @override
  String? get trigger;
  @override
  int get totalGpsMileage;
  @override
  int get pingTime;
  @override
  int get vehicleIcon;
  @override
  int get idleTime;
  @override
  int get sleepInterval;
  @override
  int get accOffTime;
  @override
  int get sleepTime;
  @override
  Trip? get trip;
  @override
  String? get tripReport;
  @override
  Daily? get daily;
  @override
  List<Sensor>? get sensors;
  @override
  ImageDriver? get image;
  @override
  String? get lastGeometry;
  @override
  String? get keys;
  @override
  int get voltage;
  @override
  int get voltagePercentage;
  @override
  int get speed;
  @override
  int get vehicleMaxSpeed;
  @override
  String? get address;
  @override
  bool get online;
  @override
  String get status;
  @override
  String get statusColor;
  @override
  String get signal;
  @override
  bool get mdvr;
  @override
  List<bool> get inputInfo;
  @override
  List<bool> get outputInfo;
  @override
  List<VehicleSetting>? get listLiveSetting;
  @override
  int get vehicleSensorMask;
  @override
  @JsonKey(ignore: true)
  _$$VehicleImplCopyWith<_$VehicleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Inputs _$InputsFromJson(Map<String, dynamic> json) {
  return _Inputs.fromJson(json);
}

/// @nodoc
mixin _$Inputs {
  int get lastInput1 => throw _privateConstructorUsedError;
  int get lastInput2 => throw _privateConstructorUsedError;
  int get lastInput3 => throw _privateConstructorUsedError;
  int get lastInput4 => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $InputsCopyWith<Inputs> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InputsCopyWith<$Res> {
  factory $InputsCopyWith(Inputs value, $Res Function(Inputs) then) =
      _$InputsCopyWithImpl<$Res, Inputs>;
  @useResult
  $Res call({int lastInput1, int lastInput2, int lastInput3, int lastInput4});
}

/// @nodoc
class _$InputsCopyWithImpl<$Res, $Val extends Inputs>
    implements $InputsCopyWith<$Res> {
  _$InputsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastInput1 = null,
    Object? lastInput2 = null,
    Object? lastInput3 = null,
    Object? lastInput4 = null,
  }) {
    return _then(_value.copyWith(
      lastInput1: null == lastInput1
          ? _value.lastInput1
          : lastInput1 // ignore: cast_nullable_to_non_nullable
              as int,
      lastInput2: null == lastInput2
          ? _value.lastInput2
          : lastInput2 // ignore: cast_nullable_to_non_nullable
              as int,
      lastInput3: null == lastInput3
          ? _value.lastInput3
          : lastInput3 // ignore: cast_nullable_to_non_nullable
              as int,
      lastInput4: null == lastInput4
          ? _value.lastInput4
          : lastInput4 // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InputsImplCopyWith<$Res> implements $InputsCopyWith<$Res> {
  factory _$$InputsImplCopyWith(
          _$InputsImpl value, $Res Function(_$InputsImpl) then) =
      __$$InputsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int lastInput1, int lastInput2, int lastInput3, int lastInput4});
}

/// @nodoc
class __$$InputsImplCopyWithImpl<$Res>
    extends _$InputsCopyWithImpl<$Res, _$InputsImpl>
    implements _$$InputsImplCopyWith<$Res> {
  __$$InputsImplCopyWithImpl(
      _$InputsImpl _value, $Res Function(_$InputsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastInput1 = null,
    Object? lastInput2 = null,
    Object? lastInput3 = null,
    Object? lastInput4 = null,
  }) {
    return _then(_$InputsImpl(
      lastInput1: null == lastInput1
          ? _value.lastInput1
          : lastInput1 // ignore: cast_nullable_to_non_nullable
              as int,
      lastInput2: null == lastInput2
          ? _value.lastInput2
          : lastInput2 // ignore: cast_nullable_to_non_nullable
              as int,
      lastInput3: null == lastInput3
          ? _value.lastInput3
          : lastInput3 // ignore: cast_nullable_to_non_nullable
              as int,
      lastInput4: null == lastInput4
          ? _value.lastInput4
          : lastInput4 // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InputsImpl extends _Inputs {
  const _$InputsImpl(
      {required this.lastInput1,
      required this.lastInput2,
      required this.lastInput3,
      required this.lastInput4})
      : super._();

  factory _$InputsImpl.fromJson(Map<String, dynamic> json) =>
      _$$InputsImplFromJson(json);

  @override
  final int lastInput1;
  @override
  final int lastInput2;
  @override
  final int lastInput3;
  @override
  final int lastInput4;

  @override
  String toString() {
    return 'Inputs(lastInput1: $lastInput1, lastInput2: $lastInput2, lastInput3: $lastInput3, lastInput4: $lastInput4)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InputsImpl &&
            (identical(other.lastInput1, lastInput1) ||
                other.lastInput1 == lastInput1) &&
            (identical(other.lastInput2, lastInput2) ||
                other.lastInput2 == lastInput2) &&
            (identical(other.lastInput3, lastInput3) ||
                other.lastInput3 == lastInput3) &&
            (identical(other.lastInput4, lastInput4) ||
                other.lastInput4 == lastInput4));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, lastInput1, lastInput2, lastInput3, lastInput4);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InputsImplCopyWith<_$InputsImpl> get copyWith =>
      __$$InputsImplCopyWithImpl<_$InputsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InputsImplToJson(
      this,
    );
  }
}

abstract class _Inputs extends Inputs {
  const factory _Inputs(
      {required final int lastInput1,
      required final int lastInput2,
      required final int lastInput3,
      required final int lastInput4}) = _$InputsImpl;
  const _Inputs._() : super._();

  factory _Inputs.fromJson(Map<String, dynamic> json) = _$InputsImpl.fromJson;

  @override
  int get lastInput1;
  @override
  int get lastInput2;
  @override
  int get lastInput3;
  @override
  int get lastInput4;
  @override
  @JsonKey(ignore: true)
  _$$InputsImplCopyWith<_$InputsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Driver _$DriverFromJson(Map<String, dynamic> json) {
  return _Driver.fromJson(json);
}

/// @nodoc
mixin _$Driver {
  String get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get phone => throw _privateConstructorUsedError;
  String? get licenseNo => throw _privateConstructorUsedError;
  int get expiredDate => throw _privateConstructorUsedError;
  String? get phone2 => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DriverCopyWith<Driver> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverCopyWith<$Res> {
  factory $DriverCopyWith(Driver value, $Res Function(Driver) then) =
      _$DriverCopyWithImpl<$Res, Driver>;
  @useResult
  $Res call(
      {String id,
      String? name,
      String? phone,
      String? licenseNo,
      int expiredDate,
      String? phone2});
}

/// @nodoc
class _$DriverCopyWithImpl<$Res, $Val extends Driver>
    implements $DriverCopyWith<$Res> {
  _$DriverCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? phone = freezed,
    Object? licenseNo = freezed,
    Object? expiredDate = null,
    Object? phone2 = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseNo: freezed == licenseNo
          ? _value.licenseNo
          : licenseNo // ignore: cast_nullable_to_non_nullable
              as String?,
      expiredDate: null == expiredDate
          ? _value.expiredDate
          : expiredDate // ignore: cast_nullable_to_non_nullable
              as int,
      phone2: freezed == phone2
          ? _value.phone2
          : phone2 // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DriverImplCopyWith<$Res> implements $DriverCopyWith<$Res> {
  factory _$$DriverImplCopyWith(
          _$DriverImpl value, $Res Function(_$DriverImpl) then) =
      __$$DriverImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? name,
      String? phone,
      String? licenseNo,
      int expiredDate,
      String? phone2});
}

/// @nodoc
class __$$DriverImplCopyWithImpl<$Res>
    extends _$DriverCopyWithImpl<$Res, _$DriverImpl>
    implements _$$DriverImplCopyWith<$Res> {
  __$$DriverImplCopyWithImpl(
      _$DriverImpl _value, $Res Function(_$DriverImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? phone = freezed,
    Object? licenseNo = freezed,
    Object? expiredDate = null,
    Object? phone2 = freezed,
  }) {
    return _then(_$DriverImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseNo: freezed == licenseNo
          ? _value.licenseNo
          : licenseNo // ignore: cast_nullable_to_non_nullable
              as String?,
      expiredDate: null == expiredDate
          ? _value.expiredDate
          : expiredDate // ignore: cast_nullable_to_non_nullable
              as int,
      phone2: freezed == phone2
          ? _value.phone2
          : phone2 // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverImpl extends _Driver {
  const _$DriverImpl(
      {required this.id,
      required this.name,
      required this.phone,
      required this.licenseNo,
      required this.expiredDate,
      required this.phone2})
      : super._();

  factory _$DriverImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverImplFromJson(json);

  @override
  final String id;
  @override
  final String? name;
  @override
  final String? phone;
  @override
  final String? licenseNo;
  @override
  final int expiredDate;
  @override
  final String? phone2;

  @override
  String toString() {
    return 'Driver(id: $id, name: $name, phone: $phone, licenseNo: $licenseNo, expiredDate: $expiredDate, phone2: $phone2)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.licenseNo, licenseNo) ||
                other.licenseNo == licenseNo) &&
            (identical(other.expiredDate, expiredDate) ||
                other.expiredDate == expiredDate) &&
            (identical(other.phone2, phone2) || other.phone2 == phone2));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, phone, licenseNo, expiredDate, phone2);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverImplCopyWith<_$DriverImpl> get copyWith =>
      __$$DriverImplCopyWithImpl<_$DriverImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverImplToJson(
      this,
    );
  }
}

abstract class _Driver extends Driver {
  const factory _Driver(
      {required final String id,
      required final String? name,
      required final String? phone,
      required final String? licenseNo,
      required final int expiredDate,
      required final String? phone2}) = _$DriverImpl;
  const _Driver._() : super._();

  factory _Driver.fromJson(Map<String, dynamic> json) = _$DriverImpl.fromJson;

  @override
  String get id;
  @override
  String? get name;
  @override
  String? get phone;
  @override
  String? get licenseNo;
  @override
  int get expiredDate;
  @override
  String? get phone2;
  @override
  @JsonKey(ignore: true)
  _$$DriverImplCopyWith<_$DriverImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Trip _$TripFromJson(Map<String, dynamic> json) {
  return _Trip.fromJson(json);
}

/// @nodoc
mixin _$Trip {
  int get fromTime => throw _privateConstructorUsedError; //0
  int get toTime => throw _privateConstructorUsedError; //1
  int get gpsMileage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TripCopyWith<Trip> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TripCopyWith<$Res> {
  factory $TripCopyWith(Trip value, $Res Function(Trip) then) =
      _$TripCopyWithImpl<$Res, Trip>;
  @useResult
  $Res call({int fromTime, int toTime, int gpsMileage});
}

/// @nodoc
class _$TripCopyWithImpl<$Res, $Val extends Trip>
    implements $TripCopyWith<$Res> {
  _$TripCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fromTime = null,
    Object? toTime = null,
    Object? gpsMileage = null,
  }) {
    return _then(_value.copyWith(
      fromTime: null == fromTime
          ? _value.fromTime
          : fromTime // ignore: cast_nullable_to_non_nullable
              as int,
      toTime: null == toTime
          ? _value.toTime
          : toTime // ignore: cast_nullable_to_non_nullable
              as int,
      gpsMileage: null == gpsMileage
          ? _value.gpsMileage
          : gpsMileage // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TripImplCopyWith<$Res> implements $TripCopyWith<$Res> {
  factory _$$TripImplCopyWith(
          _$TripImpl value, $Res Function(_$TripImpl) then) =
      __$$TripImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int fromTime, int toTime, int gpsMileage});
}

/// @nodoc
class __$$TripImplCopyWithImpl<$Res>
    extends _$TripCopyWithImpl<$Res, _$TripImpl>
    implements _$$TripImplCopyWith<$Res> {
  __$$TripImplCopyWithImpl(_$TripImpl _value, $Res Function(_$TripImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fromTime = null,
    Object? toTime = null,
    Object? gpsMileage = null,
  }) {
    return _then(_$TripImpl(
      fromTime: null == fromTime
          ? _value.fromTime
          : fromTime // ignore: cast_nullable_to_non_nullable
              as int,
      toTime: null == toTime
          ? _value.toTime
          : toTime // ignore: cast_nullable_to_non_nullable
              as int,
      gpsMileage: null == gpsMileage
          ? _value.gpsMileage
          : gpsMileage // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TripImpl extends _Trip {
  const _$TripImpl(
      {required this.fromTime, required this.toTime, required this.gpsMileage})
      : super._();

  factory _$TripImpl.fromJson(Map<String, dynamic> json) =>
      _$$TripImplFromJson(json);

  @override
  final int fromTime;
//0
  @override
  final int toTime;
//1
  @override
  final int gpsMileage;

  @override
  String toString() {
    return 'Trip(fromTime: $fromTime, toTime: $toTime, gpsMileage: $gpsMileage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TripImpl &&
            (identical(other.fromTime, fromTime) ||
                other.fromTime == fromTime) &&
            (identical(other.toTime, toTime) || other.toTime == toTime) &&
            (identical(other.gpsMileage, gpsMileage) ||
                other.gpsMileage == gpsMileage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, fromTime, toTime, gpsMileage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TripImplCopyWith<_$TripImpl> get copyWith =>
      __$$TripImplCopyWithImpl<_$TripImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TripImplToJson(
      this,
    );
  }
}

abstract class _Trip extends Trip {
  const factory _Trip(
      {required final int fromTime,
      required final int toTime,
      required final int gpsMileage}) = _$TripImpl;
  const _Trip._() : super._();

  factory _Trip.fromJson(Map<String, dynamic> json) = _$TripImpl.fromJson;

  @override
  int get fromTime;
  @override //0
  int get toTime;
  @override //1
  int get gpsMileage;
  @override
  @JsonKey(ignore: true)
  _$$TripImplCopyWith<_$TripImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Daily _$DailyFromJson(Map<String, dynamic> json) {
  return _Daily.fromJson(json);
}

/// @nodoc
mixin _$Daily {
  int get stopCount => throw _privateConstructorUsedError;
  int get idleCount => throw _privateConstructorUsedError;
  int get doorCloseCount => throw _privateConstructorUsedError; //
  int get doorOpenCount => throw _privateConstructorUsedError; //
  int get gpsMileage => throw _privateConstructorUsedError; //
  int get runTime => throw _privateConstructorUsedError; //
  int get overSpeed => throw _privateConstructorUsedError; // => số lần vượt tốc
  int get accTime => throw _privateConstructorUsedError; // =>  thời gian mở máy
  int get maxSpeed => throw _privateConstructorUsedError; // => con số
  int get idleTime => throw _privateConstructorUsedError; // thời gian Dừng
  int get stopTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DailyCopyWith<Daily> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DailyCopyWith<$Res> {
  factory $DailyCopyWith(Daily value, $Res Function(Daily) then) =
      _$DailyCopyWithImpl<$Res, Daily>;
  @useResult
  $Res call(
      {int stopCount,
      int idleCount,
      int doorCloseCount,
      int doorOpenCount,
      int gpsMileage,
      int runTime,
      int overSpeed,
      int accTime,
      int maxSpeed,
      int idleTime,
      int stopTime});
}

/// @nodoc
class _$DailyCopyWithImpl<$Res, $Val extends Daily>
    implements $DailyCopyWith<$Res> {
  _$DailyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stopCount = null,
    Object? idleCount = null,
    Object? doorCloseCount = null,
    Object? doorOpenCount = null,
    Object? gpsMileage = null,
    Object? runTime = null,
    Object? overSpeed = null,
    Object? accTime = null,
    Object? maxSpeed = null,
    Object? idleTime = null,
    Object? stopTime = null,
  }) {
    return _then(_value.copyWith(
      stopCount: null == stopCount
          ? _value.stopCount
          : stopCount // ignore: cast_nullable_to_non_nullable
              as int,
      idleCount: null == idleCount
          ? _value.idleCount
          : idleCount // ignore: cast_nullable_to_non_nullable
              as int,
      doorCloseCount: null == doorCloseCount
          ? _value.doorCloseCount
          : doorCloseCount // ignore: cast_nullable_to_non_nullable
              as int,
      doorOpenCount: null == doorOpenCount
          ? _value.doorOpenCount
          : doorOpenCount // ignore: cast_nullable_to_non_nullable
              as int,
      gpsMileage: null == gpsMileage
          ? _value.gpsMileage
          : gpsMileage // ignore: cast_nullable_to_non_nullable
              as int,
      runTime: null == runTime
          ? _value.runTime
          : runTime // ignore: cast_nullable_to_non_nullable
              as int,
      overSpeed: null == overSpeed
          ? _value.overSpeed
          : overSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      accTime: null == accTime
          ? _value.accTime
          : accTime // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      idleTime: null == idleTime
          ? _value.idleTime
          : idleTime // ignore: cast_nullable_to_non_nullable
              as int,
      stopTime: null == stopTime
          ? _value.stopTime
          : stopTime // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DailyImplCopyWith<$Res> implements $DailyCopyWith<$Res> {
  factory _$$DailyImplCopyWith(
          _$DailyImpl value, $Res Function(_$DailyImpl) then) =
      __$$DailyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int stopCount,
      int idleCount,
      int doorCloseCount,
      int doorOpenCount,
      int gpsMileage,
      int runTime,
      int overSpeed,
      int accTime,
      int maxSpeed,
      int idleTime,
      int stopTime});
}

/// @nodoc
class __$$DailyImplCopyWithImpl<$Res>
    extends _$DailyCopyWithImpl<$Res, _$DailyImpl>
    implements _$$DailyImplCopyWith<$Res> {
  __$$DailyImplCopyWithImpl(
      _$DailyImpl _value, $Res Function(_$DailyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stopCount = null,
    Object? idleCount = null,
    Object? doorCloseCount = null,
    Object? doorOpenCount = null,
    Object? gpsMileage = null,
    Object? runTime = null,
    Object? overSpeed = null,
    Object? accTime = null,
    Object? maxSpeed = null,
    Object? idleTime = null,
    Object? stopTime = null,
  }) {
    return _then(_$DailyImpl(
      stopCount: null == stopCount
          ? _value.stopCount
          : stopCount // ignore: cast_nullable_to_non_nullable
              as int,
      idleCount: null == idleCount
          ? _value.idleCount
          : idleCount // ignore: cast_nullable_to_non_nullable
              as int,
      doorCloseCount: null == doorCloseCount
          ? _value.doorCloseCount
          : doorCloseCount // ignore: cast_nullable_to_non_nullable
              as int,
      doorOpenCount: null == doorOpenCount
          ? _value.doorOpenCount
          : doorOpenCount // ignore: cast_nullable_to_non_nullable
              as int,
      gpsMileage: null == gpsMileage
          ? _value.gpsMileage
          : gpsMileage // ignore: cast_nullable_to_non_nullable
              as int,
      runTime: null == runTime
          ? _value.runTime
          : runTime // ignore: cast_nullable_to_non_nullable
              as int,
      overSpeed: null == overSpeed
          ? _value.overSpeed
          : overSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      accTime: null == accTime
          ? _value.accTime
          : accTime // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      idleTime: null == idleTime
          ? _value.idleTime
          : idleTime // ignore: cast_nullable_to_non_nullable
              as int,
      stopTime: null == stopTime
          ? _value.stopTime
          : stopTime // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DailyImpl extends _Daily {
  const _$DailyImpl(
      {required this.stopCount,
      required this.idleCount,
      required this.doorCloseCount,
      required this.doorOpenCount,
      required this.gpsMileage,
      required this.runTime,
      required this.overSpeed,
      required this.accTime,
      required this.maxSpeed,
      required this.idleTime,
      required this.stopTime})
      : super._();

  factory _$DailyImpl.fromJson(Map<String, dynamic> json) =>
      _$$DailyImplFromJson(json);

  @override
  final int stopCount;
  @override
  final int idleCount;
  @override
  final int doorCloseCount;
//
  @override
  final int doorOpenCount;
//
  @override
  final int gpsMileage;
//
  @override
  final int runTime;
//
  @override
  final int overSpeed;
// => số lần vượt tốc
  @override
  final int accTime;
// =>  thời gian mở máy
  @override
  final int maxSpeed;
// => con số
  @override
  final int idleTime;
// thời gian Dừng
  @override
  final int stopTime;

  @override
  String toString() {
    return 'Daily(stopCount: $stopCount, idleCount: $idleCount, doorCloseCount: $doorCloseCount, doorOpenCount: $doorOpenCount, gpsMileage: $gpsMileage, runTime: $runTime, overSpeed: $overSpeed, accTime: $accTime, maxSpeed: $maxSpeed, idleTime: $idleTime, stopTime: $stopTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DailyImpl &&
            (identical(other.stopCount, stopCount) ||
                other.stopCount == stopCount) &&
            (identical(other.idleCount, idleCount) ||
                other.idleCount == idleCount) &&
            (identical(other.doorCloseCount, doorCloseCount) ||
                other.doorCloseCount == doorCloseCount) &&
            (identical(other.doorOpenCount, doorOpenCount) ||
                other.doorOpenCount == doorOpenCount) &&
            (identical(other.gpsMileage, gpsMileage) ||
                other.gpsMileage == gpsMileage) &&
            (identical(other.runTime, runTime) || other.runTime == runTime) &&
            (identical(other.overSpeed, overSpeed) ||
                other.overSpeed == overSpeed) &&
            (identical(other.accTime, accTime) || other.accTime == accTime) &&
            (identical(other.maxSpeed, maxSpeed) ||
                other.maxSpeed == maxSpeed) &&
            (identical(other.idleTime, idleTime) ||
                other.idleTime == idleTime) &&
            (identical(other.stopTime, stopTime) ||
                other.stopTime == stopTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      stopCount,
      idleCount,
      doorCloseCount,
      doorOpenCount,
      gpsMileage,
      runTime,
      overSpeed,
      accTime,
      maxSpeed,
      idleTime,
      stopTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DailyImplCopyWith<_$DailyImpl> get copyWith =>
      __$$DailyImplCopyWithImpl<_$DailyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DailyImplToJson(
      this,
    );
  }
}

abstract class _Daily extends Daily {
  const factory _Daily(
      {required final int stopCount,
      required final int idleCount,
      required final int doorCloseCount,
      required final int doorOpenCount,
      required final int gpsMileage,
      required final int runTime,
      required final int overSpeed,
      required final int accTime,
      required final int maxSpeed,
      required final int idleTime,
      required final int stopTime}) = _$DailyImpl;
  const _Daily._() : super._();

  factory _Daily.fromJson(Map<String, dynamic> json) = _$DailyImpl.fromJson;

  @override
  int get stopCount;
  @override
  int get idleCount;
  @override
  int get doorCloseCount;
  @override //
  int get doorOpenCount;
  @override //
  int get gpsMileage;
  @override //
  int get runTime;
  @override //
  int get overSpeed;
  @override // => số lần vượt tốc
  int get accTime;
  @override // =>  thời gian mở máy
  int get maxSpeed;
  @override // => con số
  int get idleTime;
  @override // thời gian Dừng
  int get stopTime;
  @override
  @JsonKey(ignore: true)
  _$$DailyImplCopyWith<_$DailyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ExtraInfo _$ExtraInfoFromJson(Map<String, dynamic> json) {
  return _ExtraInfo.fromJson(json);
}

/// @nodoc
mixin _$ExtraInfo {
  String get status => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtraInfoCopyWith<ExtraInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtraInfoCopyWith<$Res> {
  factory $ExtraInfoCopyWith(ExtraInfo value, $Res Function(ExtraInfo) then) =
      _$ExtraInfoCopyWithImpl<$Res, ExtraInfo>;
  @useResult
  $Res call({String status, int quantity});
}

/// @nodoc
class _$ExtraInfoCopyWithImpl<$Res, $Val extends ExtraInfo>
    implements $ExtraInfoCopyWith<$Res> {
  _$ExtraInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? quantity = null,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExtraInfoImplCopyWith<$Res>
    implements $ExtraInfoCopyWith<$Res> {
  factory _$$ExtraInfoImplCopyWith(
          _$ExtraInfoImpl value, $Res Function(_$ExtraInfoImpl) then) =
      __$$ExtraInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String status, int quantity});
}

/// @nodoc
class __$$ExtraInfoImplCopyWithImpl<$Res>
    extends _$ExtraInfoCopyWithImpl<$Res, _$ExtraInfoImpl>
    implements _$$ExtraInfoImplCopyWith<$Res> {
  __$$ExtraInfoImplCopyWithImpl(
      _$ExtraInfoImpl _value, $Res Function(_$ExtraInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? quantity = null,
  }) {
    return _then(_$ExtraInfoImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExtraInfoImpl extends _ExtraInfo {
  const _$ExtraInfoImpl({required this.status, required this.quantity})
      : super._();

  factory _$ExtraInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExtraInfoImplFromJson(json);

  @override
  final String status;
  @override
  final int quantity;

  @override
  String toString() {
    return 'ExtraInfo(status: $status, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExtraInfoImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, status, quantity);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExtraInfoImplCopyWith<_$ExtraInfoImpl> get copyWith =>
      __$$ExtraInfoImplCopyWithImpl<_$ExtraInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExtraInfoImplToJson(
      this,
    );
  }
}

abstract class _ExtraInfo extends ExtraInfo {
  const factory _ExtraInfo(
      {required final String status,
      required final int quantity}) = _$ExtraInfoImpl;
  const _ExtraInfo._() : super._();

  factory _ExtraInfo.fromJson(Map<String, dynamic> json) =
      _$ExtraInfoImpl.fromJson;

  @override
  String get status;
  @override
  int get quantity;
  @override
  @JsonKey(ignore: true)
  _$$ExtraInfoImplCopyWith<_$ExtraInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ImageDriver _$ImageDriverFromJson(Map<String, dynamic> json) {
  return _ImageDriver.fromJson(json);
}

/// @nodoc
mixin _$ImageDriver {
  String? get url => throw _privateConstructorUsedError;
  int get time => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ImageDriverCopyWith<ImageDriver> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ImageDriverCopyWith<$Res> {
  factory $ImageDriverCopyWith(
          ImageDriver value, $Res Function(ImageDriver) then) =
      _$ImageDriverCopyWithImpl<$Res, ImageDriver>;
  @useResult
  $Res call({String? url, int time});
}

/// @nodoc
class _$ImageDriverCopyWithImpl<$Res, $Val extends ImageDriver>
    implements $ImageDriverCopyWith<$Res> {
  _$ImageDriverCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
    Object? time = null,
  }) {
    return _then(_value.copyWith(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ImageDriverImplCopyWith<$Res>
    implements $ImageDriverCopyWith<$Res> {
  factory _$$ImageDriverImplCopyWith(
          _$ImageDriverImpl value, $Res Function(_$ImageDriverImpl) then) =
      __$$ImageDriverImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? url, int time});
}

/// @nodoc
class __$$ImageDriverImplCopyWithImpl<$Res>
    extends _$ImageDriverCopyWithImpl<$Res, _$ImageDriverImpl>
    implements _$$ImageDriverImplCopyWith<$Res> {
  __$$ImageDriverImplCopyWithImpl(
      _$ImageDriverImpl _value, $Res Function(_$ImageDriverImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
    Object? time = null,
  }) {
    return _then(_$ImageDriverImpl(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ImageDriverImpl extends _ImageDriver {
  const _$ImageDriverImpl({required this.url, required this.time}) : super._();

  factory _$ImageDriverImpl.fromJson(Map<String, dynamic> json) =>
      _$$ImageDriverImplFromJson(json);

  @override
  final String? url;
  @override
  final int time;

  @override
  String toString() {
    return 'ImageDriver(url: $url, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ImageDriverImpl &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, url, time);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ImageDriverImplCopyWith<_$ImageDriverImpl> get copyWith =>
      __$$ImageDriverImplCopyWithImpl<_$ImageDriverImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ImageDriverImplToJson(
      this,
    );
  }
}

abstract class _ImageDriver extends ImageDriver {
  const factory _ImageDriver(
      {required final String? url,
      required final int time}) = _$ImageDriverImpl;
  const _ImageDriver._() : super._();

  factory _ImageDriver.fromJson(Map<String, dynamic> json) =
      _$ImageDriverImpl.fromJson;

  @override
  String? get url;
  @override
  int get time;
  @override
  @JsonKey(ignore: true)
  _$$ImageDriverImplCopyWith<_$ImageDriverImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Sensor _$SensorFromJson(Map<String, dynamic> json) {
  return _Sensor.fromJson(json);
}

/// @nodoc
mixin _$Sensor {
  int get type => throw _privateConstructorUsedError;
  int get time => throw _privateConstructorUsedError;
  int get percent => throw _privateConstructorUsedError;
  String? get data => throw _privateConstructorUsedError;
  String get unit => throw _privateConstructorUsedError;
  String get typeName => throw _privateConstructorUsedError;
  int get value => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SensorCopyWith<Sensor> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SensorCopyWith<$Res> {
  factory $SensorCopyWith(Sensor value, $Res Function(Sensor) then) =
      _$SensorCopyWithImpl<$Res, Sensor>;
  @useResult
  $Res call(
      {int type,
      int time,
      int percent,
      String? data,
      String unit,
      String typeName,
      int value});
}

/// @nodoc
class _$SensorCopyWithImpl<$Res, $Val extends Sensor>
    implements $SensorCopyWith<$Res> {
  _$SensorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? time = null,
    Object? percent = null,
    Object? data = freezed,
    Object? unit = null,
    Object? typeName = null,
    Object? value = null,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      percent: null == percent
          ? _value.percent
          : percent // ignore: cast_nullable_to_non_nullable
              as int,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String?,
      unit: null == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as String,
      typeName: null == typeName
          ? _value.typeName
          : typeName // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SensorImplCopyWith<$Res> implements $SensorCopyWith<$Res> {
  factory _$$SensorImplCopyWith(
          _$SensorImpl value, $Res Function(_$SensorImpl) then) =
      __$$SensorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int type,
      int time,
      int percent,
      String? data,
      String unit,
      String typeName,
      int value});
}

/// @nodoc
class __$$SensorImplCopyWithImpl<$Res>
    extends _$SensorCopyWithImpl<$Res, _$SensorImpl>
    implements _$$SensorImplCopyWith<$Res> {
  __$$SensorImplCopyWithImpl(
      _$SensorImpl _value, $Res Function(_$SensorImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? time = null,
    Object? percent = null,
    Object? data = freezed,
    Object? unit = null,
    Object? typeName = null,
    Object? value = null,
  }) {
    return _then(_$SensorImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      percent: null == percent
          ? _value.percent
          : percent // ignore: cast_nullable_to_non_nullable
              as int,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String?,
      unit: null == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as String,
      typeName: null == typeName
          ? _value.typeName
          : typeName // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SensorImpl extends _Sensor {
  const _$SensorImpl(
      {required this.type,
      required this.time,
      required this.percent,
      required this.data,
      required this.unit,
      required this.typeName,
      required this.value})
      : super._();

  factory _$SensorImpl.fromJson(Map<String, dynamic> json) =>
      _$$SensorImplFromJson(json);

  @override
  final int type;
  @override
  final int time;
  @override
  final int percent;
  @override
  final String? data;
  @override
  final String unit;
  @override
  final String typeName;
  @override
  final int value;

  @override
  String toString() {
    return 'Sensor(type: $type, time: $time, percent: $percent, data: $data, unit: $unit, typeName: $typeName, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SensorImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.time, time) || other.time == time) &&
            (identical(other.percent, percent) || other.percent == percent) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.unit, unit) || other.unit == unit) &&
            (identical(other.typeName, typeName) ||
                other.typeName == typeName) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, type, time, percent, data, unit, typeName, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SensorImplCopyWith<_$SensorImpl> get copyWith =>
      __$$SensorImplCopyWithImpl<_$SensorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SensorImplToJson(
      this,
    );
  }
}

abstract class _Sensor extends Sensor {
  const factory _Sensor(
      {required final int type,
      required final int time,
      required final int percent,
      required final String? data,
      required final String unit,
      required final String typeName,
      required final int value}) = _$SensorImpl;
  const _Sensor._() : super._();

  factory _Sensor.fromJson(Map<String, dynamic> json) = _$SensorImpl.fromJson;

  @override
  int get type;
  @override
  int get time;
  @override
  int get percent;
  @override
  String? get data;
  @override
  String get unit;
  @override
  String get typeName;
  @override
  int get value;
  @override
  @JsonKey(ignore: true)
  _$$SensorImplCopyWith<_$SensorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
