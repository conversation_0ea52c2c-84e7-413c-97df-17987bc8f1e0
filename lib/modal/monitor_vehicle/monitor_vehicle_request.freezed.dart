// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'monitor_vehicle_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MonitorVehicleRequest _$MonitorVehicleRequestFromJson(
    Map<String, dynamic> json) {
  return _MonitorVehicleRequest.fromJson(json);
}

/// @nodoc
mixin _$MonitorVehicleRequest {
  int get time => throw _privateConstructorUsedError;
  List<String> get ids => throw _privateConstructorUsedError;
  bool get isOverSpeedByRoad => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MonitorVehicleRequestCopyWith<MonitorVehicleRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MonitorVehicleRequestCopyWith<$Res> {
  factory $MonitorVehicleRequestCopyWith(MonitorVehicleRequest value,
          $Res Function(MonitorVehicleRequest) then) =
      _$MonitorVehicleRequestCopyWithImpl<$Res, MonitorVehicleRequest>;
  @useResult
  $Res call({int time, List<String> ids, bool isOverSpeedByRoad});
}

/// @nodoc
class _$MonitorVehicleRequestCopyWithImpl<$Res,
        $Val extends MonitorVehicleRequest>
    implements $MonitorVehicleRequestCopyWith<$Res> {
  _$MonitorVehicleRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? time = null,
    Object? ids = null,
    Object? isOverSpeedByRoad = null,
  }) {
    return _then(_value.copyWith(
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      ids: null == ids
          ? _value.ids
          : ids // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MonitorVehicleRequestImplCopyWith<$Res>
    implements $MonitorVehicleRequestCopyWith<$Res> {
  factory _$$MonitorVehicleRequestImplCopyWith(
          _$MonitorVehicleRequestImpl value,
          $Res Function(_$MonitorVehicleRequestImpl) then) =
      __$$MonitorVehicleRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int time, List<String> ids, bool isOverSpeedByRoad});
}

/// @nodoc
class __$$MonitorVehicleRequestImplCopyWithImpl<$Res>
    extends _$MonitorVehicleRequestCopyWithImpl<$Res,
        _$MonitorVehicleRequestImpl>
    implements _$$MonitorVehicleRequestImplCopyWith<$Res> {
  __$$MonitorVehicleRequestImplCopyWithImpl(_$MonitorVehicleRequestImpl _value,
      $Res Function(_$MonitorVehicleRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? time = null,
    Object? ids = null,
    Object? isOverSpeedByRoad = null,
  }) {
    return _then(_$MonitorVehicleRequestImpl(
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      ids: null == ids
          ? _value._ids
          : ids // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MonitorVehicleRequestImpl extends _MonitorVehicleRequest {
  const _$MonitorVehicleRequestImpl(
      {required this.time,
      required final List<String> ids,
      required this.isOverSpeedByRoad})
      : _ids = ids,
        super._();

  factory _$MonitorVehicleRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$MonitorVehicleRequestImplFromJson(json);

  @override
  final int time;
  final List<String> _ids;
  @override
  List<String> get ids {
    if (_ids is EqualUnmodifiableListView) return _ids;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_ids);
  }

  @override
  final bool isOverSpeedByRoad;

  @override
  String toString() {
    return 'MonitorVehicleRequest(time: $time, ids: $ids, isOverSpeedByRoad: $isOverSpeedByRoad)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonitorVehicleRequestImpl &&
            (identical(other.time, time) || other.time == time) &&
            const DeepCollectionEquality().equals(other._ids, _ids) &&
            (identical(other.isOverSpeedByRoad, isOverSpeedByRoad) ||
                other.isOverSpeedByRoad == isOverSpeedByRoad));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, time,
      const DeepCollectionEquality().hash(_ids), isOverSpeedByRoad);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MonitorVehicleRequestImplCopyWith<_$MonitorVehicleRequestImpl>
      get copyWith => __$$MonitorVehicleRequestImplCopyWithImpl<
          _$MonitorVehicleRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MonitorVehicleRequestImplToJson(
      this,
    );
  }
}

abstract class _MonitorVehicleRequest extends MonitorVehicleRequest {
  const factory _MonitorVehicleRequest(
      {required final int time,
      required final List<String> ids,
      required final bool isOverSpeedByRoad}) = _$MonitorVehicleRequestImpl;
  const _MonitorVehicleRequest._() : super._();

  factory _MonitorVehicleRequest.fromJson(Map<String, dynamic> json) =
      _$MonitorVehicleRequestImpl.fromJson;

  @override
  int get time;
  @override
  List<String> get ids;
  @override
  bool get isOverSpeedByRoad;
  @override
  @JsonKey(ignore: true)
  _$$MonitorVehicleRequestImplCopyWith<_$MonitorVehicleRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
