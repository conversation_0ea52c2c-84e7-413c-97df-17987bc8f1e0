// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'base_locator_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BaseLocatorResponse _$BaseLocatorResponseFromJson(Map<String, dynamic> json) {
  return _BaseLocatorResponse.fromJson(json);
}

/// @nodoc
mixin _$BaseLocatorResponse {
  String get id => throw _privateConstructorUsedError;
  String get token => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BaseLocatorResponseCopyWith<BaseLocatorResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BaseLocatorResponseCopyWith<$Res> {
  factory $BaseLocatorResponseCopyWith(
          BaseLocatorResponse value, $Res Function(BaseLocatorResponse) then) =
      _$BaseLocatorResponseCopyWithImpl<$Res, BaseLocatorResponse>;
  @useResult
  $Res call({String id, String token});
}

/// @nodoc
class _$BaseLocatorResponseCopyWithImpl<$Res, $Val extends BaseLocatorResponse>
    implements $BaseLocatorResponseCopyWith<$Res> {
  _$BaseLocatorResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? token = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BaseLocatorResponseImplCopyWith<$Res>
    implements $BaseLocatorResponseCopyWith<$Res> {
  factory _$$BaseLocatorResponseImplCopyWith(_$BaseLocatorResponseImpl value,
          $Res Function(_$BaseLocatorResponseImpl) then) =
      __$$BaseLocatorResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String token});
}

/// @nodoc
class __$$BaseLocatorResponseImplCopyWithImpl<$Res>
    extends _$BaseLocatorResponseCopyWithImpl<$Res, _$BaseLocatorResponseImpl>
    implements _$$BaseLocatorResponseImplCopyWith<$Res> {
  __$$BaseLocatorResponseImplCopyWithImpl(_$BaseLocatorResponseImpl _value,
      $Res Function(_$BaseLocatorResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? token = null,
  }) {
    return _then(_$BaseLocatorResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BaseLocatorResponseImpl extends _BaseLocatorResponse {
  const _$BaseLocatorResponseImpl({required this.id, required this.token})
      : super._();

  factory _$BaseLocatorResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$BaseLocatorResponseImplFromJson(json);

  @override
  final String id;
  @override
  final String token;

  @override
  String toString() {
    return 'BaseLocatorResponse(id: $id, token: $token)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BaseLocatorResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.token, token) || other.token == token));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, token);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BaseLocatorResponseImplCopyWith<_$BaseLocatorResponseImpl> get copyWith =>
      __$$BaseLocatorResponseImplCopyWithImpl<_$BaseLocatorResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BaseLocatorResponseImplToJson(
      this,
    );
  }
}

abstract class _BaseLocatorResponse extends BaseLocatorResponse {
  const factory _BaseLocatorResponse(
      {required final String id,
      required final String token}) = _$BaseLocatorResponseImpl;
  const _BaseLocatorResponse._() : super._();

  factory _BaseLocatorResponse.fromJson(Map<String, dynamic> json) =
      _$BaseLocatorResponseImpl.fromJson;

  @override
  String get id;
  @override
  String get token;
  @override
  @JsonKey(ignore: true)
  _$$BaseLocatorResponseImplCopyWith<_$BaseLocatorResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
