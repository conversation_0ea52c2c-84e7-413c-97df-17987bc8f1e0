// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_locator_link_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GetLocatorLinkRequest _$GetLocatorLinkRequestFromJson(
    Map<String, dynamic> json) {
  return _GetLocatorLinkRequest.fromJson(json);
}

/// @nodoc
mixin _$GetLocatorLinkRequest {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GetLocatorLinkRequestCopyWith<GetLocatorLinkRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetLocatorLinkRequestCopyWith<$Res> {
  factory $GetLocatorLinkRequestCopyWith(GetLocatorLinkRequest value,
          $Res Function(GetLocatorLinkRequest) then) =
      _$GetLocatorLinkRequestCopyWithImpl<$Res, GetLocatorLinkRequest>;
  @useResult
  $Res call({String id, String userId});
}

/// @nodoc
class _$GetLocatorLinkRequestCopyWithImpl<$Res,
        $Val extends GetLocatorLinkRequest>
    implements $GetLocatorLinkRequestCopyWith<$Res> {
  _$GetLocatorLinkRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetLocatorLinkRequestImplCopyWith<$Res>
    implements $GetLocatorLinkRequestCopyWith<$Res> {
  factory _$$GetLocatorLinkRequestImplCopyWith(
          _$GetLocatorLinkRequestImpl value,
          $Res Function(_$GetLocatorLinkRequestImpl) then) =
      __$$GetLocatorLinkRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String userId});
}

/// @nodoc
class __$$GetLocatorLinkRequestImplCopyWithImpl<$Res>
    extends _$GetLocatorLinkRequestCopyWithImpl<$Res,
        _$GetLocatorLinkRequestImpl>
    implements _$$GetLocatorLinkRequestImplCopyWith<$Res> {
  __$$GetLocatorLinkRequestImplCopyWithImpl(_$GetLocatorLinkRequestImpl _value,
      $Res Function(_$GetLocatorLinkRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
  }) {
    return _then(_$GetLocatorLinkRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GetLocatorLinkRequestImpl extends _GetLocatorLinkRequest {
  const _$GetLocatorLinkRequestImpl({required this.id, required this.userId})
      : super._();

  factory _$GetLocatorLinkRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetLocatorLinkRequestImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;

  @override
  String toString() {
    return 'GetLocatorLinkRequest(id: $id, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetLocatorLinkRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetLocatorLinkRequestImplCopyWith<_$GetLocatorLinkRequestImpl>
      get copyWith => __$$GetLocatorLinkRequestImplCopyWithImpl<
          _$GetLocatorLinkRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetLocatorLinkRequestImplToJson(
      this,
    );
  }
}

abstract class _GetLocatorLinkRequest extends GetLocatorLinkRequest {
  const factory _GetLocatorLinkRequest(
      {required final String id,
      required final String userId}) = _$GetLocatorLinkRequestImpl;
  const _GetLocatorLinkRequest._() : super._();

  factory _GetLocatorLinkRequest.fromJson(Map<String, dynamic> json) =
      _$GetLocatorLinkRequestImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  @JsonKey(ignore: true)
  _$$GetLocatorLinkRequestImplCopyWith<_$GetLocatorLinkRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
