// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_locator_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DetailLocatorResponseImpl _$$DetailLocatorResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$DetailLocatorResponseImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      date: (json['date'] as num).toInt(),
      toTime: (json['toTime'] as num).toInt(),
      toTimeType: (json['toTimeType'] as num).toInt(),
      email: json['email'] as String,
      vehicles:
          (json['vehicles'] as List<dynamic>).map((e) => e as String).toList(),
      geofences:
          (json['geofences'] as List<dynamic>).map((e) => e as String).toList(),
      stations:
          (json['stations'] as List<dynamic>).map((e) => e as String).toList(),
      fields:
          (json['fields'] as List<dynamic>).map((e) => e as String).toList(),
      options:
          (json['options'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$DetailLocatorResponseImplToJson(
        _$DetailLocatorResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'date': instance.date,
      'toTime': instance.toTime,
      'toTimeType': instance.toTimeType,
      'email': instance.email,
      'vehicles': instance.vehicles,
      'geofences': instance.geofences,
      'stations': instance.stations,
      'fields': instance.fields,
      'options': instance.options,
    };
