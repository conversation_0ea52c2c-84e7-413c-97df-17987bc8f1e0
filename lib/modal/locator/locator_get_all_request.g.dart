// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'locator_get_all_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LocatorGetAllRequestImpl _$$LocatorGetAllRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$LocatorGetAllRequestImpl(
      pageIndex: (json['pageIndex'] as num).toInt(),
      pageSize: (json['pageSize'] as num).toInt(),
      orderBy: json['orderBy'] as String?,
      searchTerm: json['searchTerm'] as String?,
    );

Map<String, dynamic> _$$LocatorGetAllRequestImplToJson(
        _$LocatorGetAllRequestImpl instance) =>
    <String, dynamic>{
      'pageIndex': instance.pageIndex,
      'pageSize': instance.pageSize,
      'orderBy': instance.orderBy,
      'searchTerm': instance.searchTerm,
    };
