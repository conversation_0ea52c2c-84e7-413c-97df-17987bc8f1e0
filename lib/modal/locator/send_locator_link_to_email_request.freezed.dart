// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'send_locator_link_to_email_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SendLocatorLinkToMailRequest _$SendLocatorLinkToMailRequestFromJson(
    Map<String, dynamic> json) {
  return _SendLocatorLinkToMailRequest.fromJson(json);
}

/// @nodoc
mixin _$SendLocatorLinkToMailRequest {
  String get id => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SendLocatorLinkToMailRequestCopyWith<SendLocatorLinkToMailRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SendLocatorLinkToMailRequestCopyWith<$Res> {
  factory $SendLocatorLinkToMailRequestCopyWith(
          SendLocatorLinkToMailRequest value,
          $Res Function(SendLocatorLinkToMailRequest) then) =
      _$SendLocatorLinkToMailRequestCopyWithImpl<$Res,
          SendLocatorLinkToMailRequest>;
  @useResult
  $Res call({String id, String email});
}

/// @nodoc
class _$SendLocatorLinkToMailRequestCopyWithImpl<$Res,
        $Val extends SendLocatorLinkToMailRequest>
    implements $SendLocatorLinkToMailRequestCopyWith<$Res> {
  _$SendLocatorLinkToMailRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SendLocatorLinkToMailRequestImplCopyWith<$Res>
    implements $SendLocatorLinkToMailRequestCopyWith<$Res> {
  factory _$$SendLocatorLinkToMailRequestImplCopyWith(
          _$SendLocatorLinkToMailRequestImpl value,
          $Res Function(_$SendLocatorLinkToMailRequestImpl) then) =
      __$$SendLocatorLinkToMailRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String email});
}

/// @nodoc
class __$$SendLocatorLinkToMailRequestImplCopyWithImpl<$Res>
    extends _$SendLocatorLinkToMailRequestCopyWithImpl<$Res,
        _$SendLocatorLinkToMailRequestImpl>
    implements _$$SendLocatorLinkToMailRequestImplCopyWith<$Res> {
  __$$SendLocatorLinkToMailRequestImplCopyWithImpl(
      _$SendLocatorLinkToMailRequestImpl _value,
      $Res Function(_$SendLocatorLinkToMailRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
  }) {
    return _then(_$SendLocatorLinkToMailRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SendLocatorLinkToMailRequestImpl extends _SendLocatorLinkToMailRequest {
  const _$SendLocatorLinkToMailRequestImpl(
      {required this.id, required this.email})
      : super._();

  factory _$SendLocatorLinkToMailRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$SendLocatorLinkToMailRequestImplFromJson(json);

  @override
  final String id;
  @override
  final String email;

  @override
  String toString() {
    return 'SendLocatorLinkToMailRequest(id: $id, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendLocatorLinkToMailRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, email);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SendLocatorLinkToMailRequestImplCopyWith<
          _$SendLocatorLinkToMailRequestImpl>
      get copyWith => __$$SendLocatorLinkToMailRequestImplCopyWithImpl<
          _$SendLocatorLinkToMailRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SendLocatorLinkToMailRequestImplToJson(
      this,
    );
  }
}

abstract class _SendLocatorLinkToMailRequest
    extends SendLocatorLinkToMailRequest {
  const factory _SendLocatorLinkToMailRequest(
      {required final String id,
      required final String email}) = _$SendLocatorLinkToMailRequestImpl;
  const _SendLocatorLinkToMailRequest._() : super._();

  factory _SendLocatorLinkToMailRequest.fromJson(Map<String, dynamic> json) =
      _$SendLocatorLinkToMailRequestImpl.fromJson;

  @override
  String get id;
  @override
  String get email;
  @override
  @JsonKey(ignore: true)
  _$$SendLocatorLinkToMailRequestImplCopyWith<
          _$SendLocatorLinkToMailRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
