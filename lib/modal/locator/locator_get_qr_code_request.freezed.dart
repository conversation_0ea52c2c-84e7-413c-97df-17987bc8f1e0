// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'locator_get_qr_code_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LocatorGetQRCodeRequest _$LocatorGetQRCodeRequestFromJson(
    Map<String, dynamic> json) {
  return _LocatorGetQRCodeRequest.fromJson(json);
}

/// @nodoc
mixin _$LocatorGetQRCodeRequest {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocatorGetQRCodeRequestCopyWith<LocatorGetQRCodeRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorGetQRCodeRequestCopyWith<$Res> {
  factory $LocatorGetQRCodeRequestCopyWith(LocatorGetQRCodeRequest value,
          $Res Function(LocatorGetQRCodeRequest) then) =
      _$LocatorGetQRCodeRequestCopyWithImpl<$Res, LocatorGetQRCodeRequest>;
  @useResult
  $Res call({String id, String userId});
}

/// @nodoc
class _$LocatorGetQRCodeRequestCopyWithImpl<$Res,
        $Val extends LocatorGetQRCodeRequest>
    implements $LocatorGetQRCodeRequestCopyWith<$Res> {
  _$LocatorGetQRCodeRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocatorGetQRCodeRequestImplCopyWith<$Res>
    implements $LocatorGetQRCodeRequestCopyWith<$Res> {
  factory _$$LocatorGetQRCodeRequestImplCopyWith(
          _$LocatorGetQRCodeRequestImpl value,
          $Res Function(_$LocatorGetQRCodeRequestImpl) then) =
      __$$LocatorGetQRCodeRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String userId});
}

/// @nodoc
class __$$LocatorGetQRCodeRequestImplCopyWithImpl<$Res>
    extends _$LocatorGetQRCodeRequestCopyWithImpl<$Res,
        _$LocatorGetQRCodeRequestImpl>
    implements _$$LocatorGetQRCodeRequestImplCopyWith<$Res> {
  __$$LocatorGetQRCodeRequestImplCopyWithImpl(
      _$LocatorGetQRCodeRequestImpl _value,
      $Res Function(_$LocatorGetQRCodeRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
  }) {
    return _then(_$LocatorGetQRCodeRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocatorGetQRCodeRequestImpl extends _LocatorGetQRCodeRequest {
  const _$LocatorGetQRCodeRequestImpl({required this.id, required this.userId})
      : super._();

  factory _$LocatorGetQRCodeRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocatorGetQRCodeRequestImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;

  @override
  String toString() {
    return 'LocatorGetQRCodeRequest(id: $id, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorGetQRCodeRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorGetQRCodeRequestImplCopyWith<_$LocatorGetQRCodeRequestImpl>
      get copyWith => __$$LocatorGetQRCodeRequestImplCopyWithImpl<
          _$LocatorGetQRCodeRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocatorGetQRCodeRequestImplToJson(
      this,
    );
  }
}

abstract class _LocatorGetQRCodeRequest extends LocatorGetQRCodeRequest {
  const factory _LocatorGetQRCodeRequest(
      {required final String id,
      required final String userId}) = _$LocatorGetQRCodeRequestImpl;
  const _LocatorGetQRCodeRequest._() : super._();

  factory _LocatorGetQRCodeRequest.fromJson(Map<String, dynamic> json) =
      _$LocatorGetQRCodeRequestImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  @JsonKey(ignore: true)
  _$$LocatorGetQRCodeRequestImplCopyWith<_$LocatorGetQRCodeRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
