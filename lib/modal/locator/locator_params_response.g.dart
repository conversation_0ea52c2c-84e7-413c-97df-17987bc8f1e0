// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'locator_params_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LocatorParamsResponseImpl _$$LocatorParamsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$LocatorParamsResponseImpl(
      geofences: (json['geofences'] as List<dynamic>)
          .map((e) => LocatorGeofence.fromJson(e as Map<String, dynamic>))
          .toList(),
      stations: (json['stations'] as List<dynamic>)
          .map((e) => LocatorStation.fromJson(e as Map<String, dynamic>))
          .toList(),
      statuses: (json['statuses'] as List<dynamic>)
          .map((e) => LocatorStatus.fromJson(e as Map<String, dynamic>))
          .toList(),
      options: (json['options'] as List<dynamic>)
          .map((e) => LocatorOption.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$LocatorParamsResponseImplToJson(
        _$LocatorParamsResponseImpl instance) =>
    <String, dynamic>{
      'geofences': instance.geofences,
      'stations': instance.stations,
      'statuses': instance.statuses,
      'options': instance.options,
    };

_$LocatorGeofenceImpl _$$LocatorGeofenceImplFromJson(
        Map<String, dynamic> json) =>
    _$LocatorGeofenceImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      type: (json['type'] as num).toInt(),
      data: json['data'] as String,
    );

Map<String, dynamic> _$$LocatorGeofenceImplToJson(
        _$LocatorGeofenceImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'data': instance.data,
    };

_$LocatorStationImpl _$$LocatorStationImplFromJson(Map<String, dynamic> json) =>
    _$LocatorStationImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      categoryId: (json['categoryId'] as num).toInt(),
    );

Map<String, dynamic> _$$LocatorStationImplToJson(
        _$LocatorStationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'categoryId': instance.categoryId,
    };

_$LocatorStatusImpl _$$LocatorStatusImplFromJson(Map<String, dynamic> json) =>
    _$LocatorStatusImpl(
      id: json['id'] as String,
      name: json['name'] as String,
    );

Map<String, dynamic> _$$LocatorStatusImplToJson(_$LocatorStatusImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
    };

_$LocatorOptionImpl _$$LocatorOptionImplFromJson(Map<String, dynamic> json) =>
    _$LocatorOptionImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$$LocatorOptionImplToJson(_$LocatorOptionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
    };
