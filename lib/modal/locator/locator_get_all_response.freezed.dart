// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'locator_get_all_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LocatorGetAllResponse _$LocatorGetAllResponseFromJson(
    Map<String, dynamic> json) {
  return _LocatorGetAllResponse.fromJson(json);
}

/// @nodoc
mixin _$LocatorGetAllResponse {
  int get total => throw _privateConstructorUsedError;
  List<Locator> get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocatorGetAllResponseCopyWith<LocatorGetAllResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorGetAllResponseCopyWith<$Res> {
  factory $LocatorGetAllResponseCopyWith(LocatorGetAllResponse value,
          $Res Function(LocatorGetAllResponse) then) =
      _$LocatorGetAllResponseCopyWithImpl<$Res, LocatorGetAllResponse>;
  @useResult
  $Res call({int total, List<Locator> data});
}

/// @nodoc
class _$LocatorGetAllResponseCopyWithImpl<$Res,
        $Val extends LocatorGetAllResponse>
    implements $LocatorGetAllResponseCopyWith<$Res> {
  _$LocatorGetAllResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<Locator>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocatorGetAllResponseImplCopyWith<$Res>
    implements $LocatorGetAllResponseCopyWith<$Res> {
  factory _$$LocatorGetAllResponseImplCopyWith(
          _$LocatorGetAllResponseImpl value,
          $Res Function(_$LocatorGetAllResponseImpl) then) =
      __$$LocatorGetAllResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, List<Locator> data});
}

/// @nodoc
class __$$LocatorGetAllResponseImplCopyWithImpl<$Res>
    extends _$LocatorGetAllResponseCopyWithImpl<$Res,
        _$LocatorGetAllResponseImpl>
    implements _$$LocatorGetAllResponseImplCopyWith<$Res> {
  __$$LocatorGetAllResponseImplCopyWithImpl(_$LocatorGetAllResponseImpl _value,
      $Res Function(_$LocatorGetAllResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_$LocatorGetAllResponseImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<Locator>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocatorGetAllResponseImpl extends _LocatorGetAllResponse {
  const _$LocatorGetAllResponseImpl(
      {required this.total, required final List<Locator> data})
      : _data = data,
        super._();

  factory _$LocatorGetAllResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocatorGetAllResponseImplFromJson(json);

  @override
  final int total;
  final List<Locator> _data;
  @override
  List<Locator> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'LocatorGetAllResponse(total: $total, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorGetAllResponseImpl &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, total, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorGetAllResponseImplCopyWith<_$LocatorGetAllResponseImpl>
      get copyWith => __$$LocatorGetAllResponseImplCopyWithImpl<
          _$LocatorGetAllResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocatorGetAllResponseImplToJson(
      this,
    );
  }
}

abstract class _LocatorGetAllResponse extends LocatorGetAllResponse {
  const factory _LocatorGetAllResponse(
      {required final int total,
      required final List<Locator> data}) = _$LocatorGetAllResponseImpl;
  const _LocatorGetAllResponse._() : super._();

  factory _LocatorGetAllResponse.fromJson(Map<String, dynamic> json) =
      _$LocatorGetAllResponseImpl.fromJson;

  @override
  int get total;
  @override
  List<Locator> get data;
  @override
  @JsonKey(ignore: true)
  _$$LocatorGetAllResponseImplCopyWith<_$LocatorGetAllResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

Locator _$LocatorFromJson(Map<String, dynamic> json) {
  return _Locator.fromJson(json);
}

/// @nodoc
mixin _$Locator {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get tag => throw _privateConstructorUsedError;
  List<String> get emails => throw _privateConstructorUsedError;
  int get vehicles => throw _privateConstructorUsedError;
  int get geofences => throw _privateConstructorUsedError;
  int get stations => throw _privateConstructorUsedError;
  String get fromTime => throw _privateConstructorUsedError;
  int get fields => throw _privateConstructorUsedError;
  int get options => throw _privateConstructorUsedError;
  String get toTime => throw _privateConstructorUsedError;
  String? get lastModified => throw _privateConstructorUsedError;
  String? get user1 => throw _privateConstructorUsedError;
  String? get user2 => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocatorCopyWith<Locator> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorCopyWith<$Res> {
  factory $LocatorCopyWith(Locator value, $Res Function(Locator) then) =
      _$LocatorCopyWithImpl<$Res, Locator>;
  @useResult
  $Res call(
      {String id,
      String name,
      String? tag,
      List<String> emails,
      int vehicles,
      int geofences,
      int stations,
      String fromTime,
      int fields,
      int options,
      String toTime,
      String? lastModified,
      String? user1,
      String? user2});
}

/// @nodoc
class _$LocatorCopyWithImpl<$Res, $Val extends Locator>
    implements $LocatorCopyWith<$Res> {
  _$LocatorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? tag = freezed,
    Object? emails = null,
    Object? vehicles = null,
    Object? geofences = null,
    Object? stations = null,
    Object? fromTime = null,
    Object? fields = null,
    Object? options = null,
    Object? toTime = null,
    Object? lastModified = freezed,
    Object? user1 = freezed,
    Object? user2 = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      emails: null == emails
          ? _value.emails
          : emails // ignore: cast_nullable_to_non_nullable
              as List<String>,
      vehicles: null == vehicles
          ? _value.vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as int,
      geofences: null == geofences
          ? _value.geofences
          : geofences // ignore: cast_nullable_to_non_nullable
              as int,
      stations: null == stations
          ? _value.stations
          : stations // ignore: cast_nullable_to_non_nullable
              as int,
      fromTime: null == fromTime
          ? _value.fromTime
          : fromTime // ignore: cast_nullable_to_non_nullable
              as String,
      fields: null == fields
          ? _value.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as int,
      options: null == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as int,
      toTime: null == toTime
          ? _value.toTime
          : toTime // ignore: cast_nullable_to_non_nullable
              as String,
      lastModified: freezed == lastModified
          ? _value.lastModified
          : lastModified // ignore: cast_nullable_to_non_nullable
              as String?,
      user1: freezed == user1
          ? _value.user1
          : user1 // ignore: cast_nullable_to_non_nullable
              as String?,
      user2: freezed == user2
          ? _value.user2
          : user2 // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocatorImplCopyWith<$Res> implements $LocatorCopyWith<$Res> {
  factory _$$LocatorImplCopyWith(
          _$LocatorImpl value, $Res Function(_$LocatorImpl) then) =
      __$$LocatorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String? tag,
      List<String> emails,
      int vehicles,
      int geofences,
      int stations,
      String fromTime,
      int fields,
      int options,
      String toTime,
      String? lastModified,
      String? user1,
      String? user2});
}

/// @nodoc
class __$$LocatorImplCopyWithImpl<$Res>
    extends _$LocatorCopyWithImpl<$Res, _$LocatorImpl>
    implements _$$LocatorImplCopyWith<$Res> {
  __$$LocatorImplCopyWithImpl(
      _$LocatorImpl _value, $Res Function(_$LocatorImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? tag = freezed,
    Object? emails = null,
    Object? vehicles = null,
    Object? geofences = null,
    Object? stations = null,
    Object? fromTime = null,
    Object? fields = null,
    Object? options = null,
    Object? toTime = null,
    Object? lastModified = freezed,
    Object? user1 = freezed,
    Object? user2 = freezed,
  }) {
    return _then(_$LocatorImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      emails: null == emails
          ? _value._emails
          : emails // ignore: cast_nullable_to_non_nullable
              as List<String>,
      vehicles: null == vehicles
          ? _value.vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as int,
      geofences: null == geofences
          ? _value.geofences
          : geofences // ignore: cast_nullable_to_non_nullable
              as int,
      stations: null == stations
          ? _value.stations
          : stations // ignore: cast_nullable_to_non_nullable
              as int,
      fromTime: null == fromTime
          ? _value.fromTime
          : fromTime // ignore: cast_nullable_to_non_nullable
              as String,
      fields: null == fields
          ? _value.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as int,
      options: null == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as int,
      toTime: null == toTime
          ? _value.toTime
          : toTime // ignore: cast_nullable_to_non_nullable
              as String,
      lastModified: freezed == lastModified
          ? _value.lastModified
          : lastModified // ignore: cast_nullable_to_non_nullable
              as String?,
      user1: freezed == user1
          ? _value.user1
          : user1 // ignore: cast_nullable_to_non_nullable
              as String?,
      user2: freezed == user2
          ? _value.user2
          : user2 // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocatorImpl extends _Locator {
  const _$LocatorImpl(
      {required this.id,
      required this.name,
      required this.tag,
      required final List<String> emails,
      required this.vehicles,
      required this.geofences,
      required this.stations,
      required this.fromTime,
      required this.fields,
      required this.options,
      required this.toTime,
      required this.lastModified,
      required this.user1,
      required this.user2})
      : _emails = emails,
        super._();

  factory _$LocatorImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocatorImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? tag;
  final List<String> _emails;
  @override
  List<String> get emails {
    if (_emails is EqualUnmodifiableListView) return _emails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_emails);
  }

  @override
  final int vehicles;
  @override
  final int geofences;
  @override
  final int stations;
  @override
  final String fromTime;
  @override
  final int fields;
  @override
  final int options;
  @override
  final String toTime;
  @override
  final String? lastModified;
  @override
  final String? user1;
  @override
  final String? user2;

  @override
  String toString() {
    return 'Locator(id: $id, name: $name, tag: $tag, emails: $emails, vehicles: $vehicles, geofences: $geofences, stations: $stations, fromTime: $fromTime, fields: $fields, options: $options, toTime: $toTime, lastModified: $lastModified, user1: $user1, user2: $user2)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            const DeepCollectionEquality().equals(other._emails, _emails) &&
            (identical(other.vehicles, vehicles) ||
                other.vehicles == vehicles) &&
            (identical(other.geofences, geofences) ||
                other.geofences == geofences) &&
            (identical(other.stations, stations) ||
                other.stations == stations) &&
            (identical(other.fromTime, fromTime) ||
                other.fromTime == fromTime) &&
            (identical(other.fields, fields) || other.fields == fields) &&
            (identical(other.options, options) || other.options == options) &&
            (identical(other.toTime, toTime) || other.toTime == toTime) &&
            (identical(other.lastModified, lastModified) ||
                other.lastModified == lastModified) &&
            (identical(other.user1, user1) || other.user1 == user1) &&
            (identical(other.user2, user2) || other.user2 == user2));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      tag,
      const DeepCollectionEquality().hash(_emails),
      vehicles,
      geofences,
      stations,
      fromTime,
      fields,
      options,
      toTime,
      lastModified,
      user1,
      user2);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorImplCopyWith<_$LocatorImpl> get copyWith =>
      __$$LocatorImplCopyWithImpl<_$LocatorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocatorImplToJson(
      this,
    );
  }
}

abstract class _Locator extends Locator {
  const factory _Locator(
      {required final String id,
      required final String name,
      required final String? tag,
      required final List<String> emails,
      required final int vehicles,
      required final int geofences,
      required final int stations,
      required final String fromTime,
      required final int fields,
      required final int options,
      required final String toTime,
      required final String? lastModified,
      required final String? user1,
      required final String? user2}) = _$LocatorImpl;
  const _Locator._() : super._();

  factory _Locator.fromJson(Map<String, dynamic> json) = _$LocatorImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get tag;
  @override
  List<String> get emails;
  @override
  int get vehicles;
  @override
  int get geofences;
  @override
  int get stations;
  @override
  String get fromTime;
  @override
  int get fields;
  @override
  int get options;
  @override
  String get toTime;
  @override
  String? get lastModified;
  @override
  String? get user1;
  @override
  String? get user2;
  @override
  @JsonKey(ignore: true)
  _$$LocatorImplCopyWith<_$LocatorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
