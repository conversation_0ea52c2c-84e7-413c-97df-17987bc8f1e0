// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'locator_params_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LocatorParamsResponse _$LocatorParamsResponseFromJson(
    Map<String, dynamic> json) {
  return _LocatorParamsResponse.fromJson(json);
}

/// @nodoc
mixin _$LocatorParamsResponse {
  List<LocatorGeofence> get geofences => throw _privateConstructorUsedError;
  List<LocatorStation> get stations => throw _privateConstructorUsedError;
  List<LocatorStatus> get statuses => throw _privateConstructorUsedError;
  List<LocatorOption> get options => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocatorParamsResponseCopyWith<LocatorParamsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorParamsResponseCopyWith<$Res> {
  factory $LocatorParamsResponseCopyWith(LocatorParamsResponse value,
          $Res Function(LocatorParamsResponse) then) =
      _$LocatorParamsResponseCopyWithImpl<$Res, LocatorParamsResponse>;
  @useResult
  $Res call(
      {List<LocatorGeofence> geofences,
      List<LocatorStation> stations,
      List<LocatorStatus> statuses,
      List<LocatorOption> options});
}

/// @nodoc
class _$LocatorParamsResponseCopyWithImpl<$Res,
        $Val extends LocatorParamsResponse>
    implements $LocatorParamsResponseCopyWith<$Res> {
  _$LocatorParamsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? geofences = null,
    Object? stations = null,
    Object? statuses = null,
    Object? options = null,
  }) {
    return _then(_value.copyWith(
      geofences: null == geofences
          ? _value.geofences
          : geofences // ignore: cast_nullable_to_non_nullable
              as List<LocatorGeofence>,
      stations: null == stations
          ? _value.stations
          : stations // ignore: cast_nullable_to_non_nullable
              as List<LocatorStation>,
      statuses: null == statuses
          ? _value.statuses
          : statuses // ignore: cast_nullable_to_non_nullable
              as List<LocatorStatus>,
      options: null == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<LocatorOption>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocatorParamsResponseImplCopyWith<$Res>
    implements $LocatorParamsResponseCopyWith<$Res> {
  factory _$$LocatorParamsResponseImplCopyWith(
          _$LocatorParamsResponseImpl value,
          $Res Function(_$LocatorParamsResponseImpl) then) =
      __$$LocatorParamsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<LocatorGeofence> geofences,
      List<LocatorStation> stations,
      List<LocatorStatus> statuses,
      List<LocatorOption> options});
}

/// @nodoc
class __$$LocatorParamsResponseImplCopyWithImpl<$Res>
    extends _$LocatorParamsResponseCopyWithImpl<$Res,
        _$LocatorParamsResponseImpl>
    implements _$$LocatorParamsResponseImplCopyWith<$Res> {
  __$$LocatorParamsResponseImplCopyWithImpl(_$LocatorParamsResponseImpl _value,
      $Res Function(_$LocatorParamsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? geofences = null,
    Object? stations = null,
    Object? statuses = null,
    Object? options = null,
  }) {
    return _then(_$LocatorParamsResponseImpl(
      geofences: null == geofences
          ? _value._geofences
          : geofences // ignore: cast_nullable_to_non_nullable
              as List<LocatorGeofence>,
      stations: null == stations
          ? _value._stations
          : stations // ignore: cast_nullable_to_non_nullable
              as List<LocatorStation>,
      statuses: null == statuses
          ? _value._statuses
          : statuses // ignore: cast_nullable_to_non_nullable
              as List<LocatorStatus>,
      options: null == options
          ? _value._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<LocatorOption>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocatorParamsResponseImpl extends _LocatorParamsResponse {
  const _$LocatorParamsResponseImpl(
      {required final List<LocatorGeofence> geofences,
      required final List<LocatorStation> stations,
      required final List<LocatorStatus> statuses,
      required final List<LocatorOption> options})
      : _geofences = geofences,
        _stations = stations,
        _statuses = statuses,
        _options = options,
        super._();

  factory _$LocatorParamsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocatorParamsResponseImplFromJson(json);

  final List<LocatorGeofence> _geofences;
  @override
  List<LocatorGeofence> get geofences {
    if (_geofences is EqualUnmodifiableListView) return _geofences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_geofences);
  }

  final List<LocatorStation> _stations;
  @override
  List<LocatorStation> get stations {
    if (_stations is EqualUnmodifiableListView) return _stations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_stations);
  }

  final List<LocatorStatus> _statuses;
  @override
  List<LocatorStatus> get statuses {
    if (_statuses is EqualUnmodifiableListView) return _statuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_statuses);
  }

  final List<LocatorOption> _options;
  @override
  List<LocatorOption> get options {
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_options);
  }

  @override
  String toString() {
    return 'LocatorParamsResponse(geofences: $geofences, stations: $stations, statuses: $statuses, options: $options)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorParamsResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._geofences, _geofences) &&
            const DeepCollectionEquality().equals(other._stations, _stations) &&
            const DeepCollectionEquality().equals(other._statuses, _statuses) &&
            const DeepCollectionEquality().equals(other._options, _options));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_geofences),
      const DeepCollectionEquality().hash(_stations),
      const DeepCollectionEquality().hash(_statuses),
      const DeepCollectionEquality().hash(_options));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorParamsResponseImplCopyWith<_$LocatorParamsResponseImpl>
      get copyWith => __$$LocatorParamsResponseImplCopyWithImpl<
          _$LocatorParamsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocatorParamsResponseImplToJson(
      this,
    );
  }
}

abstract class _LocatorParamsResponse extends LocatorParamsResponse {
  const factory _LocatorParamsResponse(
          {required final List<LocatorGeofence> geofences,
          required final List<LocatorStation> stations,
          required final List<LocatorStatus> statuses,
          required final List<LocatorOption> options}) =
      _$LocatorParamsResponseImpl;
  const _LocatorParamsResponse._() : super._();

  factory _LocatorParamsResponse.fromJson(Map<String, dynamic> json) =
      _$LocatorParamsResponseImpl.fromJson;

  @override
  List<LocatorGeofence> get geofences;
  @override
  List<LocatorStation> get stations;
  @override
  List<LocatorStatus> get statuses;
  @override
  List<LocatorOption> get options;
  @override
  @JsonKey(ignore: true)
  _$$LocatorParamsResponseImplCopyWith<_$LocatorParamsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

LocatorGeofence _$LocatorGeofenceFromJson(Map<String, dynamic> json) {
  return _LocatorGeofence.fromJson(json);
}

/// @nodoc
mixin _$LocatorGeofence {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  String get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocatorGeofenceCopyWith<LocatorGeofence> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorGeofenceCopyWith<$Res> {
  factory $LocatorGeofenceCopyWith(
          LocatorGeofence value, $Res Function(LocatorGeofence) then) =
      _$LocatorGeofenceCopyWithImpl<$Res, LocatorGeofence>;
  @useResult
  $Res call({String id, String name, int type, String data});
}

/// @nodoc
class _$LocatorGeofenceCopyWithImpl<$Res, $Val extends LocatorGeofence>
    implements $LocatorGeofenceCopyWith<$Res> {
  _$LocatorGeofenceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocatorGeofenceImplCopyWith<$Res>
    implements $LocatorGeofenceCopyWith<$Res> {
  factory _$$LocatorGeofenceImplCopyWith(_$LocatorGeofenceImpl value,
          $Res Function(_$LocatorGeofenceImpl) then) =
      __$$LocatorGeofenceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String name, int type, String data});
}

/// @nodoc
class __$$LocatorGeofenceImplCopyWithImpl<$Res>
    extends _$LocatorGeofenceCopyWithImpl<$Res, _$LocatorGeofenceImpl>
    implements _$$LocatorGeofenceImplCopyWith<$Res> {
  __$$LocatorGeofenceImplCopyWithImpl(
      _$LocatorGeofenceImpl _value, $Res Function(_$LocatorGeofenceImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? data = null,
  }) {
    return _then(_$LocatorGeofenceImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocatorGeofenceImpl extends _LocatorGeofence {
  const _$LocatorGeofenceImpl(
      {required this.id,
      required this.name,
      required this.type,
      required this.data})
      : super._();

  factory _$LocatorGeofenceImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocatorGeofenceImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final int type;
  @override
  final String data;

  @override
  String toString() {
    return 'LocatorGeofence(id: $id, name: $name, type: $type, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorGeofenceImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, type, data);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorGeofenceImplCopyWith<_$LocatorGeofenceImpl> get copyWith =>
      __$$LocatorGeofenceImplCopyWithImpl<_$LocatorGeofenceImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocatorGeofenceImplToJson(
      this,
    );
  }
}

abstract class _LocatorGeofence extends LocatorGeofence {
  const factory _LocatorGeofence(
      {required final String id,
      required final String name,
      required final int type,
      required final String data}) = _$LocatorGeofenceImpl;
  const _LocatorGeofence._() : super._();

  factory _LocatorGeofence.fromJson(Map<String, dynamic> json) =
      _$LocatorGeofenceImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  int get type;
  @override
  String get data;
  @override
  @JsonKey(ignore: true)
  _$$LocatorGeofenceImplCopyWith<_$LocatorGeofenceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocatorStation _$LocatorStationFromJson(Map<String, dynamic> json) {
  return _LocatorStation.fromJson(json);
}

/// @nodoc
mixin _$LocatorStation {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get categoryId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocatorStationCopyWith<LocatorStation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorStationCopyWith<$Res> {
  factory $LocatorStationCopyWith(
          LocatorStation value, $Res Function(LocatorStation) then) =
      _$LocatorStationCopyWithImpl<$Res, LocatorStation>;
  @useResult
  $Res call({String id, String name, int categoryId});
}

/// @nodoc
class _$LocatorStationCopyWithImpl<$Res, $Val extends LocatorStation>
    implements $LocatorStationCopyWith<$Res> {
  _$LocatorStationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? categoryId = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocatorStationImplCopyWith<$Res>
    implements $LocatorStationCopyWith<$Res> {
  factory _$$LocatorStationImplCopyWith(_$LocatorStationImpl value,
          $Res Function(_$LocatorStationImpl) then) =
      __$$LocatorStationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String name, int categoryId});
}

/// @nodoc
class __$$LocatorStationImplCopyWithImpl<$Res>
    extends _$LocatorStationCopyWithImpl<$Res, _$LocatorStationImpl>
    implements _$$LocatorStationImplCopyWith<$Res> {
  __$$LocatorStationImplCopyWithImpl(
      _$LocatorStationImpl _value, $Res Function(_$LocatorStationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? categoryId = null,
  }) {
    return _then(_$LocatorStationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocatorStationImpl extends _LocatorStation {
  const _$LocatorStationImpl(
      {required this.id, required this.name, required this.categoryId})
      : super._();

  factory _$LocatorStationImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocatorStationImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final int categoryId;

  @override
  String toString() {
    return 'LocatorStation(id: $id, name: $name, categoryId: $categoryId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorStationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, categoryId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorStationImplCopyWith<_$LocatorStationImpl> get copyWith =>
      __$$LocatorStationImplCopyWithImpl<_$LocatorStationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocatorStationImplToJson(
      this,
    );
  }
}

abstract class _LocatorStation extends LocatorStation {
  const factory _LocatorStation(
      {required final String id,
      required final String name,
      required final int categoryId}) = _$LocatorStationImpl;
  const _LocatorStation._() : super._();

  factory _LocatorStation.fromJson(Map<String, dynamic> json) =
      _$LocatorStationImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  int get categoryId;
  @override
  @JsonKey(ignore: true)
  _$$LocatorStationImplCopyWith<_$LocatorStationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocatorStatus _$LocatorStatusFromJson(Map<String, dynamic> json) {
  return _LocatorStatus.fromJson(json);
}

/// @nodoc
mixin _$LocatorStatus {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocatorStatusCopyWith<LocatorStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorStatusCopyWith<$Res> {
  factory $LocatorStatusCopyWith(
          LocatorStatus value, $Res Function(LocatorStatus) then) =
      _$LocatorStatusCopyWithImpl<$Res, LocatorStatus>;
  @useResult
  $Res call({String id, String name});
}

/// @nodoc
class _$LocatorStatusCopyWithImpl<$Res, $Val extends LocatorStatus>
    implements $LocatorStatusCopyWith<$Res> {
  _$LocatorStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocatorStatusImplCopyWith<$Res>
    implements $LocatorStatusCopyWith<$Res> {
  factory _$$LocatorStatusImplCopyWith(
          _$LocatorStatusImpl value, $Res Function(_$LocatorStatusImpl) then) =
      __$$LocatorStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String name});
}

/// @nodoc
class __$$LocatorStatusImplCopyWithImpl<$Res>
    extends _$LocatorStatusCopyWithImpl<$Res, _$LocatorStatusImpl>
    implements _$$LocatorStatusImplCopyWith<$Res> {
  __$$LocatorStatusImplCopyWithImpl(
      _$LocatorStatusImpl _value, $Res Function(_$LocatorStatusImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
  }) {
    return _then(_$LocatorStatusImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocatorStatusImpl extends _LocatorStatus {
  const _$LocatorStatusImpl({required this.id, required this.name}) : super._();

  factory _$LocatorStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocatorStatusImplFromJson(json);

  @override
  final String id;
  @override
  final String name;

  @override
  String toString() {
    return 'LocatorStatus(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorStatusImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorStatusImplCopyWith<_$LocatorStatusImpl> get copyWith =>
      __$$LocatorStatusImplCopyWithImpl<_$LocatorStatusImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocatorStatusImplToJson(
      this,
    );
  }
}

abstract class _LocatorStatus extends LocatorStatus {
  const factory _LocatorStatus(
      {required final String id,
      required final String name}) = _$LocatorStatusImpl;
  const _LocatorStatus._() : super._();

  factory _LocatorStatus.fromJson(Map<String, dynamic> json) =
      _$LocatorStatusImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  @JsonKey(ignore: true)
  _$$LocatorStatusImplCopyWith<_$LocatorStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocatorOption _$LocatorOptionFromJson(Map<String, dynamic> json) {
  return _LocatorOption.fromJson(json);
}

/// @nodoc
mixin _$LocatorOption {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocatorOptionCopyWith<LocatorOption> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorOptionCopyWith<$Res> {
  factory $LocatorOptionCopyWith(
          LocatorOption value, $Res Function(LocatorOption) then) =
      _$LocatorOptionCopyWithImpl<$Res, LocatorOption>;
  @useResult
  $Res call({String id, String name, String? description});
}

/// @nodoc
class _$LocatorOptionCopyWithImpl<$Res, $Val extends LocatorOption>
    implements $LocatorOptionCopyWith<$Res> {
  _$LocatorOptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocatorOptionImplCopyWith<$Res>
    implements $LocatorOptionCopyWith<$Res> {
  factory _$$LocatorOptionImplCopyWith(
          _$LocatorOptionImpl value, $Res Function(_$LocatorOptionImpl) then) =
      __$$LocatorOptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String name, String? description});
}

/// @nodoc
class __$$LocatorOptionImplCopyWithImpl<$Res>
    extends _$LocatorOptionCopyWithImpl<$Res, _$LocatorOptionImpl>
    implements _$$LocatorOptionImplCopyWith<$Res> {
  __$$LocatorOptionImplCopyWithImpl(
      _$LocatorOptionImpl _value, $Res Function(_$LocatorOptionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
  }) {
    return _then(_$LocatorOptionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocatorOptionImpl extends _LocatorOption {
  const _$LocatorOptionImpl(
      {required this.id, required this.name, required this.description})
      : super._();

  factory _$LocatorOptionImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocatorOptionImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;

  @override
  String toString() {
    return 'LocatorOption(id: $id, name: $name, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorOptionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, description);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorOptionImplCopyWith<_$LocatorOptionImpl> get copyWith =>
      __$$LocatorOptionImplCopyWithImpl<_$LocatorOptionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocatorOptionImplToJson(
      this,
    );
  }
}

abstract class _LocatorOption extends LocatorOption {
  const factory _LocatorOption(
      {required final String id,
      required final String name,
      required final String? description}) = _$LocatorOptionImpl;
  const _LocatorOption._() : super._();

  factory _LocatorOption.fromJson(Map<String, dynamic> json) =
      _$LocatorOptionImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  @JsonKey(ignore: true)
  _$$LocatorOptionImplCopyWith<_$LocatorOptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
