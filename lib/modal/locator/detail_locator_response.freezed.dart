// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'detail_locator_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DetailLocatorResponse _$DetailLocatorResponseFromJson(
    Map<String, dynamic> json) {
  return _DetailLocatorResponse.fromJson(json);
}

/// @nodoc
mixin _$DetailLocatorResponse {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get date => throw _privateConstructorUsedError;
  int get toTime => throw _privateConstructorUsedError;
  int get toTimeType => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  List<String> get vehicles => throw _privateConstructorUsedError;
  List<String> get geofences => throw _privateConstructorUsedError;
  List<String> get stations => throw _privateConstructorUsedError;
  List<String> get fields => throw _privateConstructorUsedError;
  List<String> get options => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DetailLocatorResponseCopyWith<DetailLocatorResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DetailLocatorResponseCopyWith<$Res> {
  factory $DetailLocatorResponseCopyWith(DetailLocatorResponse value,
          $Res Function(DetailLocatorResponse) then) =
      _$DetailLocatorResponseCopyWithImpl<$Res, DetailLocatorResponse>;
  @useResult
  $Res call(
      {String id,
      String name,
      int date,
      int toTime,
      int toTimeType,
      String email,
      List<String> vehicles,
      List<String> geofences,
      List<String> stations,
      List<String> fields,
      List<String> options});
}

/// @nodoc
class _$DetailLocatorResponseCopyWithImpl<$Res,
        $Val extends DetailLocatorResponse>
    implements $DetailLocatorResponseCopyWith<$Res> {
  _$DetailLocatorResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? date = null,
    Object? toTime = null,
    Object? toTimeType = null,
    Object? email = null,
    Object? vehicles = null,
    Object? geofences = null,
    Object? stations = null,
    Object? fields = null,
    Object? options = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as int,
      toTime: null == toTime
          ? _value.toTime
          : toTime // ignore: cast_nullable_to_non_nullable
              as int,
      toTimeType: null == toTimeType
          ? _value.toTimeType
          : toTimeType // ignore: cast_nullable_to_non_nullable
              as int,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      vehicles: null == vehicles
          ? _value.vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as List<String>,
      geofences: null == geofences
          ? _value.geofences
          : geofences // ignore: cast_nullable_to_non_nullable
              as List<String>,
      stations: null == stations
          ? _value.stations
          : stations // ignore: cast_nullable_to_non_nullable
              as List<String>,
      fields: null == fields
          ? _value.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as List<String>,
      options: null == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DetailLocatorResponseImplCopyWith<$Res>
    implements $DetailLocatorResponseCopyWith<$Res> {
  factory _$$DetailLocatorResponseImplCopyWith(
          _$DetailLocatorResponseImpl value,
          $Res Function(_$DetailLocatorResponseImpl) then) =
      __$$DetailLocatorResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      int date,
      int toTime,
      int toTimeType,
      String email,
      List<String> vehicles,
      List<String> geofences,
      List<String> stations,
      List<String> fields,
      List<String> options});
}

/// @nodoc
class __$$DetailLocatorResponseImplCopyWithImpl<$Res>
    extends _$DetailLocatorResponseCopyWithImpl<$Res,
        _$DetailLocatorResponseImpl>
    implements _$$DetailLocatorResponseImplCopyWith<$Res> {
  __$$DetailLocatorResponseImplCopyWithImpl(_$DetailLocatorResponseImpl _value,
      $Res Function(_$DetailLocatorResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? date = null,
    Object? toTime = null,
    Object? toTimeType = null,
    Object? email = null,
    Object? vehicles = null,
    Object? geofences = null,
    Object? stations = null,
    Object? fields = null,
    Object? options = null,
  }) {
    return _then(_$DetailLocatorResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as int,
      toTime: null == toTime
          ? _value.toTime
          : toTime // ignore: cast_nullable_to_non_nullable
              as int,
      toTimeType: null == toTimeType
          ? _value.toTimeType
          : toTimeType // ignore: cast_nullable_to_non_nullable
              as int,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      vehicles: null == vehicles
          ? _value._vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as List<String>,
      geofences: null == geofences
          ? _value._geofences
          : geofences // ignore: cast_nullable_to_non_nullable
              as List<String>,
      stations: null == stations
          ? _value._stations
          : stations // ignore: cast_nullable_to_non_nullable
              as List<String>,
      fields: null == fields
          ? _value._fields
          : fields // ignore: cast_nullable_to_non_nullable
              as List<String>,
      options: null == options
          ? _value._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DetailLocatorResponseImpl extends _DetailLocatorResponse {
  const _$DetailLocatorResponseImpl(
      {required this.id,
      required this.name,
      required this.date,
      required this.toTime,
      required this.toTimeType,
      required this.email,
      required final List<String> vehicles,
      required final List<String> geofences,
      required final List<String> stations,
      required final List<String> fields,
      required final List<String> options})
      : _vehicles = vehicles,
        _geofences = geofences,
        _stations = stations,
        _fields = fields,
        _options = options,
        super._();

  factory _$DetailLocatorResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$DetailLocatorResponseImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final int date;
  @override
  final int toTime;
  @override
  final int toTimeType;
  @override
  final String email;
  final List<String> _vehicles;
  @override
  List<String> get vehicles {
    if (_vehicles is EqualUnmodifiableListView) return _vehicles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vehicles);
  }

  final List<String> _geofences;
  @override
  List<String> get geofences {
    if (_geofences is EqualUnmodifiableListView) return _geofences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_geofences);
  }

  final List<String> _stations;
  @override
  List<String> get stations {
    if (_stations is EqualUnmodifiableListView) return _stations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_stations);
  }

  final List<String> _fields;
  @override
  List<String> get fields {
    if (_fields is EqualUnmodifiableListView) return _fields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_fields);
  }

  final List<String> _options;
  @override
  List<String> get options {
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_options);
  }

  @override
  String toString() {
    return 'DetailLocatorResponse(id: $id, name: $name, date: $date, toTime: $toTime, toTimeType: $toTimeType, email: $email, vehicles: $vehicles, geofences: $geofences, stations: $stations, fields: $fields, options: $options)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DetailLocatorResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.toTime, toTime) || other.toTime == toTime) &&
            (identical(other.toTimeType, toTimeType) ||
                other.toTimeType == toTimeType) &&
            (identical(other.email, email) || other.email == email) &&
            const DeepCollectionEquality().equals(other._vehicles, _vehicles) &&
            const DeepCollectionEquality()
                .equals(other._geofences, _geofences) &&
            const DeepCollectionEquality().equals(other._stations, _stations) &&
            const DeepCollectionEquality().equals(other._fields, _fields) &&
            const DeepCollectionEquality().equals(other._options, _options));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      date,
      toTime,
      toTimeType,
      email,
      const DeepCollectionEquality().hash(_vehicles),
      const DeepCollectionEquality().hash(_geofences),
      const DeepCollectionEquality().hash(_stations),
      const DeepCollectionEquality().hash(_fields),
      const DeepCollectionEquality().hash(_options));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DetailLocatorResponseImplCopyWith<_$DetailLocatorResponseImpl>
      get copyWith => __$$DetailLocatorResponseImplCopyWithImpl<
          _$DetailLocatorResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DetailLocatorResponseImplToJson(
      this,
    );
  }
}

abstract class _DetailLocatorResponse extends DetailLocatorResponse {
  const factory _DetailLocatorResponse(
      {required final String id,
      required final String name,
      required final int date,
      required final int toTime,
      required final int toTimeType,
      required final String email,
      required final List<String> vehicles,
      required final List<String> geofences,
      required final List<String> stations,
      required final List<String> fields,
      required final List<String> options}) = _$DetailLocatorResponseImpl;
  const _DetailLocatorResponse._() : super._();

  factory _DetailLocatorResponse.fromJson(Map<String, dynamic> json) =
      _$DetailLocatorResponseImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  int get date;
  @override
  int get toTime;
  @override
  int get toTimeType;
  @override
  String get email;
  @override
  List<String> get vehicles;
  @override
  List<String> get geofences;
  @override
  List<String> get stations;
  @override
  List<String> get fields;
  @override
  List<String> get options;
  @override
  @JsonKey(ignore: true)
  _$$DetailLocatorResponseImplCopyWith<_$DetailLocatorResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
