// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'locator_get_by_id_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LocatorGetByIdRequest _$LocatorGetByIdRequestFromJson(
    Map<String, dynamic> json) {
  return _LocatorGetByIdRequest.fromJson(json);
}

/// @nodoc
mixin _$LocatorGetByIdRequest {
  String get id => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocatorGetByIdRequestCopyWith<LocatorGetByIdRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorGetByIdRequestCopyWith<$Res> {
  factory $LocatorGetByIdRequestCopyWith(LocatorGetByIdRequest value,
          $Res Function(LocatorGetByIdRequest) then) =
      _$LocatorGetByIdRequestCopyWithImpl<$Res, LocatorGetByIdRequest>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class _$LocatorGetByIdRequestCopyWithImpl<$Res,
        $Val extends LocatorGetByIdRequest>
    implements $LocatorGetByIdRequestCopyWith<$Res> {
  _$LocatorGetByIdRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocatorGetByIdRequestImplCopyWith<$Res>
    implements $LocatorGetByIdRequestCopyWith<$Res> {
  factory _$$LocatorGetByIdRequestImplCopyWith(
          _$LocatorGetByIdRequestImpl value,
          $Res Function(_$LocatorGetByIdRequestImpl) then) =
      __$$LocatorGetByIdRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$LocatorGetByIdRequestImplCopyWithImpl<$Res>
    extends _$LocatorGetByIdRequestCopyWithImpl<$Res,
        _$LocatorGetByIdRequestImpl>
    implements _$$LocatorGetByIdRequestImplCopyWith<$Res> {
  __$$LocatorGetByIdRequestImplCopyWithImpl(_$LocatorGetByIdRequestImpl _value,
      $Res Function(_$LocatorGetByIdRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$LocatorGetByIdRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocatorGetByIdRequestImpl extends _LocatorGetByIdRequest {
  const _$LocatorGetByIdRequestImpl({required this.id}) : super._();

  factory _$LocatorGetByIdRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocatorGetByIdRequestImplFromJson(json);

  @override
  final String id;

  @override
  String toString() {
    return 'LocatorGetByIdRequest(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorGetByIdRequestImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorGetByIdRequestImplCopyWith<_$LocatorGetByIdRequestImpl>
      get copyWith => __$$LocatorGetByIdRequestImplCopyWithImpl<
          _$LocatorGetByIdRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocatorGetByIdRequestImplToJson(
      this,
    );
  }
}

abstract class _LocatorGetByIdRequest extends LocatorGetByIdRequest {
  const factory _LocatorGetByIdRequest({required final String id}) =
      _$LocatorGetByIdRequestImpl;
  const _LocatorGetByIdRequest._() : super._();

  factory _LocatorGetByIdRequest.fromJson(Map<String, dynamic> json) =
      _$LocatorGetByIdRequestImpl.fromJson;

  @override
  String get id;
  @override
  @JsonKey(ignore: true)
  _$$LocatorGetByIdRequestImplCopyWith<_$LocatorGetByIdRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
