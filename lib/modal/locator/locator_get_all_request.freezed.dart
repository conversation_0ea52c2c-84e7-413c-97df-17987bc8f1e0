// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'locator_get_all_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LocatorGetAllRequest _$LocatorGetAllRequestFromJson(Map<String, dynamic> json) {
  return _LocatorGetAllRequest.fromJson(json);
}

/// @nodoc
mixin _$LocatorGetAllRequest {
  int get pageIndex => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  String? get orderBy => throw _privateConstructorUsedError;
  String? get searchTerm => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocatorGetAllRequestCopyWith<LocatorGetAllRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorGetAllRequestCopyWith<$Res> {
  factory $LocatorGetAllRequestCopyWith(LocatorGetAllRequest value,
          $Res Function(LocatorGetAllRequest) then) =
      _$LocatorGetAllRequestCopyWithImpl<$Res, LocatorGetAllRequest>;
  @useResult
  $Res call({int pageIndex, int pageSize, String? orderBy, String? searchTerm});
}

/// @nodoc
class _$LocatorGetAllRequestCopyWithImpl<$Res,
        $Val extends LocatorGetAllRequest>
    implements $LocatorGetAllRequestCopyWith<$Res> {
  _$LocatorGetAllRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? orderBy = freezed,
    Object? searchTerm = freezed,
  }) {
    return _then(_value.copyWith(
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      orderBy: freezed == orderBy
          ? _value.orderBy
          : orderBy // ignore: cast_nullable_to_non_nullable
              as String?,
      searchTerm: freezed == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocatorGetAllRequestImplCopyWith<$Res>
    implements $LocatorGetAllRequestCopyWith<$Res> {
  factory _$$LocatorGetAllRequestImplCopyWith(_$LocatorGetAllRequestImpl value,
          $Res Function(_$LocatorGetAllRequestImpl) then) =
      __$$LocatorGetAllRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int pageIndex, int pageSize, String? orderBy, String? searchTerm});
}

/// @nodoc
class __$$LocatorGetAllRequestImplCopyWithImpl<$Res>
    extends _$LocatorGetAllRequestCopyWithImpl<$Res, _$LocatorGetAllRequestImpl>
    implements _$$LocatorGetAllRequestImplCopyWith<$Res> {
  __$$LocatorGetAllRequestImplCopyWithImpl(_$LocatorGetAllRequestImpl _value,
      $Res Function(_$LocatorGetAllRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageIndex = null,
    Object? pageSize = null,
    Object? orderBy = freezed,
    Object? searchTerm = freezed,
  }) {
    return _then(_$LocatorGetAllRequestImpl(
      pageIndex: null == pageIndex
          ? _value.pageIndex
          : pageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      orderBy: freezed == orderBy
          ? _value.orderBy
          : orderBy // ignore: cast_nullable_to_non_nullable
              as String?,
      searchTerm: freezed == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocatorGetAllRequestImpl extends _LocatorGetAllRequest {
  const _$LocatorGetAllRequestImpl(
      {required this.pageIndex,
      required this.pageSize,
      required this.orderBy,
      required this.searchTerm})
      : super._();

  factory _$LocatorGetAllRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocatorGetAllRequestImplFromJson(json);

  @override
  final int pageIndex;
  @override
  final int pageSize;
  @override
  final String? orderBy;
  @override
  final String? searchTerm;

  @override
  String toString() {
    return 'LocatorGetAllRequest(pageIndex: $pageIndex, pageSize: $pageSize, orderBy: $orderBy, searchTerm: $searchTerm)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorGetAllRequestImpl &&
            (identical(other.pageIndex, pageIndex) ||
                other.pageIndex == pageIndex) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.orderBy, orderBy) || other.orderBy == orderBy) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, pageIndex, pageSize, orderBy, searchTerm);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorGetAllRequestImplCopyWith<_$LocatorGetAllRequestImpl>
      get copyWith =>
          __$$LocatorGetAllRequestImplCopyWithImpl<_$LocatorGetAllRequestImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocatorGetAllRequestImplToJson(
      this,
    );
  }
}

abstract class _LocatorGetAllRequest extends LocatorGetAllRequest {
  const factory _LocatorGetAllRequest(
      {required final int pageIndex,
      required final int pageSize,
      required final String? orderBy,
      required final String? searchTerm}) = _$LocatorGetAllRequestImpl;
  const _LocatorGetAllRequest._() : super._();

  factory _LocatorGetAllRequest.fromJson(Map<String, dynamic> json) =
      _$LocatorGetAllRequestImpl.fromJson;

  @override
  int get pageIndex;
  @override
  int get pageSize;
  @override
  String? get orderBy;
  @override
  String? get searchTerm;
  @override
  @JsonKey(ignore: true)
  _$$LocatorGetAllRequestImplCopyWith<_$LocatorGetAllRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
