// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'locator_create_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LocatorCreateRequest _$LocatorCreateRequestFromJson(Map<String, dynamic> json) {
  return _LocatorCreateRequest.fromJson(json);
}

/// @nodoc
mixin _$LocatorCreateRequest {
  String get name => throw _privateConstructorUsedError;
  String get from => throw _privateConstructorUsedError;
  int get duration => throw _privateConstructorUsedError;
  int get toTimeType => throw _privateConstructorUsedError;
  String get vehicles => throw _privateConstructorUsedError;
  String get geofences => throw _privateConstructorUsedError;
  String get fields => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get stations => throw _privateConstructorUsedError;
  String get options => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocatorCreateRequestCopyWith<LocatorCreateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocatorCreateRequestCopyWith<$Res> {
  factory $LocatorCreateRequestCopyWith(LocatorCreateRequest value,
          $Res Function(LocatorCreateRequest) then) =
      _$LocatorCreateRequestCopyWithImpl<$Res, LocatorCreateRequest>;
  @useResult
  $Res call(
      {String name,
      String from,
      int duration,
      int toTimeType,
      String vehicles,
      String geofences,
      String fields,
      String email,
      String stations,
      String options});
}

/// @nodoc
class _$LocatorCreateRequestCopyWithImpl<$Res,
        $Val extends LocatorCreateRequest>
    implements $LocatorCreateRequestCopyWith<$Res> {
  _$LocatorCreateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? from = null,
    Object? duration = null,
    Object? toTimeType = null,
    Object? vehicles = null,
    Object? geofences = null,
    Object? fields = null,
    Object? email = null,
    Object? stations = null,
    Object? options = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      toTimeType: null == toTimeType
          ? _value.toTimeType
          : toTimeType // ignore: cast_nullable_to_non_nullable
              as int,
      vehicles: null == vehicles
          ? _value.vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as String,
      geofences: null == geofences
          ? _value.geofences
          : geofences // ignore: cast_nullable_to_non_nullable
              as String,
      fields: null == fields
          ? _value.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      stations: null == stations
          ? _value.stations
          : stations // ignore: cast_nullable_to_non_nullable
              as String,
      options: null == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocatorCreateRequestImplCopyWith<$Res>
    implements $LocatorCreateRequestCopyWith<$Res> {
  factory _$$LocatorCreateRequestImplCopyWith(_$LocatorCreateRequestImpl value,
          $Res Function(_$LocatorCreateRequestImpl) then) =
      __$$LocatorCreateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String from,
      int duration,
      int toTimeType,
      String vehicles,
      String geofences,
      String fields,
      String email,
      String stations,
      String options});
}

/// @nodoc
class __$$LocatorCreateRequestImplCopyWithImpl<$Res>
    extends _$LocatorCreateRequestCopyWithImpl<$Res, _$LocatorCreateRequestImpl>
    implements _$$LocatorCreateRequestImplCopyWith<$Res> {
  __$$LocatorCreateRequestImplCopyWithImpl(_$LocatorCreateRequestImpl _value,
      $Res Function(_$LocatorCreateRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? from = null,
    Object? duration = null,
    Object? toTimeType = null,
    Object? vehicles = null,
    Object? geofences = null,
    Object? fields = null,
    Object? email = null,
    Object? stations = null,
    Object? options = null,
  }) {
    return _then(_$LocatorCreateRequestImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      toTimeType: null == toTimeType
          ? _value.toTimeType
          : toTimeType // ignore: cast_nullable_to_non_nullable
              as int,
      vehicles: null == vehicles
          ? _value.vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as String,
      geofences: null == geofences
          ? _value.geofences
          : geofences // ignore: cast_nullable_to_non_nullable
              as String,
      fields: null == fields
          ? _value.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      stations: null == stations
          ? _value.stations
          : stations // ignore: cast_nullable_to_non_nullable
              as String,
      options: null == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocatorCreateRequestImpl extends _LocatorCreateRequest {
  const _$LocatorCreateRequestImpl(
      {required this.name,
      required this.from,
      required this.duration,
      required this.toTimeType,
      required this.vehicles,
      required this.geofences,
      required this.fields,
      required this.email,
      required this.stations,
      required this.options})
      : super._();

  factory _$LocatorCreateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocatorCreateRequestImplFromJson(json);

  @override
  final String name;
  @override
  final String from;
  @override
  final int duration;
  @override
  final int toTimeType;
  @override
  final String vehicles;
  @override
  final String geofences;
  @override
  final String fields;
  @override
  final String email;
  @override
  final String stations;
  @override
  final String options;

  @override
  String toString() {
    return 'LocatorCreateRequest(name: $name, from: $from, duration: $duration, toTimeType: $toTimeType, vehicles: $vehicles, geofences: $geofences, fields: $fields, email: $email, stations: $stations, options: $options)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocatorCreateRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.from, from) || other.from == from) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.toTimeType, toTimeType) ||
                other.toTimeType == toTimeType) &&
            (identical(other.vehicles, vehicles) ||
                other.vehicles == vehicles) &&
            (identical(other.geofences, geofences) ||
                other.geofences == geofences) &&
            (identical(other.fields, fields) || other.fields == fields) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.stations, stations) ||
                other.stations == stations) &&
            (identical(other.options, options) || other.options == options));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name, from, duration, toTimeType,
      vehicles, geofences, fields, email, stations, options);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocatorCreateRequestImplCopyWith<_$LocatorCreateRequestImpl>
      get copyWith =>
          __$$LocatorCreateRequestImplCopyWithImpl<_$LocatorCreateRequestImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocatorCreateRequestImplToJson(
      this,
    );
  }
}

abstract class _LocatorCreateRequest extends LocatorCreateRequest {
  const factory _LocatorCreateRequest(
      {required final String name,
      required final String from,
      required final int duration,
      required final int toTimeType,
      required final String vehicles,
      required final String geofences,
      required final String fields,
      required final String email,
      required final String stations,
      required final String options}) = _$LocatorCreateRequestImpl;
  const _LocatorCreateRequest._() : super._();

  factory _LocatorCreateRequest.fromJson(Map<String, dynamic> json) =
      _$LocatorCreateRequestImpl.fromJson;

  @override
  String get name;
  @override
  String get from;
  @override
  int get duration;
  @override
  int get toTimeType;
  @override
  String get vehicles;
  @override
  String get geofences;
  @override
  String get fields;
  @override
  String get email;
  @override
  String get stations;
  @override
  String get options;
  @override
  @JsonKey(ignore: true)
  _$$LocatorCreateRequestImplCopyWith<_$LocatorCreateRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
