// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'locator_get_all_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LocatorGetAllResponseImpl _$$LocatorGetAllResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$LocatorGetAllResponseImpl(
      total: (json['total'] as num).toInt(),
      data: (json['data'] as List<dynamic>)
          .map((e) => Locator.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$LocatorGetAllResponseImplToJson(
        _$LocatorGetAllResponseImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'data': instance.data,
    };

_$LocatorImpl _$$LocatorImplFromJson(Map<String, dynamic> json) =>
    _$LocatorImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      tag: json['tag'] as String?,
      emails:
          (json['emails'] as List<dynamic>).map((e) => e as String).toList(),
      vehicles: (json['vehicles'] as num).toInt(),
      geofences: (json['geofences'] as num).toInt(),
      stations: (json['stations'] as num).toInt(),
      fromTime: json['fromTime'] as String,
      fields: (json['fields'] as num).toInt(),
      options: (json['options'] as num).toInt(),
      toTime: json['toTime'] as String,
      lastModified: json['lastModified'] as String?,
      user1: json['user1'] as String?,
      user2: json['user2'] as String?,
    );

Map<String, dynamic> _$$LocatorImplToJson(_$LocatorImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'tag': instance.tag,
      'emails': instance.emails,
      'vehicles': instance.vehicles,
      'geofences': instance.geofences,
      'stations': instance.stations,
      'fromTime': instance.fromTime,
      'fields': instance.fields,
      'options': instance.options,
      'toTime': instance.toTime,
      'lastModified': instance.lastModified,
      'user1': instance.user1,
      'user2': instance.user2,
    };
