// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'station_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

StationResponse _$StationResponseFromJson(Map<String, dynamic> json) {
  return _StationResponse.fromJson(json);
}

/// @nodoc
mixin _$StationResponse {
  int get total => throw _privateConstructorUsedError;
  List<Station> get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StationResponseCopyWith<StationResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StationResponseCopyWith<$Res> {
  factory $StationResponseCopyWith(
          StationResponse value, $Res Function(StationResponse) then) =
      _$StationResponseCopyWithImpl<$Res, StationResponse>;
  @useResult
  $Res call({int total, List<Station> data});
}

/// @nodoc
class _$StationResponseCopyWithImpl<$Res, $Val extends StationResponse>
    implements $StationResponseCopyWith<$Res> {
  _$StationResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<Station>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StationResponseImplCopyWith<$Res>
    implements $StationResponseCopyWith<$Res> {
  factory _$$StationResponseImplCopyWith(_$StationResponseImpl value,
          $Res Function(_$StationResponseImpl) then) =
      __$$StationResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, List<Station> data});
}

/// @nodoc
class __$$StationResponseImplCopyWithImpl<$Res>
    extends _$StationResponseCopyWithImpl<$Res, _$StationResponseImpl>
    implements _$$StationResponseImplCopyWith<$Res> {
  __$$StationResponseImplCopyWithImpl(
      _$StationResponseImpl _value, $Res Function(_$StationResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_$StationResponseImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<Station>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StationResponseImpl extends _StationResponse {
  const _$StationResponseImpl(
      {required this.total, required final List<Station> data})
      : _data = data,
        super._();

  factory _$StationResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$StationResponseImplFromJson(json);

  @override
  final int total;
  final List<Station> _data;
  @override
  List<Station> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'StationResponse(total: $total, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StationResponseImpl &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, total, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$StationResponseImplCopyWith<_$StationResponseImpl> get copyWith =>
      __$$StationResponseImplCopyWithImpl<_$StationResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StationResponseImplToJson(
      this,
    );
  }
}

abstract class _StationResponse extends StationResponse {
  const factory _StationResponse(
      {required final int total,
      required final List<Station> data}) = _$StationResponseImpl;
  const _StationResponse._() : super._();

  factory _StationResponse.fromJson(Map<String, dynamic> json) =
      _$StationResponseImpl.fromJson;

  @override
  int get total;
  @override
  List<Station> get data;
  @override
  @JsonKey(ignore: true)
  _$$StationResponseImplCopyWith<_$StationResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Station _$StationFromJson(Map<String, dynamic> json) {
  return _Station.fromJson(json);
}

/// @nodoc
mixin _$Station {
  String? get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get stationCategoryId => throw _privateConstructorUsedError;
  int get categoryId => throw _privateConstructorUsedError;
  int get x => throw _privateConstructorUsedError;
  int get y => throw _privateConstructorUsedError;
  int get radius => throw _privateConstructorUsedError;
  String? get companyId => throw _privateConstructorUsedError;
  bool get checkOut => throw _privateConstructorUsedError;
  bool get checkIn => throw _privateConstructorUsedError;
  bool get isStation => throw _privateConstructorUsedError;
  bool get private => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StationCopyWith<Station> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StationCopyWith<$Res> {
  factory $StationCopyWith(Station value, $Res Function(Station) then) =
      _$StationCopyWithImpl<$Res, Station>;
  @useResult
  $Res call(
      {String? name,
      String? description,
      String? stationCategoryId,
      int categoryId,
      int x,
      int y,
      int radius,
      String? companyId,
      bool checkOut,
      bool checkIn,
      bool isStation,
      bool private});
}

/// @nodoc
class _$StationCopyWithImpl<$Res, $Val extends Station>
    implements $StationCopyWith<$Res> {
  _$StationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? description = freezed,
    Object? stationCategoryId = freezed,
    Object? categoryId = null,
    Object? x = null,
    Object? y = null,
    Object? radius = null,
    Object? companyId = freezed,
    Object? checkOut = null,
    Object? checkIn = null,
    Object? isStation = null,
    Object? private = null,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      stationCategoryId: freezed == stationCategoryId
          ? _value.stationCategoryId
          : stationCategoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as int,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as int,
      radius: null == radius
          ? _value.radius
          : radius // ignore: cast_nullable_to_non_nullable
              as int,
      companyId: freezed == companyId
          ? _value.companyId
          : companyId // ignore: cast_nullable_to_non_nullable
              as String?,
      checkOut: null == checkOut
          ? _value.checkOut
          : checkOut // ignore: cast_nullable_to_non_nullable
              as bool,
      checkIn: null == checkIn
          ? _value.checkIn
          : checkIn // ignore: cast_nullable_to_non_nullable
              as bool,
      isStation: null == isStation
          ? _value.isStation
          : isStation // ignore: cast_nullable_to_non_nullable
              as bool,
      private: null == private
          ? _value.private
          : private // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StationImplCopyWith<$Res> implements $StationCopyWith<$Res> {
  factory _$$StationImplCopyWith(
          _$StationImpl value, $Res Function(_$StationImpl) then) =
      __$$StationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? name,
      String? description,
      String? stationCategoryId,
      int categoryId,
      int x,
      int y,
      int radius,
      String? companyId,
      bool checkOut,
      bool checkIn,
      bool isStation,
      bool private});
}

/// @nodoc
class __$$StationImplCopyWithImpl<$Res>
    extends _$StationCopyWithImpl<$Res, _$StationImpl>
    implements _$$StationImplCopyWith<$Res> {
  __$$StationImplCopyWithImpl(
      _$StationImpl _value, $Res Function(_$StationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? description = freezed,
    Object? stationCategoryId = freezed,
    Object? categoryId = null,
    Object? x = null,
    Object? y = null,
    Object? radius = null,
    Object? companyId = freezed,
    Object? checkOut = null,
    Object? checkIn = null,
    Object? isStation = null,
    Object? private = null,
  }) {
    return _then(_$StationImpl(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      stationCategoryId: freezed == stationCategoryId
          ? _value.stationCategoryId
          : stationCategoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as int,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as int,
      radius: null == radius
          ? _value.radius
          : radius // ignore: cast_nullable_to_non_nullable
              as int,
      companyId: freezed == companyId
          ? _value.companyId
          : companyId // ignore: cast_nullable_to_non_nullable
              as String?,
      checkOut: null == checkOut
          ? _value.checkOut
          : checkOut // ignore: cast_nullable_to_non_nullable
              as bool,
      checkIn: null == checkIn
          ? _value.checkIn
          : checkIn // ignore: cast_nullable_to_non_nullable
              as bool,
      isStation: null == isStation
          ? _value.isStation
          : isStation // ignore: cast_nullable_to_non_nullable
              as bool,
      private: null == private
          ? _value.private
          : private // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StationImpl extends _Station {
  const _$StationImpl(
      {required this.name,
      required this.description,
      required this.stationCategoryId,
      required this.categoryId,
      required this.x,
      required this.y,
      required this.radius,
      required this.companyId,
      required this.checkOut,
      required this.checkIn,
      required this.isStation,
      required this.private})
      : super._();

  factory _$StationImpl.fromJson(Map<String, dynamic> json) =>
      _$$StationImplFromJson(json);

  @override
  final String? name;
  @override
  final String? description;
  @override
  final String? stationCategoryId;
  @override
  final int categoryId;
  @override
  final int x;
  @override
  final int y;
  @override
  final int radius;
  @override
  final String? companyId;
  @override
  final bool checkOut;
  @override
  final bool checkIn;
  @override
  final bool isStation;
  @override
  final bool private;

  @override
  String toString() {
    return 'Station(name: $name, description: $description, stationCategoryId: $stationCategoryId, categoryId: $categoryId, x: $x, y: $y, radius: $radius, companyId: $companyId, checkOut: $checkOut, checkIn: $checkIn, isStation: $isStation, private: $private)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StationImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.stationCategoryId, stationCategoryId) ||
                other.stationCategoryId == stationCategoryId) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y) &&
            (identical(other.radius, radius) || other.radius == radius) &&
            (identical(other.companyId, companyId) ||
                other.companyId == companyId) &&
            (identical(other.checkOut, checkOut) ||
                other.checkOut == checkOut) &&
            (identical(other.checkIn, checkIn) || other.checkIn == checkIn) &&
            (identical(other.isStation, isStation) ||
                other.isStation == isStation) &&
            (identical(other.private, private) || other.private == private));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      name,
      description,
      stationCategoryId,
      categoryId,
      x,
      y,
      radius,
      companyId,
      checkOut,
      checkIn,
      isStation,
      private);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$StationImplCopyWith<_$StationImpl> get copyWith =>
      __$$StationImplCopyWithImpl<_$StationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StationImplToJson(
      this,
    );
  }
}

abstract class _Station extends Station {
  const factory _Station(
      {required final String? name,
      required final String? description,
      required final String? stationCategoryId,
      required final int categoryId,
      required final int x,
      required final int y,
      required final int radius,
      required final String? companyId,
      required final bool checkOut,
      required final bool checkIn,
      required final bool isStation,
      required final bool private}) = _$StationImpl;
  const _Station._() : super._();

  factory _Station.fromJson(Map<String, dynamic> json) = _$StationImpl.fromJson;

  @override
  String? get name;
  @override
  String? get description;
  @override
  String? get stationCategoryId;
  @override
  int get categoryId;
  @override
  int get x;
  @override
  int get y;
  @override
  int get radius;
  @override
  String? get companyId;
  @override
  bool get checkOut;
  @override
  bool get checkIn;
  @override
  bool get isStation;
  @override
  bool get private;
  @override
  @JsonKey(ignore: true)
  _$$StationImplCopyWith<_$StationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
