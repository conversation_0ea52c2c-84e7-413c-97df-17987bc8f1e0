// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'station_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StationResponseImpl _$$StationResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$StationResponseImpl(
      total: (json['total'] as num).toInt(),
      data: (json['data'] as List<dynamic>)
          .map((e) => Station.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$StationResponseImplToJson(
        _$StationResponseImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'data': instance.data,
    };

_$StationImpl _$$StationImplFromJson(Map<String, dynamic> json) =>
    _$StationImpl(
      name: json['name'] as String?,
      description: json['description'] as String?,
      stationCategoryId: json['stationCategoryId'] as String?,
      categoryId: (json['categoryId'] as num).toInt(),
      x: (json['x'] as num).toInt(),
      y: (json['y'] as num).toInt(),
      radius: (json['radius'] as num).toInt(),
      companyId: json['companyId'] as String?,
      checkOut: json['checkOut'] as bool,
      checkIn: json['checkIn'] as bool,
      isStation: json['isStation'] as bool,
      private: json['private'] as bool,
    );

Map<String, dynamic> _$$StationImplToJson(_$StationImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'stationCategoryId': instance.stationCategoryId,
      'categoryId': instance.categoryId,
      'x': instance.x,
      'y': instance.y,
      'radius': instance.radius,
      'companyId': instance.companyId,
      'checkOut': instance.checkOut,
      'checkIn': instance.checkIn,
      'isStation': instance.isStation,
      'private': instance.private,
    };
