// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'playback_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PlaybackResponse _$PlaybackResponseFromJson(Map<String, dynamic> json) {
  return _PlaybackResponse.fromJson(json);
}

/// @nodoc
mixin _$PlaybackResponse {
  int get status => throw _privateConstructorUsedError;
  String? get token => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  List<PlaybackResult> get settings => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlaybackResponseCopyWith<PlaybackResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlaybackResponseCopyWith<$Res> {
  factory $PlaybackResponseCopyWith(
          PlaybackResponse value, $Res Function(PlaybackResponse) then) =
      _$PlaybackResponseCopyWithImpl<$Res, PlaybackResponse>;
  @useResult
  $Res call(
      {int status,
      String? token,
      String message,
      List<PlaybackResult> settings});
}

/// @nodoc
class _$PlaybackResponseCopyWithImpl<$Res, $Val extends PlaybackResponse>
    implements $PlaybackResponseCopyWith<$Res> {
  _$PlaybackResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? token = freezed,
    Object? message = null,
    Object? settings = null,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      settings: null == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as List<PlaybackResult>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PlaybackResponseImplCopyWith<$Res>
    implements $PlaybackResponseCopyWith<$Res> {
  factory _$$PlaybackResponseImplCopyWith(_$PlaybackResponseImpl value,
          $Res Function(_$PlaybackResponseImpl) then) =
      __$$PlaybackResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int status,
      String? token,
      String message,
      List<PlaybackResult> settings});
}

/// @nodoc
class __$$PlaybackResponseImplCopyWithImpl<$Res>
    extends _$PlaybackResponseCopyWithImpl<$Res, _$PlaybackResponseImpl>
    implements _$$PlaybackResponseImplCopyWith<$Res> {
  __$$PlaybackResponseImplCopyWithImpl(_$PlaybackResponseImpl _value,
      $Res Function(_$PlaybackResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? token = freezed,
    Object? message = null,
    Object? settings = null,
  }) {
    return _then(_$PlaybackResponseImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      settings: null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as List<PlaybackResult>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PlaybackResponseImpl extends _PlaybackResponse {
  const _$PlaybackResponseImpl(
      {required this.status,
      required this.token,
      required this.message,
      required final List<PlaybackResult> settings})
      : _settings = settings,
        super._();

  factory _$PlaybackResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlaybackResponseImplFromJson(json);

  @override
  final int status;
  @override
  final String? token;
  @override
  final String message;
  final List<PlaybackResult> _settings;
  @override
  List<PlaybackResult> get settings {
    if (_settings is EqualUnmodifiableListView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_settings);
  }

  @override
  String toString() {
    return 'PlaybackResponse(status: $status, token: $token, message: $message, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaybackResponseImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, status, token, message,
      const DeepCollectionEquality().hash(_settings));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PlaybackResponseImplCopyWith<_$PlaybackResponseImpl> get copyWith =>
      __$$PlaybackResponseImplCopyWithImpl<_$PlaybackResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlaybackResponseImplToJson(
      this,
    );
  }
}

abstract class _PlaybackResponse extends PlaybackResponse {
  const factory _PlaybackResponse(
      {required final int status,
      required final String? token,
      required final String message,
      required final List<PlaybackResult> settings}) = _$PlaybackResponseImpl;
  const _PlaybackResponse._() : super._();

  factory _PlaybackResponse.fromJson(Map<String, dynamic> json) =
      _$PlaybackResponseImpl.fromJson;

  @override
  int get status;
  @override
  String? get token;
  @override
  String get message;
  @override
  List<PlaybackResult> get settings;
  @override
  @JsonKey(ignore: true)
  _$$PlaybackResponseImplCopyWith<_$PlaybackResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PlaybackResult _$PlaybackResultFromJson(Map<String, dynamic> json) {
  return _PlaybackResult.fromJson(json);
}

/// @nodoc
mixin _$PlaybackResult {
  int get status => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  int get index => throw _privateConstructorUsedError;
  String get subscription => throw _privateConstructorUsedError;
  int get liveTime => throw _privateConstructorUsedError;
  int get offset => throw _privateConstructorUsedError;
  String get protocol => throw _privateConstructorUsedError;
  String get channel => throw _privateConstructorUsedError;
  String? get fileUrl => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlaybackResultCopyWith<PlaybackResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlaybackResultCopyWith<$Res> {
  factory $PlaybackResultCopyWith(
          PlaybackResult value, $Res Function(PlaybackResult) then) =
      _$PlaybackResultCopyWithImpl<$Res, PlaybackResult>;
  @useResult
  $Res call(
      {int status,
      String message,
      int index,
      String subscription,
      int liveTime,
      int offset,
      String protocol,
      String channel,
      String? fileUrl,
      String? url});
}

/// @nodoc
class _$PlaybackResultCopyWithImpl<$Res, $Val extends PlaybackResult>
    implements $PlaybackResultCopyWith<$Res> {
  _$PlaybackResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? message = null,
    Object? index = null,
    Object? subscription = null,
    Object? liveTime = null,
    Object? offset = null,
    Object? protocol = null,
    Object? channel = null,
    Object? fileUrl = freezed,
    Object? url = freezed,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      subscription: null == subscription
          ? _value.subscription
          : subscription // ignore: cast_nullable_to_non_nullable
              as String,
      liveTime: null == liveTime
          ? _value.liveTime
          : liveTime // ignore: cast_nullable_to_non_nullable
              as int,
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      protocol: null == protocol
          ? _value.protocol
          : protocol // ignore: cast_nullable_to_non_nullable
              as String,
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as String,
      fileUrl: freezed == fileUrl
          ? _value.fileUrl
          : fileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PlaybackResultImplCopyWith<$Res>
    implements $PlaybackResultCopyWith<$Res> {
  factory _$$PlaybackResultImplCopyWith(_$PlaybackResultImpl value,
          $Res Function(_$PlaybackResultImpl) then) =
      __$$PlaybackResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int status,
      String message,
      int index,
      String subscription,
      int liveTime,
      int offset,
      String protocol,
      String channel,
      String? fileUrl,
      String? url});
}

/// @nodoc
class __$$PlaybackResultImplCopyWithImpl<$Res>
    extends _$PlaybackResultCopyWithImpl<$Res, _$PlaybackResultImpl>
    implements _$$PlaybackResultImplCopyWith<$Res> {
  __$$PlaybackResultImplCopyWithImpl(
      _$PlaybackResultImpl _value, $Res Function(_$PlaybackResultImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? message = null,
    Object? index = null,
    Object? subscription = null,
    Object? liveTime = null,
    Object? offset = null,
    Object? protocol = null,
    Object? channel = null,
    Object? fileUrl = freezed,
    Object? url = freezed,
  }) {
    return _then(_$PlaybackResultImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      subscription: null == subscription
          ? _value.subscription
          : subscription // ignore: cast_nullable_to_non_nullable
              as String,
      liveTime: null == liveTime
          ? _value.liveTime
          : liveTime // ignore: cast_nullable_to_non_nullable
              as int,
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      protocol: null == protocol
          ? _value.protocol
          : protocol // ignore: cast_nullable_to_non_nullable
              as String,
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as String,
      fileUrl: freezed == fileUrl
          ? _value.fileUrl
          : fileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PlaybackResultImpl extends _PlaybackResult {
  const _$PlaybackResultImpl(
      {required this.status,
      required this.message,
      required this.index,
      required this.subscription,
      required this.liveTime,
      required this.offset,
      required this.protocol,
      required this.channel,
      required this.fileUrl,
      required this.url})
      : super._();

  factory _$PlaybackResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlaybackResultImplFromJson(json);

  @override
  final int status;
  @override
  final String message;
  @override
  final int index;
  @override
  final String subscription;
  @override
  final int liveTime;
  @override
  final int offset;
  @override
  final String protocol;
  @override
  final String channel;
  @override
  final String? fileUrl;
  @override
  final String? url;

  @override
  String toString() {
    return 'PlaybackResult(status: $status, message: $message, index: $index, subscription: $subscription, liveTime: $liveTime, offset: $offset, protocol: $protocol, channel: $channel, fileUrl: $fileUrl, url: $url)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaybackResultImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.index, index) || other.index == index) &&
            (identical(other.subscription, subscription) ||
                other.subscription == subscription) &&
            (identical(other.liveTime, liveTime) ||
                other.liveTime == liveTime) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.protocol, protocol) ||
                other.protocol == protocol) &&
            (identical(other.channel, channel) || other.channel == channel) &&
            (identical(other.fileUrl, fileUrl) || other.fileUrl == fileUrl) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, status, message, index,
      subscription, liveTime, offset, protocol, channel, fileUrl, url);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PlaybackResultImplCopyWith<_$PlaybackResultImpl> get copyWith =>
      __$$PlaybackResultImplCopyWithImpl<_$PlaybackResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlaybackResultImplToJson(
      this,
    );
  }
}

abstract class _PlaybackResult extends PlaybackResult {
  const factory _PlaybackResult(
      {required final int status,
      required final String message,
      required final int index,
      required final String subscription,
      required final int liveTime,
      required final int offset,
      required final String protocol,
      required final String channel,
      required final String? fileUrl,
      required final String? url}) = _$PlaybackResultImpl;
  const _PlaybackResult._() : super._();

  factory _PlaybackResult.fromJson(Map<String, dynamic> json) =
      _$PlaybackResultImpl.fromJson;

  @override
  int get status;
  @override
  String get message;
  @override
  int get index;
  @override
  String get subscription;
  @override
  int get liveTime;
  @override
  int get offset;
  @override
  String get protocol;
  @override
  String get channel;
  @override
  String? get fileUrl;
  @override
  String? get url;
  @override
  @JsonKey(ignore: true)
  _$$PlaybackResultImplCopyWith<_$PlaybackResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
