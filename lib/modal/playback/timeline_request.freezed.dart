// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'timeline_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TimelineRequest _$TimelineRequestFromJson(Map<String, dynamic> json) {
  return _TimelineRequest.fromJson(json);
}

/// @nodoc
mixin _$TimelineRequest {
  String get vehicleId => throw _privateConstructorUsedError;
  String get requestTime => throw _privateConstructorUsedError;
  String? get token => throw _privateConstructorUsedError;
  bool get isData => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TimelineRequestCopyWith<TimelineRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimelineRequestCopyWith<$Res> {
  factory $TimelineRequestCopyWith(
          TimelineRequest value, $Res Function(TimelineRequest) then) =
      _$TimelineRequestCopyWithImpl<$Res, TimelineRequest>;
  @useResult
  $Res call({String vehicleId, String requestTime, String? token, bool isData});
}

/// @nodoc
class _$TimelineRequestCopyWithImpl<$Res, $Val extends TimelineRequest>
    implements $TimelineRequestCopyWith<$Res> {
  _$TimelineRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? requestTime = null,
    Object? token = freezed,
    Object? isData = null,
  }) {
    return _then(_value.copyWith(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      requestTime: null == requestTime
          ? _value.requestTime
          : requestTime // ignore: cast_nullable_to_non_nullable
              as String,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      isData: null == isData
          ? _value.isData
          : isData // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TimelineRequestImplCopyWith<$Res>
    implements $TimelineRequestCopyWith<$Res> {
  factory _$$TimelineRequestImplCopyWith(_$TimelineRequestImpl value,
          $Res Function(_$TimelineRequestImpl) then) =
      __$$TimelineRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String vehicleId, String requestTime, String? token, bool isData});
}

/// @nodoc
class __$$TimelineRequestImplCopyWithImpl<$Res>
    extends _$TimelineRequestCopyWithImpl<$Res, _$TimelineRequestImpl>
    implements _$$TimelineRequestImplCopyWith<$Res> {
  __$$TimelineRequestImplCopyWithImpl(
      _$TimelineRequestImpl _value, $Res Function(_$TimelineRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? requestTime = null,
    Object? token = freezed,
    Object? isData = null,
  }) {
    return _then(_$TimelineRequestImpl(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      requestTime: null == requestTime
          ? _value.requestTime
          : requestTime // ignore: cast_nullable_to_non_nullable
              as String,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      isData: null == isData
          ? _value.isData
          : isData // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TimelineRequestImpl extends _TimelineRequest {
  const _$TimelineRequestImpl(
      {required this.vehicleId,
      required this.requestTime,
      required this.token,
      required this.isData})
      : super._();

  factory _$TimelineRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$TimelineRequestImplFromJson(json);

  @override
  final String vehicleId;
  @override
  final String requestTime;
  @override
  final String? token;
  @override
  final bool isData;

  @override
  String toString() {
    return 'TimelineRequest(vehicleId: $vehicleId, requestTime: $requestTime, token: $token, isData: $isData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimelineRequestImpl &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.requestTime, requestTime) ||
                other.requestTime == requestTime) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.isData, isData) || other.isData == isData));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, vehicleId, requestTime, token, isData);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TimelineRequestImplCopyWith<_$TimelineRequestImpl> get copyWith =>
      __$$TimelineRequestImplCopyWithImpl<_$TimelineRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TimelineRequestImplToJson(
      this,
    );
  }
}

abstract class _TimelineRequest extends TimelineRequest {
  const factory _TimelineRequest(
      {required final String vehicleId,
      required final String requestTime,
      required final String? token,
      required final bool isData}) = _$TimelineRequestImpl;
  const _TimelineRequest._() : super._();

  factory _TimelineRequest.fromJson(Map<String, dynamic> json) =
      _$TimelineRequestImpl.fromJson;

  @override
  String get vehicleId;
  @override
  String get requestTime;
  @override
  String? get token;
  @override
  bool get isData;
  @override
  @JsonKey(ignore: true)
  _$$TimelineRequestImplCopyWith<_$TimelineRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
