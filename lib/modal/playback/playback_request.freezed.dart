// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'playback_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PlaybackRequest _$PlaybackRequestFromJson(Map<String, dynamic> json) {
  return _PlaybackRequest.fromJson(json);
}

/// @nodoc
mixin _$PlaybackRequest {
  String get vehicleId => throw _privateConstructorUsedError;
  String get requestTime =>
      throw _privateConstructorUsedError; //2024-07-04 15:00:00
  List<RequestPlaybackChannel> get requestChannels =>
      throw _privateConstructorUsedError;
  String? get token => throw _privateConstructorUsedError;
  int get duration => throw _privateConstructorUsedError;
  bool get isDevice => throw _privateConstructorUsedError;
  bool get file => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlaybackRequestCopyWith<PlaybackRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlaybackRequestCopyWith<$Res> {
  factory $PlaybackRequestCopyWith(
          PlaybackRequest value, $Res Function(PlaybackRequest) then) =
      _$PlaybackRequestCopyWithImpl<$Res, PlaybackRequest>;
  @useResult
  $Res call(
      {String vehicleId,
      String requestTime,
      List<RequestPlaybackChannel> requestChannels,
      String? token,
      int duration,
      bool isDevice,
      bool file});
}

/// @nodoc
class _$PlaybackRequestCopyWithImpl<$Res, $Val extends PlaybackRequest>
    implements $PlaybackRequestCopyWith<$Res> {
  _$PlaybackRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? requestTime = null,
    Object? requestChannels = null,
    Object? token = freezed,
    Object? duration = null,
    Object? isDevice = null,
    Object? file = null,
  }) {
    return _then(_value.copyWith(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      requestTime: null == requestTime
          ? _value.requestTime
          : requestTime // ignore: cast_nullable_to_non_nullable
              as String,
      requestChannels: null == requestChannels
          ? _value.requestChannels
          : requestChannels // ignore: cast_nullable_to_non_nullable
              as List<RequestPlaybackChannel>,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      isDevice: null == isDevice
          ? _value.isDevice
          : isDevice // ignore: cast_nullable_to_non_nullable
              as bool,
      file: null == file
          ? _value.file
          : file // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PlaybackRequestImplCopyWith<$Res>
    implements $PlaybackRequestCopyWith<$Res> {
  factory _$$PlaybackRequestImplCopyWith(_$PlaybackRequestImpl value,
          $Res Function(_$PlaybackRequestImpl) then) =
      __$$PlaybackRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String vehicleId,
      String requestTime,
      List<RequestPlaybackChannel> requestChannels,
      String? token,
      int duration,
      bool isDevice,
      bool file});
}

/// @nodoc
class __$$PlaybackRequestImplCopyWithImpl<$Res>
    extends _$PlaybackRequestCopyWithImpl<$Res, _$PlaybackRequestImpl>
    implements _$$PlaybackRequestImplCopyWith<$Res> {
  __$$PlaybackRequestImplCopyWithImpl(
      _$PlaybackRequestImpl _value, $Res Function(_$PlaybackRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? requestTime = null,
    Object? requestChannels = null,
    Object? token = freezed,
    Object? duration = null,
    Object? isDevice = null,
    Object? file = null,
  }) {
    return _then(_$PlaybackRequestImpl(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      requestTime: null == requestTime
          ? _value.requestTime
          : requestTime // ignore: cast_nullable_to_non_nullable
              as String,
      requestChannels: null == requestChannels
          ? _value._requestChannels
          : requestChannels // ignore: cast_nullable_to_non_nullable
              as List<RequestPlaybackChannel>,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      isDevice: null == isDevice
          ? _value.isDevice
          : isDevice // ignore: cast_nullable_to_non_nullable
              as bool,
      file: null == file
          ? _value.file
          : file // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PlaybackRequestImpl extends _PlaybackRequest {
  const _$PlaybackRequestImpl(
      {required this.vehicleId,
      required this.requestTime,
      required final List<RequestPlaybackChannel> requestChannels,
      required this.token,
      required this.duration,
      required this.isDevice,
      required this.file})
      : _requestChannels = requestChannels,
        super._();

  factory _$PlaybackRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlaybackRequestImplFromJson(json);

  @override
  final String vehicleId;
  @override
  final String requestTime;
//2024-07-04 15:00:00
  final List<RequestPlaybackChannel> _requestChannels;
//2024-07-04 15:00:00
  @override
  List<RequestPlaybackChannel> get requestChannels {
    if (_requestChannels is EqualUnmodifiableListView) return _requestChannels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_requestChannels);
  }

  @override
  final String? token;
  @override
  final int duration;
  @override
  final bool isDevice;
  @override
  final bool file;

  @override
  String toString() {
    return 'PlaybackRequest(vehicleId: $vehicleId, requestTime: $requestTime, requestChannels: $requestChannels, token: $token, duration: $duration, isDevice: $isDevice, file: $file)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaybackRequestImpl &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.requestTime, requestTime) ||
                other.requestTime == requestTime) &&
            const DeepCollectionEquality()
                .equals(other._requestChannels, _requestChannels) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.isDevice, isDevice) ||
                other.isDevice == isDevice) &&
            (identical(other.file, file) || other.file == file));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      vehicleId,
      requestTime,
      const DeepCollectionEquality().hash(_requestChannels),
      token,
      duration,
      isDevice,
      file);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PlaybackRequestImplCopyWith<_$PlaybackRequestImpl> get copyWith =>
      __$$PlaybackRequestImplCopyWithImpl<_$PlaybackRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlaybackRequestImplToJson(
      this,
    );
  }
}

abstract class _PlaybackRequest extends PlaybackRequest {
  const factory _PlaybackRequest(
      {required final String vehicleId,
      required final String requestTime,
      required final List<RequestPlaybackChannel> requestChannels,
      required final String? token,
      required final int duration,
      required final bool isDevice,
      required final bool file}) = _$PlaybackRequestImpl;
  const _PlaybackRequest._() : super._();

  factory _PlaybackRequest.fromJson(Map<String, dynamic> json) =
      _$PlaybackRequestImpl.fromJson;

  @override
  String get vehicleId;
  @override
  String get requestTime;
  @override //2024-07-04 15:00:00
  List<RequestPlaybackChannel> get requestChannels;
  @override
  String? get token;
  @override
  int get duration;
  @override
  bool get isDevice;
  @override
  bool get file;
  @override
  @JsonKey(ignore: true)
  _$$PlaybackRequestImplCopyWith<_$PlaybackRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RequestPlaybackChannel _$RequestPlaybackChannelFromJson(
    Map<String, dynamic> json) {
  return _RequestPlaybackChannel.fromJson(json);
}

/// @nodoc
mixin _$RequestPlaybackChannel {
  String get id => throw _privateConstructorUsedError;
  int get channel => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RequestPlaybackChannelCopyWith<RequestPlaybackChannel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RequestPlaybackChannelCopyWith<$Res> {
  factory $RequestPlaybackChannelCopyWith(RequestPlaybackChannel value,
          $Res Function(RequestPlaybackChannel) then) =
      _$RequestPlaybackChannelCopyWithImpl<$Res, RequestPlaybackChannel>;
  @useResult
  $Res call({String id, int channel});
}

/// @nodoc
class _$RequestPlaybackChannelCopyWithImpl<$Res,
        $Val extends RequestPlaybackChannel>
    implements $RequestPlaybackChannelCopyWith<$Res> {
  _$RequestPlaybackChannelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? channel = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RequestPlaybackChannelImplCopyWith<$Res>
    implements $RequestPlaybackChannelCopyWith<$Res> {
  factory _$$RequestPlaybackChannelImplCopyWith(
          _$RequestPlaybackChannelImpl value,
          $Res Function(_$RequestPlaybackChannelImpl) then) =
      __$$RequestPlaybackChannelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, int channel});
}

/// @nodoc
class __$$RequestPlaybackChannelImplCopyWithImpl<$Res>
    extends _$RequestPlaybackChannelCopyWithImpl<$Res,
        _$RequestPlaybackChannelImpl>
    implements _$$RequestPlaybackChannelImplCopyWith<$Res> {
  __$$RequestPlaybackChannelImplCopyWithImpl(
      _$RequestPlaybackChannelImpl _value,
      $Res Function(_$RequestPlaybackChannelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? channel = null,
  }) {
    return _then(_$RequestPlaybackChannelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RequestPlaybackChannelImpl extends _RequestPlaybackChannel {
  const _$RequestPlaybackChannelImpl({required this.id, required this.channel})
      : super._();

  factory _$RequestPlaybackChannelImpl.fromJson(Map<String, dynamic> json) =>
      _$$RequestPlaybackChannelImplFromJson(json);

  @override
  final String id;
  @override
  final int channel;

  @override
  String toString() {
    return 'RequestPlaybackChannel(id: $id, channel: $channel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RequestPlaybackChannelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.channel, channel) || other.channel == channel));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, channel);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RequestPlaybackChannelImplCopyWith<_$RequestPlaybackChannelImpl>
      get copyWith => __$$RequestPlaybackChannelImplCopyWithImpl<
          _$RequestPlaybackChannelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RequestPlaybackChannelImplToJson(
      this,
    );
  }
}

abstract class _RequestPlaybackChannel extends RequestPlaybackChannel {
  const factory _RequestPlaybackChannel(
      {required final String id,
      required final int channel}) = _$RequestPlaybackChannelImpl;
  const _RequestPlaybackChannel._() : super._();

  factory _RequestPlaybackChannel.fromJson(Map<String, dynamic> json) =
      _$RequestPlaybackChannelImpl.fromJson;

  @override
  String get id;
  @override
  int get channel;
  @override
  @JsonKey(ignore: true)
  _$$RequestPlaybackChannelImplCopyWith<_$RequestPlaybackChannelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
