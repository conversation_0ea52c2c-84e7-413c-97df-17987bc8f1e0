// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'playback_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PlaybackRequestImpl _$$PlaybackRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$PlaybackRequestImpl(
      vehicleId: json['vehicleId'] as String,
      requestTime: json['requestTime'] as String,
      requestChannels: (json['requestChannels'] as List<dynamic>)
          .map(
              (e) => RequestPlaybackChannel.fromJson(e as Map<String, dynamic>))
          .toList(),
      token: json['token'] as String?,
      duration: (json['duration'] as num).toInt(),
      isDevice: json['isDevice'] as bool,
      file: json['file'] as bool,
    );

Map<String, dynamic> _$$PlaybackRequestImplToJson(
        _$PlaybackRequestImpl instance) =>
    <String, dynamic>{
      'vehicleId': instance.vehicleId,
      'requestTime': instance.requestTime,
      'requestChannels': instance.requestChannels,
      'token': instance.token,
      'duration': instance.duration,
      'isDevice': instance.isDevice,
      'file': instance.file,
    };

_$RequestPlaybackChannelImpl _$$RequestPlaybackChannelImplFromJson(
        Map<String, dynamic> json) =>
    _$RequestPlaybackChannelImpl(
      id: json['id'] as String,
      channel: (json['channel'] as num).toInt(),
    );

Map<String, dynamic> _$$RequestPlaybackChannelImplToJson(
        _$RequestPlaybackChannelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'channel': instance.channel,
    };
