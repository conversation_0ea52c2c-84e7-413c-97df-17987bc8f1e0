// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'timeline_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TimelineResponse _$TimelineResponseFromJson(Map<String, dynamic> json) {
  return _TimelineResponse.fromJson(json);
}

/// @nodoc
mixin _$TimelineResponse {
  String? get token => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  int get status => throw _privateConstructorUsedError;
  String? get data => throw _privateConstructorUsedError;
  List<TimelinePlayback>? get timeline => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TimelineResponseCopyWith<TimelineResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimelineResponseCopyWith<$Res> {
  factory $TimelineResponseCopyWith(
          TimelineResponse value, $Res Function(TimelineResponse) then) =
      _$TimelineResponseCopyWithImpl<$Res, TimelineResponse>;
  @useResult
  $Res call(
      {String? token,
      String message,
      int status,
      String? data,
      List<TimelinePlayback>? timeline});
}

/// @nodoc
class _$TimelineResponseCopyWithImpl<$Res, $Val extends TimelineResponse>
    implements $TimelineResponseCopyWith<$Res> {
  _$TimelineResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = freezed,
    Object? message = null,
    Object? status = null,
    Object? data = freezed,
    Object? timeline = freezed,
  }) {
    return _then(_value.copyWith(
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String?,
      timeline: freezed == timeline
          ? _value.timeline
          : timeline // ignore: cast_nullable_to_non_nullable
              as List<TimelinePlayback>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TimelineResponseImplCopyWith<$Res>
    implements $TimelineResponseCopyWith<$Res> {
  factory _$$TimelineResponseImplCopyWith(_$TimelineResponseImpl value,
          $Res Function(_$TimelineResponseImpl) then) =
      __$$TimelineResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? token,
      String message,
      int status,
      String? data,
      List<TimelinePlayback>? timeline});
}

/// @nodoc
class __$$TimelineResponseImplCopyWithImpl<$Res>
    extends _$TimelineResponseCopyWithImpl<$Res, _$TimelineResponseImpl>
    implements _$$TimelineResponseImplCopyWith<$Res> {
  __$$TimelineResponseImplCopyWithImpl(_$TimelineResponseImpl _value,
      $Res Function(_$TimelineResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = freezed,
    Object? message = null,
    Object? status = null,
    Object? data = freezed,
    Object? timeline = freezed,
  }) {
    return _then(_$TimelineResponseImpl(
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String?,
      timeline: freezed == timeline
          ? _value._timeline
          : timeline // ignore: cast_nullable_to_non_nullable
              as List<TimelinePlayback>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TimelineResponseImpl extends _TimelineResponse {
  const _$TimelineResponseImpl(
      {required this.token,
      required this.message,
      required this.status,
      required this.data,
      required final List<TimelinePlayback>? timeline})
      : _timeline = timeline,
        super._();

  factory _$TimelineResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$TimelineResponseImplFromJson(json);

  @override
  final String? token;
  @override
  final String message;
  @override
  final int status;
  @override
  final String? data;
  final List<TimelinePlayback>? _timeline;
  @override
  List<TimelinePlayback>? get timeline {
    final value = _timeline;
    if (value == null) return null;
    if (_timeline is EqualUnmodifiableListView) return _timeline;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TimelineResponse(token: $token, message: $message, status: $status, data: $data, timeline: $timeline)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimelineResponseImpl &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.data, data) || other.data == data) &&
            const DeepCollectionEquality().equals(other._timeline, _timeline));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, token, message, status, data,
      const DeepCollectionEquality().hash(_timeline));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TimelineResponseImplCopyWith<_$TimelineResponseImpl> get copyWith =>
      __$$TimelineResponseImplCopyWithImpl<_$TimelineResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TimelineResponseImplToJson(
      this,
    );
  }
}

abstract class _TimelineResponse extends TimelineResponse {
  const factory _TimelineResponse(
          {required final String? token,
          required final String message,
          required final int status,
          required final String? data,
          required final List<TimelinePlayback>? timeline}) =
      _$TimelineResponseImpl;
  const _TimelineResponse._() : super._();

  factory _TimelineResponse.fromJson(Map<String, dynamic> json) =
      _$TimelineResponseImpl.fromJson;

  @override
  String? get token;
  @override
  String get message;
  @override
  int get status;
  @override
  String? get data;
  @override
  List<TimelinePlayback>? get timeline;
  @override
  @JsonKey(ignore: true)
  _$$TimelineResponseImplCopyWith<_$TimelineResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TimelinePlayback _$TimelinePlaybackFromJson(Map<String, dynamic> json) {
  return _TimelinePlayback.fromJson(json);
}

/// @nodoc
mixin _$TimelinePlayback {
  String get from => throw _privateConstructorUsedError;
  String get to => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TimelinePlaybackCopyWith<TimelinePlayback> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimelinePlaybackCopyWith<$Res> {
  factory $TimelinePlaybackCopyWith(
          TimelinePlayback value, $Res Function(TimelinePlayback) then) =
      _$TimelinePlaybackCopyWithImpl<$Res, TimelinePlayback>;
  @useResult
  $Res call({String from, String to});
}

/// @nodoc
class _$TimelinePlaybackCopyWithImpl<$Res, $Val extends TimelinePlayback>
    implements $TimelinePlaybackCopyWith<$Res> {
  _$TimelinePlaybackCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? from = null,
    Object? to = null,
  }) {
    return _then(_value.copyWith(
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TimelinePlaybackImplCopyWith<$Res>
    implements $TimelinePlaybackCopyWith<$Res> {
  factory _$$TimelinePlaybackImplCopyWith(_$TimelinePlaybackImpl value,
          $Res Function(_$TimelinePlaybackImpl) then) =
      __$$TimelinePlaybackImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String from, String to});
}

/// @nodoc
class __$$TimelinePlaybackImplCopyWithImpl<$Res>
    extends _$TimelinePlaybackCopyWithImpl<$Res, _$TimelinePlaybackImpl>
    implements _$$TimelinePlaybackImplCopyWith<$Res> {
  __$$TimelinePlaybackImplCopyWithImpl(_$TimelinePlaybackImpl _value,
      $Res Function(_$TimelinePlaybackImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? from = null,
    Object? to = null,
  }) {
    return _then(_$TimelinePlaybackImpl(
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TimelinePlaybackImpl extends _TimelinePlayback {
  const _$TimelinePlaybackImpl({required this.from, required this.to})
      : super._();

  factory _$TimelinePlaybackImpl.fromJson(Map<String, dynamic> json) =>
      _$$TimelinePlaybackImplFromJson(json);

  @override
  final String from;
  @override
  final String to;

  @override
  String toString() {
    return 'TimelinePlayback(from: $from, to: $to)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimelinePlaybackImpl &&
            (identical(other.from, from) || other.from == from) &&
            (identical(other.to, to) || other.to == to));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, from, to);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TimelinePlaybackImplCopyWith<_$TimelinePlaybackImpl> get copyWith =>
      __$$TimelinePlaybackImplCopyWithImpl<_$TimelinePlaybackImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TimelinePlaybackImplToJson(
      this,
    );
  }
}

abstract class _TimelinePlayback extends TimelinePlayback {
  const factory _TimelinePlayback(
      {required final String from,
      required final String to}) = _$TimelinePlaybackImpl;
  const _TimelinePlayback._() : super._();

  factory _TimelinePlayback.fromJson(Map<String, dynamic> json) =
      _$TimelinePlaybackImpl.fromJson;

  @override
  String get from;
  @override
  String get to;
  @override
  @JsonKey(ignore: true)
  _$$TimelinePlaybackImplCopyWith<_$TimelinePlaybackImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
