// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'timeline_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TimelineResponseImpl _$$TimelineResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$TimelineResponseImpl(
      token: json['token'] as String?,
      message: json['message'] as String,
      status: (json['status'] as num).toInt(),
      data: json['data'] as String?,
      timeline: (json['timeline'] as List<dynamic>?)
          ?.map((e) => TimelinePlayback.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$TimelineResponseImplToJson(
        _$TimelineResponseImpl instance) =>
    <String, dynamic>{
      'token': instance.token,
      'message': instance.message,
      'status': instance.status,
      'data': instance.data,
      'timeline': instance.timeline,
    };

_$TimelinePlaybackImpl _$$TimelinePlaybackImplFromJson(
        Map<String, dynamic> json) =>
    _$TimelinePlaybackImpl(
      from: json['from'] as String,
      to: json['to'] as String,
    );

Map<String, dynamic> _$$TimelinePlaybackImplToJson(
        _$TimelinePlaybackImpl instance) =>
    <String, dynamic>{
      'from': instance.from,
      'to': instance.to,
    };
