// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'playback_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PlaybackResponseImpl _$$PlaybackResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$PlaybackResponseImpl(
      status: (json['status'] as num).toInt(),
      token: json['token'] as String?,
      message: json['message'] as String,
      settings: (json['settings'] as List<dynamic>)
          .map((e) => PlaybackResult.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$PlaybackResponseImplToJson(
        _$PlaybackResponseImpl instance) =>
    <String, dynamic>{
      'status': instance.status,
      'token': instance.token,
      'message': instance.message,
      'settings': instance.settings,
    };

_$PlaybackResultImpl _$$PlaybackResultImplFromJson(Map<String, dynamic> json) =>
    _$PlaybackResultImpl(
      status: (json['status'] as num).toInt(),
      message: json['message'] as String,
      index: (json['index'] as num).toInt(),
      subscription: json['subscription'] as String,
      liveTime: (json['liveTime'] as num).toInt(),
      offset: (json['offset'] as num).toInt(),
      protocol: json['protocol'] as String,
      channel: json['channel'] as String,
      fileUrl: json['fileUrl'] as String?,
      url: json['url'] as String?,
    );

Map<String, dynamic> _$$PlaybackResultImplToJson(
        _$PlaybackResultImpl instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'index': instance.index,
      'subscription': instance.subscription,
      'liveTime': instance.liveTime,
      'offset': instance.offset,
      'protocol': instance.protocol,
      'channel': instance.channel,
      'fileUrl': instance.fileUrl,
      'url': instance.url,
    };
