// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'changelog_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ChangelogResponseImpl _$$ChangelogResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ChangelogResponseImpl(
      error: json['error'] as String?,
      data: (json['data'] as List<dynamic>)
          .map((e) => Data.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ChangelogResponseImplToJson(
        _$ChangelogResponseImpl instance) =>
    <String, dynamic>{
      'error': instance.error,
      'data': instance.data,
    };

_$DataImpl _$$DataImplFromJson(Map<String, dynamic> json) => _$DataImpl(
      id: (json['id'] as num).toInt(),
      version: json['version'] as String,
      info: (json['info'] as List<dynamic>)
          .map((e) => Info.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$DataImplToJson(_$DataImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'version': instance.version,
      'info': instance.info,
    };

_$InfoImpl _$$InfoImplFromJson(Map<String, dynamic> json) => _$InfoImpl(
      type: json['type'] as String,
      message: json['message'] as String,
    );

Map<String, dynamic> _$$InfoImplToJson(_$InfoImpl instance) =>
    <String, dynamic>{
      'type': instance.type,
      'message': instance.message,
    };
