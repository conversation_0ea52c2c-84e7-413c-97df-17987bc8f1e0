// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_image_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VehicleImageResponseImpl _$$VehicleImageResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$VehicleImageResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => VehicleImage.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$VehicleImageResponseImplToJson(
        _$VehicleImageResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

_$VehicleImageImpl _$$VehicleImageImplFromJson(Map<String, dynamic> json) =>
    _$VehicleImageImpl(
      vehicleId: json['vehicleId'] as String,
      time: (json['time'] as num).toInt(),
      idx: (json['idx'] as num).toInt(),
      x: (json['x'] as num).toInt(),
      y: (json['y'] as num).toInt(),
      address: json['address'] as String,
      url: json['url'] as String,
    );

Map<String, dynamic> _$$VehicleImageImplToJson(_$VehicleImageImpl instance) =>
    <String, dynamic>{
      'vehicleId': instance.vehicleId,
      'time': instance.time,
      'idx': instance.idx,
      'x': instance.x,
      'y': instance.y,
      'address': instance.address,
      'url': instance.url,
    };
