// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_image_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VehicleImageRequest _$VehicleImageRequestFromJson(Map<String, dynamic> json) {
  return _VehicleImageRequest.fromJson(json);
}

/// @nodoc
mixin _$VehicleImageRequest {
  String get id => throw _privateConstructorUsedError;
  int get time => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VehicleImageRequestCopyWith<VehicleImageRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleImageRequestCopyWith<$Res> {
  factory $VehicleImageRequestCopyWith(
          VehicleImageRequest value, $Res Function(VehicleImageRequest) then) =
      _$VehicleImageRequestCopyWithImpl<$Res, VehicleImageRequest>;
  @useResult
  $Res call({String id, int time});
}

/// @nodoc
class _$VehicleImageRequestCopyWithImpl<$Res, $Val extends VehicleImageRequest>
    implements $VehicleImageRequestCopyWith<$Res> {
  _$VehicleImageRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? time = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleImageRequestImplCopyWith<$Res>
    implements $VehicleImageRequestCopyWith<$Res> {
  factory _$$VehicleImageRequestImplCopyWith(_$VehicleImageRequestImpl value,
          $Res Function(_$VehicleImageRequestImpl) then) =
      __$$VehicleImageRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, int time});
}

/// @nodoc
class __$$VehicleImageRequestImplCopyWithImpl<$Res>
    extends _$VehicleImageRequestCopyWithImpl<$Res, _$VehicleImageRequestImpl>
    implements _$$VehicleImageRequestImplCopyWith<$Res> {
  __$$VehicleImageRequestImplCopyWithImpl(_$VehicleImageRequestImpl _value,
      $Res Function(_$VehicleImageRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? time = null,
  }) {
    return _then(_$VehicleImageRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleImageRequestImpl extends _VehicleImageRequest {
  const _$VehicleImageRequestImpl({required this.id, required this.time})
      : super._();

  factory _$VehicleImageRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleImageRequestImplFromJson(json);

  @override
  final String id;
  @override
  final int time;

  @override
  String toString() {
    return 'VehicleImageRequest(id: $id, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImageRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, time);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleImageRequestImplCopyWith<_$VehicleImageRequestImpl> get copyWith =>
      __$$VehicleImageRequestImplCopyWithImpl<_$VehicleImageRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleImageRequestImplToJson(
      this,
    );
  }
}

abstract class _VehicleImageRequest extends VehicleImageRequest {
  const factory _VehicleImageRequest(
      {required final String id,
      required final int time}) = _$VehicleImageRequestImpl;
  const _VehicleImageRequest._() : super._();

  factory _VehicleImageRequest.fromJson(Map<String, dynamic> json) =
      _$VehicleImageRequestImpl.fromJson;

  @override
  String get id;
  @override
  int get time;
  @override
  @JsonKey(ignore: true)
  _$$VehicleImageRequestImplCopyWith<_$VehicleImageRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
