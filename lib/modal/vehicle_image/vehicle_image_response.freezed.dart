// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_image_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VehicleImageResponse _$VehicleImageResponseFromJson(Map<String, dynamic> json) {
  return _VehicleImageResponse.fromJson(json);
}

/// @nodoc
mixin _$VehicleImageResponse {
  List<VehicleImage> get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VehicleImageResponseCopyWith<VehicleImageResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleImageResponseCopyWith<$Res> {
  factory $VehicleImageResponseCopyWith(VehicleImageResponse value,
          $Res Function(VehicleImageResponse) then) =
      _$VehicleImageResponseCopyWithImpl<$Res, VehicleImageResponse>;
  @useResult
  $Res call({List<VehicleImage> data});
}

/// @nodoc
class _$VehicleImageResponseCopyWithImpl<$Res,
        $Val extends VehicleImageResponse>
    implements $VehicleImageResponseCopyWith<$Res> {
  _$VehicleImageResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<VehicleImage>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleImageResponseImplCopyWith<$Res>
    implements $VehicleImageResponseCopyWith<$Res> {
  factory _$$VehicleImageResponseImplCopyWith(_$VehicleImageResponseImpl value,
          $Res Function(_$VehicleImageResponseImpl) then) =
      __$$VehicleImageResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<VehicleImage> data});
}

/// @nodoc
class __$$VehicleImageResponseImplCopyWithImpl<$Res>
    extends _$VehicleImageResponseCopyWithImpl<$Res, _$VehicleImageResponseImpl>
    implements _$$VehicleImageResponseImplCopyWith<$Res> {
  __$$VehicleImageResponseImplCopyWithImpl(_$VehicleImageResponseImpl _value,
      $Res Function(_$VehicleImageResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$VehicleImageResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<VehicleImage>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleImageResponseImpl extends _VehicleImageResponse {
  const _$VehicleImageResponseImpl({required final List<VehicleImage> data})
      : _data = data,
        super._();

  factory _$VehicleImageResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleImageResponseImplFromJson(json);

  final List<VehicleImage> _data;
  @override
  List<VehicleImage> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'VehicleImageResponse(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImageResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleImageResponseImplCopyWith<_$VehicleImageResponseImpl>
      get copyWith =>
          __$$VehicleImageResponseImplCopyWithImpl<_$VehicleImageResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleImageResponseImplToJson(
      this,
    );
  }
}

abstract class _VehicleImageResponse extends VehicleImageResponse {
  const factory _VehicleImageResponse(
      {required final List<VehicleImage> data}) = _$VehicleImageResponseImpl;
  const _VehicleImageResponse._() : super._();

  factory _VehicleImageResponse.fromJson(Map<String, dynamic> json) =
      _$VehicleImageResponseImpl.fromJson;

  @override
  List<VehicleImage> get data;
  @override
  @JsonKey(ignore: true)
  _$$VehicleImageResponseImplCopyWith<_$VehicleImageResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

VehicleImage _$VehicleImageFromJson(Map<String, dynamic> json) {
  return _VehicleImage.fromJson(json);
}

/// @nodoc
mixin _$VehicleImage {
  String get vehicleId => throw _privateConstructorUsedError;
  int get time => throw _privateConstructorUsedError;
  int get idx => throw _privateConstructorUsedError;
  int get x => throw _privateConstructorUsedError;
  int get y => throw _privateConstructorUsedError;
  String get address => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VehicleImageCopyWith<VehicleImage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleImageCopyWith<$Res> {
  factory $VehicleImageCopyWith(
          VehicleImage value, $Res Function(VehicleImage) then) =
      _$VehicleImageCopyWithImpl<$Res, VehicleImage>;
  @useResult
  $Res call(
      {String vehicleId,
      int time,
      int idx,
      int x,
      int y,
      String address,
      String url});
}

/// @nodoc
class _$VehicleImageCopyWithImpl<$Res, $Val extends VehicleImage>
    implements $VehicleImageCopyWith<$Res> {
  _$VehicleImageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? time = null,
    Object? idx = null,
    Object? x = null,
    Object? y = null,
    Object? address = null,
    Object? url = null,
  }) {
    return _then(_value.copyWith(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      idx: null == idx
          ? _value.idx
          : idx // ignore: cast_nullable_to_non_nullable
              as int,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as int,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as int,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleImageImplCopyWith<$Res>
    implements $VehicleImageCopyWith<$Res> {
  factory _$$VehicleImageImplCopyWith(
          _$VehicleImageImpl value, $Res Function(_$VehicleImageImpl) then) =
      __$$VehicleImageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String vehicleId,
      int time,
      int idx,
      int x,
      int y,
      String address,
      String url});
}

/// @nodoc
class __$$VehicleImageImplCopyWithImpl<$Res>
    extends _$VehicleImageCopyWithImpl<$Res, _$VehicleImageImpl>
    implements _$$VehicleImageImplCopyWith<$Res> {
  __$$VehicleImageImplCopyWithImpl(
      _$VehicleImageImpl _value, $Res Function(_$VehicleImageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? time = null,
    Object? idx = null,
    Object? x = null,
    Object? y = null,
    Object? address = null,
    Object? url = null,
  }) {
    return _then(_$VehicleImageImpl(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      idx: null == idx
          ? _value.idx
          : idx // ignore: cast_nullable_to_non_nullable
              as int,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as int,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as int,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleImageImpl extends _VehicleImage {
  const _$VehicleImageImpl(
      {required this.vehicleId,
      required this.time,
      required this.idx,
      required this.x,
      required this.y,
      required this.address,
      required this.url})
      : super._();

  factory _$VehicleImageImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleImageImplFromJson(json);

  @override
  final String vehicleId;
  @override
  final int time;
  @override
  final int idx;
  @override
  final int x;
  @override
  final int y;
  @override
  final String address;
  @override
  final String url;

  @override
  String toString() {
    return 'VehicleImage(vehicleId: $vehicleId, time: $time, idx: $idx, x: $x, y: $y, address: $address, url: $url)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImageImpl &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.time, time) || other.time == time) &&
            (identical(other.idx, idx) || other.idx == idx) &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, vehicleId, time, idx, x, y, address, url);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleImageImplCopyWith<_$VehicleImageImpl> get copyWith =>
      __$$VehicleImageImplCopyWithImpl<_$VehicleImageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleImageImplToJson(
      this,
    );
  }
}

abstract class _VehicleImage extends VehicleImage {
  const factory _VehicleImage(
      {required final String vehicleId,
      required final int time,
      required final int idx,
      required final int x,
      required final int y,
      required final String address,
      required final String url}) = _$VehicleImageImpl;
  const _VehicleImage._() : super._();

  factory _VehicleImage.fromJson(Map<String, dynamic> json) =
      _$VehicleImageImpl.fromJson;

  @override
  String get vehicleId;
  @override
  int get time;
  @override
  int get idx;
  @override
  int get x;
  @override
  int get y;
  @override
  String get address;
  @override
  String get url;
  @override
  @JsonKey(ignore: true)
  _$$VehicleImageImplCopyWith<_$VehicleImageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
