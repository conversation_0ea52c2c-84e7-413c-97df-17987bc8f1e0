// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_profile_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserProfileRequest _$UserProfileRequestFromJson(Map<String, dynamic> json) {
  return _UserProfileRequest.fromJson(json);
}

/// @nodoc
mixin _$UserProfileRequest {
  bool get isChangePwd => throw _privateConstructorUsedError;
  String? get phone => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get oldPassword => throw _privateConstructorUsedError;
  String? get password => throw _privateConstructorUsedError;
  String? get confirmPassword => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserProfileRequestCopyWith<UserProfileRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserProfileRequestCopyWith<$Res> {
  factory $UserProfileRequestCopyWith(
          UserProfileRequest value, $Res Function(UserProfileRequest) then) =
      _$UserProfileRequestCopyWithImpl<$Res, UserProfileRequest>;
  @useResult
  $Res call(
      {bool isChangePwd,
      String? phone,
      String? email,
      String? oldPassword,
      String? password,
      String? confirmPassword});
}

/// @nodoc
class _$UserProfileRequestCopyWithImpl<$Res, $Val extends UserProfileRequest>
    implements $UserProfileRequestCopyWith<$Res> {
  _$UserProfileRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isChangePwd = null,
    Object? phone = freezed,
    Object? email = freezed,
    Object? oldPassword = freezed,
    Object? password = freezed,
    Object? confirmPassword = freezed,
  }) {
    return _then(_value.copyWith(
      isChangePwd: null == isChangePwd
          ? _value.isChangePwd
          : isChangePwd // ignore: cast_nullable_to_non_nullable
              as bool,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      oldPassword: freezed == oldPassword
          ? _value.oldPassword
          : oldPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      confirmPassword: freezed == confirmPassword
          ? _value.confirmPassword
          : confirmPassword // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserProfileRequestImplCopyWith<$Res>
    implements $UserProfileRequestCopyWith<$Res> {
  factory _$$UserProfileRequestImplCopyWith(_$UserProfileRequestImpl value,
          $Res Function(_$UserProfileRequestImpl) then) =
      __$$UserProfileRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isChangePwd,
      String? phone,
      String? email,
      String? oldPassword,
      String? password,
      String? confirmPassword});
}

/// @nodoc
class __$$UserProfileRequestImplCopyWithImpl<$Res>
    extends _$UserProfileRequestCopyWithImpl<$Res, _$UserProfileRequestImpl>
    implements _$$UserProfileRequestImplCopyWith<$Res> {
  __$$UserProfileRequestImplCopyWithImpl(_$UserProfileRequestImpl _value,
      $Res Function(_$UserProfileRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isChangePwd = null,
    Object? phone = freezed,
    Object? email = freezed,
    Object? oldPassword = freezed,
    Object? password = freezed,
    Object? confirmPassword = freezed,
  }) {
    return _then(_$UserProfileRequestImpl(
      isChangePwd: null == isChangePwd
          ? _value.isChangePwd
          : isChangePwd // ignore: cast_nullable_to_non_nullable
              as bool,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      oldPassword: freezed == oldPassword
          ? _value.oldPassword
          : oldPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      confirmPassword: freezed == confirmPassword
          ? _value.confirmPassword
          : confirmPassword // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserProfileRequestImpl extends _UserProfileRequest {
  const _$UserProfileRequestImpl(
      {required this.isChangePwd,
      required this.phone,
      required this.email,
      required this.oldPassword,
      required this.password,
      required this.confirmPassword})
      : super._();

  factory _$UserProfileRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserProfileRequestImplFromJson(json);

  @override
  final bool isChangePwd;
  @override
  final String? phone;
  @override
  final String? email;
  @override
  final String? oldPassword;
  @override
  final String? password;
  @override
  final String? confirmPassword;

  @override
  String toString() {
    return 'UserProfileRequest(isChangePwd: $isChangePwd, phone: $phone, email: $email, oldPassword: $oldPassword, password: $password, confirmPassword: $confirmPassword)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserProfileRequestImpl &&
            (identical(other.isChangePwd, isChangePwd) ||
                other.isChangePwd == isChangePwd) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.oldPassword, oldPassword) ||
                other.oldPassword == oldPassword) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.confirmPassword, confirmPassword) ||
                other.confirmPassword == confirmPassword));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, isChangePwd, phone, email,
      oldPassword, password, confirmPassword);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserProfileRequestImplCopyWith<_$UserProfileRequestImpl> get copyWith =>
      __$$UserProfileRequestImplCopyWithImpl<_$UserProfileRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserProfileRequestImplToJson(
      this,
    );
  }
}

abstract class _UserProfileRequest extends UserProfileRequest {
  const factory _UserProfileRequest(
      {required final bool isChangePwd,
      required final String? phone,
      required final String? email,
      required final String? oldPassword,
      required final String? password,
      required final String? confirmPassword}) = _$UserProfileRequestImpl;
  const _UserProfileRequest._() : super._();

  factory _UserProfileRequest.fromJson(Map<String, dynamic> json) =
      _$UserProfileRequestImpl.fromJson;

  @override
  bool get isChangePwd;
  @override
  String? get phone;
  @override
  String? get email;
  @override
  String? get oldPassword;
  @override
  String? get password;
  @override
  String? get confirmPassword;
  @override
  @JsonKey(ignore: true)
  _$$UserProfileRequestImplCopyWith<_$UserProfileRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
