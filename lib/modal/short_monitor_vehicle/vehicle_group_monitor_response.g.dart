// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_group_monitor_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VehicleCompactResponseImpl _$$VehicleCompactResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$VehicleCompactResponseImpl(
      vehicleCompacts: (json['vehicleCompacts'] as List<dynamic>)
          .map((e) => VehicleCompact.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$VehicleCompactResponseImplToJson(
        _$VehicleCompactResponseImpl instance) =>
    <String, dynamic>{
      'vehicleCompacts': instance.vehicleCompacts,
    };

_$VehicleCompactImpl _$$VehicleCompactImplFromJson(Map<String, dynamic> json) =>
    _$VehicleCompactImpl(
      id: json['id'] as String,
      plate: json['plate'] as String,
      vehicleGroup: json['vehicleGroup'] as String,
      vehicleGroupId: json['vehicleGroupId'] as String,
    );

Map<String, dynamic> _$$VehicleCompactImplToJson(
        _$VehicleCompactImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'plate': instance.plate,
      'vehicleGroup': instance.vehicleGroup,
      'vehicleGroupId': instance.vehicleGroupId,
    };

_$VehicleGroupDTOImpl _$$VehicleGroupDTOImplFromJson(
        Map<String, dynamic> json) =>
    _$VehicleGroupDTOImpl(
      vehicleGroup: json['vehicleGroup'] as String,
      vehicelGroupId: json['vehicelGroupId'] as String,
      listVehicleCompact: (json['listVehicleCompact'] as List<dynamic>)
          .map((e) => IdsAndPlate.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$VehicleGroupDTOImplToJson(
        _$VehicleGroupDTOImpl instance) =>
    <String, dynamic>{
      'vehicleGroup': instance.vehicleGroup,
      'vehicelGroupId': instance.vehicelGroupId,
      'listVehicleCompact': instance.listVehicleCompact,
    };

_$IdsAndPlateImpl _$$IdsAndPlateImplFromJson(Map<String, dynamic> json) =>
    _$IdsAndPlateImpl(
      id: json['id'] as String,
      plate: json['plate'] as String,
    );

Map<String, dynamic> _$$IdsAndPlateImplToJson(_$IdsAndPlateImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'plate': instance.plate,
    };
