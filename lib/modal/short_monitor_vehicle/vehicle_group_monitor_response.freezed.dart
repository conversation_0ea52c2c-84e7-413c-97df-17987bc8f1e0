// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_group_monitor_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VehicleCompactResponse _$VehicleCompactResponseFromJson(
    Map<String, dynamic> json) {
  return _VehicleCompactResponse.fromJson(json);
}

/// @nodoc
mixin _$VehicleCompactResponse {
  List<VehicleCompact> get vehicleCompacts =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VehicleCompactResponseCopyWith<VehicleCompactResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleCompactResponseCopyWith<$Res> {
  factory $VehicleCompactResponseCopyWith(VehicleCompactResponse value,
          $Res Function(VehicleCompactResponse) then) =
      _$VehicleCompactResponseCopyWithImpl<$Res, VehicleCompactResponse>;
  @useResult
  $Res call({List<VehicleCompact> vehicleCompacts});
}

/// @nodoc
class _$VehicleCompactResponseCopyWithImpl<$Res,
        $Val extends VehicleCompactResponse>
    implements $VehicleCompactResponseCopyWith<$Res> {
  _$VehicleCompactResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleCompacts = null,
  }) {
    return _then(_value.copyWith(
      vehicleCompacts: null == vehicleCompacts
          ? _value.vehicleCompacts
          : vehicleCompacts // ignore: cast_nullable_to_non_nullable
              as List<VehicleCompact>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleCompactResponseImplCopyWith<$Res>
    implements $VehicleCompactResponseCopyWith<$Res> {
  factory _$$VehicleCompactResponseImplCopyWith(
          _$VehicleCompactResponseImpl value,
          $Res Function(_$VehicleCompactResponseImpl) then) =
      __$$VehicleCompactResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<VehicleCompact> vehicleCompacts});
}

/// @nodoc
class __$$VehicleCompactResponseImplCopyWithImpl<$Res>
    extends _$VehicleCompactResponseCopyWithImpl<$Res,
        _$VehicleCompactResponseImpl>
    implements _$$VehicleCompactResponseImplCopyWith<$Res> {
  __$$VehicleCompactResponseImplCopyWithImpl(
      _$VehicleCompactResponseImpl _value,
      $Res Function(_$VehicleCompactResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleCompacts = null,
  }) {
    return _then(_$VehicleCompactResponseImpl(
      vehicleCompacts: null == vehicleCompacts
          ? _value._vehicleCompacts
          : vehicleCompacts // ignore: cast_nullable_to_non_nullable
              as List<VehicleCompact>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleCompactResponseImpl extends _VehicleCompactResponse {
  const _$VehicleCompactResponseImpl(
      {required final List<VehicleCompact> vehicleCompacts})
      : _vehicleCompacts = vehicleCompacts,
        super._();

  factory _$VehicleCompactResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleCompactResponseImplFromJson(json);

  final List<VehicleCompact> _vehicleCompacts;
  @override
  List<VehicleCompact> get vehicleCompacts {
    if (_vehicleCompacts is EqualUnmodifiableListView) return _vehicleCompacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vehicleCompacts);
  }

  @override
  String toString() {
    return 'VehicleCompactResponse(vehicleCompacts: $vehicleCompacts)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleCompactResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._vehicleCompacts, _vehicleCompacts));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_vehicleCompacts));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleCompactResponseImplCopyWith<_$VehicleCompactResponseImpl>
      get copyWith => __$$VehicleCompactResponseImplCopyWithImpl<
          _$VehicleCompactResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleCompactResponseImplToJson(
      this,
    );
  }
}

abstract class _VehicleCompactResponse extends VehicleCompactResponse {
  const factory _VehicleCompactResponse(
          {required final List<VehicleCompact> vehicleCompacts}) =
      _$VehicleCompactResponseImpl;
  const _VehicleCompactResponse._() : super._();

  factory _VehicleCompactResponse.fromJson(Map<String, dynamic> json) =
      _$VehicleCompactResponseImpl.fromJson;

  @override
  List<VehicleCompact> get vehicleCompacts;
  @override
  @JsonKey(ignore: true)
  _$$VehicleCompactResponseImplCopyWith<_$VehicleCompactResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

VehicleCompact _$VehicleCompactFromJson(Map<String, dynamic> json) {
  return _VehicleCompact.fromJson(json);
}

/// @nodoc
mixin _$VehicleCompact {
  String get id => throw _privateConstructorUsedError;
  String get plate => throw _privateConstructorUsedError;
  String get vehicleGroup => throw _privateConstructorUsedError;
  String get vehicleGroupId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VehicleCompactCopyWith<VehicleCompact> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleCompactCopyWith<$Res> {
  factory $VehicleCompactCopyWith(
          VehicleCompact value, $Res Function(VehicleCompact) then) =
      _$VehicleCompactCopyWithImpl<$Res, VehicleCompact>;
  @useResult
  $Res call(
      {String id, String plate, String vehicleGroup, String vehicleGroupId});
}

/// @nodoc
class _$VehicleCompactCopyWithImpl<$Res, $Val extends VehicleCompact>
    implements $VehicleCompactCopyWith<$Res> {
  _$VehicleCompactCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? vehicleGroup = null,
    Object? vehicleGroupId = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleGroup: null == vehicleGroup
          ? _value.vehicleGroup
          : vehicleGroup // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleGroupId: null == vehicleGroupId
          ? _value.vehicleGroupId
          : vehicleGroupId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleCompactImplCopyWith<$Res>
    implements $VehicleCompactCopyWith<$Res> {
  factory _$$VehicleCompactImplCopyWith(_$VehicleCompactImpl value,
          $Res Function(_$VehicleCompactImpl) then) =
      __$$VehicleCompactImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, String plate, String vehicleGroup, String vehicleGroupId});
}

/// @nodoc
class __$$VehicleCompactImplCopyWithImpl<$Res>
    extends _$VehicleCompactCopyWithImpl<$Res, _$VehicleCompactImpl>
    implements _$$VehicleCompactImplCopyWith<$Res> {
  __$$VehicleCompactImplCopyWithImpl(
      _$VehicleCompactImpl _value, $Res Function(_$VehicleCompactImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? vehicleGroup = null,
    Object? vehicleGroupId = null,
  }) {
    return _then(_$VehicleCompactImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleGroup: null == vehicleGroup
          ? _value.vehicleGroup
          : vehicleGroup // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleGroupId: null == vehicleGroupId
          ? _value.vehicleGroupId
          : vehicleGroupId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleCompactImpl extends _VehicleCompact {
  const _$VehicleCompactImpl(
      {required this.id,
      required this.plate,
      required this.vehicleGroup,
      required this.vehicleGroupId})
      : super._();

  factory _$VehicleCompactImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleCompactImplFromJson(json);

  @override
  final String id;
  @override
  final String plate;
  @override
  final String vehicleGroup;
  @override
  final String vehicleGroupId;

  @override
  String toString() {
    return 'VehicleCompact(id: $id, plate: $plate, vehicleGroup: $vehicleGroup, vehicleGroupId: $vehicleGroupId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleCompactImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.plate, plate) || other.plate == plate) &&
            (identical(other.vehicleGroup, vehicleGroup) ||
                other.vehicleGroup == vehicleGroup) &&
            (identical(other.vehicleGroupId, vehicleGroupId) ||
                other.vehicleGroupId == vehicleGroupId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, plate, vehicleGroup, vehicleGroupId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleCompactImplCopyWith<_$VehicleCompactImpl> get copyWith =>
      __$$VehicleCompactImplCopyWithImpl<_$VehicleCompactImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleCompactImplToJson(
      this,
    );
  }
}

abstract class _VehicleCompact extends VehicleCompact {
  const factory _VehicleCompact(
      {required final String id,
      required final String plate,
      required final String vehicleGroup,
      required final String vehicleGroupId}) = _$VehicleCompactImpl;
  const _VehicleCompact._() : super._();

  factory _VehicleCompact.fromJson(Map<String, dynamic> json) =
      _$VehicleCompactImpl.fromJson;

  @override
  String get id;
  @override
  String get plate;
  @override
  String get vehicleGroup;
  @override
  String get vehicleGroupId;
  @override
  @JsonKey(ignore: true)
  _$$VehicleCompactImplCopyWith<_$VehicleCompactImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VehicleGroupDTO _$VehicleGroupDTOFromJson(Map<String, dynamic> json) {
  return _VehicleGroupDTO.fromJson(json);
}

/// @nodoc
mixin _$VehicleGroupDTO {
  String get vehicleGroup => throw _privateConstructorUsedError;
  String get vehicelGroupId => throw _privateConstructorUsedError;
  List<IdsAndPlate> get listVehicleCompact =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VehicleGroupDTOCopyWith<VehicleGroupDTO> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleGroupDTOCopyWith<$Res> {
  factory $VehicleGroupDTOCopyWith(
          VehicleGroupDTO value, $Res Function(VehicleGroupDTO) then) =
      _$VehicleGroupDTOCopyWithImpl<$Res, VehicleGroupDTO>;
  @useResult
  $Res call(
      {String vehicleGroup,
      String vehicelGroupId,
      List<IdsAndPlate> listVehicleCompact});
}

/// @nodoc
class _$VehicleGroupDTOCopyWithImpl<$Res, $Val extends VehicleGroupDTO>
    implements $VehicleGroupDTOCopyWith<$Res> {
  _$VehicleGroupDTOCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleGroup = null,
    Object? vehicelGroupId = null,
    Object? listVehicleCompact = null,
  }) {
    return _then(_value.copyWith(
      vehicleGroup: null == vehicleGroup
          ? _value.vehicleGroup
          : vehicleGroup // ignore: cast_nullable_to_non_nullable
              as String,
      vehicelGroupId: null == vehicelGroupId
          ? _value.vehicelGroupId
          : vehicelGroupId // ignore: cast_nullable_to_non_nullable
              as String,
      listVehicleCompact: null == listVehicleCompact
          ? _value.listVehicleCompact
          : listVehicleCompact // ignore: cast_nullable_to_non_nullable
              as List<IdsAndPlate>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleGroupDTOImplCopyWith<$Res>
    implements $VehicleGroupDTOCopyWith<$Res> {
  factory _$$VehicleGroupDTOImplCopyWith(_$VehicleGroupDTOImpl value,
          $Res Function(_$VehicleGroupDTOImpl) then) =
      __$$VehicleGroupDTOImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String vehicleGroup,
      String vehicelGroupId,
      List<IdsAndPlate> listVehicleCompact});
}

/// @nodoc
class __$$VehicleGroupDTOImplCopyWithImpl<$Res>
    extends _$VehicleGroupDTOCopyWithImpl<$Res, _$VehicleGroupDTOImpl>
    implements _$$VehicleGroupDTOImplCopyWith<$Res> {
  __$$VehicleGroupDTOImplCopyWithImpl(
      _$VehicleGroupDTOImpl _value, $Res Function(_$VehicleGroupDTOImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleGroup = null,
    Object? vehicelGroupId = null,
    Object? listVehicleCompact = null,
  }) {
    return _then(_$VehicleGroupDTOImpl(
      vehicleGroup: null == vehicleGroup
          ? _value.vehicleGroup
          : vehicleGroup // ignore: cast_nullable_to_non_nullable
              as String,
      vehicelGroupId: null == vehicelGroupId
          ? _value.vehicelGroupId
          : vehicelGroupId // ignore: cast_nullable_to_non_nullable
              as String,
      listVehicleCompact: null == listVehicleCompact
          ? _value._listVehicleCompact
          : listVehicleCompact // ignore: cast_nullable_to_non_nullable
              as List<IdsAndPlate>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleGroupDTOImpl extends _VehicleGroupDTO {
  const _$VehicleGroupDTOImpl(
      {required this.vehicleGroup,
      required this.vehicelGroupId,
      required final List<IdsAndPlate> listVehicleCompact})
      : _listVehicleCompact = listVehicleCompact,
        super._();

  factory _$VehicleGroupDTOImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleGroupDTOImplFromJson(json);

  @override
  final String vehicleGroup;
  @override
  final String vehicelGroupId;
  final List<IdsAndPlate> _listVehicleCompact;
  @override
  List<IdsAndPlate> get listVehicleCompact {
    if (_listVehicleCompact is EqualUnmodifiableListView)
      return _listVehicleCompact;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listVehicleCompact);
  }

  @override
  String toString() {
    return 'VehicleGroupDTO(vehicleGroup: $vehicleGroup, vehicelGroupId: $vehicelGroupId, listVehicleCompact: $listVehicleCompact)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleGroupDTOImpl &&
            (identical(other.vehicleGroup, vehicleGroup) ||
                other.vehicleGroup == vehicleGroup) &&
            (identical(other.vehicelGroupId, vehicelGroupId) ||
                other.vehicelGroupId == vehicelGroupId) &&
            const DeepCollectionEquality()
                .equals(other._listVehicleCompact, _listVehicleCompact));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, vehicleGroup, vehicelGroupId,
      const DeepCollectionEquality().hash(_listVehicleCompact));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleGroupDTOImplCopyWith<_$VehicleGroupDTOImpl> get copyWith =>
      __$$VehicleGroupDTOImplCopyWithImpl<_$VehicleGroupDTOImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleGroupDTOImplToJson(
      this,
    );
  }
}

abstract class _VehicleGroupDTO extends VehicleGroupDTO {
  const factory _VehicleGroupDTO(
          {required final String vehicleGroup,
          required final String vehicelGroupId,
          required final List<IdsAndPlate> listVehicleCompact}) =
      _$VehicleGroupDTOImpl;
  const _VehicleGroupDTO._() : super._();

  factory _VehicleGroupDTO.fromJson(Map<String, dynamic> json) =
      _$VehicleGroupDTOImpl.fromJson;

  @override
  String get vehicleGroup;
  @override
  String get vehicelGroupId;
  @override
  List<IdsAndPlate> get listVehicleCompact;
  @override
  @JsonKey(ignore: true)
  _$$VehicleGroupDTOImplCopyWith<_$VehicleGroupDTOImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

IdsAndPlate _$IdsAndPlateFromJson(Map<String, dynamic> json) {
  return _IdsAndPlate.fromJson(json);
}

/// @nodoc
mixin _$IdsAndPlate {
  String get id => throw _privateConstructorUsedError;
  String get plate => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $IdsAndPlateCopyWith<IdsAndPlate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdsAndPlateCopyWith<$Res> {
  factory $IdsAndPlateCopyWith(
          IdsAndPlate value, $Res Function(IdsAndPlate) then) =
      _$IdsAndPlateCopyWithImpl<$Res, IdsAndPlate>;
  @useResult
  $Res call({String id, String plate});
}

/// @nodoc
class _$IdsAndPlateCopyWithImpl<$Res, $Val extends IdsAndPlate>
    implements $IdsAndPlateCopyWith<$Res> {
  _$IdsAndPlateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IdsAndPlateImplCopyWith<$Res>
    implements $IdsAndPlateCopyWith<$Res> {
  factory _$$IdsAndPlateImplCopyWith(
          _$IdsAndPlateImpl value, $Res Function(_$IdsAndPlateImpl) then) =
      __$$IdsAndPlateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String plate});
}

/// @nodoc
class __$$IdsAndPlateImplCopyWithImpl<$Res>
    extends _$IdsAndPlateCopyWithImpl<$Res, _$IdsAndPlateImpl>
    implements _$$IdsAndPlateImplCopyWith<$Res> {
  __$$IdsAndPlateImplCopyWithImpl(
      _$IdsAndPlateImpl _value, $Res Function(_$IdsAndPlateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
  }) {
    return _then(_$IdsAndPlateImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$IdsAndPlateImpl extends _IdsAndPlate {
  const _$IdsAndPlateImpl({required this.id, required this.plate}) : super._();

  factory _$IdsAndPlateImpl.fromJson(Map<String, dynamic> json) =>
      _$$IdsAndPlateImplFromJson(json);

  @override
  final String id;
  @override
  final String plate;

  @override
  String toString() {
    return 'IdsAndPlate(id: $id, plate: $plate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdsAndPlateImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.plate, plate) || other.plate == plate));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, plate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$IdsAndPlateImplCopyWith<_$IdsAndPlateImpl> get copyWith =>
      __$$IdsAndPlateImplCopyWithImpl<_$IdsAndPlateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IdsAndPlateImplToJson(
      this,
    );
  }
}

abstract class _IdsAndPlate extends IdsAndPlate {
  const factory _IdsAndPlate(
      {required final String id,
      required final String plate}) = _$IdsAndPlateImpl;
  const _IdsAndPlate._() : super._();

  factory _IdsAndPlate.fromJson(Map<String, dynamic> json) =
      _$IdsAndPlateImpl.fromJson;

  @override
  String get id;
  @override
  String get plate;
  @override
  @JsonKey(ignore: true)
  _$$IdsAndPlateImplCopyWith<_$IdsAndPlateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
