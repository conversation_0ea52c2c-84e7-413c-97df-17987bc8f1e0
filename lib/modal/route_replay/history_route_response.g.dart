// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'history_route_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$HistoryRouteResponseImpl _$$HistoryRouteResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$HistoryRouteResponseImpl(
      vehicleType: json['vehicleType'] as String,
      vehicleIcon: (json['vehicleIcon'] as num).toInt(),
      route: (json['route'] as List<dynamic>)
          .map((e) => HistoryRoute.fromJson(e as Map<String, dynamic>))
          .toList(),
      drivers: (json['drivers'] as List<dynamic>)
          .map((e) => Driver.fromJson(e as Map<String, dynamic>))
          .toList(),
      keys: HistoryKeys.fromJson(json['keys'] as Map<String, dynamic>),
      summary: HistorySymmaryReport.fromJson(
          json['summary'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$HistoryRouteResponseImplToJson(
        _$HistoryRouteResponseImpl instance) =>
    <String, dynamic>{
      'vehicleType': instance.vehicleType,
      'vehicleIcon': instance.vehicleIcon,
      'route': instance.route,
      'drivers': instance.drivers,
      'keys': instance.keys,
      'summary': instance.summary,
    };

_$HistoryRouteImpl _$$HistoryRouteImplFromJson(Map<String, dynamic> json) =>
    _$HistoryRouteImpl(
      id: (json['id'] as num).toInt(),
      driverId: json['driverId'] as String?,
      gpsTime: (json['gpsTime'] as num).toInt(),
      speed: (json['speed'] as num).toInt(),
      x: (json['x'] as num).toInt(),
      y: (json['y'] as num).toInt(),
      status: (json['status'] as num).toInt(),
      info: json['info'] as String?,
      heading: (json['heading'] as num?)?.toInt(),
      direction: json['direction'] as String,
      satellite: (json['satellite'] as num).toInt(),
      maxSpeed: (json['maxSpeed'] as num).toInt(),
      gpsMileage: (json['gpsMileage'] as num).toInt(),
      input: (json['input'] as num?)?.toInt(),
      inputs: json['inputs'] == null
          ? null
          : Inputs.fromJson(json['inputs'] as Map<String, dynamic>),
      output: (json['output'] as num?)?.toInt(),
      roadSpeed: (json['roadSpeed'] as num).toInt(),
      roadName: json['roadName'] as String?,
      eventId: (json['eventId'] as num).toInt(),
      deviceTypeId: (json['deviceTypeId'] as num).toInt(),
      isOverSpeed: json['isOverSpeed'] as bool,
      isOverSpeedByRoad: json['isOverSpeedByRoad'] as bool,
      voltage: (json['voltage'] as num).toInt(),
      voltagePercentage: (json['voltagePercentage'] as num).toInt(),
      idleTime: (json['idleTime'] as num).toInt(),
      vehicleMaxSpeed: (json['vehicleMaxSpeed'] as num).toInt(),
      statusColor: json['statusColor'] as String,
      sensors: (json['sensors'] as List<dynamic>?)
          ?.map((e) => Sensor.fromJson(e as Map<String, dynamic>))
          .toList(),
      inputInfo:
          (json['inputInfo'] as List<dynamic>).map((e) => e as bool).toList(),
      outputInfo:
          (json['outputInfo'] as List<dynamic>).map((e) => e as bool).toList(),
    );

Map<String, dynamic> _$$HistoryRouteImplToJson(_$HistoryRouteImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'driverId': instance.driverId,
      'gpsTime': instance.gpsTime,
      'speed': instance.speed,
      'x': instance.x,
      'y': instance.y,
      'status': instance.status,
      'info': instance.info,
      'heading': instance.heading,
      'direction': instance.direction,
      'satellite': instance.satellite,
      'maxSpeed': instance.maxSpeed,
      'gpsMileage': instance.gpsMileage,
      'input': instance.input,
      'inputs': instance.inputs,
      'output': instance.output,
      'roadSpeed': instance.roadSpeed,
      'roadName': instance.roadName,
      'eventId': instance.eventId,
      'deviceTypeId': instance.deviceTypeId,
      'isOverSpeed': instance.isOverSpeed,
      'isOverSpeedByRoad': instance.isOverSpeedByRoad,
      'voltage': instance.voltage,
      'voltagePercentage': instance.voltagePercentage,
      'idleTime': instance.idleTime,
      'vehicleMaxSpeed': instance.vehicleMaxSpeed,
      'statusColor': instance.statusColor,
      'sensors': instance.sensors,
      'inputInfo': instance.inputInfo,
      'outputInfo': instance.outputInfo,
    };

_$HistorySymmaryReportImpl _$$HistorySymmaryReportImplFromJson(
        Map<String, dynamic> json) =>
    _$HistorySymmaryReportImpl(
      from: json['from'] as String,
      to: json['to'] as String,
      duration: (json['duration'] as num).toInt(),
      distance: (json['distance'] as num).toInt(),
      startTime: (json['startTime'] as num).toInt(),
      endTime: (json['endTime'] as num).toInt(),
      maxSpeed: (json['maxSpeed'] as num).toInt(),
      overSpeedCount: (json['overSpeedCount'] as num).toInt(),
      idleCount: (json['idleCount'] as num).toInt(),
      timeline: (json['timeline'] as List<dynamic>)
          .map((e) => Timeline.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$HistorySymmaryReportImplToJson(
        _$HistorySymmaryReportImpl instance) =>
    <String, dynamic>{
      'from': instance.from,
      'to': instance.to,
      'duration': instance.duration,
      'distance': instance.distance,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'maxSpeed': instance.maxSpeed,
      'overSpeedCount': instance.overSpeedCount,
      'idleCount': instance.idleCount,
      'timeline': instance.timeline,
    };

_$HistoryKeysImpl _$$HistoryKeysImplFromJson(Map<String, dynamic> json) =>
    _$HistoryKeysImpl(
      additionalProps1: json['additionalProps1'] as String?,
      additionalProps2: json['additionalProps2'] as String?,
      additionalProps3: json['additionalProps3'] as String?,
    );

Map<String, dynamic> _$$HistoryKeysImplToJson(_$HistoryKeysImpl instance) =>
    <String, dynamic>{
      'additionalProps1': instance.additionalProps1,
      'additionalProps2': instance.additionalProps2,
      'additionalProps3': instance.additionalProps3,
    };

_$TimelineImpl _$$TimelineImplFromJson(Map<String, dynamic> json) =>
    _$TimelineImpl(
      x: (json['x'] as num).toInt(),
      y: (json['y'] as num).toInt(),
      duration: (json['duration'] as num).toInt(),
      type: (json['type'] as num).toInt(),
      id: (json['id'] as num).toInt(),
      from: (json['from'] as num).toInt(),
      to: (json['to'] as num).toInt(),
      address: json['address'] as String,
      toAddress: json['toAddress'] as String,
    );

Map<String, dynamic> _$$TimelineImplToJson(_$TimelineImpl instance) =>
    <String, dynamic>{
      'x': instance.x,
      'y': instance.y,
      'duration': instance.duration,
      'type': instance.type,
      'id': instance.id,
      'from': instance.from,
      'to': instance.to,
      'address': instance.address,
      'toAddress': instance.toAddress,
    };
