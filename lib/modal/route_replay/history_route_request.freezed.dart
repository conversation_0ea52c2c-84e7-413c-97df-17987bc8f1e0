// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'history_route_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

HistoryRouteRequest _$HistoryRouteRequestFromJson(Map<String, dynamic> json) {
  return _HistoryRouteRequest.fromJson(json);
}

/// @nodoc
mixin _$HistoryRouteRequest {
  String get vehicleId => throw _privateConstructorUsedError;
  String get from => throw _privateConstructorUsedError;
  String get to => throw _privateConstructorUsedError;
  bool get isOverSpeedByRoad => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HistoryRouteRequestCopyWith<HistoryRouteRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryRouteRequestCopyWith<$Res> {
  factory $HistoryRouteRequestCopyWith(
          HistoryRouteRequest value, $Res Function(HistoryRouteRequest) then) =
      _$HistoryRouteRequestCopyWithImpl<$Res, HistoryRouteRequest>;
  @useResult
  $Res call({String vehicleId, String from, String to, bool isOverSpeedByRoad});
}

/// @nodoc
class _$HistoryRouteRequestCopyWithImpl<$Res, $Val extends HistoryRouteRequest>
    implements $HistoryRouteRequestCopyWith<$Res> {
  _$HistoryRouteRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? from = null,
    Object? to = null,
    Object? isOverSpeedByRoad = null,
  }) {
    return _then(_value.copyWith(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HistoryRouteRequestImplCopyWith<$Res>
    implements $HistoryRouteRequestCopyWith<$Res> {
  factory _$$HistoryRouteRequestImplCopyWith(_$HistoryRouteRequestImpl value,
          $Res Function(_$HistoryRouteRequestImpl) then) =
      __$$HistoryRouteRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String vehicleId, String from, String to, bool isOverSpeedByRoad});
}

/// @nodoc
class __$$HistoryRouteRequestImplCopyWithImpl<$Res>
    extends _$HistoryRouteRequestCopyWithImpl<$Res, _$HistoryRouteRequestImpl>
    implements _$$HistoryRouteRequestImplCopyWith<$Res> {
  __$$HistoryRouteRequestImplCopyWithImpl(_$HistoryRouteRequestImpl _value,
      $Res Function(_$HistoryRouteRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = null,
    Object? from = null,
    Object? to = null,
    Object? isOverSpeedByRoad = null,
  }) {
    return _then(_$HistoryRouteRequestImpl(
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HistoryRouteRequestImpl extends _HistoryRouteRequest {
  const _$HistoryRouteRequestImpl(
      {required this.vehicleId,
      required this.from,
      required this.to,
      required this.isOverSpeedByRoad})
      : super._();

  factory _$HistoryRouteRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistoryRouteRequestImplFromJson(json);

  @override
  final String vehicleId;
  @override
  final String from;
  @override
  final String to;
  @override
  final bool isOverSpeedByRoad;

  @override
  String toString() {
    return 'HistoryRouteRequest(vehicleId: $vehicleId, from: $from, to: $to, isOverSpeedByRoad: $isOverSpeedByRoad)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryRouteRequestImpl &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.from, from) || other.from == from) &&
            (identical(other.to, to) || other.to == to) &&
            (identical(other.isOverSpeedByRoad, isOverSpeedByRoad) ||
                other.isOverSpeedByRoad == isOverSpeedByRoad));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, vehicleId, from, to, isOverSpeedByRoad);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoryRouteRequestImplCopyWith<_$HistoryRouteRequestImpl> get copyWith =>
      __$$HistoryRouteRequestImplCopyWithImpl<_$HistoryRouteRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HistoryRouteRequestImplToJson(
      this,
    );
  }
}

abstract class _HistoryRouteRequest extends HistoryRouteRequest {
  const factory _HistoryRouteRequest(
      {required final String vehicleId,
      required final String from,
      required final String to,
      required final bool isOverSpeedByRoad}) = _$HistoryRouteRequestImpl;
  const _HistoryRouteRequest._() : super._();

  factory _HistoryRouteRequest.fromJson(Map<String, dynamic> json) =
      _$HistoryRouteRequestImpl.fromJson;

  @override
  String get vehicleId;
  @override
  String get from;
  @override
  String get to;
  @override
  bool get isOverSpeedByRoad;
  @override
  @JsonKey(ignore: true)
  _$$HistoryRouteRequestImplCopyWith<_$HistoryRouteRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
