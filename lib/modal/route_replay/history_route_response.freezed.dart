// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'history_route_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

HistoryRouteResponse _$HistoryRouteResponseFromJson(Map<String, dynamic> json) {
  return _HistoryRouteResponse.fromJson(json);
}

/// @nodoc
mixin _$HistoryRouteResponse {
  String get vehicleType => throw _privateConstructorUsedError;
  int get vehicleIcon => throw _privateConstructorUsedError;
  List<HistoryRoute> get route => throw _privateConstructorUsedError;
  List<Driver> get drivers => throw _privateConstructorUsedError;
  HistoryKeys get keys => throw _privateConstructorUsedError;
  HistorySymmaryReport get summary => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HistoryRouteResponseCopyWith<HistoryRouteResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryRouteResponseCopyWith<$Res> {
  factory $HistoryRouteResponseCopyWith(HistoryRouteResponse value,
          $Res Function(HistoryRouteResponse) then) =
      _$HistoryRouteResponseCopyWithImpl<$Res, HistoryRouteResponse>;
  @useResult
  $Res call(
      {String vehicleType,
      int vehicleIcon,
      List<HistoryRoute> route,
      List<Driver> drivers,
      HistoryKeys keys,
      HistorySymmaryReport summary});

  $HistoryKeysCopyWith<$Res> get keys;
  $HistorySymmaryReportCopyWith<$Res> get summary;
}

/// @nodoc
class _$HistoryRouteResponseCopyWithImpl<$Res,
        $Val extends HistoryRouteResponse>
    implements $HistoryRouteResponseCopyWith<$Res> {
  _$HistoryRouteResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleType = null,
    Object? vehicleIcon = null,
    Object? route = null,
    Object? drivers = null,
    Object? keys = null,
    Object? summary = null,
  }) {
    return _then(_value.copyWith(
      vehicleType: null == vehicleType
          ? _value.vehicleType
          : vehicleType // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleIcon: null == vehicleIcon
          ? _value.vehicleIcon
          : vehicleIcon // ignore: cast_nullable_to_non_nullable
              as int,
      route: null == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as List<HistoryRoute>,
      drivers: null == drivers
          ? _value.drivers
          : drivers // ignore: cast_nullable_to_non_nullable
              as List<Driver>,
      keys: null == keys
          ? _value.keys
          : keys // ignore: cast_nullable_to_non_nullable
              as HistoryKeys,
      summary: null == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as HistorySymmaryReport,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $HistoryKeysCopyWith<$Res> get keys {
    return $HistoryKeysCopyWith<$Res>(_value.keys, (value) {
      return _then(_value.copyWith(keys: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $HistorySymmaryReportCopyWith<$Res> get summary {
    return $HistorySymmaryReportCopyWith<$Res>(_value.summary, (value) {
      return _then(_value.copyWith(summary: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$HistoryRouteResponseImplCopyWith<$Res>
    implements $HistoryRouteResponseCopyWith<$Res> {
  factory _$$HistoryRouteResponseImplCopyWith(_$HistoryRouteResponseImpl value,
          $Res Function(_$HistoryRouteResponseImpl) then) =
      __$$HistoryRouteResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String vehicleType,
      int vehicleIcon,
      List<HistoryRoute> route,
      List<Driver> drivers,
      HistoryKeys keys,
      HistorySymmaryReport summary});

  @override
  $HistoryKeysCopyWith<$Res> get keys;
  @override
  $HistorySymmaryReportCopyWith<$Res> get summary;
}

/// @nodoc
class __$$HistoryRouteResponseImplCopyWithImpl<$Res>
    extends _$HistoryRouteResponseCopyWithImpl<$Res, _$HistoryRouteResponseImpl>
    implements _$$HistoryRouteResponseImplCopyWith<$Res> {
  __$$HistoryRouteResponseImplCopyWithImpl(_$HistoryRouteResponseImpl _value,
      $Res Function(_$HistoryRouteResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleType = null,
    Object? vehicleIcon = null,
    Object? route = null,
    Object? drivers = null,
    Object? keys = null,
    Object? summary = null,
  }) {
    return _then(_$HistoryRouteResponseImpl(
      vehicleType: null == vehicleType
          ? _value.vehicleType
          : vehicleType // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleIcon: null == vehicleIcon
          ? _value.vehicleIcon
          : vehicleIcon // ignore: cast_nullable_to_non_nullable
              as int,
      route: null == route
          ? _value._route
          : route // ignore: cast_nullable_to_non_nullable
              as List<HistoryRoute>,
      drivers: null == drivers
          ? _value._drivers
          : drivers // ignore: cast_nullable_to_non_nullable
              as List<Driver>,
      keys: null == keys
          ? _value.keys
          : keys // ignore: cast_nullable_to_non_nullable
              as HistoryKeys,
      summary: null == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as HistorySymmaryReport,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HistoryRouteResponseImpl extends _HistoryRouteResponse {
  const _$HistoryRouteResponseImpl(
      {required this.vehicleType,
      required this.vehicleIcon,
      required final List<HistoryRoute> route,
      required final List<Driver> drivers,
      required this.keys,
      required this.summary})
      : _route = route,
        _drivers = drivers,
        super._();

  factory _$HistoryRouteResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistoryRouteResponseImplFromJson(json);

  @override
  final String vehicleType;
  @override
  final int vehicleIcon;
  final List<HistoryRoute> _route;
  @override
  List<HistoryRoute> get route {
    if (_route is EqualUnmodifiableListView) return _route;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_route);
  }

  final List<Driver> _drivers;
  @override
  List<Driver> get drivers {
    if (_drivers is EqualUnmodifiableListView) return _drivers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_drivers);
  }

  @override
  final HistoryKeys keys;
  @override
  final HistorySymmaryReport summary;

  @override
  String toString() {
    return 'HistoryRouteResponse(vehicleType: $vehicleType, vehicleIcon: $vehicleIcon, route: $route, drivers: $drivers, keys: $keys, summary: $summary)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryRouteResponseImpl &&
            (identical(other.vehicleType, vehicleType) ||
                other.vehicleType == vehicleType) &&
            (identical(other.vehicleIcon, vehicleIcon) ||
                other.vehicleIcon == vehicleIcon) &&
            const DeepCollectionEquality().equals(other._route, _route) &&
            const DeepCollectionEquality().equals(other._drivers, _drivers) &&
            (identical(other.keys, keys) || other.keys == keys) &&
            (identical(other.summary, summary) || other.summary == summary));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      vehicleType,
      vehicleIcon,
      const DeepCollectionEquality().hash(_route),
      const DeepCollectionEquality().hash(_drivers),
      keys,
      summary);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoryRouteResponseImplCopyWith<_$HistoryRouteResponseImpl>
      get copyWith =>
          __$$HistoryRouteResponseImplCopyWithImpl<_$HistoryRouteResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HistoryRouteResponseImplToJson(
      this,
    );
  }
}

abstract class _HistoryRouteResponse extends HistoryRouteResponse {
  const factory _HistoryRouteResponse(
          {required final String vehicleType,
          required final int vehicleIcon,
          required final List<HistoryRoute> route,
          required final List<Driver> drivers,
          required final HistoryKeys keys,
          required final HistorySymmaryReport summary}) =
      _$HistoryRouteResponseImpl;
  const _HistoryRouteResponse._() : super._();

  factory _HistoryRouteResponse.fromJson(Map<String, dynamic> json) =
      _$HistoryRouteResponseImpl.fromJson;

  @override
  String get vehicleType;
  @override
  int get vehicleIcon;
  @override
  List<HistoryRoute> get route;
  @override
  List<Driver> get drivers;
  @override
  HistoryKeys get keys;
  @override
  HistorySymmaryReport get summary;
  @override
  @JsonKey(ignore: true)
  _$$HistoryRouteResponseImplCopyWith<_$HistoryRouteResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

HistoryRoute _$HistoryRouteFromJson(Map<String, dynamic> json) {
  return _HistoryRoute.fromJson(json);
}

/// @nodoc
mixin _$HistoryRoute {
  int get id => throw _privateConstructorUsedError;
  String? get driverId => throw _privateConstructorUsedError;
  int get gpsTime => throw _privateConstructorUsedError;
  int get speed => throw _privateConstructorUsedError;
  int get x => throw _privateConstructorUsedError;
  int get y => throw _privateConstructorUsedError;
  int get status => throw _privateConstructorUsedError;
  String? get info => throw _privateConstructorUsedError; // address
  int? get heading => throw _privateConstructorUsedError;
  String get direction => throw _privateConstructorUsedError;
  int get satellite => throw _privateConstructorUsedError;
  int get maxSpeed => throw _privateConstructorUsedError;
  int get gpsMileage => throw _privateConstructorUsedError;
  int? get input => throw _privateConstructorUsedError;
  Inputs? get inputs => throw _privateConstructorUsedError;
  int? get output => throw _privateConstructorUsedError;
  int get roadSpeed => throw _privateConstructorUsedError;
  String? get roadName => throw _privateConstructorUsedError;
  int get eventId => throw _privateConstructorUsedError;
  int get deviceTypeId => throw _privateConstructorUsedError;
  bool get isOverSpeed => throw _privateConstructorUsedError;
  bool get isOverSpeedByRoad => throw _privateConstructorUsedError;
  int get voltage => throw _privateConstructorUsedError;
  int get voltagePercentage => throw _privateConstructorUsedError;
  int get idleTime => throw _privateConstructorUsedError;
  int get vehicleMaxSpeed => throw _privateConstructorUsedError;
  String get statusColor => throw _privateConstructorUsedError;
  List<Sensor>? get sensors => throw _privateConstructorUsedError;
  List<bool> get inputInfo => throw _privateConstructorUsedError;
  List<bool> get outputInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HistoryRouteCopyWith<HistoryRoute> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryRouteCopyWith<$Res> {
  factory $HistoryRouteCopyWith(
          HistoryRoute value, $Res Function(HistoryRoute) then) =
      _$HistoryRouteCopyWithImpl<$Res, HistoryRoute>;
  @useResult
  $Res call(
      {int id,
      String? driverId,
      int gpsTime,
      int speed,
      int x,
      int y,
      int status,
      String? info,
      int? heading,
      String direction,
      int satellite,
      int maxSpeed,
      int gpsMileage,
      int? input,
      Inputs? inputs,
      int? output,
      int roadSpeed,
      String? roadName,
      int eventId,
      int deviceTypeId,
      bool isOverSpeed,
      bool isOverSpeedByRoad,
      int voltage,
      int voltagePercentage,
      int idleTime,
      int vehicleMaxSpeed,
      String statusColor,
      List<Sensor>? sensors,
      List<bool> inputInfo,
      List<bool> outputInfo});

  $InputsCopyWith<$Res>? get inputs;
}

/// @nodoc
class _$HistoryRouteCopyWithImpl<$Res, $Val extends HistoryRoute>
    implements $HistoryRouteCopyWith<$Res> {
  _$HistoryRouteCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? driverId = freezed,
    Object? gpsTime = null,
    Object? speed = null,
    Object? x = null,
    Object? y = null,
    Object? status = null,
    Object? info = freezed,
    Object? heading = freezed,
    Object? direction = null,
    Object? satellite = null,
    Object? maxSpeed = null,
    Object? gpsMileage = null,
    Object? input = freezed,
    Object? inputs = freezed,
    Object? output = freezed,
    Object? roadSpeed = null,
    Object? roadName = freezed,
    Object? eventId = null,
    Object? deviceTypeId = null,
    Object? isOverSpeed = null,
    Object? isOverSpeedByRoad = null,
    Object? voltage = null,
    Object? voltagePercentage = null,
    Object? idleTime = null,
    Object? vehicleMaxSpeed = null,
    Object? statusColor = null,
    Object? sensors = freezed,
    Object? inputInfo = null,
    Object? outputInfo = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      driverId: freezed == driverId
          ? _value.driverId
          : driverId // ignore: cast_nullable_to_non_nullable
              as String?,
      gpsTime: null == gpsTime
          ? _value.gpsTime
          : gpsTime // ignore: cast_nullable_to_non_nullable
              as int,
      speed: null == speed
          ? _value.speed
          : speed // ignore: cast_nullable_to_non_nullable
              as int,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as int,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      info: freezed == info
          ? _value.info
          : info // ignore: cast_nullable_to_non_nullable
              as String?,
      heading: freezed == heading
          ? _value.heading
          : heading // ignore: cast_nullable_to_non_nullable
              as int?,
      direction: null == direction
          ? _value.direction
          : direction // ignore: cast_nullable_to_non_nullable
              as String,
      satellite: null == satellite
          ? _value.satellite
          : satellite // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      gpsMileage: null == gpsMileage
          ? _value.gpsMileage
          : gpsMileage // ignore: cast_nullable_to_non_nullable
              as int,
      input: freezed == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as int?,
      inputs: freezed == inputs
          ? _value.inputs
          : inputs // ignore: cast_nullable_to_non_nullable
              as Inputs?,
      output: freezed == output
          ? _value.output
          : output // ignore: cast_nullable_to_non_nullable
              as int?,
      roadSpeed: null == roadSpeed
          ? _value.roadSpeed
          : roadSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      roadName: freezed == roadName
          ? _value.roadName
          : roadName // ignore: cast_nullable_to_non_nullable
              as String?,
      eventId: null == eventId
          ? _value.eventId
          : eventId // ignore: cast_nullable_to_non_nullable
              as int,
      deviceTypeId: null == deviceTypeId
          ? _value.deviceTypeId
          : deviceTypeId // ignore: cast_nullable_to_non_nullable
              as int,
      isOverSpeed: null == isOverSpeed
          ? _value.isOverSpeed
          : isOverSpeed // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
      voltage: null == voltage
          ? _value.voltage
          : voltage // ignore: cast_nullable_to_non_nullable
              as int,
      voltagePercentage: null == voltagePercentage
          ? _value.voltagePercentage
          : voltagePercentage // ignore: cast_nullable_to_non_nullable
              as int,
      idleTime: null == idleTime
          ? _value.idleTime
          : idleTime // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleMaxSpeed: null == vehicleMaxSpeed
          ? _value.vehicleMaxSpeed
          : vehicleMaxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      statusColor: null == statusColor
          ? _value.statusColor
          : statusColor // ignore: cast_nullable_to_non_nullable
              as String,
      sensors: freezed == sensors
          ? _value.sensors
          : sensors // ignore: cast_nullable_to_non_nullable
              as List<Sensor>?,
      inputInfo: null == inputInfo
          ? _value.inputInfo
          : inputInfo // ignore: cast_nullable_to_non_nullable
              as List<bool>,
      outputInfo: null == outputInfo
          ? _value.outputInfo
          : outputInfo // ignore: cast_nullable_to_non_nullable
              as List<bool>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $InputsCopyWith<$Res>? get inputs {
    if (_value.inputs == null) {
      return null;
    }

    return $InputsCopyWith<$Res>(_value.inputs!, (value) {
      return _then(_value.copyWith(inputs: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$HistoryRouteImplCopyWith<$Res>
    implements $HistoryRouteCopyWith<$Res> {
  factory _$$HistoryRouteImplCopyWith(
          _$HistoryRouteImpl value, $Res Function(_$HistoryRouteImpl) then) =
      __$$HistoryRouteImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String? driverId,
      int gpsTime,
      int speed,
      int x,
      int y,
      int status,
      String? info,
      int? heading,
      String direction,
      int satellite,
      int maxSpeed,
      int gpsMileage,
      int? input,
      Inputs? inputs,
      int? output,
      int roadSpeed,
      String? roadName,
      int eventId,
      int deviceTypeId,
      bool isOverSpeed,
      bool isOverSpeedByRoad,
      int voltage,
      int voltagePercentage,
      int idleTime,
      int vehicleMaxSpeed,
      String statusColor,
      List<Sensor>? sensors,
      List<bool> inputInfo,
      List<bool> outputInfo});

  @override
  $InputsCopyWith<$Res>? get inputs;
}

/// @nodoc
class __$$HistoryRouteImplCopyWithImpl<$Res>
    extends _$HistoryRouteCopyWithImpl<$Res, _$HistoryRouteImpl>
    implements _$$HistoryRouteImplCopyWith<$Res> {
  __$$HistoryRouteImplCopyWithImpl(
      _$HistoryRouteImpl _value, $Res Function(_$HistoryRouteImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? driverId = freezed,
    Object? gpsTime = null,
    Object? speed = null,
    Object? x = null,
    Object? y = null,
    Object? status = null,
    Object? info = freezed,
    Object? heading = freezed,
    Object? direction = null,
    Object? satellite = null,
    Object? maxSpeed = null,
    Object? gpsMileage = null,
    Object? input = freezed,
    Object? inputs = freezed,
    Object? output = freezed,
    Object? roadSpeed = null,
    Object? roadName = freezed,
    Object? eventId = null,
    Object? deviceTypeId = null,
    Object? isOverSpeed = null,
    Object? isOverSpeedByRoad = null,
    Object? voltage = null,
    Object? voltagePercentage = null,
    Object? idleTime = null,
    Object? vehicleMaxSpeed = null,
    Object? statusColor = null,
    Object? sensors = freezed,
    Object? inputInfo = null,
    Object? outputInfo = null,
  }) {
    return _then(_$HistoryRouteImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      driverId: freezed == driverId
          ? _value.driverId
          : driverId // ignore: cast_nullable_to_non_nullable
              as String?,
      gpsTime: null == gpsTime
          ? _value.gpsTime
          : gpsTime // ignore: cast_nullable_to_non_nullable
              as int,
      speed: null == speed
          ? _value.speed
          : speed // ignore: cast_nullable_to_non_nullable
              as int,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as int,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      info: freezed == info
          ? _value.info
          : info // ignore: cast_nullable_to_non_nullable
              as String?,
      heading: freezed == heading
          ? _value.heading
          : heading // ignore: cast_nullable_to_non_nullable
              as int?,
      direction: null == direction
          ? _value.direction
          : direction // ignore: cast_nullable_to_non_nullable
              as String,
      satellite: null == satellite
          ? _value.satellite
          : satellite // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      gpsMileage: null == gpsMileage
          ? _value.gpsMileage
          : gpsMileage // ignore: cast_nullable_to_non_nullable
              as int,
      input: freezed == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as int?,
      inputs: freezed == inputs
          ? _value.inputs
          : inputs // ignore: cast_nullable_to_non_nullable
              as Inputs?,
      output: freezed == output
          ? _value.output
          : output // ignore: cast_nullable_to_non_nullable
              as int?,
      roadSpeed: null == roadSpeed
          ? _value.roadSpeed
          : roadSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      roadName: freezed == roadName
          ? _value.roadName
          : roadName // ignore: cast_nullable_to_non_nullable
              as String?,
      eventId: null == eventId
          ? _value.eventId
          : eventId // ignore: cast_nullable_to_non_nullable
              as int,
      deviceTypeId: null == deviceTypeId
          ? _value.deviceTypeId
          : deviceTypeId // ignore: cast_nullable_to_non_nullable
              as int,
      isOverSpeed: null == isOverSpeed
          ? _value.isOverSpeed
          : isOverSpeed // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
      voltage: null == voltage
          ? _value.voltage
          : voltage // ignore: cast_nullable_to_non_nullable
              as int,
      voltagePercentage: null == voltagePercentage
          ? _value.voltagePercentage
          : voltagePercentage // ignore: cast_nullable_to_non_nullable
              as int,
      idleTime: null == idleTime
          ? _value.idleTime
          : idleTime // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleMaxSpeed: null == vehicleMaxSpeed
          ? _value.vehicleMaxSpeed
          : vehicleMaxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      statusColor: null == statusColor
          ? _value.statusColor
          : statusColor // ignore: cast_nullable_to_non_nullable
              as String,
      sensors: freezed == sensors
          ? _value._sensors
          : sensors // ignore: cast_nullable_to_non_nullable
              as List<Sensor>?,
      inputInfo: null == inputInfo
          ? _value._inputInfo
          : inputInfo // ignore: cast_nullable_to_non_nullable
              as List<bool>,
      outputInfo: null == outputInfo
          ? _value._outputInfo
          : outputInfo // ignore: cast_nullable_to_non_nullable
              as List<bool>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HistoryRouteImpl extends _HistoryRoute {
  const _$HistoryRouteImpl(
      {required this.id,
      required this.driverId,
      required this.gpsTime,
      required this.speed,
      required this.x,
      required this.y,
      required this.status,
      required this.info,
      required this.heading,
      required this.direction,
      required this.satellite,
      required this.maxSpeed,
      required this.gpsMileage,
      required this.input,
      required this.inputs,
      required this.output,
      required this.roadSpeed,
      required this.roadName,
      required this.eventId,
      required this.deviceTypeId,
      required this.isOverSpeed,
      required this.isOverSpeedByRoad,
      required this.voltage,
      required this.voltagePercentage,
      required this.idleTime,
      required this.vehicleMaxSpeed,
      required this.statusColor,
      required final List<Sensor>? sensors,
      required final List<bool> inputInfo,
      required final List<bool> outputInfo})
      : _sensors = sensors,
        _inputInfo = inputInfo,
        _outputInfo = outputInfo,
        super._();

  factory _$HistoryRouteImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistoryRouteImplFromJson(json);

  @override
  final int id;
  @override
  final String? driverId;
  @override
  final int gpsTime;
  @override
  final int speed;
  @override
  final int x;
  @override
  final int y;
  @override
  final int status;
  @override
  final String? info;
// address
  @override
  final int? heading;
  @override
  final String direction;
  @override
  final int satellite;
  @override
  final int maxSpeed;
  @override
  final int gpsMileage;
  @override
  final int? input;
  @override
  final Inputs? inputs;
  @override
  final int? output;
  @override
  final int roadSpeed;
  @override
  final String? roadName;
  @override
  final int eventId;
  @override
  final int deviceTypeId;
  @override
  final bool isOverSpeed;
  @override
  final bool isOverSpeedByRoad;
  @override
  final int voltage;
  @override
  final int voltagePercentage;
  @override
  final int idleTime;
  @override
  final int vehicleMaxSpeed;
  @override
  final String statusColor;
  final List<Sensor>? _sensors;
  @override
  List<Sensor>? get sensors {
    final value = _sensors;
    if (value == null) return null;
    if (_sensors is EqualUnmodifiableListView) return _sensors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<bool> _inputInfo;
  @override
  List<bool> get inputInfo {
    if (_inputInfo is EqualUnmodifiableListView) return _inputInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_inputInfo);
  }

  final List<bool> _outputInfo;
  @override
  List<bool> get outputInfo {
    if (_outputInfo is EqualUnmodifiableListView) return _outputInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_outputInfo);
  }

  @override
  String toString() {
    return 'HistoryRoute(id: $id, driverId: $driverId, gpsTime: $gpsTime, speed: $speed, x: $x, y: $y, status: $status, info: $info, heading: $heading, direction: $direction, satellite: $satellite, maxSpeed: $maxSpeed, gpsMileage: $gpsMileage, input: $input, inputs: $inputs, output: $output, roadSpeed: $roadSpeed, roadName: $roadName, eventId: $eventId, deviceTypeId: $deviceTypeId, isOverSpeed: $isOverSpeed, isOverSpeedByRoad: $isOverSpeedByRoad, voltage: $voltage, voltagePercentage: $voltagePercentage, idleTime: $idleTime, vehicleMaxSpeed: $vehicleMaxSpeed, statusColor: $statusColor, sensors: $sensors, inputInfo: $inputInfo, outputInfo: $outputInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryRouteImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.driverId, driverId) ||
                other.driverId == driverId) &&
            (identical(other.gpsTime, gpsTime) || other.gpsTime == gpsTime) &&
            (identical(other.speed, speed) || other.speed == speed) &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.info, info) || other.info == info) &&
            (identical(other.heading, heading) || other.heading == heading) &&
            (identical(other.direction, direction) ||
                other.direction == direction) &&
            (identical(other.satellite, satellite) ||
                other.satellite == satellite) &&
            (identical(other.maxSpeed, maxSpeed) ||
                other.maxSpeed == maxSpeed) &&
            (identical(other.gpsMileage, gpsMileage) ||
                other.gpsMileage == gpsMileage) &&
            (identical(other.input, input) || other.input == input) &&
            (identical(other.inputs, inputs) || other.inputs == inputs) &&
            (identical(other.output, output) || other.output == output) &&
            (identical(other.roadSpeed, roadSpeed) ||
                other.roadSpeed == roadSpeed) &&
            (identical(other.roadName, roadName) ||
                other.roadName == roadName) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.deviceTypeId, deviceTypeId) ||
                other.deviceTypeId == deviceTypeId) &&
            (identical(other.isOverSpeed, isOverSpeed) ||
                other.isOverSpeed == isOverSpeed) &&
            (identical(other.isOverSpeedByRoad, isOverSpeedByRoad) ||
                other.isOverSpeedByRoad == isOverSpeedByRoad) &&
            (identical(other.voltage, voltage) || other.voltage == voltage) &&
            (identical(other.voltagePercentage, voltagePercentage) ||
                other.voltagePercentage == voltagePercentage) &&
            (identical(other.idleTime, idleTime) ||
                other.idleTime == idleTime) &&
            (identical(other.vehicleMaxSpeed, vehicleMaxSpeed) ||
                other.vehicleMaxSpeed == vehicleMaxSpeed) &&
            (identical(other.statusColor, statusColor) ||
                other.statusColor == statusColor) &&
            const DeepCollectionEquality().equals(other._sensors, _sensors) &&
            const DeepCollectionEquality()
                .equals(other._inputInfo, _inputInfo) &&
            const DeepCollectionEquality()
                .equals(other._outputInfo, _outputInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        driverId,
        gpsTime,
        speed,
        x,
        y,
        status,
        info,
        heading,
        direction,
        satellite,
        maxSpeed,
        gpsMileage,
        input,
        inputs,
        output,
        roadSpeed,
        roadName,
        eventId,
        deviceTypeId,
        isOverSpeed,
        isOverSpeedByRoad,
        voltage,
        voltagePercentage,
        idleTime,
        vehicleMaxSpeed,
        statusColor,
        const DeepCollectionEquality().hash(_sensors),
        const DeepCollectionEquality().hash(_inputInfo),
        const DeepCollectionEquality().hash(_outputInfo)
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoryRouteImplCopyWith<_$HistoryRouteImpl> get copyWith =>
      __$$HistoryRouteImplCopyWithImpl<_$HistoryRouteImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HistoryRouteImplToJson(
      this,
    );
  }
}

abstract class _HistoryRoute extends HistoryRoute {
  const factory _HistoryRoute(
      {required final int id,
      required final String? driverId,
      required final int gpsTime,
      required final int speed,
      required final int x,
      required final int y,
      required final int status,
      required final String? info,
      required final int? heading,
      required final String direction,
      required final int satellite,
      required final int maxSpeed,
      required final int gpsMileage,
      required final int? input,
      required final Inputs? inputs,
      required final int? output,
      required final int roadSpeed,
      required final String? roadName,
      required final int eventId,
      required final int deviceTypeId,
      required final bool isOverSpeed,
      required final bool isOverSpeedByRoad,
      required final int voltage,
      required final int voltagePercentage,
      required final int idleTime,
      required final int vehicleMaxSpeed,
      required final String statusColor,
      required final List<Sensor>? sensors,
      required final List<bool> inputInfo,
      required final List<bool> outputInfo}) = _$HistoryRouteImpl;
  const _HistoryRoute._() : super._();

  factory _HistoryRoute.fromJson(Map<String, dynamic> json) =
      _$HistoryRouteImpl.fromJson;

  @override
  int get id;
  @override
  String? get driverId;
  @override
  int get gpsTime;
  @override
  int get speed;
  @override
  int get x;
  @override
  int get y;
  @override
  int get status;
  @override
  String? get info;
  @override // address
  int? get heading;
  @override
  String get direction;
  @override
  int get satellite;
  @override
  int get maxSpeed;
  @override
  int get gpsMileage;
  @override
  int? get input;
  @override
  Inputs? get inputs;
  @override
  int? get output;
  @override
  int get roadSpeed;
  @override
  String? get roadName;
  @override
  int get eventId;
  @override
  int get deviceTypeId;
  @override
  bool get isOverSpeed;
  @override
  bool get isOverSpeedByRoad;
  @override
  int get voltage;
  @override
  int get voltagePercentage;
  @override
  int get idleTime;
  @override
  int get vehicleMaxSpeed;
  @override
  String get statusColor;
  @override
  List<Sensor>? get sensors;
  @override
  List<bool> get inputInfo;
  @override
  List<bool> get outputInfo;
  @override
  @JsonKey(ignore: true)
  _$$HistoryRouteImplCopyWith<_$HistoryRouteImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

HistorySymmaryReport _$HistorySymmaryReportFromJson(Map<String, dynamic> json) {
  return _HistorySymmaryReport.fromJson(json);
}

/// @nodoc
mixin _$HistorySymmaryReport {
  String get from => throw _privateConstructorUsedError;
  String get to => throw _privateConstructorUsedError;
  int get duration => throw _privateConstructorUsedError;
  int get distance => throw _privateConstructorUsedError;
  int get startTime => throw _privateConstructorUsedError;
  int get endTime => throw _privateConstructorUsedError;
  int get maxSpeed => throw _privateConstructorUsedError;
  int get overSpeedCount => throw _privateConstructorUsedError;
  int get idleCount => throw _privateConstructorUsedError;
  List<Timeline> get timeline => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HistorySymmaryReportCopyWith<HistorySymmaryReport> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistorySymmaryReportCopyWith<$Res> {
  factory $HistorySymmaryReportCopyWith(HistorySymmaryReport value,
          $Res Function(HistorySymmaryReport) then) =
      _$HistorySymmaryReportCopyWithImpl<$Res, HistorySymmaryReport>;
  @useResult
  $Res call(
      {String from,
      String to,
      int duration,
      int distance,
      int startTime,
      int endTime,
      int maxSpeed,
      int overSpeedCount,
      int idleCount,
      List<Timeline> timeline});
}

/// @nodoc
class _$HistorySymmaryReportCopyWithImpl<$Res,
        $Val extends HistorySymmaryReport>
    implements $HistorySymmaryReportCopyWith<$Res> {
  _$HistorySymmaryReportCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? from = null,
    Object? to = null,
    Object? duration = null,
    Object? distance = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? maxSpeed = null,
    Object? overSpeedCount = null,
    Object? idleCount = null,
    Object? timeline = null,
  }) {
    return _then(_value.copyWith(
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      distance: null == distance
          ? _value.distance
          : distance // ignore: cast_nullable_to_non_nullable
              as int,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      overSpeedCount: null == overSpeedCount
          ? _value.overSpeedCount
          : overSpeedCount // ignore: cast_nullable_to_non_nullable
              as int,
      idleCount: null == idleCount
          ? _value.idleCount
          : idleCount // ignore: cast_nullable_to_non_nullable
              as int,
      timeline: null == timeline
          ? _value.timeline
          : timeline // ignore: cast_nullable_to_non_nullable
              as List<Timeline>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HistorySymmaryReportImplCopyWith<$Res>
    implements $HistorySymmaryReportCopyWith<$Res> {
  factory _$$HistorySymmaryReportImplCopyWith(_$HistorySymmaryReportImpl value,
          $Res Function(_$HistorySymmaryReportImpl) then) =
      __$$HistorySymmaryReportImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String from,
      String to,
      int duration,
      int distance,
      int startTime,
      int endTime,
      int maxSpeed,
      int overSpeedCount,
      int idleCount,
      List<Timeline> timeline});
}

/// @nodoc
class __$$HistorySymmaryReportImplCopyWithImpl<$Res>
    extends _$HistorySymmaryReportCopyWithImpl<$Res, _$HistorySymmaryReportImpl>
    implements _$$HistorySymmaryReportImplCopyWith<$Res> {
  __$$HistorySymmaryReportImplCopyWithImpl(_$HistorySymmaryReportImpl _value,
      $Res Function(_$HistorySymmaryReportImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? from = null,
    Object? to = null,
    Object? duration = null,
    Object? distance = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? maxSpeed = null,
    Object? overSpeedCount = null,
    Object? idleCount = null,
    Object? timeline = null,
  }) {
    return _then(_$HistorySymmaryReportImpl(
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      distance: null == distance
          ? _value.distance
          : distance // ignore: cast_nullable_to_non_nullable
              as int,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int,
      maxSpeed: null == maxSpeed
          ? _value.maxSpeed
          : maxSpeed // ignore: cast_nullable_to_non_nullable
              as int,
      overSpeedCount: null == overSpeedCount
          ? _value.overSpeedCount
          : overSpeedCount // ignore: cast_nullable_to_non_nullable
              as int,
      idleCount: null == idleCount
          ? _value.idleCount
          : idleCount // ignore: cast_nullable_to_non_nullable
              as int,
      timeline: null == timeline
          ? _value._timeline
          : timeline // ignore: cast_nullable_to_non_nullable
              as List<Timeline>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HistorySymmaryReportImpl extends _HistorySymmaryReport {
  const _$HistorySymmaryReportImpl(
      {required this.from,
      required this.to,
      required this.duration,
      required this.distance,
      required this.startTime,
      required this.endTime,
      required this.maxSpeed,
      required this.overSpeedCount,
      required this.idleCount,
      required final List<Timeline> timeline})
      : _timeline = timeline,
        super._();

  factory _$HistorySymmaryReportImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistorySymmaryReportImplFromJson(json);

  @override
  final String from;
  @override
  final String to;
  @override
  final int duration;
  @override
  final int distance;
  @override
  final int startTime;
  @override
  final int endTime;
  @override
  final int maxSpeed;
  @override
  final int overSpeedCount;
  @override
  final int idleCount;
  final List<Timeline> _timeline;
  @override
  List<Timeline> get timeline {
    if (_timeline is EqualUnmodifiableListView) return _timeline;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_timeline);
  }

  @override
  String toString() {
    return 'HistorySymmaryReport(from: $from, to: $to, duration: $duration, distance: $distance, startTime: $startTime, endTime: $endTime, maxSpeed: $maxSpeed, overSpeedCount: $overSpeedCount, idleCount: $idleCount, timeline: $timeline)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistorySymmaryReportImpl &&
            (identical(other.from, from) || other.from == from) &&
            (identical(other.to, to) || other.to == to) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.distance, distance) ||
                other.distance == distance) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.maxSpeed, maxSpeed) ||
                other.maxSpeed == maxSpeed) &&
            (identical(other.overSpeedCount, overSpeedCount) ||
                other.overSpeedCount == overSpeedCount) &&
            (identical(other.idleCount, idleCount) ||
                other.idleCount == idleCount) &&
            const DeepCollectionEquality().equals(other._timeline, _timeline));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      from,
      to,
      duration,
      distance,
      startTime,
      endTime,
      maxSpeed,
      overSpeedCount,
      idleCount,
      const DeepCollectionEquality().hash(_timeline));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HistorySymmaryReportImplCopyWith<_$HistorySymmaryReportImpl>
      get copyWith =>
          __$$HistorySymmaryReportImplCopyWithImpl<_$HistorySymmaryReportImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HistorySymmaryReportImplToJson(
      this,
    );
  }
}

abstract class _HistorySymmaryReport extends HistorySymmaryReport {
  const factory _HistorySymmaryReport(
      {required final String from,
      required final String to,
      required final int duration,
      required final int distance,
      required final int startTime,
      required final int endTime,
      required final int maxSpeed,
      required final int overSpeedCount,
      required final int idleCount,
      required final List<Timeline> timeline}) = _$HistorySymmaryReportImpl;
  const _HistorySymmaryReport._() : super._();

  factory _HistorySymmaryReport.fromJson(Map<String, dynamic> json) =
      _$HistorySymmaryReportImpl.fromJson;

  @override
  String get from;
  @override
  String get to;
  @override
  int get duration;
  @override
  int get distance;
  @override
  int get startTime;
  @override
  int get endTime;
  @override
  int get maxSpeed;
  @override
  int get overSpeedCount;
  @override
  int get idleCount;
  @override
  List<Timeline> get timeline;
  @override
  @JsonKey(ignore: true)
  _$$HistorySymmaryReportImplCopyWith<_$HistorySymmaryReportImpl>
      get copyWith => throw _privateConstructorUsedError;
}

HistoryKeys _$HistoryKeysFromJson(Map<String, dynamic> json) {
  return _HistoryKeys.fromJson(json);
}

/// @nodoc
mixin _$HistoryKeys {
  String? get additionalProps1 => throw _privateConstructorUsedError;
  String? get additionalProps2 => throw _privateConstructorUsedError;
  String? get additionalProps3 => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HistoryKeysCopyWith<HistoryKeys> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryKeysCopyWith<$Res> {
  factory $HistoryKeysCopyWith(
          HistoryKeys value, $Res Function(HistoryKeys) then) =
      _$HistoryKeysCopyWithImpl<$Res, HistoryKeys>;
  @useResult
  $Res call(
      {String? additionalProps1,
      String? additionalProps2,
      String? additionalProps3});
}

/// @nodoc
class _$HistoryKeysCopyWithImpl<$Res, $Val extends HistoryKeys>
    implements $HistoryKeysCopyWith<$Res> {
  _$HistoryKeysCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? additionalProps1 = freezed,
    Object? additionalProps2 = freezed,
    Object? additionalProps3 = freezed,
  }) {
    return _then(_value.copyWith(
      additionalProps1: freezed == additionalProps1
          ? _value.additionalProps1
          : additionalProps1 // ignore: cast_nullable_to_non_nullable
              as String?,
      additionalProps2: freezed == additionalProps2
          ? _value.additionalProps2
          : additionalProps2 // ignore: cast_nullable_to_non_nullable
              as String?,
      additionalProps3: freezed == additionalProps3
          ? _value.additionalProps3
          : additionalProps3 // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HistoryKeysImplCopyWith<$Res>
    implements $HistoryKeysCopyWith<$Res> {
  factory _$$HistoryKeysImplCopyWith(
          _$HistoryKeysImpl value, $Res Function(_$HistoryKeysImpl) then) =
      __$$HistoryKeysImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? additionalProps1,
      String? additionalProps2,
      String? additionalProps3});
}

/// @nodoc
class __$$HistoryKeysImplCopyWithImpl<$Res>
    extends _$HistoryKeysCopyWithImpl<$Res, _$HistoryKeysImpl>
    implements _$$HistoryKeysImplCopyWith<$Res> {
  __$$HistoryKeysImplCopyWithImpl(
      _$HistoryKeysImpl _value, $Res Function(_$HistoryKeysImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? additionalProps1 = freezed,
    Object? additionalProps2 = freezed,
    Object? additionalProps3 = freezed,
  }) {
    return _then(_$HistoryKeysImpl(
      additionalProps1: freezed == additionalProps1
          ? _value.additionalProps1
          : additionalProps1 // ignore: cast_nullable_to_non_nullable
              as String?,
      additionalProps2: freezed == additionalProps2
          ? _value.additionalProps2
          : additionalProps2 // ignore: cast_nullable_to_non_nullable
              as String?,
      additionalProps3: freezed == additionalProps3
          ? _value.additionalProps3
          : additionalProps3 // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HistoryKeysImpl extends _HistoryKeys {
  const _$HistoryKeysImpl(
      {required this.additionalProps1,
      required this.additionalProps2,
      required this.additionalProps3})
      : super._();

  factory _$HistoryKeysImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistoryKeysImplFromJson(json);

  @override
  final String? additionalProps1;
  @override
  final String? additionalProps2;
  @override
  final String? additionalProps3;

  @override
  String toString() {
    return 'HistoryKeys(additionalProps1: $additionalProps1, additionalProps2: $additionalProps2, additionalProps3: $additionalProps3)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryKeysImpl &&
            (identical(other.additionalProps1, additionalProps1) ||
                other.additionalProps1 == additionalProps1) &&
            (identical(other.additionalProps2, additionalProps2) ||
                other.additionalProps2 == additionalProps2) &&
            (identical(other.additionalProps3, additionalProps3) ||
                other.additionalProps3 == additionalProps3));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, additionalProps1, additionalProps2, additionalProps3);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoryKeysImplCopyWith<_$HistoryKeysImpl> get copyWith =>
      __$$HistoryKeysImplCopyWithImpl<_$HistoryKeysImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HistoryKeysImplToJson(
      this,
    );
  }
}

abstract class _HistoryKeys extends HistoryKeys {
  const factory _HistoryKeys(
      {required final String? additionalProps1,
      required final String? additionalProps2,
      required final String? additionalProps3}) = _$HistoryKeysImpl;
  const _HistoryKeys._() : super._();

  factory _HistoryKeys.fromJson(Map<String, dynamic> json) =
      _$HistoryKeysImpl.fromJson;

  @override
  String? get additionalProps1;
  @override
  String? get additionalProps2;
  @override
  String? get additionalProps3;
  @override
  @JsonKey(ignore: true)
  _$$HistoryKeysImplCopyWith<_$HistoryKeysImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Timeline _$TimelineFromJson(Map<String, dynamic> json) {
  return _Timeline.fromJson(json);
}

/// @nodoc
mixin _$Timeline {
  int get x => throw _privateConstructorUsedError;
  int get y => throw _privateConstructorUsedError;
  int get duration => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  int get id => throw _privateConstructorUsedError;
  int get from => throw _privateConstructorUsedError;
  int get to => throw _privateConstructorUsedError;
  String get address => throw _privateConstructorUsedError;
  String get toAddress => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TimelineCopyWith<Timeline> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimelineCopyWith<$Res> {
  factory $TimelineCopyWith(Timeline value, $Res Function(Timeline) then) =
      _$TimelineCopyWithImpl<$Res, Timeline>;
  @useResult
  $Res call(
      {int x,
      int y,
      int duration,
      int type,
      int id,
      int from,
      int to,
      String address,
      String toAddress});
}

/// @nodoc
class _$TimelineCopyWithImpl<$Res, $Val extends Timeline>
    implements $TimelineCopyWith<$Res> {
  _$TimelineCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? x = null,
    Object? y = null,
    Object? duration = null,
    Object? type = null,
    Object? id = null,
    Object? from = null,
    Object? to = null,
    Object? address = null,
    Object? toAddress = null,
  }) {
    return _then(_value.copyWith(
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as int,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as int,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as int,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as int,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      toAddress: null == toAddress
          ? _value.toAddress
          : toAddress // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TimelineImplCopyWith<$Res>
    implements $TimelineCopyWith<$Res> {
  factory _$$TimelineImplCopyWith(
          _$TimelineImpl value, $Res Function(_$TimelineImpl) then) =
      __$$TimelineImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int x,
      int y,
      int duration,
      int type,
      int id,
      int from,
      int to,
      String address,
      String toAddress});
}

/// @nodoc
class __$$TimelineImplCopyWithImpl<$Res>
    extends _$TimelineCopyWithImpl<$Res, _$TimelineImpl>
    implements _$$TimelineImplCopyWith<$Res> {
  __$$TimelineImplCopyWithImpl(
      _$TimelineImpl _value, $Res Function(_$TimelineImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? x = null,
    Object? y = null,
    Object? duration = null,
    Object? type = null,
    Object? id = null,
    Object? from = null,
    Object? to = null,
    Object? address = null,
    Object? toAddress = null,
  }) {
    return _then(_$TimelineImpl(
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as int,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as int,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as int,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as int,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      toAddress: null == toAddress
          ? _value.toAddress
          : toAddress // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TimelineImpl extends _Timeline {
  const _$TimelineImpl(
      {required this.x,
      required this.y,
      required this.duration,
      required this.type,
      required this.id,
      required this.from,
      required this.to,
      required this.address,
      required this.toAddress})
      : super._();

  factory _$TimelineImpl.fromJson(Map<String, dynamic> json) =>
      _$$TimelineImplFromJson(json);

  @override
  final int x;
  @override
  final int y;
  @override
  final int duration;
  @override
  final int type;
  @override
  final int id;
  @override
  final int from;
  @override
  final int to;
  @override
  final String address;
  @override
  final String toAddress;

  @override
  String toString() {
    return 'Timeline(x: $x, y: $y, duration: $duration, type: $type, id: $id, from: $from, to: $to, address: $address, toAddress: $toAddress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimelineImpl &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.from, from) || other.from == from) &&
            (identical(other.to, to) || other.to == to) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.toAddress, toAddress) ||
                other.toAddress == toAddress));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, x, y, duration, type, id, from, to, address, toAddress);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TimelineImplCopyWith<_$TimelineImpl> get copyWith =>
      __$$TimelineImplCopyWithImpl<_$TimelineImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TimelineImplToJson(
      this,
    );
  }
}

abstract class _Timeline extends Timeline {
  const factory _Timeline(
      {required final int x,
      required final int y,
      required final int duration,
      required final int type,
      required final int id,
      required final int from,
      required final int to,
      required final String address,
      required final String toAddress}) = _$TimelineImpl;
  const _Timeline._() : super._();

  factory _Timeline.fromJson(Map<String, dynamic> json) =
      _$TimelineImpl.fromJson;

  @override
  int get x;
  @override
  int get y;
  @override
  int get duration;
  @override
  int get type;
  @override
  int get id;
  @override
  int get from;
  @override
  int get to;
  @override
  String get address;
  @override
  String get toAddress;
  @override
  @JsonKey(ignore: true)
  _$$TimelineImplCopyWith<_$TimelineImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
