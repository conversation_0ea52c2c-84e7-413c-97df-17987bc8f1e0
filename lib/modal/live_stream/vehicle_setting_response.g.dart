// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_setting_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VehicleSettingResponseImpl _$$VehicleSettingResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$VehicleSettingResponseImpl(
      vehicle: (json['vehicle'] as List<dynamic>)
          .map((e) => VehicleWithSetting.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$VehicleSettingResponseImplToJson(
        _$VehicleSettingResponseImpl instance) =>
    <String, dynamic>{
      'vehicle': instance.vehicle,
    };

_$VehicleWithSettingImpl _$$VehicleWithSettingImplFromJson(
        Map<String, dynamic> json) =>
    _$VehicleWithSettingImpl(
      id: json['id'] as String,
      plate: json['plate'] as String,
      settings: (json['settings'] as List<dynamic>)
          .map((e) => VehicleSetting.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$VehicleWithSettingImplToJson(
        _$VehicleWithSettingImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'plate': instance.plate,
      'settings': instance.settings,
    };

_$VehicleSettingImpl _$$VehicleSettingImplFromJson(Map<String, dynamic> json) =>
    _$VehicleSettingImpl(
      id: json['id'] as String,
      label: json['label'] as String,
      index: (json['index'] as num).toInt(),
    );

Map<String, dynamic> _$$VehicleSettingImplToJson(
        _$VehicleSettingImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'label': instance.label,
      'index': instance.index,
    };
