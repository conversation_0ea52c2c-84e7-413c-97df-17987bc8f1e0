// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'live_stream_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LiveStreamDataImpl _$$LiveStreamDataImplFromJson(Map<String, dynamic> json) =>
    _$LiveStreamDataImpl(
      vehicleId: json['vehicleId'] as String?,
      status: (json['status'] as num).toInt(),
      message: json['message'] as String,
      setting: json['setting'] == null
          ? null
          : LiveSetting.fromJson(json['setting'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$LiveStreamDataImplToJson(
        _$LiveStreamDataImpl instance) =>
    <String, dynamic>{
      'vehicleId': instance.vehicleId,
      'status': instance.status,
      'message': instance.message,
      'setting': instance.setting,
    };

_$LiveSettingImpl _$$LiveSettingImplFromJson(Map<String, dynamic> json) =>
    _$LiveSettingImpl(
      index: (json['index'] as num).toInt(),
      liveTime: (json['liveTime'] as num).toInt(),
      offset: (json['offset'] as num).toInt(),
      subscription: json['subscription'] as String,
      channel: json['channel'] as String,
      protocol: json['protocol'] as String,
      url: json['url'] as String?,
    );

Map<String, dynamic> _$$LiveSettingImplToJson(_$LiveSettingImpl instance) =>
    <String, dynamic>{
      'index': instance.index,
      'liveTime': instance.liveTime,
      'offset': instance.offset,
      'subscription': instance.subscription,
      'channel': instance.channel,
      'protocol': instance.protocol,
      'url': instance.url,
    };
