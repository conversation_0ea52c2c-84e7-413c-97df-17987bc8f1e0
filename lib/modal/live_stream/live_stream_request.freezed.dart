// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'live_stream_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LiveStreamRequest _$LiveStreamRequestFromJson(Map<String, dynamic> json) {
  return _LiveStreamRequest.fromJson(json);
}

/// @nodoc
mixin _$LiveStreamRequest {
  String get id => throw _privateConstructorUsedError;
  String get vehicleId => throw _privateConstructorUsedError;
  int get channel => throw _privateConstructorUsedError;
  int get streamTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LiveStreamRequestCopyWith<LiveStreamRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LiveStreamRequestCopyWith<$Res> {
  factory $LiveStreamRequestCopyWith(
          LiveStreamRequest value, $Res Function(LiveStreamRequest) then) =
      _$LiveStreamRequestCopyWithImpl<$Res, LiveStreamRequest>;
  @useResult
  $Res call({String id, String vehicleId, int channel, int streamTime});
}

/// @nodoc
class _$LiveStreamRequestCopyWithImpl<$Res, $Val extends LiveStreamRequest>
    implements $LiveStreamRequestCopyWith<$Res> {
  _$LiveStreamRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? vehicleId = null,
    Object? channel = null,
    Object? streamTime = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as int,
      streamTime: null == streamTime
          ? _value.streamTime
          : streamTime // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LiveStreamRequestImplCopyWith<$Res>
    implements $LiveStreamRequestCopyWith<$Res> {
  factory _$$LiveStreamRequestImplCopyWith(_$LiveStreamRequestImpl value,
          $Res Function(_$LiveStreamRequestImpl) then) =
      __$$LiveStreamRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String vehicleId, int channel, int streamTime});
}

/// @nodoc
class __$$LiveStreamRequestImplCopyWithImpl<$Res>
    extends _$LiveStreamRequestCopyWithImpl<$Res, _$LiveStreamRequestImpl>
    implements _$$LiveStreamRequestImplCopyWith<$Res> {
  __$$LiveStreamRequestImplCopyWithImpl(_$LiveStreamRequestImpl _value,
      $Res Function(_$LiveStreamRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? vehicleId = null,
    Object? channel = null,
    Object? streamTime = null,
  }) {
    return _then(_$LiveStreamRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as int,
      streamTime: null == streamTime
          ? _value.streamTime
          : streamTime // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LiveStreamRequestImpl extends _LiveStreamRequest {
  const _$LiveStreamRequestImpl(
      {required this.id,
      required this.vehicleId,
      required this.channel,
      required this.streamTime})
      : super._();

  factory _$LiveStreamRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$LiveStreamRequestImplFromJson(json);

  @override
  final String id;
  @override
  final String vehicleId;
  @override
  final int channel;
  @override
  final int streamTime;

  @override
  String toString() {
    return 'LiveStreamRequest(id: $id, vehicleId: $vehicleId, channel: $channel, streamTime: $streamTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LiveStreamRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.channel, channel) || other.channel == channel) &&
            (identical(other.streamTime, streamTime) ||
                other.streamTime == streamTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, vehicleId, channel, streamTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LiveStreamRequestImplCopyWith<_$LiveStreamRequestImpl> get copyWith =>
      __$$LiveStreamRequestImplCopyWithImpl<_$LiveStreamRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LiveStreamRequestImplToJson(
      this,
    );
  }
}

abstract class _LiveStreamRequest extends LiveStreamRequest {
  const factory _LiveStreamRequest(
      {required final String id,
      required final String vehicleId,
      required final int channel,
      required final int streamTime}) = _$LiveStreamRequestImpl;
  const _LiveStreamRequest._() : super._();

  factory _LiveStreamRequest.fromJson(Map<String, dynamic> json) =
      _$LiveStreamRequestImpl.fromJson;

  @override
  String get id;
  @override
  String get vehicleId;
  @override
  int get channel;
  @override
  int get streamTime;
  @override
  @JsonKey(ignore: true)
  _$$LiveStreamRequestImplCopyWith<_$LiveStreamRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
