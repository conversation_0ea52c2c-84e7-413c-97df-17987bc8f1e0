// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'live_stream_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LiveStreamData _$LiveStreamDataFromJson(Map<String, dynamic> json) {
  return _LiveStreamData.fromJson(json);
}

/// @nodoc
mixin _$LiveStreamData {
  String? get vehicleId => throw _privateConstructorUsedError;
  int get status => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  LiveSetting? get setting => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LiveStreamDataCopyWith<LiveStreamData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LiveStreamDataCopyWith<$Res> {
  factory $LiveStreamDataCopyWith(
          LiveStreamData value, $Res Function(LiveStreamData) then) =
      _$LiveStreamDataCopyWithImpl<$Res, LiveStreamData>;
  @useResult
  $Res call(
      {String? vehicleId, int status, String message, LiveSetting? setting});

  $LiveSettingCopyWith<$Res>? get setting;
}

/// @nodoc
class _$LiveStreamDataCopyWithImpl<$Res, $Val extends LiveStreamData>
    implements $LiveStreamDataCopyWith<$Res> {
  _$LiveStreamDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = freezed,
    Object? status = null,
    Object? message = null,
    Object? setting = freezed,
  }) {
    return _then(_value.copyWith(
      vehicleId: freezed == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      setting: freezed == setting
          ? _value.setting
          : setting // ignore: cast_nullable_to_non_nullable
              as LiveSetting?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LiveSettingCopyWith<$Res>? get setting {
    if (_value.setting == null) {
      return null;
    }

    return $LiveSettingCopyWith<$Res>(_value.setting!, (value) {
      return _then(_value.copyWith(setting: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LiveStreamDataImplCopyWith<$Res>
    implements $LiveStreamDataCopyWith<$Res> {
  factory _$$LiveStreamDataImplCopyWith(_$LiveStreamDataImpl value,
          $Res Function(_$LiveStreamDataImpl) then) =
      __$$LiveStreamDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? vehicleId, int status, String message, LiveSetting? setting});

  @override
  $LiveSettingCopyWith<$Res>? get setting;
}

/// @nodoc
class __$$LiveStreamDataImplCopyWithImpl<$Res>
    extends _$LiveStreamDataCopyWithImpl<$Res, _$LiveStreamDataImpl>
    implements _$$LiveStreamDataImplCopyWith<$Res> {
  __$$LiveStreamDataImplCopyWithImpl(
      _$LiveStreamDataImpl _value, $Res Function(_$LiveStreamDataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicleId = freezed,
    Object? status = null,
    Object? message = null,
    Object? setting = freezed,
  }) {
    return _then(_$LiveStreamDataImpl(
      vehicleId: freezed == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      setting: freezed == setting
          ? _value.setting
          : setting // ignore: cast_nullable_to_non_nullable
              as LiveSetting?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LiveStreamDataImpl extends _LiveStreamData {
  const _$LiveStreamDataImpl(
      {required this.vehicleId,
      required this.status,
      required this.message,
      required this.setting})
      : super._();

  factory _$LiveStreamDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$LiveStreamDataImplFromJson(json);

  @override
  final String? vehicleId;
  @override
  final int status;
  @override
  final String message;
  @override
  final LiveSetting? setting;

  @override
  String toString() {
    return 'LiveStreamData(vehicleId: $vehicleId, status: $status, message: $message, setting: $setting)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LiveStreamDataImpl &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.setting, setting) || other.setting == setting));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, vehicleId, status, message, setting);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LiveStreamDataImplCopyWith<_$LiveStreamDataImpl> get copyWith =>
      __$$LiveStreamDataImplCopyWithImpl<_$LiveStreamDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LiveStreamDataImplToJson(
      this,
    );
  }
}

abstract class _LiveStreamData extends LiveStreamData {
  const factory _LiveStreamData(
      {required final String? vehicleId,
      required final int status,
      required final String message,
      required final LiveSetting? setting}) = _$LiveStreamDataImpl;
  const _LiveStreamData._() : super._();

  factory _LiveStreamData.fromJson(Map<String, dynamic> json) =
      _$LiveStreamDataImpl.fromJson;

  @override
  String? get vehicleId;
  @override
  int get status;
  @override
  String get message;
  @override
  LiveSetting? get setting;
  @override
  @JsonKey(ignore: true)
  _$$LiveStreamDataImplCopyWith<_$LiveStreamDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LiveSetting _$LiveSettingFromJson(Map<String, dynamic> json) {
  return _LiveSetting.fromJson(json);
}

/// @nodoc
mixin _$LiveSetting {
  int get index => throw _privateConstructorUsedError;
  int get liveTime => throw _privateConstructorUsedError;
  int get offset => throw _privateConstructorUsedError;
  String get subscription => throw _privateConstructorUsedError;
  String get channel => throw _privateConstructorUsedError;
  String get protocol => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LiveSettingCopyWith<LiveSetting> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LiveSettingCopyWith<$Res> {
  factory $LiveSettingCopyWith(
          LiveSetting value, $Res Function(LiveSetting) then) =
      _$LiveSettingCopyWithImpl<$Res, LiveSetting>;
  @useResult
  $Res call(
      {int index,
      int liveTime,
      int offset,
      String subscription,
      String channel,
      String protocol,
      String? url});
}

/// @nodoc
class _$LiveSettingCopyWithImpl<$Res, $Val extends LiveSetting>
    implements $LiveSettingCopyWith<$Res> {
  _$LiveSettingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? liveTime = null,
    Object? offset = null,
    Object? subscription = null,
    Object? channel = null,
    Object? protocol = null,
    Object? url = freezed,
  }) {
    return _then(_value.copyWith(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      liveTime: null == liveTime
          ? _value.liveTime
          : liveTime // ignore: cast_nullable_to_non_nullable
              as int,
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      subscription: null == subscription
          ? _value.subscription
          : subscription // ignore: cast_nullable_to_non_nullable
              as String,
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as String,
      protocol: null == protocol
          ? _value.protocol
          : protocol // ignore: cast_nullable_to_non_nullable
              as String,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LiveSettingImplCopyWith<$Res>
    implements $LiveSettingCopyWith<$Res> {
  factory _$$LiveSettingImplCopyWith(
          _$LiveSettingImpl value, $Res Function(_$LiveSettingImpl) then) =
      __$$LiveSettingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int index,
      int liveTime,
      int offset,
      String subscription,
      String channel,
      String protocol,
      String? url});
}

/// @nodoc
class __$$LiveSettingImplCopyWithImpl<$Res>
    extends _$LiveSettingCopyWithImpl<$Res, _$LiveSettingImpl>
    implements _$$LiveSettingImplCopyWith<$Res> {
  __$$LiveSettingImplCopyWithImpl(
      _$LiveSettingImpl _value, $Res Function(_$LiveSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? liveTime = null,
    Object? offset = null,
    Object? subscription = null,
    Object? channel = null,
    Object? protocol = null,
    Object? url = freezed,
  }) {
    return _then(_$LiveSettingImpl(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      liveTime: null == liveTime
          ? _value.liveTime
          : liveTime // ignore: cast_nullable_to_non_nullable
              as int,
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      subscription: null == subscription
          ? _value.subscription
          : subscription // ignore: cast_nullable_to_non_nullable
              as String,
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as String,
      protocol: null == protocol
          ? _value.protocol
          : protocol // ignore: cast_nullable_to_non_nullable
              as String,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LiveSettingImpl extends _LiveSetting {
  const _$LiveSettingImpl(
      {required this.index,
      required this.liveTime,
      required this.offset,
      required this.subscription,
      required this.channel,
      required this.protocol,
      required this.url})
      : super._();

  factory _$LiveSettingImpl.fromJson(Map<String, dynamic> json) =>
      _$$LiveSettingImplFromJson(json);

  @override
  final int index;
  @override
  final int liveTime;
  @override
  final int offset;
  @override
  final String subscription;
  @override
  final String channel;
  @override
  final String protocol;
  @override
  final String? url;

  @override
  String toString() {
    return 'LiveSetting(index: $index, liveTime: $liveTime, offset: $offset, subscription: $subscription, channel: $channel, protocol: $protocol, url: $url)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LiveSettingImpl &&
            (identical(other.index, index) || other.index == index) &&
            (identical(other.liveTime, liveTime) ||
                other.liveTime == liveTime) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.subscription, subscription) ||
                other.subscription == subscription) &&
            (identical(other.channel, channel) || other.channel == channel) &&
            (identical(other.protocol, protocol) ||
                other.protocol == protocol) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, index, liveTime, offset,
      subscription, channel, protocol, url);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LiveSettingImplCopyWith<_$LiveSettingImpl> get copyWith =>
      __$$LiveSettingImplCopyWithImpl<_$LiveSettingImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LiveSettingImplToJson(
      this,
    );
  }
}

abstract class _LiveSetting extends LiveSetting {
  const factory _LiveSetting(
      {required final int index,
      required final int liveTime,
      required final int offset,
      required final String subscription,
      required final String channel,
      required final String protocol,
      required final String? url}) = _$LiveSettingImpl;
  const _LiveSetting._() : super._();

  factory _LiveSetting.fromJson(Map<String, dynamic> json) =
      _$LiveSettingImpl.fromJson;

  @override
  int get index;
  @override
  int get liveTime;
  @override
  int get offset;
  @override
  String get subscription;
  @override
  String get channel;
  @override
  String get protocol;
  @override
  String? get url;
  @override
  @JsonKey(ignore: true)
  _$$LiveSettingImplCopyWith<_$LiveSettingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
