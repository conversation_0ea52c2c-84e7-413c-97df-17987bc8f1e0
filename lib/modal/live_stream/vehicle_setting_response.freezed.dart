// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_setting_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VehicleSettingResponse _$VehicleSettingResponseFromJson(
    Map<String, dynamic> json) {
  return _VehicleSettingResponse.fromJson(json);
}

/// @nodoc
mixin _$VehicleSettingResponse {
  List<VehicleWithSetting> get vehicle => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VehicleSettingResponseCopyWith<VehicleSettingResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleSettingResponseCopyWith<$Res> {
  factory $VehicleSettingResponseCopyWith(VehicleSettingResponse value,
          $Res Function(VehicleSettingResponse) then) =
      _$VehicleSettingResponseCopyWithImpl<$Res, VehicleSettingResponse>;
  @useResult
  $Res call({List<VehicleWithSetting> vehicle});
}

/// @nodoc
class _$VehicleSettingResponseCopyWithImpl<$Res,
        $Val extends VehicleSettingResponse>
    implements $VehicleSettingResponseCopyWith<$Res> {
  _$VehicleSettingResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicle = null,
  }) {
    return _then(_value.copyWith(
      vehicle: null == vehicle
          ? _value.vehicle
          : vehicle // ignore: cast_nullable_to_non_nullable
              as List<VehicleWithSetting>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleSettingResponseImplCopyWith<$Res>
    implements $VehicleSettingResponseCopyWith<$Res> {
  factory _$$VehicleSettingResponseImplCopyWith(
          _$VehicleSettingResponseImpl value,
          $Res Function(_$VehicleSettingResponseImpl) then) =
      __$$VehicleSettingResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<VehicleWithSetting> vehicle});
}

/// @nodoc
class __$$VehicleSettingResponseImplCopyWithImpl<$Res>
    extends _$VehicleSettingResponseCopyWithImpl<$Res,
        _$VehicleSettingResponseImpl>
    implements _$$VehicleSettingResponseImplCopyWith<$Res> {
  __$$VehicleSettingResponseImplCopyWithImpl(
      _$VehicleSettingResponseImpl _value,
      $Res Function(_$VehicleSettingResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vehicle = null,
  }) {
    return _then(_$VehicleSettingResponseImpl(
      vehicle: null == vehicle
          ? _value._vehicle
          : vehicle // ignore: cast_nullable_to_non_nullable
              as List<VehicleWithSetting>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleSettingResponseImpl extends _VehicleSettingResponse {
  const _$VehicleSettingResponseImpl(
      {required final List<VehicleWithSetting> vehicle})
      : _vehicle = vehicle,
        super._();

  factory _$VehicleSettingResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleSettingResponseImplFromJson(json);

  final List<VehicleWithSetting> _vehicle;
  @override
  List<VehicleWithSetting> get vehicle {
    if (_vehicle is EqualUnmodifiableListView) return _vehicle;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vehicle);
  }

  @override
  String toString() {
    return 'VehicleSettingResponse(vehicle: $vehicle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleSettingResponseImpl &&
            const DeepCollectionEquality().equals(other._vehicle, _vehicle));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_vehicle));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleSettingResponseImplCopyWith<_$VehicleSettingResponseImpl>
      get copyWith => __$$VehicleSettingResponseImplCopyWithImpl<
          _$VehicleSettingResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleSettingResponseImplToJson(
      this,
    );
  }
}

abstract class _VehicleSettingResponse extends VehicleSettingResponse {
  const factory _VehicleSettingResponse(
          {required final List<VehicleWithSetting> vehicle}) =
      _$VehicleSettingResponseImpl;
  const _VehicleSettingResponse._() : super._();

  factory _VehicleSettingResponse.fromJson(Map<String, dynamic> json) =
      _$VehicleSettingResponseImpl.fromJson;

  @override
  List<VehicleWithSetting> get vehicle;
  @override
  @JsonKey(ignore: true)
  _$$VehicleSettingResponseImplCopyWith<_$VehicleSettingResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

VehicleWithSetting _$VehicleWithSettingFromJson(Map<String, dynamic> json) {
  return _VehicleWithSetting.fromJson(json);
}

/// @nodoc
mixin _$VehicleWithSetting {
  String get id => throw _privateConstructorUsedError;
  String get plate => throw _privateConstructorUsedError;
  List<VehicleSetting> get settings => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VehicleWithSettingCopyWith<VehicleWithSetting> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleWithSettingCopyWith<$Res> {
  factory $VehicleWithSettingCopyWith(
          VehicleWithSetting value, $Res Function(VehicleWithSetting) then) =
      _$VehicleWithSettingCopyWithImpl<$Res, VehicleWithSetting>;
  @useResult
  $Res call({String id, String plate, List<VehicleSetting> settings});
}

/// @nodoc
class _$VehicleWithSettingCopyWithImpl<$Res, $Val extends VehicleWithSetting>
    implements $VehicleWithSettingCopyWith<$Res> {
  _$VehicleWithSettingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? settings = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      settings: null == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as List<VehicleSetting>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleWithSettingImplCopyWith<$Res>
    implements $VehicleWithSettingCopyWith<$Res> {
  factory _$$VehicleWithSettingImplCopyWith(_$VehicleWithSettingImpl value,
          $Res Function(_$VehicleWithSettingImpl) then) =
      __$$VehicleWithSettingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String plate, List<VehicleSetting> settings});
}

/// @nodoc
class __$$VehicleWithSettingImplCopyWithImpl<$Res>
    extends _$VehicleWithSettingCopyWithImpl<$Res, _$VehicleWithSettingImpl>
    implements _$$VehicleWithSettingImplCopyWith<$Res> {
  __$$VehicleWithSettingImplCopyWithImpl(_$VehicleWithSettingImpl _value,
      $Res Function(_$VehicleWithSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? plate = null,
    Object? settings = null,
  }) {
    return _then(_$VehicleWithSettingImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      plate: null == plate
          ? _value.plate
          : plate // ignore: cast_nullable_to_non_nullable
              as String,
      settings: null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as List<VehicleSetting>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleWithSettingImpl extends _VehicleWithSetting {
  const _$VehicleWithSettingImpl(
      {required this.id,
      required this.plate,
      required final List<VehicleSetting> settings})
      : _settings = settings,
        super._();

  factory _$VehicleWithSettingImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleWithSettingImplFromJson(json);

  @override
  final String id;
  @override
  final String plate;
  final List<VehicleSetting> _settings;
  @override
  List<VehicleSetting> get settings {
    if (_settings is EqualUnmodifiableListView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_settings);
  }

  @override
  String toString() {
    return 'VehicleWithSetting(id: $id, plate: $plate, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleWithSettingImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.plate, plate) || other.plate == plate) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, plate, const DeepCollectionEquality().hash(_settings));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleWithSettingImplCopyWith<_$VehicleWithSettingImpl> get copyWith =>
      __$$VehicleWithSettingImplCopyWithImpl<_$VehicleWithSettingImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleWithSettingImplToJson(
      this,
    );
  }
}

abstract class _VehicleWithSetting extends VehicleWithSetting {
  const factory _VehicleWithSetting(
      {required final String id,
      required final String plate,
      required final List<VehicleSetting> settings}) = _$VehicleWithSettingImpl;
  const _VehicleWithSetting._() : super._();

  factory _VehicleWithSetting.fromJson(Map<String, dynamic> json) =
      _$VehicleWithSettingImpl.fromJson;

  @override
  String get id;
  @override
  String get plate;
  @override
  List<VehicleSetting> get settings;
  @override
  @JsonKey(ignore: true)
  _$$VehicleWithSettingImplCopyWith<_$VehicleWithSettingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VehicleSetting _$VehicleSettingFromJson(Map<String, dynamic> json) {
  return _VehicleSetting.fromJson(json);
}

/// @nodoc
mixin _$VehicleSetting {
  String get id => throw _privateConstructorUsedError;
  String get label => throw _privateConstructorUsedError;
  int get index => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VehicleSettingCopyWith<VehicleSetting> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleSettingCopyWith<$Res> {
  factory $VehicleSettingCopyWith(
          VehicleSetting value, $Res Function(VehicleSetting) then) =
      _$VehicleSettingCopyWithImpl<$Res, VehicleSetting>;
  @useResult
  $Res call({String id, String label, int index});
}

/// @nodoc
class _$VehicleSettingCopyWithImpl<$Res, $Val extends VehicleSetting>
    implements $VehicleSettingCopyWith<$Res> {
  _$VehicleSettingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? index = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleSettingImplCopyWith<$Res>
    implements $VehicleSettingCopyWith<$Res> {
  factory _$$VehicleSettingImplCopyWith(_$VehicleSettingImpl value,
          $Res Function(_$VehicleSettingImpl) then) =
      __$$VehicleSettingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String label, int index});
}

/// @nodoc
class __$$VehicleSettingImplCopyWithImpl<$Res>
    extends _$VehicleSettingCopyWithImpl<$Res, _$VehicleSettingImpl>
    implements _$$VehicleSettingImplCopyWith<$Res> {
  __$$VehicleSettingImplCopyWithImpl(
      _$VehicleSettingImpl _value, $Res Function(_$VehicleSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? index = null,
  }) {
    return _then(_$VehicleSettingImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleSettingImpl extends _VehicleSetting {
  const _$VehicleSettingImpl(
      {required this.id, required this.label, required this.index})
      : super._();

  factory _$VehicleSettingImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleSettingImplFromJson(json);

  @override
  final String id;
  @override
  final String label;
  @override
  final int index;

  @override
  String toString() {
    return 'VehicleSetting(id: $id, label: $label, index: $index)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleSettingImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.index, index) || other.index == index));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, label, index);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleSettingImplCopyWith<_$VehicleSettingImpl> get copyWith =>
      __$$VehicleSettingImplCopyWithImpl<_$VehicleSettingImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleSettingImplToJson(
      this,
    );
  }
}

abstract class _VehicleSetting extends VehicleSetting {
  const factory _VehicleSetting(
      {required final String id,
      required final String label,
      required final int index}) = _$VehicleSettingImpl;
  const _VehicleSetting._() : super._();

  factory _VehicleSetting.fromJson(Map<String, dynamic> json) =
      _$VehicleSettingImpl.fromJson;

  @override
  String get id;
  @override
  String get label;
  @override
  int get index;
  @override
  @JsonKey(ignore: true)
  _$$VehicleSettingImplCopyWith<_$VehicleSettingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
