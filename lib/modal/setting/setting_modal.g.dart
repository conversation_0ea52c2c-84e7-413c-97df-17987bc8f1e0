// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'setting_modal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MonitorSettingModalImpl _$$MonitorSettingModalImplFromJson(
        Map<String, dynamic> json) =>
    _$MonitorSettingModalImpl(
      typeOfMap: $enumDecode(_$TypeOfMapEnumMap, json['typeOfMap']),
      showStation: json['showStation'] as bool,
      showRegion: json['showRegion'] as bool,
      showTotalDistance: json['showTotalDistance'] as bool,
      isOverSpeedByRoad: json['isOverSpeedByRoad'] as bool,
      timeToGetListVehicle: (json['timeToGetListVehicle'] as num).toDouble(),
      avemaTypeTheme:
          $enumDecode(_$AvemaTypeThemeEnumMap, json['avemaTypeTheme']),
      primaryColor:
          $enumDecode(_$AvemaPrimaryColorEnumMap, json['primaryColor']),
    );

Map<String, dynamic> _$$MonitorSettingModalImplToJson(
        _$MonitorSettingModalImpl instance) =>
    <String, dynamic>{
      'typeOfMap': _$TypeOfMapEnumMap[instance.typeOfMap]!,
      'showStation': instance.showStation,
      'showRegion': instance.showRegion,
      'showTotalDistance': instance.showTotalDistance,
      'isOverSpeedByRoad': instance.isOverSpeedByRoad,
      'timeToGetListVehicle': instance.timeToGetListVehicle,
      'avemaTypeTheme': _$AvemaTypeThemeEnumMap[instance.avemaTypeTheme]!,
      'primaryColor': _$AvemaPrimaryColorEnumMap[instance.primaryColor]!,
    };

const _$TypeOfMapEnumMap = {
  TypeOfMap.standard: 'standard',
  TypeOfMap.satellite: 'satellite',
  TypeOfMap.traffic: 'traffic',
};

const _$AvemaTypeThemeEnumMap = {
  AvemaTypeTheme.light: 'light',
  AvemaTypeTheme.dark: 'dark',
};

const _$AvemaPrimaryColorEnumMap = {
  AvemaPrimaryColor.organe: 'organe',
  AvemaPrimaryColor.pink: 'pink',
  AvemaPrimaryColor.blue: 'blue',
  AvemaPrimaryColor.green: 'green',
  AvemaPrimaryColor.cyan: 'cyan',
};

_$HistorySettingModalImpl _$$HistorySettingModalImplFromJson(
        Map<String, dynamic> json) =>
    _$HistorySettingModalImpl(
      typeOfMap: $enumDecode(_$TypeOfMapEnumMap, json['typeOfMap']),
      heightRouteType:
          $enumDecode(_$HeightRouteTypeEnumMap, json['heightRouteType']),
      showStopPoint: json['showStopPoint'] as bool,
      isFocusWhenPlay: json['isFocusWhenPlay'] as bool,
      isOverSpeedByRoad: json['isOverSpeedByRoad'] as bool,
    );

Map<String, dynamic> _$$HistorySettingModalImplToJson(
        _$HistorySettingModalImpl instance) =>
    <String, dynamic>{
      'typeOfMap': _$TypeOfMapEnumMap[instance.typeOfMap]!,
      'heightRouteType': _$HeightRouteTypeEnumMap[instance.heightRouteType]!,
      'showStopPoint': instance.showStopPoint,
      'isFocusWhenPlay': instance.isFocusWhenPlay,
      'isOverSpeedByRoad': instance.isOverSpeedByRoad,
    };

const _$HeightRouteTypeEnumMap = {
  HeightRouteType.low: 'low',
  HeightRouteType.normal: 'normal',
  HeightRouteType.high: 'high',
};
