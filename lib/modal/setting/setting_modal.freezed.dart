// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'setting_modal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MonitorSettingModal _$MonitorSettingModalFromJson(Map<String, dynamic> json) {
  return _MonitorSettingModal.fromJson(json);
}

/// @nodoc
mixin _$MonitorSettingModal {
  TypeOfMap get typeOfMap => throw _privateConstructorUsedError;
  bool get showStation => throw _privateConstructorUsedError;
  bool get showRegion => throw _privateConstructorUsedError;
  bool get showTotalDistance => throw _privateConstructorUsedError;
  bool get isOverSpeedByRoad => throw _privateConstructorUsedError;
  double get timeToGetListVehicle => throw _privateConstructorUsedError;
  AvemaTypeTheme get avemaTypeTheme => throw _privateConstructorUsedError;
  AvemaPrimaryColor get primaryColor => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MonitorSettingModalCopyWith<MonitorSettingModal> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MonitorSettingModalCopyWith<$Res> {
  factory $MonitorSettingModalCopyWith(
          MonitorSettingModal value, $Res Function(MonitorSettingModal) then) =
      _$MonitorSettingModalCopyWithImpl<$Res, MonitorSettingModal>;
  @useResult
  $Res call(
      {TypeOfMap typeOfMap,
      bool showStation,
      bool showRegion,
      bool showTotalDistance,
      bool isOverSpeedByRoad,
      double timeToGetListVehicle,
      AvemaTypeTheme avemaTypeTheme,
      AvemaPrimaryColor primaryColor});
}

/// @nodoc
class _$MonitorSettingModalCopyWithImpl<$Res, $Val extends MonitorSettingModal>
    implements $MonitorSettingModalCopyWith<$Res> {
  _$MonitorSettingModalCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? typeOfMap = null,
    Object? showStation = null,
    Object? showRegion = null,
    Object? showTotalDistance = null,
    Object? isOverSpeedByRoad = null,
    Object? timeToGetListVehicle = null,
    Object? avemaTypeTheme = null,
    Object? primaryColor = null,
  }) {
    return _then(_value.copyWith(
      typeOfMap: null == typeOfMap
          ? _value.typeOfMap
          : typeOfMap // ignore: cast_nullable_to_non_nullable
              as TypeOfMap,
      showStation: null == showStation
          ? _value.showStation
          : showStation // ignore: cast_nullable_to_non_nullable
              as bool,
      showRegion: null == showRegion
          ? _value.showRegion
          : showRegion // ignore: cast_nullable_to_non_nullable
              as bool,
      showTotalDistance: null == showTotalDistance
          ? _value.showTotalDistance
          : showTotalDistance // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
      timeToGetListVehicle: null == timeToGetListVehicle
          ? _value.timeToGetListVehicle
          : timeToGetListVehicle // ignore: cast_nullable_to_non_nullable
              as double,
      avemaTypeTheme: null == avemaTypeTheme
          ? _value.avemaTypeTheme
          : avemaTypeTheme // ignore: cast_nullable_to_non_nullable
              as AvemaTypeTheme,
      primaryColor: null == primaryColor
          ? _value.primaryColor
          : primaryColor // ignore: cast_nullable_to_non_nullable
              as AvemaPrimaryColor,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MonitorSettingModalImplCopyWith<$Res>
    implements $MonitorSettingModalCopyWith<$Res> {
  factory _$$MonitorSettingModalImplCopyWith(_$MonitorSettingModalImpl value,
          $Res Function(_$MonitorSettingModalImpl) then) =
      __$$MonitorSettingModalImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {TypeOfMap typeOfMap,
      bool showStation,
      bool showRegion,
      bool showTotalDistance,
      bool isOverSpeedByRoad,
      double timeToGetListVehicle,
      AvemaTypeTheme avemaTypeTheme,
      AvemaPrimaryColor primaryColor});
}

/// @nodoc
class __$$MonitorSettingModalImplCopyWithImpl<$Res>
    extends _$MonitorSettingModalCopyWithImpl<$Res, _$MonitorSettingModalImpl>
    implements _$$MonitorSettingModalImplCopyWith<$Res> {
  __$$MonitorSettingModalImplCopyWithImpl(_$MonitorSettingModalImpl _value,
      $Res Function(_$MonitorSettingModalImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? typeOfMap = null,
    Object? showStation = null,
    Object? showRegion = null,
    Object? showTotalDistance = null,
    Object? isOverSpeedByRoad = null,
    Object? timeToGetListVehicle = null,
    Object? avemaTypeTheme = null,
    Object? primaryColor = null,
  }) {
    return _then(_$MonitorSettingModalImpl(
      typeOfMap: null == typeOfMap
          ? _value.typeOfMap
          : typeOfMap // ignore: cast_nullable_to_non_nullable
              as TypeOfMap,
      showStation: null == showStation
          ? _value.showStation
          : showStation // ignore: cast_nullable_to_non_nullable
              as bool,
      showRegion: null == showRegion
          ? _value.showRegion
          : showRegion // ignore: cast_nullable_to_non_nullable
              as bool,
      showTotalDistance: null == showTotalDistance
          ? _value.showTotalDistance
          : showTotalDistance // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
      timeToGetListVehicle: null == timeToGetListVehicle
          ? _value.timeToGetListVehicle
          : timeToGetListVehicle // ignore: cast_nullable_to_non_nullable
              as double,
      avemaTypeTheme: null == avemaTypeTheme
          ? _value.avemaTypeTheme
          : avemaTypeTheme // ignore: cast_nullable_to_non_nullable
              as AvemaTypeTheme,
      primaryColor: null == primaryColor
          ? _value.primaryColor
          : primaryColor // ignore: cast_nullable_to_non_nullable
              as AvemaPrimaryColor,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MonitorSettingModalImpl extends _MonitorSettingModal {
  const _$MonitorSettingModalImpl(
      {required this.typeOfMap,
      required this.showStation,
      required this.showRegion,
      required this.showTotalDistance,
      required this.isOverSpeedByRoad,
      required this.timeToGetListVehicle,
      required this.avemaTypeTheme,
      required this.primaryColor})
      : super._();

  factory _$MonitorSettingModalImpl.fromJson(Map<String, dynamic> json) =>
      _$$MonitorSettingModalImplFromJson(json);

  @override
  final TypeOfMap typeOfMap;
  @override
  final bool showStation;
  @override
  final bool showRegion;
  @override
  final bool showTotalDistance;
  @override
  final bool isOverSpeedByRoad;
  @override
  final double timeToGetListVehicle;
  @override
  final AvemaTypeTheme avemaTypeTheme;
  @override
  final AvemaPrimaryColor primaryColor;

  @override
  String toString() {
    return 'MonitorSettingModal(typeOfMap: $typeOfMap, showStation: $showStation, showRegion: $showRegion, showTotalDistance: $showTotalDistance, isOverSpeedByRoad: $isOverSpeedByRoad, timeToGetListVehicle: $timeToGetListVehicle, avemaTypeTheme: $avemaTypeTheme, primaryColor: $primaryColor)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonitorSettingModalImpl &&
            (identical(other.typeOfMap, typeOfMap) ||
                other.typeOfMap == typeOfMap) &&
            (identical(other.showStation, showStation) ||
                other.showStation == showStation) &&
            (identical(other.showRegion, showRegion) ||
                other.showRegion == showRegion) &&
            (identical(other.showTotalDistance, showTotalDistance) ||
                other.showTotalDistance == showTotalDistance) &&
            (identical(other.isOverSpeedByRoad, isOverSpeedByRoad) ||
                other.isOverSpeedByRoad == isOverSpeedByRoad) &&
            (identical(other.timeToGetListVehicle, timeToGetListVehicle) ||
                other.timeToGetListVehicle == timeToGetListVehicle) &&
            (identical(other.avemaTypeTheme, avemaTypeTheme) ||
                other.avemaTypeTheme == avemaTypeTheme) &&
            (identical(other.primaryColor, primaryColor) ||
                other.primaryColor == primaryColor));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      typeOfMap,
      showStation,
      showRegion,
      showTotalDistance,
      isOverSpeedByRoad,
      timeToGetListVehicle,
      avemaTypeTheme,
      primaryColor);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MonitorSettingModalImplCopyWith<_$MonitorSettingModalImpl> get copyWith =>
      __$$MonitorSettingModalImplCopyWithImpl<_$MonitorSettingModalImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MonitorSettingModalImplToJson(
      this,
    );
  }
}

abstract class _MonitorSettingModal extends MonitorSettingModal {
  const factory _MonitorSettingModal(
          {required final TypeOfMap typeOfMap,
          required final bool showStation,
          required final bool showRegion,
          required final bool showTotalDistance,
          required final bool isOverSpeedByRoad,
          required final double timeToGetListVehicle,
          required final AvemaTypeTheme avemaTypeTheme,
          required final AvemaPrimaryColor primaryColor}) =
      _$MonitorSettingModalImpl;
  const _MonitorSettingModal._() : super._();

  factory _MonitorSettingModal.fromJson(Map<String, dynamic> json) =
      _$MonitorSettingModalImpl.fromJson;

  @override
  TypeOfMap get typeOfMap;
  @override
  bool get showStation;
  @override
  bool get showRegion;
  @override
  bool get showTotalDistance;
  @override
  bool get isOverSpeedByRoad;
  @override
  double get timeToGetListVehicle;
  @override
  AvemaTypeTheme get avemaTypeTheme;
  @override
  AvemaPrimaryColor get primaryColor;
  @override
  @JsonKey(ignore: true)
  _$$MonitorSettingModalImplCopyWith<_$MonitorSettingModalImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

HistorySettingModal _$HistorySettingModalFromJson(Map<String, dynamic> json) {
  return _HistorySettingModal.fromJson(json);
}

/// @nodoc
mixin _$HistorySettingModal {
  TypeOfMap get typeOfMap => throw _privateConstructorUsedError;
  HeightRouteType get heightRouteType => throw _privateConstructorUsedError;
  bool get showStopPoint => throw _privateConstructorUsedError;
  bool get isFocusWhenPlay => throw _privateConstructorUsedError;
  bool get isOverSpeedByRoad => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HistorySettingModalCopyWith<HistorySettingModal> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistorySettingModalCopyWith<$Res> {
  factory $HistorySettingModalCopyWith(
          HistorySettingModal value, $Res Function(HistorySettingModal) then) =
      _$HistorySettingModalCopyWithImpl<$Res, HistorySettingModal>;
  @useResult
  $Res call(
      {TypeOfMap typeOfMap,
      HeightRouteType heightRouteType,
      bool showStopPoint,
      bool isFocusWhenPlay,
      bool isOverSpeedByRoad});
}

/// @nodoc
class _$HistorySettingModalCopyWithImpl<$Res, $Val extends HistorySettingModal>
    implements $HistorySettingModalCopyWith<$Res> {
  _$HistorySettingModalCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? typeOfMap = null,
    Object? heightRouteType = null,
    Object? showStopPoint = null,
    Object? isFocusWhenPlay = null,
    Object? isOverSpeedByRoad = null,
  }) {
    return _then(_value.copyWith(
      typeOfMap: null == typeOfMap
          ? _value.typeOfMap
          : typeOfMap // ignore: cast_nullable_to_non_nullable
              as TypeOfMap,
      heightRouteType: null == heightRouteType
          ? _value.heightRouteType
          : heightRouteType // ignore: cast_nullable_to_non_nullable
              as HeightRouteType,
      showStopPoint: null == showStopPoint
          ? _value.showStopPoint
          : showStopPoint // ignore: cast_nullable_to_non_nullable
              as bool,
      isFocusWhenPlay: null == isFocusWhenPlay
          ? _value.isFocusWhenPlay
          : isFocusWhenPlay // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HistorySettingModalImplCopyWith<$Res>
    implements $HistorySettingModalCopyWith<$Res> {
  factory _$$HistorySettingModalImplCopyWith(_$HistorySettingModalImpl value,
          $Res Function(_$HistorySettingModalImpl) then) =
      __$$HistorySettingModalImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {TypeOfMap typeOfMap,
      HeightRouteType heightRouteType,
      bool showStopPoint,
      bool isFocusWhenPlay,
      bool isOverSpeedByRoad});
}

/// @nodoc
class __$$HistorySettingModalImplCopyWithImpl<$Res>
    extends _$HistorySettingModalCopyWithImpl<$Res, _$HistorySettingModalImpl>
    implements _$$HistorySettingModalImplCopyWith<$Res> {
  __$$HistorySettingModalImplCopyWithImpl(_$HistorySettingModalImpl _value,
      $Res Function(_$HistorySettingModalImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? typeOfMap = null,
    Object? heightRouteType = null,
    Object? showStopPoint = null,
    Object? isFocusWhenPlay = null,
    Object? isOverSpeedByRoad = null,
  }) {
    return _then(_$HistorySettingModalImpl(
      typeOfMap: null == typeOfMap
          ? _value.typeOfMap
          : typeOfMap // ignore: cast_nullable_to_non_nullable
              as TypeOfMap,
      heightRouteType: null == heightRouteType
          ? _value.heightRouteType
          : heightRouteType // ignore: cast_nullable_to_non_nullable
              as HeightRouteType,
      showStopPoint: null == showStopPoint
          ? _value.showStopPoint
          : showStopPoint // ignore: cast_nullable_to_non_nullable
              as bool,
      isFocusWhenPlay: null == isFocusWhenPlay
          ? _value.isFocusWhenPlay
          : isFocusWhenPlay // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverSpeedByRoad: null == isOverSpeedByRoad
          ? _value.isOverSpeedByRoad
          : isOverSpeedByRoad // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HistorySettingModalImpl extends _HistorySettingModal {
  const _$HistorySettingModalImpl(
      {required this.typeOfMap,
      required this.heightRouteType,
      required this.showStopPoint,
      required this.isFocusWhenPlay,
      required this.isOverSpeedByRoad})
      : super._();

  factory _$HistorySettingModalImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistorySettingModalImplFromJson(json);

  @override
  final TypeOfMap typeOfMap;
  @override
  final HeightRouteType heightRouteType;
  @override
  final bool showStopPoint;
  @override
  final bool isFocusWhenPlay;
  @override
  final bool isOverSpeedByRoad;

  @override
  String toString() {
    return 'HistorySettingModal(typeOfMap: $typeOfMap, heightRouteType: $heightRouteType, showStopPoint: $showStopPoint, isFocusWhenPlay: $isFocusWhenPlay, isOverSpeedByRoad: $isOverSpeedByRoad)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistorySettingModalImpl &&
            (identical(other.typeOfMap, typeOfMap) ||
                other.typeOfMap == typeOfMap) &&
            (identical(other.heightRouteType, heightRouteType) ||
                other.heightRouteType == heightRouteType) &&
            (identical(other.showStopPoint, showStopPoint) ||
                other.showStopPoint == showStopPoint) &&
            (identical(other.isFocusWhenPlay, isFocusWhenPlay) ||
                other.isFocusWhenPlay == isFocusWhenPlay) &&
            (identical(other.isOverSpeedByRoad, isOverSpeedByRoad) ||
                other.isOverSpeedByRoad == isOverSpeedByRoad));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, typeOfMap, heightRouteType,
      showStopPoint, isFocusWhenPlay, isOverSpeedByRoad);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HistorySettingModalImplCopyWith<_$HistorySettingModalImpl> get copyWith =>
      __$$HistorySettingModalImplCopyWithImpl<_$HistorySettingModalImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HistorySettingModalImplToJson(
      this,
    );
  }
}

abstract class _HistorySettingModal extends HistorySettingModal {
  const factory _HistorySettingModal(
      {required final TypeOfMap typeOfMap,
      required final HeightRouteType heightRouteType,
      required final bool showStopPoint,
      required final bool isFocusWhenPlay,
      required final bool isOverSpeedByRoad}) = _$HistorySettingModalImpl;
  const _HistorySettingModal._() : super._();

  factory _HistorySettingModal.fromJson(Map<String, dynamic> json) =
      _$HistorySettingModalImpl.fromJson;

  @override
  TypeOfMap get typeOfMap;
  @override
  HeightRouteType get heightRouteType;
  @override
  bool get showStopPoint;
  @override
  bool get isFocusWhenPlay;
  @override
  bool get isOverSpeedByRoad;
  @override
  @JsonKey(ignore: true)
  _$$HistorySettingModalImplCopyWith<_$HistorySettingModalImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
