// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'curl_representation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CurlRepresentationImpl _$$CurlRepresentationImplFromJson(
        Map<String, dynamic> json) =>
    _$CurlRepresentationImpl(
      endPoint: json['endPoint'] as String,
      id: json['id'] as String,
      statusCode: (json['statusCode'] as num?)?.toInt(),
      typeOfHTTPRequest:
          $enumDecode(_$TypeOfHTTPRequestEnumMap, json['typeOfHTTPRequest']),
      curl: json['curl'] as String,
      response: json['response'] as Map<String, dynamic>?,
      error: json['error'] as String?,
      dateTime: json['dateTime'] as String,
    );

Map<String, dynamic> _$$CurlRepresentationImplToJson(
        _$CurlRepresentationImpl instance) =>
    <String, dynamic>{
      'endPoint': instance.endPoint,
      'id': instance.id,
      'statusCode': instance.statusCode,
      'typeOfHTTPRequest':
          _$TypeOfHTTPRequestEnumMap[instance.typeOfHTTPRequest]!,
      'curl': instance.curl,
      'response': instance.response,
      'error': instance.error,
      'dateTime': instance.dateTime,
    };

const _$TypeOfHTTPRequestEnumMap = {
  TypeOfHTTPRequest.get: 'get',
  TypeOfHTTPRequest.post: 'post',
  TypeOfHTTPRequest.delete: 'delete',
  TypeOfHTTPRequest.put: 'put',
  TypeOfHTTPRequest.errorType: 'errorType',
};
