// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'curl_representation.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CurlRepresentation _$CurlRepresentationFromJson(Map<String, dynamic> json) {
  return _CurlRepresentation.fromJson(json);
}

/// @nodoc
mixin _$CurlRepresentation {
  String get endPoint => throw _privateConstructorUsedError;
  String get id => throw _privateConstructorUsedError;
  int? get statusCode => throw _privateConstructorUsedError;
  TypeOfHTTPRequest get typeOfHTTPRequest => throw _privateConstructorUsedError;
  String get curl => throw _privateConstructorUsedError;
  Map<String, dynamic>? get response => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  String get dateTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CurlRepresentationCopyWith<CurlRepresentation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CurlRepresentationCopyWith<$Res> {
  factory $CurlRepresentationCopyWith(
          CurlRepresentation value, $Res Function(CurlRepresentation) then) =
      _$CurlRepresentationCopyWithImpl<$Res, CurlRepresentation>;
  @useResult
  $Res call(
      {String endPoint,
      String id,
      int? statusCode,
      TypeOfHTTPRequest typeOfHTTPRequest,
      String curl,
      Map<String, dynamic>? response,
      String? error,
      String dateTime});
}

/// @nodoc
class _$CurlRepresentationCopyWithImpl<$Res, $Val extends CurlRepresentation>
    implements $CurlRepresentationCopyWith<$Res> {
  _$CurlRepresentationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? endPoint = null,
    Object? id = null,
    Object? statusCode = freezed,
    Object? typeOfHTTPRequest = null,
    Object? curl = null,
    Object? response = freezed,
    Object? error = freezed,
    Object? dateTime = null,
  }) {
    return _then(_value.copyWith(
      endPoint: null == endPoint
          ? _value.endPoint
          : endPoint // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      typeOfHTTPRequest: null == typeOfHTTPRequest
          ? _value.typeOfHTTPRequest
          : typeOfHTTPRequest // ignore: cast_nullable_to_non_nullable
              as TypeOfHTTPRequest,
      curl: null == curl
          ? _value.curl
          : curl // ignore: cast_nullable_to_non_nullable
              as String,
      response: freezed == response
          ? _value.response
          : response // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      dateTime: null == dateTime
          ? _value.dateTime
          : dateTime // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CurlRepresentationImplCopyWith<$Res>
    implements $CurlRepresentationCopyWith<$Res> {
  factory _$$CurlRepresentationImplCopyWith(_$CurlRepresentationImpl value,
          $Res Function(_$CurlRepresentationImpl) then) =
      __$$CurlRepresentationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String endPoint,
      String id,
      int? statusCode,
      TypeOfHTTPRequest typeOfHTTPRequest,
      String curl,
      Map<String, dynamic>? response,
      String? error,
      String dateTime});
}

/// @nodoc
class __$$CurlRepresentationImplCopyWithImpl<$Res>
    extends _$CurlRepresentationCopyWithImpl<$Res, _$CurlRepresentationImpl>
    implements _$$CurlRepresentationImplCopyWith<$Res> {
  __$$CurlRepresentationImplCopyWithImpl(_$CurlRepresentationImpl _value,
      $Res Function(_$CurlRepresentationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? endPoint = null,
    Object? id = null,
    Object? statusCode = freezed,
    Object? typeOfHTTPRequest = null,
    Object? curl = null,
    Object? response = freezed,
    Object? error = freezed,
    Object? dateTime = null,
  }) {
    return _then(_$CurlRepresentationImpl(
      endPoint: null == endPoint
          ? _value.endPoint
          : endPoint // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      typeOfHTTPRequest: null == typeOfHTTPRequest
          ? _value.typeOfHTTPRequest
          : typeOfHTTPRequest // ignore: cast_nullable_to_non_nullable
              as TypeOfHTTPRequest,
      curl: null == curl
          ? _value.curl
          : curl // ignore: cast_nullable_to_non_nullable
              as String,
      response: freezed == response
          ? _value._response
          : response // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      dateTime: null == dateTime
          ? _value.dateTime
          : dateTime // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CurlRepresentationImpl extends _CurlRepresentation {
  const _$CurlRepresentationImpl(
      {required this.endPoint,
      required this.id,
      required this.statusCode,
      required this.typeOfHTTPRequest,
      required this.curl,
      required final Map<String, dynamic>? response,
      required this.error,
      required this.dateTime})
      : _response = response,
        super._();

  factory _$CurlRepresentationImpl.fromJson(Map<String, dynamic> json) =>
      _$$CurlRepresentationImplFromJson(json);

  @override
  final String endPoint;
  @override
  final String id;
  @override
  final int? statusCode;
  @override
  final TypeOfHTTPRequest typeOfHTTPRequest;
  @override
  final String curl;
  final Map<String, dynamic>? _response;
  @override
  Map<String, dynamic>? get response {
    final value = _response;
    if (value == null) return null;
    if (_response is EqualUnmodifiableMapView) return _response;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final String? error;
  @override
  final String dateTime;

  @override
  String toString() {
    return 'CurlRepresentation(endPoint: $endPoint, id: $id, statusCode: $statusCode, typeOfHTTPRequest: $typeOfHTTPRequest, curl: $curl, response: $response, error: $error, dateTime: $dateTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CurlRepresentationImpl &&
            (identical(other.endPoint, endPoint) ||
                other.endPoint == endPoint) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.typeOfHTTPRequest, typeOfHTTPRequest) ||
                other.typeOfHTTPRequest == typeOfHTTPRequest) &&
            (identical(other.curl, curl) || other.curl == curl) &&
            const DeepCollectionEquality().equals(other._response, _response) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.dateTime, dateTime) ||
                other.dateTime == dateTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      endPoint,
      id,
      statusCode,
      typeOfHTTPRequest,
      curl,
      const DeepCollectionEquality().hash(_response),
      error,
      dateTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CurlRepresentationImplCopyWith<_$CurlRepresentationImpl> get copyWith =>
      __$$CurlRepresentationImplCopyWithImpl<_$CurlRepresentationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CurlRepresentationImplToJson(
      this,
    );
  }
}

abstract class _CurlRepresentation extends CurlRepresentation {
  const factory _CurlRepresentation(
      {required final String endPoint,
      required final String id,
      required final int? statusCode,
      required final TypeOfHTTPRequest typeOfHTTPRequest,
      required final String curl,
      required final Map<String, dynamic>? response,
      required final String? error,
      required final String dateTime}) = _$CurlRepresentationImpl;
  const _CurlRepresentation._() : super._();

  factory _CurlRepresentation.fromJson(Map<String, dynamic> json) =
      _$CurlRepresentationImpl.fromJson;

  @override
  String get endPoint;
  @override
  String get id;
  @override
  int? get statusCode;
  @override
  TypeOfHTTPRequest get typeOfHTTPRequest;
  @override
  String get curl;
  @override
  Map<String, dynamic>? get response;
  @override
  String? get error;
  @override
  String get dateTime;
  @override
  @JsonKey(ignore: true)
  _$$CurlRepresentationImplCopyWith<_$CurlRepresentationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
