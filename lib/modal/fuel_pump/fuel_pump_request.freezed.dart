// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'fuel_pump_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FuelPumpRequest _$FuelPumpRequestFromJson(Map<String, dynamic> json) {
  return _FuelPumpRequest.fromJson(json);
}

/// @nodoc
mixin _$FuelPumpRequest {
  String get command => throw _privateConstructorUsedError;
  String get id => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FuelPumpRequestCopyWith<FuelPumpRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FuelPumpRequestCopyWith<$Res> {
  factory $FuelPumpRequestCopyWith(
          FuelPumpRequest value, $Res Function(FuelPumpRequest) then) =
      _$FuelPumpRequestCopyWithImpl<$Res, FuelPumpRequest>;
  @useResult
  $Res call({String command, String id});
}

/// @nodoc
class _$FuelPumpRequestCopyWithImpl<$Res, $Val extends FuelPumpRequest>
    implements $FuelPumpRequestCopyWith<$Res> {
  _$FuelPumpRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? command = null,
    Object? id = null,
  }) {
    return _then(_value.copyWith(
      command: null == command
          ? _value.command
          : command // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FuelPumpRequestImplCopyWith<$Res>
    implements $FuelPumpRequestCopyWith<$Res> {
  factory _$$FuelPumpRequestImplCopyWith(_$FuelPumpRequestImpl value,
          $Res Function(_$FuelPumpRequestImpl) then) =
      __$$FuelPumpRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String command, String id});
}

/// @nodoc
class __$$FuelPumpRequestImplCopyWithImpl<$Res>
    extends _$FuelPumpRequestCopyWithImpl<$Res, _$FuelPumpRequestImpl>
    implements _$$FuelPumpRequestImplCopyWith<$Res> {
  __$$FuelPumpRequestImplCopyWithImpl(
      _$FuelPumpRequestImpl _value, $Res Function(_$FuelPumpRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? command = null,
    Object? id = null,
  }) {
    return _then(_$FuelPumpRequestImpl(
      command: null == command
          ? _value.command
          : command // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FuelPumpRequestImpl extends _FuelPumpRequest {
  const _$FuelPumpRequestImpl({required this.command, required this.id})
      : super._();

  factory _$FuelPumpRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$FuelPumpRequestImplFromJson(json);

  @override
  final String command;
  @override
  final String id;

  @override
  String toString() {
    return 'FuelPumpRequest(command: $command, id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FuelPumpRequestImpl &&
            (identical(other.command, command) || other.command == command) &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, command, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FuelPumpRequestImplCopyWith<_$FuelPumpRequestImpl> get copyWith =>
      __$$FuelPumpRequestImplCopyWithImpl<_$FuelPumpRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FuelPumpRequestImplToJson(
      this,
    );
  }
}

abstract class _FuelPumpRequest extends FuelPumpRequest {
  const factory _FuelPumpRequest(
      {required final String command,
      required final String id}) = _$FuelPumpRequestImpl;
  const _FuelPumpRequest._() : super._();

  factory _FuelPumpRequest.fromJson(Map<String, dynamic> json) =
      _$FuelPumpRequestImpl.fromJson;

  @override
  String get command;
  @override
  String get id;
  @override
  @JsonKey(ignore: true)
  _$$FuelPumpRequestImplCopyWith<_$FuelPumpRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
