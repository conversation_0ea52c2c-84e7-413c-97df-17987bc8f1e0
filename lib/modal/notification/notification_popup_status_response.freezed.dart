// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_popup_status_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NotificationPopUpStatusResponse _$NotificationPopUpStatusResponseFromJson(
    Map<String, dynamic> json) {
  return _NotificationPopUpStatusResponse.fromJson(json);
}

/// @nodoc
mixin _$NotificationPopUpStatusResponse {
  int get count => throw _privateConstructorUsedError;
  List<String> get notificationIds => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NotificationPopUpStatusResponseCopyWith<NotificationPopUpStatusResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationPopUpStatusResponseCopyWith<$Res> {
  factory $NotificationPopUpStatusResponseCopyWith(
          NotificationPopUpStatusResponse value,
          $Res Function(NotificationPopUpStatusResponse) then) =
      _$NotificationPopUpStatusResponseCopyWithImpl<$Res,
          NotificationPopUpStatusResponse>;
  @useResult
  $Res call({int count, List<String> notificationIds});
}

/// @nodoc
class _$NotificationPopUpStatusResponseCopyWithImpl<$Res,
        $Val extends NotificationPopUpStatusResponse>
    implements $NotificationPopUpStatusResponseCopyWith<$Res> {
  _$NotificationPopUpStatusResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? count = null,
    Object? notificationIds = null,
  }) {
    return _then(_value.copyWith(
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      notificationIds: null == notificationIds
          ? _value.notificationIds
          : notificationIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationPopUpStatusResponseImplCopyWith<$Res>
    implements $NotificationPopUpStatusResponseCopyWith<$Res> {
  factory _$$NotificationPopUpStatusResponseImplCopyWith(
          _$NotificationPopUpStatusResponseImpl value,
          $Res Function(_$NotificationPopUpStatusResponseImpl) then) =
      __$$NotificationPopUpStatusResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int count, List<String> notificationIds});
}

/// @nodoc
class __$$NotificationPopUpStatusResponseImplCopyWithImpl<$Res>
    extends _$NotificationPopUpStatusResponseCopyWithImpl<$Res,
        _$NotificationPopUpStatusResponseImpl>
    implements _$$NotificationPopUpStatusResponseImplCopyWith<$Res> {
  __$$NotificationPopUpStatusResponseImplCopyWithImpl(
      _$NotificationPopUpStatusResponseImpl _value,
      $Res Function(_$NotificationPopUpStatusResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? count = null,
    Object? notificationIds = null,
  }) {
    return _then(_$NotificationPopUpStatusResponseImpl(
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      notificationIds: null == notificationIds
          ? _value._notificationIds
          : notificationIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationPopUpStatusResponseImpl
    extends _NotificationPopUpStatusResponse {
  const _$NotificationPopUpStatusResponseImpl(
      {required this.count, required final List<String> notificationIds})
      : _notificationIds = notificationIds,
        super._();

  factory _$NotificationPopUpStatusResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$NotificationPopUpStatusResponseImplFromJson(json);

  @override
  final int count;
  final List<String> _notificationIds;
  @override
  List<String> get notificationIds {
    if (_notificationIds is EqualUnmodifiableListView) return _notificationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_notificationIds);
  }

  @override
  String toString() {
    return 'NotificationPopUpStatusResponse(count: $count, notificationIds: $notificationIds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationPopUpStatusResponseImpl &&
            (identical(other.count, count) || other.count == count) &&
            const DeepCollectionEquality()
                .equals(other._notificationIds, _notificationIds));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, count,
      const DeepCollectionEquality().hash(_notificationIds));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationPopUpStatusResponseImplCopyWith<
          _$NotificationPopUpStatusResponseImpl>
      get copyWith => __$$NotificationPopUpStatusResponseImplCopyWithImpl<
          _$NotificationPopUpStatusResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationPopUpStatusResponseImplToJson(
      this,
    );
  }
}

abstract class _NotificationPopUpStatusResponse
    extends NotificationPopUpStatusResponse {
  const factory _NotificationPopUpStatusResponse(
          {required final int count,
          required final List<String> notificationIds}) =
      _$NotificationPopUpStatusResponseImpl;
  const _NotificationPopUpStatusResponse._() : super._();

  factory _NotificationPopUpStatusResponse.fromJson(Map<String, dynamic> json) =
      _$NotificationPopUpStatusResponseImpl.fromJson;

  @override
  int get count;
  @override
  List<String> get notificationIds;
  @override
  @JsonKey(ignore: true)
  _$$NotificationPopUpStatusResponseImplCopyWith<
          _$NotificationPopUpStatusResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
