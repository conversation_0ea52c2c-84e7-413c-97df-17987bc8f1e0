// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_setting_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationSettingResponseImpl _$$NotificationSettingResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationSettingResponseImpl(
      total: (json['total'] as num).toInt(),
      data: (json['data'] as List<dynamic>)
          .map((e) => NotificationSetting.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$NotificationSettingResponseImplToJson(
        _$NotificationSettingResponseImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'data': instance.data,
    };

_$NotificationSettingImpl _$$NotificationSettingImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationSettingImpl(
      id: json['id'] as String,
      type: (json['type'] as num).toInt(),
      typeName: json['typeName'] as String,
      description: json['description'] as String,
      active: json['active'] as bool,
      lastModified: json['lastModified'] as String,
      deviceTypes: json['deviceTypes'] as String,
      priority: (json['priority'] as num).toInt(),
      groupIndex: (json['groupIndex'] as num).toInt(),
      group: json['group'] as String?,
    );

Map<String, dynamic> _$$NotificationSettingImplToJson(
        _$NotificationSettingImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'typeName': instance.typeName,
      'description': instance.description,
      'active': instance.active,
      'lastModified': instance.lastModified,
      'deviceTypes': instance.deviceTypes,
      'priority': instance.priority,
      'groupIndex': instance.groupIndex,
      'group': instance.group,
    };
