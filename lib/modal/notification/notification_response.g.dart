// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationResponseImpl _$$NotificationResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => NotificationInApp.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$NotificationResponseImplToJson(
        _$NotificationResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

_$NotificationInAppImpl _$$NotificationInAppImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationInAppImpl(
      id: json['id'] as String,
      time: (json['time'] as num).toInt(),
      type: (json['type'] as num).toInt(),
      vehicleId: json['vehicleId'] as String,
      event: json['event'] as String,
      level: (json['level'] as num).toInt(),
      readTime: (json['readTime'] as num).toInt(),
      title: json['title'] as String,
      address: json['address'] as String,
      x: (json['x'] as num).toInt(),
      y: (json['y'] as num).toInt(),
    );

Map<String, dynamic> _$$NotificationInAppImplToJson(
        _$NotificationInAppImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'time': instance.time,
      'type': instance.type,
      'vehicleId': instance.vehicleId,
      'event': instance.event,
      'level': instance.level,
      'readTime': instance.readTime,
      'title': instance.title,
      'address': instance.address,
      'x': instance.x,
      'y': instance.y,
    };
