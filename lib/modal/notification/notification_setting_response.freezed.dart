// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_setting_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NotificationSettingResponse _$NotificationSettingResponseFromJson(
    Map<String, dynamic> json) {
  return _NotificationSettingResponse.fromJson(json);
}

/// @nodoc
mixin _$NotificationSettingResponse {
  int get total => throw _privateConstructorUsedError;
  List<NotificationSetting> get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NotificationSettingResponseCopyWith<NotificationSettingResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationSettingResponseCopyWith<$Res> {
  factory $NotificationSettingResponseCopyWith(
          NotificationSettingResponse value,
          $Res Function(NotificationSettingResponse) then) =
      _$NotificationSettingResponseCopyWithImpl<$Res,
          NotificationSettingResponse>;
  @useResult
  $Res call({int total, List<NotificationSetting> data});
}

/// @nodoc
class _$NotificationSettingResponseCopyWithImpl<$Res,
        $Val extends NotificationSettingResponse>
    implements $NotificationSettingResponseCopyWith<$Res> {
  _$NotificationSettingResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<NotificationSetting>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationSettingResponseImplCopyWith<$Res>
    implements $NotificationSettingResponseCopyWith<$Res> {
  factory _$$NotificationSettingResponseImplCopyWith(
          _$NotificationSettingResponseImpl value,
          $Res Function(_$NotificationSettingResponseImpl) then) =
      __$$NotificationSettingResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, List<NotificationSetting> data});
}

/// @nodoc
class __$$NotificationSettingResponseImplCopyWithImpl<$Res>
    extends _$NotificationSettingResponseCopyWithImpl<$Res,
        _$NotificationSettingResponseImpl>
    implements _$$NotificationSettingResponseImplCopyWith<$Res> {
  __$$NotificationSettingResponseImplCopyWithImpl(
      _$NotificationSettingResponseImpl _value,
      $Res Function(_$NotificationSettingResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? data = null,
  }) {
    return _then(_$NotificationSettingResponseImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<NotificationSetting>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationSettingResponseImpl extends _NotificationSettingResponse {
  const _$NotificationSettingResponseImpl(
      {required this.total, required final List<NotificationSetting> data})
      : _data = data,
        super._();

  factory _$NotificationSettingResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$NotificationSettingResponseImplFromJson(json);

  @override
  final int total;
  final List<NotificationSetting> _data;
  @override
  List<NotificationSetting> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'NotificationSettingResponse(total: $total, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationSettingResponseImpl &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, total, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationSettingResponseImplCopyWith<_$NotificationSettingResponseImpl>
      get copyWith => __$$NotificationSettingResponseImplCopyWithImpl<
          _$NotificationSettingResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationSettingResponseImplToJson(
      this,
    );
  }
}

abstract class _NotificationSettingResponse
    extends NotificationSettingResponse {
  const factory _NotificationSettingResponse(
          {required final int total,
          required final List<NotificationSetting> data}) =
      _$NotificationSettingResponseImpl;
  const _NotificationSettingResponse._() : super._();

  factory _NotificationSettingResponse.fromJson(Map<String, dynamic> json) =
      _$NotificationSettingResponseImpl.fromJson;

  @override
  int get total;
  @override
  List<NotificationSetting> get data;
  @override
  @JsonKey(ignore: true)
  _$$NotificationSettingResponseImplCopyWith<_$NotificationSettingResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

NotificationSetting _$NotificationSettingFromJson(Map<String, dynamic> json) {
  return _NotificationSetting.fromJson(json);
}

/// @nodoc
mixin _$NotificationSetting {
  String get id => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  String get typeName => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  bool get active => throw _privateConstructorUsedError;
  String get lastModified => throw _privateConstructorUsedError;
  String get deviceTypes => throw _privateConstructorUsedError;
  int get priority => throw _privateConstructorUsedError;
  int get groupIndex => throw _privateConstructorUsedError;
  String? get group => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NotificationSettingCopyWith<NotificationSetting> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationSettingCopyWith<$Res> {
  factory $NotificationSettingCopyWith(
          NotificationSetting value, $Res Function(NotificationSetting) then) =
      _$NotificationSettingCopyWithImpl<$Res, NotificationSetting>;
  @useResult
  $Res call(
      {String id,
      int type,
      String typeName,
      String description,
      bool active,
      String lastModified,
      String deviceTypes,
      int priority,
      int groupIndex,
      String? group});
}

/// @nodoc
class _$NotificationSettingCopyWithImpl<$Res, $Val extends NotificationSetting>
    implements $NotificationSettingCopyWith<$Res> {
  _$NotificationSettingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? typeName = null,
    Object? description = null,
    Object? active = null,
    Object? lastModified = null,
    Object? deviceTypes = null,
    Object? priority = null,
    Object? groupIndex = null,
    Object? group = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      typeName: null == typeName
          ? _value.typeName
          : typeName // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      active: null == active
          ? _value.active
          : active // ignore: cast_nullable_to_non_nullable
              as bool,
      lastModified: null == lastModified
          ? _value.lastModified
          : lastModified // ignore: cast_nullable_to_non_nullable
              as String,
      deviceTypes: null == deviceTypes
          ? _value.deviceTypes
          : deviceTypes // ignore: cast_nullable_to_non_nullable
              as String,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      groupIndex: null == groupIndex
          ? _value.groupIndex
          : groupIndex // ignore: cast_nullable_to_non_nullable
              as int,
      group: freezed == group
          ? _value.group
          : group // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationSettingImplCopyWith<$Res>
    implements $NotificationSettingCopyWith<$Res> {
  factory _$$NotificationSettingImplCopyWith(_$NotificationSettingImpl value,
          $Res Function(_$NotificationSettingImpl) then) =
      __$$NotificationSettingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      int type,
      String typeName,
      String description,
      bool active,
      String lastModified,
      String deviceTypes,
      int priority,
      int groupIndex,
      String? group});
}

/// @nodoc
class __$$NotificationSettingImplCopyWithImpl<$Res>
    extends _$NotificationSettingCopyWithImpl<$Res, _$NotificationSettingImpl>
    implements _$$NotificationSettingImplCopyWith<$Res> {
  __$$NotificationSettingImplCopyWithImpl(_$NotificationSettingImpl _value,
      $Res Function(_$NotificationSettingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? typeName = null,
    Object? description = null,
    Object? active = null,
    Object? lastModified = null,
    Object? deviceTypes = null,
    Object? priority = null,
    Object? groupIndex = null,
    Object? group = freezed,
  }) {
    return _then(_$NotificationSettingImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      typeName: null == typeName
          ? _value.typeName
          : typeName // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      active: null == active
          ? _value.active
          : active // ignore: cast_nullable_to_non_nullable
              as bool,
      lastModified: null == lastModified
          ? _value.lastModified
          : lastModified // ignore: cast_nullable_to_non_nullable
              as String,
      deviceTypes: null == deviceTypes
          ? _value.deviceTypes
          : deviceTypes // ignore: cast_nullable_to_non_nullable
              as String,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      groupIndex: null == groupIndex
          ? _value.groupIndex
          : groupIndex // ignore: cast_nullable_to_non_nullable
              as int,
      group: freezed == group
          ? _value.group
          : group // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationSettingImpl extends _NotificationSetting {
  const _$NotificationSettingImpl(
      {required this.id,
      required this.type,
      required this.typeName,
      required this.description,
      required this.active,
      required this.lastModified,
      required this.deviceTypes,
      required this.priority,
      required this.groupIndex,
      required this.group})
      : super._();

  factory _$NotificationSettingImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationSettingImplFromJson(json);

  @override
  final String id;
  @override
  final int type;
  @override
  final String typeName;
  @override
  final String description;
  @override
  final bool active;
  @override
  final String lastModified;
  @override
  final String deviceTypes;
  @override
  final int priority;
  @override
  final int groupIndex;
  @override
  final String? group;

  @override
  String toString() {
    return 'NotificationSetting(id: $id, type: $type, typeName: $typeName, description: $description, active: $active, lastModified: $lastModified, deviceTypes: $deviceTypes, priority: $priority, groupIndex: $groupIndex, group: $group)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationSettingImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.typeName, typeName) ||
                other.typeName == typeName) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.active, active) || other.active == active) &&
            (identical(other.lastModified, lastModified) ||
                other.lastModified == lastModified) &&
            (identical(other.deviceTypes, deviceTypes) ||
                other.deviceTypes == deviceTypes) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.groupIndex, groupIndex) ||
                other.groupIndex == groupIndex) &&
            (identical(other.group, group) || other.group == group));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, type, typeName, description,
      active, lastModified, deviceTypes, priority, groupIndex, group);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationSettingImplCopyWith<_$NotificationSettingImpl> get copyWith =>
      __$$NotificationSettingImplCopyWithImpl<_$NotificationSettingImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationSettingImplToJson(
      this,
    );
  }
}

abstract class _NotificationSetting extends NotificationSetting {
  const factory _NotificationSetting(
      {required final String id,
      required final int type,
      required final String typeName,
      required final String description,
      required final bool active,
      required final String lastModified,
      required final String deviceTypes,
      required final int priority,
      required final int groupIndex,
      required final String? group}) = _$NotificationSettingImpl;
  const _NotificationSetting._() : super._();

  factory _NotificationSetting.fromJson(Map<String, dynamic> json) =
      _$NotificationSettingImpl.fromJson;

  @override
  String get id;
  @override
  int get type;
  @override
  String get typeName;
  @override
  String get description;
  @override
  bool get active;
  @override
  String get lastModified;
  @override
  String get deviceTypes;
  @override
  int get priority;
  @override
  int get groupIndex;
  @override
  String? get group;
  @override
  @JsonKey(ignore: true)
  _$$NotificationSettingImplCopyWith<_$NotificationSettingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
