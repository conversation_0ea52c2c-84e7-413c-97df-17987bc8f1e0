// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NotificationResponse _$NotificationResponseFromJson(Map<String, dynamic> json) {
  return _NotificationResponse.fromJson(json);
}

/// @nodoc
mixin _$NotificationResponse {
  List<NotificationInApp> get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NotificationResponseCopyWith<NotificationResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationResponseCopyWith<$Res> {
  factory $NotificationResponseCopyWith(NotificationResponse value,
          $Res Function(NotificationResponse) then) =
      _$NotificationResponseCopyWithImpl<$Res, NotificationResponse>;
  @useResult
  $Res call({List<NotificationInApp> data});
}

/// @nodoc
class _$NotificationResponseCopyWithImpl<$Res,
        $Val extends NotificationResponse>
    implements $NotificationResponseCopyWith<$Res> {
  _$NotificationResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<NotificationInApp>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationResponseImplCopyWith<$Res>
    implements $NotificationResponseCopyWith<$Res> {
  factory _$$NotificationResponseImplCopyWith(_$NotificationResponseImpl value,
          $Res Function(_$NotificationResponseImpl) then) =
      __$$NotificationResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<NotificationInApp> data});
}

/// @nodoc
class __$$NotificationResponseImplCopyWithImpl<$Res>
    extends _$NotificationResponseCopyWithImpl<$Res, _$NotificationResponseImpl>
    implements _$$NotificationResponseImplCopyWith<$Res> {
  __$$NotificationResponseImplCopyWithImpl(_$NotificationResponseImpl _value,
      $Res Function(_$NotificationResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$NotificationResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<NotificationInApp>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationResponseImpl extends _NotificationResponse {
  const _$NotificationResponseImpl(
      {required final List<NotificationInApp> data})
      : _data = data,
        super._();

  factory _$NotificationResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationResponseImplFromJson(json);

  final List<NotificationInApp> _data;
  @override
  List<NotificationInApp> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'NotificationResponse(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationResponseImplCopyWith<_$NotificationResponseImpl>
      get copyWith =>
          __$$NotificationResponseImplCopyWithImpl<_$NotificationResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationResponseImplToJson(
      this,
    );
  }
}

abstract class _NotificationResponse extends NotificationResponse {
  const factory _NotificationResponse(
          {required final List<NotificationInApp> data}) =
      _$NotificationResponseImpl;
  const _NotificationResponse._() : super._();

  factory _NotificationResponse.fromJson(Map<String, dynamic> json) =
      _$NotificationResponseImpl.fromJson;

  @override
  List<NotificationInApp> get data;
  @override
  @JsonKey(ignore: true)
  _$$NotificationResponseImplCopyWith<_$NotificationResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

NotificationInApp _$NotificationInAppFromJson(Map<String, dynamic> json) {
  return _NotificationInApp.fromJson(json);
}

/// @nodoc
mixin _$NotificationInApp {
  String get id => throw _privateConstructorUsedError;
  int get time => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  String get vehicleId => throw _privateConstructorUsedError;
  String get event => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  int get readTime => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get address => throw _privateConstructorUsedError;
  int get x => throw _privateConstructorUsedError;
  int get y => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NotificationInAppCopyWith<NotificationInApp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationInAppCopyWith<$Res> {
  factory $NotificationInAppCopyWith(
          NotificationInApp value, $Res Function(NotificationInApp) then) =
      _$NotificationInAppCopyWithImpl<$Res, NotificationInApp>;
  @useResult
  $Res call(
      {String id,
      int time,
      int type,
      String vehicleId,
      String event,
      int level,
      int readTime,
      String title,
      String address,
      int x,
      int y});
}

/// @nodoc
class _$NotificationInAppCopyWithImpl<$Res, $Val extends NotificationInApp>
    implements $NotificationInAppCopyWith<$Res> {
  _$NotificationInAppCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? time = null,
    Object? type = null,
    Object? vehicleId = null,
    Object? event = null,
    Object? level = null,
    Object? readTime = null,
    Object? title = null,
    Object? address = null,
    Object? x = null,
    Object? y = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      event: null == event
          ? _value.event
          : event // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      readTime: null == readTime
          ? _value.readTime
          : readTime // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as int,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationInAppImplCopyWith<$Res>
    implements $NotificationInAppCopyWith<$Res> {
  factory _$$NotificationInAppImplCopyWith(_$NotificationInAppImpl value,
          $Res Function(_$NotificationInAppImpl) then) =
      __$$NotificationInAppImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      int time,
      int type,
      String vehicleId,
      String event,
      int level,
      int readTime,
      String title,
      String address,
      int x,
      int y});
}

/// @nodoc
class __$$NotificationInAppImplCopyWithImpl<$Res>
    extends _$NotificationInAppCopyWithImpl<$Res, _$NotificationInAppImpl>
    implements _$$NotificationInAppImplCopyWith<$Res> {
  __$$NotificationInAppImplCopyWithImpl(_$NotificationInAppImpl _value,
      $Res Function(_$NotificationInAppImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? time = null,
    Object? type = null,
    Object? vehicleId = null,
    Object? event = null,
    Object? level = null,
    Object? readTime = null,
    Object? title = null,
    Object? address = null,
    Object? x = null,
    Object? y = null,
  }) {
    return _then(_$NotificationInAppImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      vehicleId: null == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String,
      event: null == event
          ? _value.event
          : event // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      readTime: null == readTime
          ? _value.readTime
          : readTime // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as int,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationInAppImpl extends _NotificationInApp {
  const _$NotificationInAppImpl(
      {required this.id,
      required this.time,
      required this.type,
      required this.vehicleId,
      required this.event,
      required this.level,
      required this.readTime,
      required this.title,
      required this.address,
      required this.x,
      required this.y})
      : super._();

  factory _$NotificationInAppImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationInAppImplFromJson(json);

  @override
  final String id;
  @override
  final int time;
  @override
  final int type;
  @override
  final String vehicleId;
  @override
  final String event;
  @override
  final int level;
  @override
  final int readTime;
  @override
  final String title;
  @override
  final String address;
  @override
  final int x;
  @override
  final int y;

  @override
  String toString() {
    return 'NotificationInApp(id: $id, time: $time, type: $type, vehicleId: $vehicleId, event: $event, level: $level, readTime: $readTime, title: $title, address: $address, x: $x, y: $y)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationInAppImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.time, time) || other.time == time) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId) &&
            (identical(other.event, event) || other.event == event) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.readTime, readTime) ||
                other.readTime == readTime) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, time, type, vehicleId, event,
      level, readTime, title, address, x, y);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationInAppImplCopyWith<_$NotificationInAppImpl> get copyWith =>
      __$$NotificationInAppImplCopyWithImpl<_$NotificationInAppImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationInAppImplToJson(
      this,
    );
  }
}

abstract class _NotificationInApp extends NotificationInApp {
  const factory _NotificationInApp(
      {required final String id,
      required final int time,
      required final int type,
      required final String vehicleId,
      required final String event,
      required final int level,
      required final int readTime,
      required final String title,
      required final String address,
      required final int x,
      required final int y}) = _$NotificationInAppImpl;
  const _NotificationInApp._() : super._();

  factory _NotificationInApp.fromJson(Map<String, dynamic> json) =
      _$NotificationInAppImpl.fromJson;

  @override
  String get id;
  @override
  int get time;
  @override
  int get type;
  @override
  String get vehicleId;
  @override
  String get event;
  @override
  int get level;
  @override
  int get readTime;
  @override
  String get title;
  @override
  String get address;
  @override
  int get x;
  @override
  int get y;
  @override
  @JsonKey(ignore: true)
  _$$NotificationInAppImplCopyWith<_$NotificationInAppImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
