// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_popup.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationPopUpImpl _$$NotificationPopUpImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationPopUpImpl(
      title: json['title'] as String,
      content: json['content'] as String,
      isDisplay: json['isDisplay'] as bool,
      fromTime: (json['fromTime'] as num).toInt(),
      toTime: (json['toTime'] as num).toInt(),
      type: (json['type'] as num).toInt(),
      isShowLogin: json['isShowLogin'] as bool,
    );

Map<String, dynamic> _$$NotificationPopUpImplToJson(
        _$NotificationPopUpImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'content': instance.content,
      'isDisplay': instance.isDisplay,
      'fromTime': instance.fromTime,
      'toTime': instance.toTime,
      'type': instance.type,
      'isShowLogin': instance.isShowLogin,
    };
