// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_popup.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NotificationPopUp _$NotificationPopUpFromJson(Map<String, dynamic> json) {
  return _NotificationPopUp.fromJson(json);
}

/// @nodoc
mixin _$NotificationPopUp {
  String get title => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  bool get isDisplay => throw _privateConstructorUsedError;
  int get fromTime => throw _privateConstructorUsedError;
  int get toTime => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  bool get isShowLogin => throw _privateConstructorUsedError;
  @JsonKey(includeFromJson: false, includeToJson: false, defaultValue: false)
  bool get isRead => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NotificationPopUpCopyWith<NotificationPopUp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationPopUpCopyWith<$Res> {
  factory $NotificationPopUpCopyWith(
          NotificationPopUp value, $Res Function(NotificationPopUp) then) =
      _$NotificationPopUpCopyWithImpl<$Res, NotificationPopUp>;
  @useResult
  $Res call(
      {String title,
      String content,
      bool isDisplay,
      int fromTime,
      int toTime,
      int type,
      bool isShowLogin,
      @JsonKey(
          includeFromJson: false, includeToJson: false, defaultValue: false)
      bool isRead});
}

/// @nodoc
class _$NotificationPopUpCopyWithImpl<$Res, $Val extends NotificationPopUp>
    implements $NotificationPopUpCopyWith<$Res> {
  _$NotificationPopUpCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? content = null,
    Object? isDisplay = null,
    Object? fromTime = null,
    Object? toTime = null,
    Object? type = null,
    Object? isShowLogin = null,
    Object? isRead = null,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      isDisplay: null == isDisplay
          ? _value.isDisplay
          : isDisplay // ignore: cast_nullable_to_non_nullable
              as bool,
      fromTime: null == fromTime
          ? _value.fromTime
          : fromTime // ignore: cast_nullable_to_non_nullable
              as int,
      toTime: null == toTime
          ? _value.toTime
          : toTime // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      isShowLogin: null == isShowLogin
          ? _value.isShowLogin
          : isShowLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      isRead: null == isRead
          ? _value.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationPopUpImplCopyWith<$Res>
    implements $NotificationPopUpCopyWith<$Res> {
  factory _$$NotificationPopUpImplCopyWith(_$NotificationPopUpImpl value,
          $Res Function(_$NotificationPopUpImpl) then) =
      __$$NotificationPopUpImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String title,
      String content,
      bool isDisplay,
      int fromTime,
      int toTime,
      int type,
      bool isShowLogin,
      @JsonKey(
          includeFromJson: false, includeToJson: false, defaultValue: false)
      bool isRead});
}

/// @nodoc
class __$$NotificationPopUpImplCopyWithImpl<$Res>
    extends _$NotificationPopUpCopyWithImpl<$Res, _$NotificationPopUpImpl>
    implements _$$NotificationPopUpImplCopyWith<$Res> {
  __$$NotificationPopUpImplCopyWithImpl(_$NotificationPopUpImpl _value,
      $Res Function(_$NotificationPopUpImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? content = null,
    Object? isDisplay = null,
    Object? fromTime = null,
    Object? toTime = null,
    Object? type = null,
    Object? isShowLogin = null,
    Object? isRead = null,
  }) {
    return _then(_$NotificationPopUpImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      isDisplay: null == isDisplay
          ? _value.isDisplay
          : isDisplay // ignore: cast_nullable_to_non_nullable
              as bool,
      fromTime: null == fromTime
          ? _value.fromTime
          : fromTime // ignore: cast_nullable_to_non_nullable
              as int,
      toTime: null == toTime
          ? _value.toTime
          : toTime // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      isShowLogin: null == isShowLogin
          ? _value.isShowLogin
          : isShowLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      isRead: null == isRead
          ? _value.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationPopUpImpl extends _NotificationPopUp {
  const _$NotificationPopUpImpl(
      {required this.title,
      required this.content,
      required this.isDisplay,
      required this.fromTime,
      required this.toTime,
      required this.type,
      required this.isShowLogin,
      @JsonKey(
          includeFromJson: false, includeToJson: false, defaultValue: false)
      this.isRead = false})
      : super._();

  factory _$NotificationPopUpImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationPopUpImplFromJson(json);

  @override
  final String title;
  @override
  final String content;
  @override
  final bool isDisplay;
  @override
  final int fromTime;
  @override
  final int toTime;
  @override
  final int type;
  @override
  final bool isShowLogin;
  @override
  @JsonKey(includeFromJson: false, includeToJson: false, defaultValue: false)
  final bool isRead;

  @override
  String toString() {
    return 'NotificationPopUp(title: $title, content: $content, isDisplay: $isDisplay, fromTime: $fromTime, toTime: $toTime, type: $type, isShowLogin: $isShowLogin, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationPopUpImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.isDisplay, isDisplay) ||
                other.isDisplay == isDisplay) &&
            (identical(other.fromTime, fromTime) ||
                other.fromTime == fromTime) &&
            (identical(other.toTime, toTime) || other.toTime == toTime) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.isShowLogin, isShowLogin) ||
                other.isShowLogin == isShowLogin) &&
            (identical(other.isRead, isRead) || other.isRead == isRead));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, content, isDisplay,
      fromTime, toTime, type, isShowLogin, isRead);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationPopUpImplCopyWith<_$NotificationPopUpImpl> get copyWith =>
      __$$NotificationPopUpImplCopyWithImpl<_$NotificationPopUpImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationPopUpImplToJson(
      this,
    );
  }
}

abstract class _NotificationPopUp extends NotificationPopUp {
  const factory _NotificationPopUp(
      {required final String title,
      required final String content,
      required final bool isDisplay,
      required final int fromTime,
      required final int toTime,
      required final int type,
      required final bool isShowLogin,
      @JsonKey(
          includeFromJson: false, includeToJson: false, defaultValue: false)
      final bool isRead}) = _$NotificationPopUpImpl;
  const _NotificationPopUp._() : super._();

  factory _NotificationPopUp.fromJson(Map<String, dynamic> json) =
      _$NotificationPopUpImpl.fromJson;

  @override
  String get title;
  @override
  String get content;
  @override
  bool get isDisplay;
  @override
  int get fromTime;
  @override
  int get toTime;
  @override
  int get type;
  @override
  bool get isShowLogin;
  @override
  @JsonKey(includeFromJson: false, includeToJson: false, defaultValue: false)
  bool get isRead;
  @override
  @JsonKey(ignore: true)
  _$$NotificationPopUpImplCopyWith<_$NotificationPopUpImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
