// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:avema_v2/application/injection_module.dart' as _i33;
import 'package:avema_v2/application/utils/local_cache.dart' as _i11;
import 'package:avema_v2/features/changelog/bloc/changelog_bloc.dart' as _i6;
import 'package:avema_v2/features/curl_helper/application/curl_helper_bloc.dart'
    as _i9;
import 'package:avema_v2/features/fuel_pump/application/fuel_pump_bloc.dart'
    as _i26;
import 'package:avema_v2/features/fuel_pump/data/repo/fuel_pump_repo.dart'
    as _i15;
import 'package:avema_v2/features/history_route/data/repo/history_route_repo.dart'
    as _i17;
import 'package:avema_v2/features/language/data/repo/language_repo.dart'
    as _i21;
import 'package:avema_v2/features/locator/application/quick_send_locator_bloc.dart'
    as _i24;
import 'package:avema_v2/features/locator/data/repos/locator_repo.dart' as _i14;
import 'package:avema_v2/features/login/application/auth_bloc.dart' as _i31;
import 'package:avema_v2/features/login/data/repos/auth_repo.dart' as _i18;
import 'package:avema_v2/features/manage_vehicle/application/detail_edit_vehicle_group_bloc.dart'
    as _i28;
import 'package:avema_v2/features/manage_vehicle/data/repo/manage_vehicle_repo.dart'
    as _i29;
import 'package:avema_v2/features/mdvr/live_stream/applications/window_live_player_bloc.dart'
    as _i27;
import 'package:avema_v2/features/mdvr/live_stream/data/repos/live_stream_repo.dart'
    as _i16;
import 'package:avema_v2/features/monitor/application/bottom_sheet/bottom_sheet_bloc.dart'
    as _i5;
import 'package:avema_v2/features/monitor/application/monitor/monitor_bloc.dart'
    as _i32;
import 'package:avema_v2/features/monitor/application/moving_map/moving_map_bloc.dart'
    as _i4;
import 'package:avema_v2/features/monitor/application/quick_history_waypoint/quick_history_waypoint_bloc.dart'
    as _i25;
import 'package:avema_v2/features/monitor/application/visible_plate_monitor/visible_plate_monitor_bloc.dart'
    as _i3;
import 'package:avema_v2/features/monitor/data/repos/monitor_repo.dart' as _i23;
import 'package:avema_v2/features/notification_in_app/applications/notification_from_server_bloc.dart'
    as _i30;
import 'package:avema_v2/features/notification_in_app/applications/receiver_notification_bloc.dart'
    as _i10;
import 'package:avema_v2/features/notification_in_app/data/repo/notification_repo.dart'
    as _i19;
import 'package:avema_v2/features/setting/setting_bloc.dart' as _i8;
import 'package:avema_v2/features/theme/theme_bloc.dart' as _i7;
import 'package:avema_v2/features/vehicle_image/applications/vehicle_image_bloc.dart'
    as _i22;
import 'package:avema_v2/features/vehicle_image/data/vehicle_image_repo.dart'
    as _i20;
import 'package:avema_v2/rest_client/main_rest_client.dart' as _i13;
import 'package:dio/dio.dart' as _i12;
import 'package:get_it/get_it.dart' as _i1;
import 'package:injectable/injectable.dart' as _i2;

extension GetItInjectableX on _i1.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i1.GetIt init({
    String? environment,
    _i2.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i2.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final clientInjectableModule = _$ClientInjectableModule();
    gh.lazySingleton<_i3.VisiblePlateMonitorBloc>(
        () => _i3.VisiblePlateMonitorBloc());
    gh.lazySingleton<_i4.MovingMapBloc>(() => _i4.MovingMapBloc());
    gh.lazySingleton<_i5.BottomSheetBloc>(() => _i5.BottomSheetBloc());
    gh.lazySingleton<_i6.ChangelogBloc>(() => _i6.ChangelogBloc());
    gh.lazySingleton<_i7.ThemeBloc>(() => _i7.ThemeBloc());
    gh.lazySingleton<_i8.SettingBloc>(() => _i8.SettingBloc());
    gh.lazySingleton<_i9.CurlHelperBloc>(() => _i9.CurlHelperBloc());
    gh.lazySingleton<_i10.ReceiverNotificationBloc>(
        () => _i10.ReceiverNotificationBloc());
    gh.lazySingleton<_i11.LocalCache>(() => _i11.LocalCache());
    gh.lazySingleton<_i12.Dio>(() => clientInjectableModule.dio);
    gh.lazySingleton<_i13.MainRESTClient>(
        () => clientInjectableModule.mainRestClient);
    gh.lazySingleton<_i14.ILocatorRepository>(() => _i14.LocatorRepository());
    gh.lazySingleton<_i15.IFuelPumpRepository>(() => _i15.FuelPumpRepository());
    gh.lazySingleton<_i16.ILiveStreamRepository>(
        () => _i16.LiveStreamRepository());
    gh.lazySingleton<_i17.IHistoryRouteRepository>(
        () => _i17.HistoryRouteRepository());
    gh.lazySingleton<_i18.IAuthRepository>(() => _i18.AuthRepository());
    gh.lazySingleton<_i19.INotificationRepository>(
        () => _i19.NotificationRepository());
    gh.lazySingleton<_i20.IVehicleImageRepository>(
        () => _i20.VehicleImageRepository());
    gh.lazySingleton<_i21.ILanguageRepository>(() => _i21.LanguageRepository());
    gh.lazySingleton<_i22.VehicleImageBloc>(() => _i22.VehicleImageBloc(
        liveStreamRepo: gh<_i20.IVehicleImageRepository>()));
    gh.lazySingleton<_i23.IMonitorRepository>(() => _i23.MonitorRepository());
    gh.lazySingleton<_i24.QuickSendLocatorBloc>(() =>
        _i24.QuickSendLocatorBloc(locatorRepo: gh<_i14.ILocatorRepository>()));
    gh.lazySingleton<_i25.QuickHistoryWaypointBloc>(() =>
        _i25.QuickHistoryWaypointBloc(
            historyRepo: gh<_i17.IHistoryRouteRepository>()));
    gh.lazySingleton<_i26.FuelPumpBloc>(
        () => _i26.FuelPumpBloc(fuelPumpRepo: gh<_i15.IFuelPumpRepository>()));
    gh.lazySingleton<_i27.WindowLivePlayerBloc>(() => _i27.WindowLivePlayerBloc(
        liveStreamRepo: gh<_i16.ILiveStreamRepository>()));
    gh.lazySingleton<_i28.DetailEditVehicleGroupBloc>(() =>
        _i28.DetailEditVehicleGroupBloc(
            manageVehicleRepository: gh<_i29.IManageVehicleRepository>()));
    gh.lazySingleton<_i30.NotificationFromServerBloc>(() =>
        _i30.NotificationFromServerBloc(
            notificationRepo: gh<_i19.INotificationRepository>()));
    gh.lazySingleton<_i31.AuthBloc>(
        () => _i31.AuthBloc(authRepo: gh<_i18.IAuthRepository>()));
    gh.lazySingleton<_i32.MonitorBloc>(
        () => _i32.MonitorBloc(monitorRepo: gh<_i23.IMonitorRepository>()));
    return this;
  }
}

class _$ClientInjectableModule extends _i33.ClientInjectableModule {}
